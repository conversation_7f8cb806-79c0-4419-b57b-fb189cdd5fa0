package appiconchange

const (
	ButtonTextYesKey           = "zm-yes"
	ButtonTextNoKey            = "zm-no"
	ButtonTextOkKey            = "zm-ok"
	goldDefaultTitleKey        = "zm-gold-app-icon-change-title-default"
	goldDefaultSubtitleKey     = "zm-gold-app-icon-change-subtitle-default"
	proDefaultTitleKey         = "zm-pro-app-icon-change-title-default"
	proDefaultSubtitleKey      = "zm-pro-app-icon-change-subtitle-default"
	goldInactiveTitleKey       = "zm-gold-app-icon-change-title-inactive"
	proInactiveTitleKey        = "zm-pro-app-icon-change-title-inactive"
	proplusInactiveTitleKey    = "zm-pro-plus-app-icon-change-title-inactive"
	defaultInactiveSubtitleKey = "zm-default-app-icon-change-subtitle-inactive"
)

type AppIconType string

const (
	AppIconTypeInvalid AppIconType = ""
	AppIconTypeGold    AppIconType = "gold"
	AppIconTypePro     AppIconType = "pro"
	AppIconTypeProPlus AppIconType = "pro_plus"
	AppIconTypeDefault AppIconType = "default"
)

var validAppIconsMap = map[AppIconType]bool{
	AppIconTypeGold:    true,
	AppIconTypePro:     true,
	AppIconTypeProPlus: true,
	AppIconTypeDefault: true,
	AppIconTypeInvalid: false,
}

type IconTransition struct {
	OldIcon, NewIcon AppIconType
}

var IconTransitionDefaultToPro = IconTransition{OldIcon: AppIconTypeDefault, NewIcon: AppIconTypePro}
var IconTransitionDefaultToGold = IconTransition{OldIcon: AppIconTypeDefault, NewIcon: AppIconTypeGold}
var IconTransitionProToGold = IconTransition{OldIcon: AppIconTypePro, NewIcon: AppIconTypeGold}
var IconTransitionProPlusToGold = IconTransition{OldIcon: AppIconTypeProPlus, NewIcon: AppIconTypeGold}
var IconTransitionProPlusToDefault = IconTransition{OldIcon: AppIconTypeProPlus, NewIcon: AppIconTypeDefault}
var IconTransitionProToDefault = IconTransition{OldIcon: AppIconTypePro, NewIcon: AppIconTypeDefault}
var IconTransitionGoldToDefault = IconTransition{OldIcon: AppIconTypeGold, NewIcon: AppIconTypeDefault}
var IconTransitionGoldToPro = IconTransition{OldIcon: AppIconTypeGold, NewIcon: AppIconTypePro}

// AppIconChangeTitle maps IconTransition to icon change title copies
var AppIconChangeTitle = map[IconTransition]string{
	IconTransitionDefaultToGold: goldDefaultTitleKey,
	IconTransitionProToGold:     goldDefaultTitleKey,
	IconTransitionProPlusToGold: goldDefaultTitleKey,

	IconTransitionDefaultToPro: proDefaultTitleKey,
	IconTransitionGoldToPro:    proDefaultTitleKey,

	IconTransitionGoldToDefault:    goldInactiveTitleKey,
	IconTransitionProPlusToDefault: proplusInactiveTitleKey,
	IconTransitionProToDefault:     proInactiveTitleKey,
}

// AppIconChangeSubtitle maps IconTransition to icon change subtitle copies
var AppIconChangeSubtitle = map[IconTransition]string{
	IconTransitionDefaultToGold: goldDefaultSubtitleKey,
	IconTransitionProToGold:     goldDefaultSubtitleKey,
	IconTransitionProPlusToGold: goldDefaultSubtitleKey,

	IconTransitionDefaultToPro: proDefaultSubtitleKey,
	IconTransitionGoldToPro:    proDefaultSubtitleKey,

	IconTransitionGoldToDefault:    defaultInactiveSubtitleKey,
	IconTransitionProPlusToDefault: defaultInactiveSubtitleKey,
	IconTransitionProToDefault:     defaultInactiveSubtitleKey,
}
