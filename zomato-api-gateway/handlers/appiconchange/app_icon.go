package appiconchange

func GetAppIconTransition(currentAppIcon, newAppIcon AppIconType) (appIconTransition IconTransition) {
	isOldIconValid := IsAppIconTypeStringValid(currentAppIcon)
	isNewIconValid := IsAppIconTypeStringValid(newAppIcon)
	if !isOldIconValid || !isNewIconValid {
		return
	}

	appIconTransition.OldIcon = AppIconType(currentAppIcon)
	appIconTransition.NewIcon = AppIconType(newAppIcon)
	return
}

func IsAppIconTypeStringValid(appIconType AppIconType) bool {
	isValid, ok := validAppIconsMap[appIconType]
	if ok && isValid {
		return true
	}

	return false
}
