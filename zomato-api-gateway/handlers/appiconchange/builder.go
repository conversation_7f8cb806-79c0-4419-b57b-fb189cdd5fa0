package appiconchange

import (
	"fmt"

	"github.com/Zomato/zomato-api-gateway/models/sushi"
)

type Builder struct {
	NewAppIconType                               AppIconType
	ShouldShowYesButton, ShouldShowNoButton      bool
	Title, Subtitle, YesButtonText, NoButtonText string
	YesButtonActionType, NoButtonActionType      sushi.ActionType
	YesButtonRefreshPages, NoButtonRefreshPages  []*sushi.RefreshPage
}

func (b *Builder) BuildChangeAppIconSnippet() (item *sushi.OpenChangeAppIcon, err error) {
	if b == nil {
		err = fmt.Errorf("[appiconchange/buildChangeAppIconSnippet] nil input")
		return
	}

	item = sushi.NewOpenChangeAppIcon(string(b.NewAppIconType))

	titleItem, err := sushi.NewTextSnippetItem(b.Title)
	if err != nil {
		err = fmt.Errorf("[appiconchange/buildChangeAppIconSnippet] Error in generating new sushi text snippet item - title: %v, error: %v", b.Title, err)
		return nil, err
	}
	item.SetTitle(titleItem)

	subtitleItem, err := sushi.NewTextSnippetItem(b.Subtitle)
	if err != nil {
		err = fmt.Errorf("[appiconchange/buildChangeAppIconSnippet] Error in generating new sushi text snippet item - subtitle: %v, error: %v", b.Subtitle, err)
		return nil, err
	}
	item.SetSubtitle(subtitleItem)

	positiveButton := getButton(b.YesButtonText, b.YesButtonActionType, b.YesButtonRefreshPages)
	if b.ShouldShowYesButton && positiveButton != nil {
		item.SetPositiveButton(positiveButton)
	}

	negativeButton := getButton(b.NoButtonText, b.NoButtonActionType, b.NoButtonRefreshPages)
	if b.ShouldShowNoButton && negativeButton != nil {
		item.SetNegativeButton(negativeButton)
	}

	return
}

func getButton(buttonText string, clickActionType sushi.ActionType, refreshPages []*sushi.RefreshPage) *sushi.Button {
	button, err := sushi.NewButton(buttonText, "", "")
	if err != nil {
		return nil
	}

	clickAction, err := sushi.NewClickAction(clickActionType)
	if err != nil {
		return button
	}

	if len(refreshPages) > 0 {
		clickAction.RefreshPages = refreshPages
	}

	button.SetClickAction(clickAction)

	return button
}
