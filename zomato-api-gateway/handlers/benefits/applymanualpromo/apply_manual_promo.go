package applymanualpromo

import (
	"fmt"
	"net/http"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/handlers/benefits/applymanualpromo/controller"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/Zomato/zomato-api-gateway/models/benefits"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

func ApplyManualPromo(ctx *gin.Context) {
	var req *benefits.ApplyManualPromoRequest
	if err := ctx.MustBindWith(&req, binding.JSON); err != nil {
		log.FromContext(ctx).WithFields(map[string]interface{}{
			"API": "ApplyManualPromoV2",
		}).Errorf("could not bind ApplyManualPromoV2 request, err: %s", err.<PERSON>rror())
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(fmt.Sprintf("Bad request - JSON binding error in incoming request, err: %v", err.Error())),
		)
		return
	}

	log.FromContext(ctx).WithFields(map[string]interface{}{
		"API": "ApplyManualPromoV2",
	}).Debugf("ApplyManualPromoV2 Request: %+v", req)

	applyManualPromoResp, err := controller.ApplyManualPromo(ctx, req)
	if err != nil {
		log.Errorf("error while fetching offers for userID: %d. Faced Err: %+v", api.GetClientFromContext(ctx).UserID(), err)
		ctx.JSON(
			http.StatusOK,
			models.StatusSuccess(""),
		)
		return
	}

	ctx.JSON(
		http.StatusOK,
		applyManualPromoResp,
	)
}
