package controller

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	pbBenefitCart "github.com/Zomato/benefits-service-client-golang/proto/arcade/cart/v1"
	"github.com/Zomato/go/i18n"
	"github.com/Zomato/go/jumbo"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/promos/promoevents"
	"github.com/Zomato/promo-service-client-golang/proto/promo"
	"github.com/Zomato/zomato-api-gateway/handlers/benefits/bridges"
	"github.com/Zomato/zomato-api-gateway/handlers/benefits/constants"
	"github.com/Zomato/zomato-api-gateway/handlers/benefits/operations"
	"github.com/Zomato/zomato-api-gateway/handlers/benefits/services"
	"github.com/Zomato/zomato-api-gateway/handlers/benefits/shared"
	"github.com/Zomato/zomato-api-gateway/handlers/subscription/util"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models/benefits"
	"github.com/Zomato/zomato-api-gateway/models/sushi"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/wrapperspb"

	"github.com/gin-gonic/gin"
)

const (
	EventTypeApplied = "AdditiveOfferWallTypedPromoApplied"
	EventTypeFailure = "AdditiveOfferWallTypedPromoFailure"
)

func ApplyManualPromo(ctx *gin.Context, request *benefits.ApplyManualPromoRequest) (*benefits.ApplyManualPromoResponse, error) {
	if err := validateRequest(request); err != nil {
		return nil, err
	}

	var resp *benefits.ApplyManualPromoResponse

	voucherInfo, err := (&services.FetchVoucherInfoRequest{
		ServiceType: request.ServiceType,
		PromoCode:   request.PromoName,
	}).Fetch(ctx)

	if err != nil {
		log.Errorf("could not fetch voucher data for request: %+v: to block free dc promo code with err : %s", request, err)
	}

	resID, err := strconv.ParseInt(request.Entities[0].Entity.ID, 10, 32)
	if err != nil {
		log.Errorf("could not parse resID: %s to int32, err: %s", request.Entities[0].Entity.ID, err)
	}

	if !voucherInfo.GetRPCStatus().GetStatus() {
		if resID != 0 {
			sendManualPromoEvent(ctx, resID, request.PromoName, false)
		}
		return &benefits.ApplyManualPromoResponse{
			Status:  "failed",
			Message: i18n.Translate(ctx, "zm-promo-cart-offer-failure-reason-invalid-code", request.PromoName),
		}, nil
	}

	for index := range request.Benefits {
		if (voucherInfo.GetVoucherDetails().GetPromoCode() == request.Benefits[index].ID) &&
			(request.Benefits[index].BenefitStatus != sushi.BenefitStatusRemove) {
			return &benefits.ApplyManualPromoResponse{
				Status:  "failed",
				Message: i18n.Translate(ctx, "zm-promo-cart-offer-failure-reason-already-selected", request.PromoName),
			}, nil
		}
	}

	switch voucherInfo.GetVoucherDetails().GetPromoType() {
	case promo.PromoType_REAL_TIME_PROMO:
		resp = getResponseForBulkPromos(ctx, request, voucherInfo.GetVoucherDetails().GetPromoType())
	case promo.PromoType_PAYMENT_PROMO:
		resp = getRespForPaymentPromo(ctx, request)
	default:
		resp = getResponseForPromo(ctx, request, voucherInfo.GetVoucherDetails().GetPromoType())
	}

	if resp != nil && resID != 0 {
		isSuccess := resp.Status == "success"
		sendManualPromoEvent(ctx, resID, request.PromoName, isSuccess)
	}
	return resp, nil
}

func validateRequest(request *benefits.ApplyManualPromoRequest) error {
	if request == nil {
		return errors.New("empty input request received")
	}

	if _, found := bridges.ServiceTypeProtoMap[strings.ToUpper(request.ServiceType)]; !found {
		return fmt.Errorf("invalid service type %v provided", request.ServiceType)
	}

	if request.LocationDetails.CityID <= 0 {
		return fmt.Errorf("invalid city id: %v in request", request.LocationDetails.CityID)
	}

	if request.LocationDetails.CountryID <= 0 {
		return fmt.Errorf("invalid country id: %v in request", request.LocationDetails.CountryID)
	}

	// There won't be any search bar when it comes to multi-cart-offer-wall
	if len(request.Entities) != 1 {
		return fmt.Errorf("invalid entity details length for request, length: %v", len(request.Entities))
	}

	return nil
}

func getRespForPaymentPromo(ctx *gin.Context, request *benefits.ApplyManualPromoRequest) *benefits.ApplyManualPromoResponse {
	resp := &benefits.ApplyManualPromoResponse{}
	promoPaymentInfo, err := services.GetPromoPaymentInfo(ctx, benefits.PromoPaymentInfoRequest{
		PromoName:         request.PromoName,
		PaymentMethodName: request.PaymentDetail.PaymentMethodName,
		ServiceType:       request.ServiceType,
		CountryID:         request.LocationDetails.CountryID,
		CartDetails: benefits.CartDetails{ // Since this flow will only be triggered for single entity
			CartID:           request.Entities[0].CartData.CartID,
			Charges:          request.Entities[0].CartData.Charges,
			Totals:           request.Entities[0].CartData.Totals,
			Items:            request.Entities[0].CartData.Dishes,
			AdditionalParams: request.Entities[0].AdditionalParams,
		},
	})

	if err != nil {
		log.Errorf("could not fetch payment data for request: %+v: err : %s", request, err)
		return &benefits.ApplyManualPromoResponse{
			Status:  "failed",
			Message: i18n.Translate(ctx, "zm-promo-cart-offer-failure-reason-generic"),
		}
	}

	sdkData := (&shared.PromoPaymentInfo{
		PromoPaymentDetails:         promoPaymentInfo.PaymentMethodDetails,
		PromoID:                     promoPaymentInfo.PromoId,
		ShouldStartPaymentSelection: promoPaymentInfo.ShouldStartPaymentSelection,
		CardName:                    promoPaymentInfo.CardName,
		CardBinNumbers:              promoPaymentInfo.CardBinNumbers,
	}).GetPaymentSDKData()

	successAction := getSuccessActionForPaymentCode(ctx, sdkData, request.PromoName, request.Benefits)
	resp.Status = "success"
	resp.SuccessAction = successAction

	return resp
}

func getResponseForBulkPromos(ctx *gin.Context, request *benefits.ApplyManualPromoRequest, promoType promo.PromoType) *benefits.ApplyManualPromoResponse {
	return &benefits.ApplyManualPromoResponse{
		Status: "success",
		SuccessAction: &sushi.ClickAction{
			Type: sushi.ActionTypeSendBackResult,
			SendBackResult: &sushi.SuccessAction{
				Type: string(sushi.ActionTypeSyncSelectedBenefits),
				SyncSelectedBenefits: &sushi.SyncSelectedBenefits{
					Benefits: getBenefits(request, promoType),
					SuccessAction: &sushi.SuccessAction{
						Type: sushi.SuccessActionTypeRefreshPages,
						RefreshPages: []*sushi.RefreshPage{
							{
								Type: sushi.RefreshPageTypeOnlineOrderCart,
								O2Cart: &sushi.RefreshPageData{
									ShowPartialLoader: true,
								},
							},
						},
					},
				},
			},
		},
	}
}

func getResponseForPromo(ctx *gin.Context, request *benefits.ApplyManualPromoRequest, promoType promo.PromoType) *benefits.ApplyManualPromoResponse {
	resp := &benefits.ApplyManualPromoResponse{}
	voucherApplicabilityResp, err := services.GetVoucherApplicability(ctx, request, promoType)
	if err != nil {
		log.Errorf("error while fetching voucherApplicabilityResp for req: %+v: err : %s", request, err)
		return &benefits.ApplyManualPromoResponse{
			Status:  "failed",
			Message: i18n.Translate(ctx, "zm-promo-cart-offer-failure-reason-generic"),
		}
	}

	// if voucher code is applicable -> return success action and reload offer wall
	if voucherApplicabilityResp.GetIsPromoApplicable() {
		resp.Status = "success"
		var isSuccessActionSet bool
		for index := range request.Benefits {
			if request.Benefits[index].Type == constants.AppliedOfferTypeSALT {
				resp.SuccessAction = getCustomPopupClickAction(ctx, request.PromoName, request.Entities[0].Entity.ID, request.Benefits)
				isSuccessActionSet = true
				break
			}
		}

		if !isSuccessActionSet {
			resp.SuccessAction = getSuccessActionForPromoCode(ctx, request.PromoName, request.Entities[0].Entity.ID, request.Benefits)
		}

	} else {
		resp.Status = "failed"
		resp.Message = func() string {
			failureReasons := getFailureReasons(ctx, voucherApplicabilityResp.GetFailureReasons(), request.LocationDetails.CountryID)
			if len(failureReasons) > 0 {
				return operations.JoinStrings(failureReasons, constants.SpaceSeperator)
			}

			if operations.IsEmptyString(voucherApplicabilityResp.GetFailureMessage()) {
				return i18n.Translate(ctx, "zm-promo-cart-offer-failure-reason-invalid-code", request.PromoName)
			}

			return voucherApplicabilityResp.GetFailureMessage()
		}()
	}

	return resp
}

func getSuccessActionForPaymentCode(ctx *gin.Context, sdkData, voucherCode string, benefits []*benefits.Benefit) *sushi.ClickAction {
	cAction := &sushi.ClickAction{
		Type: sushi.ActionTypePaymentHandshakeFlow,
	}

	updateBenefits := []*sushi.UpdateBenefit{}
	for index := range benefits {
		uBenefit := &sushi.UpdateBenefit{
			BenefitID:         benefits[index].ID,
			BenefitProviderID: benefits[index].ProviderID,
			BenefitType:       benefits[index].Type,
		}

		switch benefits[index].Type {
		case constants.AppliedOfferTypePaymentPromo:
			uBenefit.ActionType = sushi.SuccessActionTypeRemove
			uBenefit.BenefitStatus = sushi.BenefitStatusRemove
		default:
			uBenefit.ActionType = sushi.SuccessActionTypeAdd
			uBenefit.BenefitStatus = sushi.BenefitStatusApply
		}

		updateBenefits = append(updateBenefits, uBenefit)
	}

	updateBenefits = append(updateBenefits, &sushi.UpdateBenefit{
		ActionType:                   sushi.SuccessActionTypeAdd,
		BenefitID:                    voucherCode,
		BenefitType:                  constants.AppliedOfferTypePaymentPromo,
		BenefitProviderID:            "", // No provider ID for payment codes
		BenefitStatus:                sushi.BenefitStatusApply,
		BenefitApplicationActionType: constants.BenefitApplicationActionTypeManuallyTyped,
	})

	cAction.PaymentHandshakeFlow = &sushi.PaymentHandshakeClickAction{
		SDKData:     sdkData,
		VoucherCode: voucherCode,
		SuccessAction: &sushi.ClickAction{
			Type:           sushi.ActionTypeUpdateBenefits,
			UpdateBenefits: updateBenefits,
		},
		FailureAction: &sushi.ClickAction{
			Type: sushi.ActionTypeShowToast,
			ShowToast: &sushi.ShowToast{
				Title: &sushi.Label{
					Text: i18n.Translate(ctx, "zm-promo-cart-offer-failure-reason-generic"),
				},
				DurationType: sushi.TTLShort,
			},
		},
	}

	return cAction
}

func getSuccessActionForPromoCode(ctx *gin.Context, voucherCode string, entityID string, benefits []*benefits.Benefit) *sushi.ClickAction {
	cAction := &sushi.ClickAction{
		Type: sushi.ActionTypeUpdateBenefits,
	}

	for index := range benefits {
		updateBenefit := &sushi.UpdateBenefit{
			BenefitID:         benefits[index].ID,
			BenefitProviderID: benefits[index].ProviderID,
			BenefitType:       benefits[index].Type,
			BenefitProvider:   "restaurant",
		}

		switch benefits[index].Type {
		case constants.AppliedOfferTypePaymentPromo:
			updateBenefit.ActionType = sushi.SuccessActionTypeAdd
			updateBenefit.BenefitStatus = sushi.BenefitStatusApply
		default:
			updateBenefit.ActionType = sushi.SuccessActionTypeRemove
			updateBenefit.BenefitStatus = sushi.BenefitStatusRemove
		}

		cAction.UpdateBenefits = append(cAction.UpdateBenefits, updateBenefit)
	}

	cAction.UpdateBenefits = append(cAction.UpdateBenefits, &sushi.UpdateBenefit{
		ActionType:                   sushi.SuccessActionTypeAdd,
		BenefitID:                    voucherCode,
		BenefitType:                  constants.AppliedOfferTypePROMO,
		BenefitProviderID:            entityID,
		BenefitStatus:                sushi.BenefitStatusApply,
		BenefitApplicationActionType: constants.BenefitApplicationActionTypeManuallyTyped,
	})

	return cAction
}

func getCustomPopupClickAction(ctx *gin.Context, voucherCode, entityID string, benefits []*benefits.Benefit) *sushi.ClickAction {
	return &sushi.ClickAction{
		Type: sushi.ActionTypeCustomAlertType2,
		CustomAlertType2: &sushi.CustomAlertType2{
			Title: &sushi.TextSnippetItem{
				Text: i18n.Translate(ctx, "zm-promo-cart-offer-current-coupon-title"),
				Font: &sushi.Font{
					Size:   sushi.FontSize500,
					Weight: sushi.FontBold,
				},
			},
			Message: &sushi.TextSnippetItem{
				Text: i18n.Translate(ctx, "zm-promo-cart-offer-bxgy-removal",
					voucherCode,
				),
				Font: &sushi.Font{
					Size:   sushi.FontSize300,
					Weight: sushi.FontMedium,
				},
			},
			IsBlocking:     true,
			NegativeAction: getNegativeAction(ctx),
			PositiveAction: getPositiveAction(ctx, voucherCode, entityID, benefits),
		},
	}
}

func getNegativeAction(ctx *gin.Context) *sushi.OfferWallAlertAction {
	return &sushi.OfferWallAlertAction{
		Text: i18n.Translate(ctx, "zm-promo-cart-offer-current-coupon-message"),
		Color: &sushi.Color{
			Tint: sushi.ColorTint500,
			Type: sushi.ColorRed,
		},
		Font: &sushi.Font{
			Size:   sushi.FontSize300,
			Weight: sushi.FontSemiBold,
		},
	}

}

func getPositiveAction(ctx *gin.Context, voucherCode, entityID string, benefits []*benefits.Benefit) *sushi.OfferWallAlertAction {
	cAction := getSuccessActionForPromoCode(ctx, voucherCode, entityID, benefits)

	return &sushi.OfferWallAlertAction{
		Text: i18n.Translate(ctx, "zm-promo-multi-cart-offer-bxgy-positive-action-text",
			voucherCode),
		Color: &sushi.Color{
			Tint: sushi.ColorTint500,
			Type: sushi.ColorRed,
		},
		Font: &sushi.Font{
			Size:   sushi.FontSize300,
			Weight: sushi.FontSemiBold,
		},
		Type:        sushi.ButtonTypeText,
		ClickAction: cAction,
	}
}

func getBenefits(request *benefits.ApplyManualPromoRequest, pType promo.PromoType) []*sushi.UpdateBenefit {
	benefits := []*sushi.UpdateBenefit{}
	for index := range request.InitialBenefits {
		benefits = append(benefits, shared.GetUpdateBenefitsForBenefit(sushi.SuccessActionTypeRemove, request.InitialBenefits[index]))
	}

	benefits = append(benefits, &sushi.UpdateBenefit{
		ActionType:        sushi.SuccessActionTypeAdd,
		BenefitID:         request.PromoName,
		BenefitProviderID: request.Entities[0].Entity.ID,
		BenefitType: func() string {
			if pType == promo.PromoType_BULK_PAYMENT_PROMO {
				return constants.AppliedOfferTypePaymentPromo
			}

			return constants.AppliedOfferTypePROMO
		}(),
		BenefitProvider: func() string {
			if pType == promo.PromoType_BULK_PAYMENT_PROMO {
				return ""
			}

			return "restaurant"
		}(),
		BenefitStatus: sushi.BenefitStatusApply,
	})

	return benefits
}

func getFailureReasons(ctx *gin.Context, failureReasons []*pbBenefitCart.FailureReason, countryID int64) []string {
	var reasons []string
	for _, failureReason := range failureReasons {
		switch failureReason.FailureReasonType {
		case pbBenefitCart.FailureReasonType_FAILURE_REASON_TYPE_MOV:
			if mov, err := operations.ConvertToFloat64(failureReason.FailureReasonValue); err == nil {
				failureTextKey := "zm-promo-cart-offer-failure-reason-mov-text"
				reasons = append(
					reasons,
					i18n.Translate(
						ctx,
						failureTextKey,
						shared.FormatCurrency(mov, countryID),
					),
				)
			}
		case pbBenefitCart.FailureReasonType_FAILURE_REASON_TYPE_ITEM_TAG:
			reasons = append(
				reasons,
				i18n.Translate(
					ctx,
					"zm-promo-cart-offer-failure-reason-item-tag-text",
				),
			)
		}
	}

	return reasons
}

func sendManualPromoEvent(ctx *gin.Context, resID int64, promoCode string, isSuccess bool) {
	eventName := EventTypeApplied

	if !isSuccess {
		eventName = EventTypeFailure
	}

	protoEvent := &promoevents.PromoEvents{
		EventName: &wrapperspb.StringValue{Value: eventName},
		PromoCode: &wrapperspb.StringValue{Value: promoCode},
		ResId:     &wrapperspb.Int32Value{Value: int32(resID)},
	}

	jsonOpts := protojson.MarshalOptions{
		UseProtoNames: true,
	}

	jsonBytes, err := jsonOpts.Marshal(protoEvent)

	if err != nil {
		log.WithError(err).Error("Failed to marshal promo event proto to JSON")
		return
	}

	var jumboPayload map[string]interface{}

	if err := json.Unmarshal(jsonBytes, &jumboPayload); err != nil {
		log.WithError(err).Error("Failed to unmarshal promo event JSON to map")
		return
	}

	userID := ""
	client := api.GetClientFromContext(ctx)

	if client != nil {
		userID = util.ParseToString(client.UserID())
	}

	if err := SendPromoEventToJumbo(ctx, jumboPayload, userID); err != nil {
		log.WithError(err).Errorf("Failed to send manual promo event to Jumbo: %s", string(jsonBytes))
	}
}

func SendPromoEventToJumbo(ctx *gin.Context, payload map[string]interface{}, userID string) error {
	jumboHeader := jumbo.Header{
		IPAddress: ctx.ClientIP(),
		UserAgent: ctx.Request.UserAgent(),
		Time:      time.Now().Unix(),
		UserID:    userID,
		SessionID: ctx.GetHeader("X-Jumbo-Session-Id"),
	}

	environment := env.FromContext(ctx)

	if environment == nil {
		return fmt.Errorf("environment not available")
	}

	jc := environment.JumboClient()

	if jc == nil {
		return fmt.Errorf("jumbo client not available")
	}

	event := jumbo.NewEvent().
		WithPayload(payload).
		WithHeader(jumboHeader)

	if err := jc.SendEvent(sushi.PromoEventsTable, event); err != nil {
		return err
	}

	return nil
}
