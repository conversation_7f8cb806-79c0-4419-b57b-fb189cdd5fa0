package bridges

import (
	pbBenefit "github.com/Zomato/benefits-service-client-golang/proto/arcade/benefit/v1"
	pbBenefitCart "github.com/Zomato/benefits-service-client-golang/proto/arcade/cart/v1"
	pbBenefitShared "github.com/Zomato/benefits-service-client-golang/proto/arcade/shared/v1"

	"github.com/Zomato/zomato-api-gateway/handlers/benefits/constants"
	"github.com/Zomato/zomato-api-gateway/models/benefits"
)

var ServiceTypeProtoMap = map[string]pbBenefitShared.ServiceType{
	constants.ServiceTypeDelivery:   pbBenefitShared.ServiceType_SERVICE_TYPE_DELIVERY,
	constants.ServiceTypeIntercity:  pbBenefitShared.ServiceType_SERVICE_TYPE_INTERCITY,
	constants.ServiceTypeGiftCard:   pbBenefitShared.ServiceType_SERVICE_TYPE_GIFT_CARD,
	constants.ServiceTypeDining:     pbBenefitShared.ServiceType_SERVICE_TYPE_DINING,
	constants.ServiceTypeZomatoLive: pbBenefitShared.ServiceType_SERVICE_TYPE_ZOMATO_LIVE,
	constants.ServiceTypePickup:     pbBenefitShared.ServiceType_SERVICE_TYPE_PICKUP,
}

var EntityTypeProtoMap = map[string]pbBenefitShared.EntityType{
	constants.EntityTypeRestaurant: pbBenefitShared.EntityType_ENTITY_TYPE_RESTAURANT,
}

var AppledOfferTypeProtoMap = map[string]pbBenefit.BenefitClassType{
	constants.AppliedOfferTypePROMO:        pbBenefit.BenefitClassType_BENEFIT_CLASS_PROMO,
	constants.AppliedOfferTypePaymentPromo: pbBenefit.BenefitClassType_BENEFIT_CLASS_PAYMENT_PROMO,
	constants.AppliedOfferTypeSALT:         pbBenefit.BenefitClassType_BENEFIT_CLASS_SALT,
	constants.AppliedOfferTypeDeal:         pbBenefit.BenefitClassType_BENEFIT_CLASS_DEAL,
	constants.AppliedOfferTypeGOLD:         pbBenefit.BenefitClassType_BENEFIT_CLASS_GOLD,
}

var OfferwallTypeProtoMap = map[string]pbBenefitCart.OfferwallType{
	constants.OfferwallTypeO2Payment:    pbBenefitCart.OfferwallType_OFFER_WALL_TYPE_O2_PAYMENT,
	constants.OfferwallTypeO2Restaurant: pbBenefitCart.OfferwallType_OFFER_WALL_TYPE_O2_RESTAURANT,
}

var ChargeTypeProtoMap = map[string]pbBenefitCart.ChargeType{
	constants.ChargeTypeDelivery: pbBenefitCart.ChargeType_CHARGE_TYPE_DELIVERY,
}

var TotalTypeProtoMap = map[string]pbBenefitCart.TotalType{
	constants.TotalTypeSubtotal:   pbBenefitCart.TotalType_TOTAL_TYPE_SUBTOTAL,
	constants.TotalTypeGrandtotal: pbBenefitCart.TotalType_TOTAL_TYPE_GRANDTOTAL,
}

var BenefitClassTypeMap = map[pbBenefit.BenefitClassType]string{
	pbBenefit.BenefitClassType_BENEFIT_CLASS_SALT:          constants.AppliedOfferTypeSALT,
	pbBenefit.BenefitClassType_BENEFIT_CLASS_PROMO:         constants.AppliedOfferTypePROMO,
	pbBenefit.BenefitClassType_BENEFIT_CLASS_PAYMENT_PROMO: constants.AppliedOfferTypePaymentPromo,
	pbBenefit.BenefitClassType_BENEFIT_CLASS_DEAL:          constants.AppliedOfferTypeDeal,
	pbBenefit.BenefitClassType_BENEFIT_CLASS_BRUNCH:        constants.AppliedOfferTypeBRUNCH,
}

var BenefitClassTypeProtoMap = map[string]pbBenefit.BenefitClassType{
	constants.BenefitClassSaltType:         pbBenefit.BenefitClassType_BENEFIT_CLASS_SALT,
	constants.BenefitClassPromoType:        pbBenefit.BenefitClassType_BENEFIT_CLASS_PROMO,
	constants.AppliedOfferTypePaymentPromo: pbBenefit.BenefitClassType_BENEFIT_CLASS_PAYMENT_PROMO,
	constants.AppliedOfferTypeDeal:         pbBenefit.BenefitClassType_BENEFIT_CLASS_DEAL,
	constants.AppliedOfferTypeBRUNCH:       pbBenefit.BenefitClassType_BENEFIT_CLASS_BRUNCH,
}

// OfferFunctionMap offer function map
var OfferFunctionMap = map[pbBenefit.OfferFunction]string{
	pbBenefit.OfferFunction_OFFER_FUNCTION_BXGY:       constants.AppliedSaltTypeBXGY,
	pbBenefit.OfferFunction_OFFER_FUNCTION_PERCENTAGE: constants.AppliedOfferTypePercentage,
	pbBenefit.OfferFunction_OFFER_FUNCTION_FLATRATE:   constants.AppliedOfferTypeFlatrate,
}

var PaymentMethodTypeStringProtoMap = map[string]pbBenefitCart.PaymentMethodType{
	constants.PaymentMethodTypeWallet:         pbBenefitCart.PaymentMethodType_PAYMENT_METHOD_TYPE_WALLET,
	constants.PaymentMethodTypeUPI:            pbBenefitCart.PaymentMethodType_PAYMENT_METHOD_TYPE_UPI,
	constants.PaymentMethodTypeCard:           pbBenefitCart.PaymentMethodType_PAYMENT_METHOD_TYPE_CARD,
	constants.PaymentMethodTypePostPaidWallet: pbBenefitCart.PaymentMethodType_PAYMENT_METHOD_TYPE_POSTPAID_WALLET,
	constants.PaymentMethodTypeNetbanking:     pbBenefitCart.PaymentMethodType_PAYMENT_METHOD_TYPE_NET_BANKING,
}

func GetBenefitClassTypeMap(benefitClass pbBenefit.BenefitClassType) (string, bool) {
	classType, found := BenefitClassTypeMap[benefitClass]
	return classType, found
}

var benefitsToCartPBChargeType = map[benefits.ItemChargeType]pbBenefitCart.ItemChargeType{
	benefits.ItemChargeTypeNoneCalculation: pbBenefitCart.ItemChargeType_ITEM_CHARGE_TYPE_NONECALCULATION,
	benefits.ItemChargeTypePercentage:      pbBenefitCart.ItemChargeType_ITEM_CHARGE_TYPE_PERCENTAGE,
	benefits.ItemChargeTypeFixed:           pbBenefitCart.ItemChargeType_ITEM_CHARGE_TYPE_FIXED,
}

func MapBenefitsToCartPBChargeType(benefitsType benefits.ItemChargeType) pbBenefitCart.ItemChargeType {
	if cartPBType, ok := benefitsToCartPBChargeType[benefitsType]; ok {
		return cartPBType
	}
	return pbBenefitCart.ItemChargeType_ITEM_CHARGE_TYPE_NONECALCULATION
}
