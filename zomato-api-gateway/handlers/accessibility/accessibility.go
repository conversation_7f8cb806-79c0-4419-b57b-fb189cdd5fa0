package accessibility

import (
	"fmt"
	"net/http"

	"github.com/Zomato/go/i18n"
	"github.com/Zomato/go/logger"
	userproto "github.com/Zomato/user-preferences-service-client-golang/userpreferences/user"
	tracking "github.com/Zomato/zomato-api-gateway/handlers/accessibility/tracking"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	ups "github.com/Zomato/zomato-api-gateway/internal/user_preferences_service"
	"github.com/Zomato/zomato-api-gateway/internal/user_preferences_service/models"
	"github.com/Zomato/zomato-api-gateway/models/accessibility"
	"github.com/Zomato/zomato-api-gateway/models/sushi"
	"github.com/gin-gonic/gin"
)

const (
	// API Endpoints
	UpdateAccessibilityInfoEndpoint = "gw/user-preference/accessibility/update-accessibility-info"

	// Images
	LightModeHearingImage = "https://b.zmtcdn.com/data/o2_assets/3d58a5cf6957e5e11aa1c151ade2fca11748419463.png"
	DarkModeHearingImage  = "https://b.zmtcdn.com/data/o2_assets/3d25a87b9d1e964051a3775c0e44c5d91749109949.png"

	LightModeMobilityImage = "https://b.zmtcdn.com/data/o2_assets/60a8dd6b685adf01e127b5e5463462111748419445.png"
	DarkModeMobilityImage  = "https://b.zmtcdn.com/data/o2_assets/9607c9adc1e0fd88b05c41bd1edb7d611749109949.png"

	LightModeVisionImage = "https://b.zmtcdn.com/data/o2_assets/f0d41cedaee123d7433cd814bddee9b81748419433.png"
	DarkModeVisionImage  = "https://b.zmtcdn.com/data/o2_assets/b2e2aa8ea69fbfef0c04ed8d95623f711749109949.png"

	LightModeBannerImage = "https://b.zmtcdn.com/data/o2_assets/9ba3234276bd5d6ac9488d2ccb4331821749107831.png"
	DarktModeBannerImage = "https://b.zmtcdn.com/data/o2_assets/9ba3234276bd5d6ac9488d2ccb4331821749107831.png"

	// Tracking Events
	HeaderImpressionTracking           = "Accessibility_Header_Impression_Tracking"
	AccessibilitySaveButtonTapped      = "Accessibility_Save_Button_Tapped"
	AccessibilitySaveButtonImpression  = "Accessibility_Save_Button_Impression"
	AccessibilityRadioButtonTapped     = "Accessibility_Radio_Button_Tapped"
	AccessibilityRadioButtonImpression = "Accessibility_Radio_Button_Impression"
)

// createEmptySnippet creates a standard empty snippet with specified height
func createEmptySnippet(height int) *sushi.Results {
	return &sushi.Results{
		LayoutConfig: &sushi.LayoutConfig{
			SnippetType:         sushi.SnippetTypeEmptySnippet,
			ShouldPerformHaptic: false,
		},
		SnippetConfig: &sushi.SnippetConfig{
			Identifier: sushi.EmptySnippetIdentifier,
		},
		EmptySnippet: &sushi.EmptySnippet{
			BGColor: &sushi.Color{
				ThemeBucket: &sushi.ThemeBucket{
					Token: sushi.TokenColorBackgroundSecondary,
				},
			},
			Height: height,
		},
	}
}

// createBannerSection creates a header section with title, subtitle and image
func createBannerSection(c *gin.Context, subtitleKey, lightModeImageURL string, darkModeImageURL string) *sushi.Results {
	trackingData := []*sushi.TrackingData{
		tracking.GetAccessibilityTrackingData(
			HeaderImpressionTracking,
			HeaderImpressionTracking,
			sushi.TrackingTypeImpression,
		),
	}
	return &sushi.Results{
		LayoutConfig: &sushi.LayoutConfig{
			SnippetType: "v4_image_text_snippet_type_23",
			LayoutType:  sushi.LayoutTypeGrid,
		},
		SnippetConfig: &sushi.SnippetConfig{
			TopRadius:    16,
			BottomRadius: 16,
			BGColor: &sushi.Color{
				ThemeBucket: &sushi.ThemeBucket{
					Token: sushi.TokenColorSurfacePrimary,
				},
			},
			BorderData: &sushi.SnippetBorderData{
				Width:  1,
				Radius: 16,
				Colors: []*sushi.Color{
					{
						ThemeBucket: &sushi.ThemeBucket{
							Token: sushi.TokenColorBorderAccentPurpleIntense,
						},
					},
				},
			},
		},
		V4ImageTextSnippetType23: &sushi.V4ImageTextSnippetType23{
			BgColor: &sushi.Color{
				ThemeBucket: &sushi.ThemeBucket{
					Token: sushi.TokenColorSurfacePrimary,
				},
			},
			Items: []*sushi.ImageTextSnippetItem{
				{
					ShouldUseDecoration:  true,
					ShouldRoundForHeight: false,
					Padding: &sushi.Padding{
						Right:  16,
						Left:   16,
						Bottom: 16,
					},
					SubTitle1: &sushi.Label{
						Text: i18n.Translate(c, subtitleKey),
						Color: &sushi.Color{
							ThemeBucket: &sushi.ThemeBucket{
								Token: sushi.TokenColorTextTertiary,
							},
						},
						Font: &sushi.Font{
							Size:   sushi.FontSize100,
							Weight: sushi.FontRegular,
						},
						ShouldRoundForHeight: false,
						NumberOfLines:        2,
					},
					RightImage: &sushi.Image{
						URL:         lightModeImageURL,
						AspectRatio: 1,
						PlaceHolderColor: &sushi.Color{
							Tint: sushi.ColorTint500,
							Type: sushi.ColorWhite,
							ThemeBucket: &sushi.ThemeBucket{
								Token: sushi.TokenColorSurfaceInsetIntense,
							},
						},
						Height: 40,
						ThemeConfig: []*sushi.ThemeConfig{
							{
								Theme:         sushi.ThemeDark,
								ComponentType: sushi.ComponentTypeImage,
								Image: &sushi.Image{
									URL: darkModeImageURL,
								},
							},
						},
					},
					BGColor: &sushi.Color{
						ThemeBucket: &sushi.ThemeBucket{
							Token: sushi.TokenColorSurfaceAccentPurple,
						},
					},
					TrackingData: trackingData,
				},
			},
		},
	}
}

// createSectionHeader creates a header section with title, subtitle and image
func createSectionHeader(c *gin.Context, titleKey, subtitleKey, lightModeImageURL string, darkModeImageURL string) *sushi.Results {
	return &sushi.Results{
		LayoutConfig: &sushi.LayoutConfig{
			SnippetType: "v4_image_text_snippet_type_23",
			LayoutType:  sushi.LayoutTypeGrid,
		},
		SnippetConfig: &sushi.SnippetConfig{
			TopRadius: 16,
			BGColor: &sushi.Color{
				ThemeBucket: &sushi.ThemeBucket{
					Token: sushi.TokenColorSurfacePrimary,
				},
			},
		},
		V4ImageTextSnippetType23: &sushi.V4ImageTextSnippetType23{
			BgColor: &sushi.Color{
				ThemeBucket: &sushi.ThemeBucket{
					Token: sushi.TokenColorSurfacePrimary,
				},
			},
			Items: []*sushi.ImageTextSnippetItem{
				{
					ShouldUseDecoration: true,
					Title: &sushi.Label{
						Text: i18n.Translate(c, titleKey),
						Color: &sushi.Color{
							ThemeBucket: &sushi.ThemeBucket{
								Token: sushi.TokenColorTextPrimary,
							},
						},
						Font: &sushi.Font{
							Size:   sushi.FontSize400,
							Weight: sushi.FontSemiBold,
						},
						ShouldRoundForHeight: false,
					},
					Padding: &sushi.Padding{
						Right:  16,
						Left:   16,
						Bottom: 6,
					},
					SubTitle1: &sushi.Label{
						Text: i18n.Translate(c, subtitleKey),
						Color: &sushi.Color{
							ThemeBucket: &sushi.ThemeBucket{
								Token: sushi.TokenColorTextTertiary,
							},
						},
						Font: &sushi.Font{
							Size:   sushi.FontSize100,
							Weight: sushi.FontRegular,
						},
						ShouldRoundForHeight: false,
					},
					RightImage: &sushi.Image{
						URL:         lightModeImageURL,
						AspectRatio: 1,
						PlaceHolderColor: &sushi.Color{
							Tint: sushi.ColorTint500,
							Type: sushi.ColorWhite,
							ThemeBucket: &sushi.ThemeBucket{
								Token: sushi.TokenColorSurfaceInsetIntense,
							},
						},
						Height: 40,
						ThemeConfig: []*sushi.ThemeConfig{
							{
								Theme:         sushi.ThemeDark,
								ComponentType: sushi.ComponentTypeImage,
								Image: &sushi.Image{
									URL: darkModeImageURL,
								},
							},
						},
					},
					BGColor: &sushi.Color{
						ThemeBucket: &sushi.ThemeBucket{
							Token: sushi.TokenColorSurfacePrimary,
						},
					},
				},
			},
		},
	}
}

// createRadioButtonItem creates a single radio button item
func createRadioButtonItem(c *gin.Context, identifier string, textKey string, defaultSelected bool) *sushi.RadioButtonItem6 {
	trackingData := []*sushi.TrackingData{
		tracking.GetAccessibilityTrackingData(
			fmt.Sprintf("%s_%s", AccessibilityRadioButtonTapped, identifier),
			"",
			sushi.TrackingTypeTap,
		),
		tracking.GetAccessibilityTrackingData(
			fmt.Sprintf("%s_%s", AccessibilityRadioButtonTapped, identifier),
			"",
			sushi.TrackingTypeImpression,
		),
	}
	return &sushi.RadioButtonItem6{
		ShouldUseDecoration: true,
		Identifier:          identifier,
		Title: &sushi.Label{
			Text: i18n.Translate(c, textKey),
			Color: &sushi.Color{
				ThemeBucket: &sushi.ThemeBucket{
					Token: sushi.TokenColorTextPrimary,
				},
			},
			Font: &sushi.Font{
				Size:   sushi.FontSize300,
				Weight: sushi.FontRegular,
			},
			ShouldRoundForHeight: false,
		},
		RadioButtonMarginConfig: &sushi.RadioButtonMarginConfig{
			MarginEnd: 6,
		},
		BorderColor: &sushi.Color{
			Tint:         sushi.ColorTint500,
			Type:         sushi.ColorWhite,
			Transparency: 1,
		},
		DefaultSelected: defaultSelected,
		RadioColor: &sushi.Color{
			ThemeBucket: &sushi.ThemeBucket{
				Token: sushi.TokenColorIconBrand,
			},
		},
		BgColor: &sushi.Color{
			ThemeBucket: &sushi.ThemeBucket{
				Token: sushi.TokenColorSurfacePrimary,
			},
		},
		ShouldDisableBorder: true,
		TopPadding:          12,
		BottomPadding:       12,
		LeadingPadding:      6,
		TrailingPadding:     6,
		TrackingData:        trackingData,
	}
}

// createRadioButtonSection creates a radio button section with multiple options
func createRadioButtonSection(identifier string, items []*sushi.RadioButtonItem6) *sushi.Results {
	return &sushi.Results{
		LayoutConfig: &sushi.LayoutConfig{
			SnippetType:         sushi.SnippetTypeRadioButtonSnippetType6,
			LayoutType:          sushi.LayoutTypeGrid,
			ShouldPerformHaptic: false,
		},
		SnippetConfig: &sushi.SnippetConfig{
			ShouldAddOffset: true,
			BottomRadius:    12,
			BorderData: &sushi.SnippetBorderData{
				Width:  1,
				Radius: 16,
				Colors: []*sushi.Color{
					{
						Tint: sushi.ColorTint200,
						Type: sushi.ColorGrey,
					},
				},
			},
		},
		RadioButtonSnippetType6: &sushi.RadioButtonSnippetType6{
			Separator: &sushi.Separator{
				Type: sushi.SeparatorTypeLine,
				Color: &sushi.Color{
					ThemeBucket: &sushi.ThemeBucket{
						Token: sushi.TokenColorBorderModerate,
					},
				},
				BGColor: &sushi.Color{
					ThemeBucket: &sushi.ThemeBucket{
						Token: sushi.TokenColorSurfacePrimary,
					},
				},
			},
			BottomRadius:        16,
			ShouldUseDecoration: true,
			Identifier:          identifier,
			BgColor: &sushi.Color{
				ThemeBucket: &sushi.ThemeBucket{
					Token: sushi.TokenColorSurfacePrimary,
				},
			},
			ContentSeparatorPadding:         6,
			ContentSeparatorVerticalPadding: 6,
			Items:                           items,
		},
	}
}

// createHeaderData creates the header data for the accessibility page
func createHeaderData(c *gin.Context) *sushi.HeaderData {
	return &sushi.HeaderData{
		Title: &sushi.TextSnippetItem{
			Text: i18n.Translate(c, "zm-accessibility-settings-title"),
			Font: &sushi.Font{
				Weight: sushi.FontBold,
				Size:   sushi.FontSize400,
			},
			Color: &sushi.Color{
				Tint: sushi.ColorTint900,
				Type: sushi.ColorGrey,
			},
		},
		BGColor: &sushi.Color{
			Tint: sushi.ColorTint500,
			Type: sushi.ColorWhite,
			ThemeBucket: &sushi.ThemeBucket{
				Token: "color.crystal.delay",
			},
		},
	}
}

// createBottomButtonContainer creates the bottom button container with save action
func createBottomButtonContainer(c *gin.Context) *sushi.BottomContainer {
	trackingData := []*sushi.TrackingData{
		tracking.GetAccessibilityTrackingData(
			AccessibilitySaveButtonTapped,
			"",
			sushi.TrackingTypeTap,
		),
		tracking.GetAccessibilityTrackingData(
			AccessibilitySaveButtonImpression,
			"",
			sushi.TrackingTypeImpression,
		),
	}
	return &sushi.BottomContainer{
		Button: &sushi.Button{
			Size: sushi.ButtonSizeLarge,
			Type: sushi.ButtonTypeSolid,
			Text: i18n.Translate(c, "zm-accessibility-settings-save"),
			BgColor: &sushi.Color{
				ThemeBucket: &sushi.ThemeBucket{
					Token: sushi.TokenColorButtonPrimaryBackground,
				},
			},
			ClickAction: &sushi.ClickAction{
				Type: sushi.ActionTypeAPICallOnTap,
				APICallOnTap: &sushi.APICallOnTap{
					URL:                   UpdateAccessibilityInfoEndpoint,
					Type:                  string(sushi.APITypeGRPC),
					RequestEncodingType:   "application/json",
					ShouldSerialize:       false,
					ShouldEnableAllParams: false,
					SuccessAction: &sushi.Action{
						Type: string(sushi.ActionTypeShowSnippetPopup),
						ShowSnippetPopup: &sushi.ShowSnippetPopup{
							Snippets: []sushi.Snippet{
								{
									LayoutConfig: &sushi.LayoutConfig{
										SnippetType:         sushi.SnippetTypeImageTextSnippetType43,
										LayoutType:          sushi.LayoutTypeGrid,
										SectionCount:        1,
										ShouldPerformHaptic: false,
									},
									ImageTextSnippetType43: &sushi.ImageTextSnippetType43{
										Items: []*sushi.ImageTextSnippetItem{
											{
												Image: &sushi.Image{
													URL:         "https://b.zmtcdn.com/data/o2_assets/e6d5ad08d3d28ba95b8a730ce6edb58e1750657606.png",
													AspectRatio: 1.4,
													BgColor: &sushi.Color{
														ThemeBucket: &sushi.ThemeBucket{
															Token: sushi.TokenColorSurfacePrimary,
														},
													},
													Width: 150,
													ThemeConfig: []*sushi.ThemeConfig{
														{
															Theme:         sushi.ThemeDark,
															ComponentType: sushi.ComponentTypeImage,
															Image: &sushi.Image{
																URL: "https://b.zmtcdn.com/data/o2_assets/e6d5ad08d3d28ba95b8a730ce6edb58e1750657606.png",
															},
														},
													},
												},
												Title: &sushi.Label{
													Text: i18n.Translate(c, "zm-accessibility-settings-updated"),
													Color: &sushi.Color{
														Tint: sushi.ColorTint500,
														Type: sushi.ColorBlack,
														ThemeBucket: &sushi.ThemeBucket{
															Token: sushi.TokenColorTextPrimary,
														},
													},
													Font: &sushi.Font{
														Size:   sushi.FontSize500,
														Weight: sushi.FontBold,
													},
													IsMarkdown:           1,
													ShouldRoundForHeight: false,
												},
												ShouldRoundForHeight: false,
											},
										},
									},
								},
							},
							ShouldHideCross: true,
							Footer: &sushi.TextSnippetItem{
								BottomButton: &sushi.Button{
									Size: sushi.ButtonSizeMedium,
									Type: sushi.ButtonTypeText,
									Text: i18n.Translate(c, "zm-accessibility-settings-okay"),
									Color: &sushi.Color{
										Tint: sushi.ColorTint500,
										Type: sushi.ColorRed,
										ThemeBucket: &sushi.ThemeBucket{
											Token: sushi.TokenColorButtonGhostLabel,
										},
									},
									ClickAction: &sushi.ClickAction{
										Type: sushi.ActionTypeDismissPage,
										DismissPage: &sushi.DismissPage{
											Type:               "dismiss_page",
											IsAnimated:         false,
											DismissInBackstack: true,
										},
									},
									ShouldUseDecoration: false,
									ShouldUseSquircle:   false,
									ShouldRoundCorner:   false,
									IsEnabled:           false,
								},
								Separator: &sushi.Separator{
									Type: sushi.SeparatorTypeLineEndToEnd,
								},
								ShouldRoundForHeight: false,
							},
						},
					},
				},
			},
			Disabled: 1,
			TrackingData: trackingData,
		},
		BgColor: &sushi.Color{
			ThemeBucket: &sushi.ThemeBucket{
				Token: sushi.TokenColorBackgroundPrimary,
			},
		},
		ButtonSubmitActionPayloadKey: "generic_listing_form",
	}
}

func createTopBannerSection(c *gin.Context) []*sushi.Results {
	return []*sushi.Results{
		createEmptySnippet(16),
		createBannerSection(c, "zm-accessibility-mobility-banner-message", LightModeBannerImage, DarktModeBannerImage),
	}
}

func createHearingSectionWithItems(c *gin.Context, items []*sushi.RadioButtonItem6) []*sushi.Results {
	return []*sushi.Results{
		createEmptySnippet(16),
		createSectionHeader(c, "zm-accessibility-hearing-title", "zm-accessibility-hearing-subtitle", LightModeHearingImage, DarkModeHearingImage),
		createRadioButtonSection("hearing", items),
		createEmptySnippet(16),
		createEmptySnippet(16),
	}
}

func createVisionSectionWithItems(c *gin.Context, items []*sushi.RadioButtonItem6) []*sushi.Results {
	return []*sushi.Results{
		createSectionHeader(c, "zm-accessibility-vision-title", "zm-accessibility-vision-subtitle", LightModeVisionImage, DarkModeVisionImage),
		createRadioButtonSection("vision", items),
		createEmptySnippet(16),
		createEmptySnippet(16),
	}
}

func createMobilitySectionWithItems(c *gin.Context, items []*sushi.RadioButtonItem6) []*sushi.Results {
	return []*sushi.Results{
		createSectionHeader(c, "zm-accessibility-mobility-title", "zm-accessibility-mobility-subtitle", LightModeMobilityImage, DarkModeMobilityImage),
		createRadioButtonSection("mobility", items),
		createEmptySnippet(16),
	}
}

func GetAccessibilityHandler(c *gin.Context) {
	log := logger.FromContext(c)
	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()

	// Get current preferences
	en := env.FromContext(c)
	conn := en.UserPreferenceServiceConn()
	client := userproto.NewUserClient(conn)
	genericUserPreferenceService := ups.NewGenericUserPreferenceService(client)

	currentPreferences, err := genericUserPreferenceService.GetUserGenericPreferences(c, uint64(userID))
	if err != nil {
		log.WithError(err).Error("Failed to get current preferences")
		c.JSON(http.StatusInternalServerError, gin.H{"error": i18n.Translate(c, "zm-accessibility-settings-error-preferences")})
		return
	}

	// Create the response structure
	response := &accessibility.Response{
		HeaderData: createHeaderData(c),
		BgColor: &sushi.Color{
			ThemeBucket: &sushi.ThemeBucket{
				Token: sushi.TokenColorBackgroundSecondary,
			},
		},
		BottomButtonContainer: createBottomButtonContainer(c),
	}

	// Get current accessibility info
	accessibilityInfo := currentPreferences.AccessibilityInfo

	// Create sections with pre-filled values
	var results []*sushi.Results

	results = append(results, createTopBannerSection(c)...)
	// Hearing section
	hearingItems := []*sushi.RadioButtonItem6{
		createRadioButtonItem(c, string(models.COMPLETE_HEARING_IMPAIRMENT), "zm-accessibility-hearing-option-deaf",
			accessibilityInfo[models.DisabilityTypeHearing] == models.COMPLETE_HEARING_IMPAIRMENT),
		createRadioButtonItem(c, string(models.PARTIAL_HEARING_IMPAIRMENT), "zm-accessibility-hearing-option-hard-of-hearing",
			accessibilityInfo[models.DisabilityTypeHearing] == models.PARTIAL_HEARING_IMPAIRMENT),
		createRadioButtonItem(c, string(models.NO_HEARING_IMPAIRMENT), "zm-accessibility-hearing-option-none",
			accessibilityInfo[models.DisabilityTypeHearing] == models.NO_HEARING_IMPAIRMENT),
	}
	results = append(results, createHearingSectionWithItems(c, hearingItems)...)

	// Vision section
	visionItems := []*sushi.RadioButtonItem6{
		createRadioButtonItem(c, string(models.COMPLETE_VISUAL_IMPAIRMENT), "zm-accessibility-vision-option-blind",
			accessibilityInfo[models.DisabilityTypeVisual] == models.COMPLETE_VISUAL_IMPAIRMENT),
		createRadioButtonItem(c, string(models.PARTIAL_VISUAL_IMPAIRMENT), "zm-accessibility-vision-option-impaired",
			accessibilityInfo[models.DisabilityTypeVisual] == models.PARTIAL_VISUAL_IMPAIRMENT),
		createRadioButtonItem(c, string(models.NO_VISUAL_IMPAIRMENT), "zm-accessibility-vision-option-none",
			accessibilityInfo[models.DisabilityTypeVisual] == models.NO_VISUAL_IMPAIRMENT),
	}
	results = append(results, createVisionSectionWithItems(c, visionItems)...)

	// Mobility section
	mobilityItems := []*sushi.RadioButtonItem6{
		createRadioButtonItem(c, string(models.COMPLETE_MOBILITY_IMPAIRMENT), "zm-accessibility-mobility-option-wheelchair",
			accessibilityInfo[models.DisabilityTypeMobility] == models.COMPLETE_MOBILITY_IMPAIRMENT),
		createRadioButtonItem(c, string(models.PARTIAL_MOBILITY_IMPAIRMENT), "zm-accessibility-mobility-option-disability",
			accessibilityInfo[models.DisabilityTypeMobility] == models.PARTIAL_MOBILITY_IMPAIRMENT),
		createRadioButtonItem(c, string(models.NO_MOBILITY_IMPAIRMENT), "zm-accessibility-mobility-option-none",
			accessibilityInfo[models.DisabilityTypeMobility] == models.NO_MOBILITY_IMPAIRMENT),
	}
	results = append(results, createMobilitySectionWithItems(c, mobilityItems)...)

	response.SetResults(results)
	c.JSON(http.StatusOK, response)
}
