package tracking

import (
	"encoding/json"

	"github.com/Zomato/zomato-api-gateway/models/sushi"
)

const TableNameUserLifecycle = sushi.UserLifeCycleTableName

// GetAccessibilityTrackingData returns a TrackingData for a given event and payload ename, for either tap or impression.
func GetAccessibilityTrackingData(
	eventName string,
	payloadEname string,
	trackingType string, // "tap" or "impression"
) *sushi.TrackingData {
	event := &sushi.Event{
		EventName: eventName,
	}
	payload := &sushi.JeventPayload{}

	payloadString, err := getPayloadString(payload)
	if err != nil {
		return &sushi.TrackingData{}
	}
	eventStr, err := getEventString(event)
	if err != nil {
		return &sushi.TrackingData{}
	}

	eventNames := &sushi.EventNames{}
	switch trackingType {
	case "tap":
		eventNames.Tap = eventStr
	case "impression":
		eventNames.Impression = eventStr
	}

	return &sushi.TrackingData{
		TableName:  TableNameUserLifecycle,
		EventNames: eventNames,
		Payload:    payloadString,
	}
}

func getPayloadString(payload *sushi.JeventPayload) (string, error) {
	response, err := json.Marshal(payload)
	if err != nil {
		return "", err
	}
	return string(response), nil
}

func getEventString(event *sushi.Event) (string, error) {
	response, err := json.Marshal(event)
	if err != nil {
		return "", err
	}
	return string(response), nil
}
