package accessibility

import (
	"net/http"

	"github.com/Zomato/go/logger"
	"github.com/Zomato/go/i18n"
	userproto "github.com/Zomato/user-preferences-service-client-golang/userpreferences/user"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	ups "github.com/Zomato/zomato-api-gateway/internal/user_preferences_service"
	"github.com/Zomato/zomato-api-gateway/internal/user_preferences_service/models"
	"github.com/gin-gonic/gin"
)

// UpdateAccessibilityRequest represents the request body from frontend
type UpdateAccessibilityRequest struct {
	GenericListingForm struct {
		Hearing  models.DisabilityStatus `json:"hearing"`
		Vision   models.DisabilityStatus `json:"vision"`
		Mobility models.DisabilityStatus `json:"mobility"`
	} `json:"generic_listing_form"`
}

func UpdateAccessibilityHandler(c *gin.Context) {
	log := logger.FromContext(c)
	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()

	// Parse request body
	var req UpdateAccessibilityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.WithError(err).Error("Failed to parse request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": i18n.Translate(c, "zm-accessibility-settings-error-invalid-request")})
		return
	}

	// Create accessibility info map
	accessibilityInfo := make(map[models.DisabilityType]models.DisabilityStatus)

	// Set the disability statuses directly from the request
	accessibilityInfo[models.DisabilityTypeHearing] = req.GenericListingForm.Hearing
	accessibilityInfo[models.DisabilityTypeVisual] = req.GenericListingForm.Vision
	accessibilityInfo[models.DisabilityTypeMobility] = req.GenericListingForm.Mobility

	// Initialize service
	en := env.FromContext(c)
	conn := en.UserPreferenceServiceConn()
	client := userproto.NewUserClient(conn)
	genericUserPreferenceService := ups.NewGenericUserPreferenceService(client)

	// Create update request
	updateRequest := &models.UpdateGenericPreferencesRequest{
		UserID:            uint64(userID),
		PreferenceType:    models.PreferenceTypeUserGeneric,
		AccessibilityInfo: accessibilityInfo,
	}

	// Update preferences
	err := genericUserPreferenceService.UpdateGenericPreferenceForUser(c, updateRequest)
	if err != nil {
		log.WithError(err).Error("Failed to update accessibility preferences")
		c.JSON(http.StatusInternalServerError, gin.H{"error": i18n.Translate(c, "zm-accessibility-settings-error-update-failed")})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": i18n.Translate(c, "zm-accessibility-settings-update-success"),
	})
}
