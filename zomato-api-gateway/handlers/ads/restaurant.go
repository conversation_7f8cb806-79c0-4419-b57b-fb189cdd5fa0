package ads

import (
	"errors"
	"github.com/Zomato/zomato-api-gateway/internal/ads/platform"
	"net/http"
	"strconv"

	adspb "github.com/Zomato/ads-service-client-golang/ads"
	"github.com/Zomato/ads-service-client-golang/common"
	"github.com/Zomato/ads-service-client-golang/restaurant"
	v2 "github.com/Zomato/ads-service-client-golang/v2"
	zconfig "github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/Zomato/zomato-api-gateway/models/ads"
	"github.com/gin-gonic/gin"
)

func GetRestaurantDetails(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	resID, err := strconv.ParseUint(c.Param("res_id"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid res_id"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	response, err := client.GetRestaurantDetails(outgoingCtx, &restaurant.GetResDetailsRequest{
		ResID: resID,
	})
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetResGroupDetails(c *gin.Context) {
	resp, err := platform.GetResGroupDetails(c)
	if err != nil {
		logger.FromContext(c).Errorf("GetResGroupDetails - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(statusCode, message)
		return
	}
	c.JSON(http.StatusOK, resp)
}

func GetDishInfosByResID(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "GetDishInfosByResID",
	}
	log := logger.FromContext(c).WithFields(span)
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	resID, err := strconv.ParseInt(c.Param("res_id"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid res_id"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	response, err := client.GetDishInfosByResID(outgoingCtx, &restaurant.GetDishInfosByResIDRequest{
		ResId: resID,
	})
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetResDetailsCsv(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "GetResDetailsCsv",
	}
	log := logger.FromContext(c).WithFields(span)
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	var req restaurant.GetResDetailsCsvRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	response, err := client.GetResDetailsCsv(outgoingCtx, &req)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func ValidateResDetailsCsv(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "ValidateResDetailsCsv",
	}
	log := logger.FromContext(c).WithFields(span)
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	var req restaurant.ValidateResDetailsCsvRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	response, err := client.ValidateResDetailsCsv(outgoingCtx, &req)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func CreateTargetSegment(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var createTargetSegmentRequest restaurant.CreateTargetSegmentRequest
	err := c.ShouldBindJSON(&createTargetSegmentRequest)
	if err != nil {
		log.WithError(err).Error("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.CreateTargetSegment(outgoingCtx, &createTargetSegmentRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func CreateRestaurantConfig(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var createResConfigRequest restaurant.CreateResConfigRequest
	err := c.ShouldBindJSON(&createResConfigRequest)
	if err != nil {
		log.WithError(err).Error("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.CreateResConfig(outgoingCtx, &createResConfigRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func UpsertRestaurantConfig(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var upsertResConfigRequest restaurant.UpsertResConfigRequest
	err := c.ShouldBindJSON(&upsertResConfigRequest)
	if err != nil {
		log.WithError(err).Error("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	err = validateUpsertResConfigRequest(&upsertResConfigRequest)
	if err != nil {
		log.WithError(err).Error("Invalid request")
		c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
		return
	}

	response, err := client.UpsertResConfig(outgoingCtx, &upsertResConfigRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func validateUpsertResConfigRequest(req *restaurant.UpsertResConfigRequest) error {
	if req.ResId <= 0 {
		return errors.New("invalid resID")
	}
	if req.Configs == nil && req.BrandConfig == nil {
		return errors.New("empty request")
	}
	return nil
}

func CreateCityConfig(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var createCityConfigRequest restaurant.CreateCityConfigRequest
	err := c.ShouldBindJSON(&createCityConfigRequest)
	if err != nil {
		log.WithError(err).Error("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.CreateCityConfig(outgoingCtx, &createCityConfigRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func CreateProductConfig(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var createProductConfigRequest restaurant.CreateProductConfigRequest
	err := c.ShouldBindJSON(&createProductConfigRequest)
	if err != nil {
		log.WithError(err).Error("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.CreateProductConfig(outgoingCtx, &createProductConfigRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetEligibleProducts(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	resID, err := strconv.ParseUint(c.Param("res_id"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid res_id"))
		return
	}

	objective, err := strconv.ParseInt(c.Query("objective"), 10, 32)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid objective value"))
		return
	}

	paymentType, err := strconv.ParseInt(c.Query("payment_type"), 10, 32)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid payment type"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	response, err := client.GetEligibleProducts(outgoingCtx, &restaurant.GetEligibleProductsRequest{
		ResID:       resID,
		Objective:   restaurant.ObjectiveList(objective),
		PaymentType: v2.PaymentTypes(paymentType),
	})
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}

	// final response preparation
	responsePrepared := ads.GetEligibleProductsResponse{}
	responsePrepared.Warning = response.Warning

	if len(response.Data) > 0 {
		responsePrepared.Data = make([]ads.EligibleProducts, 0)
		for _, product := range response.Data {
			eligibleProductResponse := ads.EligibleProducts{
				ProductID:              product.ProductId,
				Name:                   product.Name,
				UnitPrice:              product.UnitPrice,
				BillingUnit:            product.BillingUnit,
				BillingUnitTerminology: product.BillingUnitTerminology,
				TargetFeatures:         make([]ads.TargetFeatures, 0),
				SmartRoiDailyMaxBudget: product.SmartRoiDailyMaxBudget,
			}

			for _, targetFeature := range product.TargetFeatures {
				targetFeatureResponse := ads.TargetFeatures{
					FeatureID:   targetFeature.FeatureId,
					Name:        targetFeature.Name,
					SubFeatures: make([]ads.SubFeatures, 0),
				}
				for _, subFeature := range targetFeature.SubFeatures {
					subFeatureResponse := ads.SubFeatures{
						FeatureID:              subFeature.FeatureId,
						Name:                   subFeature.Name,
						UnitPrice:              subFeature.UnitPrice,
						BillingUnit:            product.BillingUnit,
						BillingUnitTerminology: product.BillingUnitTerminology,
						FeatureAliasID:         subFeature.FeatureAliasId,
					}
					targetFeatureResponse.SubFeatures = append(targetFeatureResponse.SubFeatures, subFeatureResponse)
				}
				eligibleProductResponse.TargetFeatures = append(eligibleProductResponse.TargetFeatures, targetFeatureResponse)
			}
			responsePrepared.Data = append(responsePrepared.Data, eligibleProductResponse)
		}
	} else if response.Error != nil {
		responsePrepared.Error = &common.Error{
			Code:    response.Error.Code,
			Message: response.Error.Message,
		}
	}
	c.JSON(http.StatusOK, responsePrepared)
}
