package ads

import (
	"encoding/json"
	zconfig "github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/webclient"
	"github.com/Zomato/zomato-api-gateway/models"
	usermodels "github.com/Zomato/zomato-api-gateway/models/user"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

const flexibleMbbRangeEndpoint = "/api-internal/ads/dining/mbb/flexible-range-version"

func getFlexibleMBBDateRangeVersion(c *gin.Context) (int, error) {
	type webResponseStruct struct {
		Status  string `json:"status"`
		Version int    `json:"version"`
	}
	req := webclient.NewZomatoWebRequest(c, http.MethodGet, flexibleMbbRangeEndpoint)

	for _, header := range webclient.RequiredHeaders {
		if header == "x-zomato-api-key" {
			continue
		}
		req.Set<PERSON>eader(header, c.GetHeader(header))
	}
	req.SetHostHeader(zconfig.GetString(c, "api_internal.host"))
	resp, err := req.ExecuteWithTimeout(c, time.Second)
	if err != nil {
		logger.FromContext(c).WithField("err", err).Errorf("[FetchWebData] Failed to get response from web")
		return 1, err
	}
	var webResponse webResponseStruct
	err = json.Unmarshal(resp.GetResponseBody(), &webResponse)
	if err != nil {
		logger.FromContext(c).WithField("err", err).Errorf("[FetchWebData] Received invalid response from web")
		return 1, err
	}
	return webResponse.Version, nil
}

func IsAdsServiceMbbCreationFlow(c *gin.Context) bool {
	if !zconfig.GetBool(c, "dining.ads_service_mbb_creation_flow.enabled") {
		return false
	}
	enabledUsers := zconfig.GetIntSlice(c, "dining.ads_service_mbb_creation_flow.enabled_user_ids")
	if len(enabledUsers) == 0 {
		return true
	}
	client := api.GetClientFromContext(c)
	userID := client.UserID()
	for i := range enabledUsers {
		if int64(enabledUsers[i]) == userID {
			return true
		}
	}
	return false
}

func IsResGroupMbbCreationEnabled(c *gin.Context) bool {
	if !zconfig.GetBool(c, "dining.res_group_mbb_creation.enabled") {
		return false
	}
	enabledForAll := zconfig.GetBool(c, "dining.res_group_mbb_creation.enabled_for_all")
	if enabledForAll {
		return true
	}
	enabledUsers := zconfig.GetIntSlice(c, "dining.res_group_mbb_creation.enabled_user_ids")
	client := api.GetClientFromContext(c)
	userID := client.UserID()
	for i := range enabledUsers {
		if int64(enabledUsers[i]) == userID {
			return true
		}
	}
	return false
}

func GetDashboardConfigs(c *gin.Context) {
	adsUiConfigs := usermodels.GetAllAdsUiConfigs()
	var adsUIDashboardConfigResponse usermodels.AdsUiDashboardConfigResponse
	for _, config := range adsUiConfigs {
		switch config {
		case usermodels.FlexibleMbbRangeVersion:
			resp, err := getFlexibleMBBDateRangeVersion(c)
			if err != nil {
				c.AbortWithStatusJSON(
					http.StatusInternalServerError,
					models.StatusFailed("Internal server error - Failed to get response from Web"))
				return
			}
			flexibleResponse := usermodels.DashboardConfig{
				Property: usermodels.FlexibleMbbRangeVersion,
				Value:    resp,
			}
			adsUIDashboardConfigResponse.Config = append(adsUIDashboardConfigResponse.Config, flexibleResponse)
			break
		case usermodels.MbbCreationFlow:
			var value string
			if IsAdsServiceMbbCreationFlow(c) {
				value = usermodels.MbbCreationFlowAdsService
			} else {
				value = usermodels.MbbCreationFlowWeb
			}
			adsUIDashboardConfigResponse.Config = append(adsUIDashboardConfigResponse.Config, usermodels.DashboardConfig{
				Property: usermodels.MbbCreationFlow,
				Value:    value,
			})
		case usermodels.ResGroupMBBCreation:
			adsUIDashboardConfigResponse.Config = append(adsUIDashboardConfigResponse.Config, usermodels.DashboardConfig{
				Property: usermodels.ResGroupMBBCreation,
				Value:    IsResGroupMbbCreationEnabled(c),
			})
		default:
			continue
		}
	}

	c.JSON(http.StatusOK, &adsUIDashboardConfigResponse)
	return
}
