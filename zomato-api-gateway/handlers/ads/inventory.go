package ads

import (
	"net/http"
	"strconv"

	"github.com/Zomato/ads-service-client-golang/admin/inventory"
	zconfig "github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/Zomato/zomato-api-gateway/models/ads"
	"github.com/gin-gonic/gin"
)

const (
	DefaultPaginationLimit = 100
)

func GetCitySummary(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := inventory.NewInventoryClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	// Some validations
	cityID, err := strconv.ParseInt(c.Param("city_id"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid city_id"))
		return
	}
	if c.Query("product") == "" || c.Query("vslot") == "" || c.Query("start") == "" || c.Query("end") == "" {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "product, vslot, start and end parameters are required"))
		return
	}
	productID, err := strconv.ParseInt(c.Query("product"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid product id"))
		return
	}
	vSlot, err := strconv.ParseInt(c.Query("vslot"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid vslot"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)
	res, err := client.CitySummary(outgoingCtx, &inventory.CitySummaryRequest{
		CityId:       cityID,
		StartDate:    c.Query("start"),
		EndDate:      c.Query("end"),
		ProductId:    uint32(productID),
		VerticalSlot: uint32(vSlot),
	})

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", "upstream error"))
		return
	}

	if res.Error != nil {
		log.WithField("err_code", res.Error.Code).Error("error response from ads-service. ", res.Error.String())
		c.AbortWithStatusJSON(http.StatusInternalServerError, res)
		return
	}

	c.JSON(http.StatusOK, res)
}

func GetSubzonesByCity(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := inventory.NewInventoryClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	// Some validations
	cityID, err := strconv.ParseInt(c.Param("city_id"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid city_id"))
		return
	}
	if c.Query("product") == "" || c.Query("vslot") == "" || c.Query("start") == "" || c.Query("end") == "" {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "product, vslot, start and end parameters are required"))
		return
	}
	productID, err := strconv.ParseInt(c.Query("product"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid product id"))
		return
	}
	vSlot, err := strconv.ParseInt(c.Query("vslot"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid vslot"))
		return
	}

	offset, err := strconv.ParseInt(c.Query("offset"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse offset")
	}
	limit, err := strconv.ParseInt(c.Query("limit"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse limit")
		limit = DefaultPaginationLimit
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)
	res, err := client.SubzonesPerCity(outgoingCtx, &inventory.SubzonesRequest{
		CityId:       cityID,
		StartDate:    c.Query("start"),
		EndDate:      c.Query("end"),
		ProductId:    uint32(productID),
		VerticalSlot: uint32(vSlot),
		Offset:       offset,
		Limit:        limit,
	})

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", "upstream error"))
		return
	}

	if res.Error != nil {
		log.WithField("err_code", res.Error.Code).Error("error response from ads-service. ", res.Error.String())
		c.AbortWithStatusJSON(http.StatusInternalServerError, res)
		return
	}

	c.JSON(http.StatusOK, res)
}

func GetRestaurantsBySubzone(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := inventory.NewInventoryClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	// Some validations
	cityID, err := strconv.ParseInt(c.Param("city_id"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid city_id"))
		return
	}

	subzoneID, err := strconv.ParseInt(c.Param("subzone_id"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid subzone_id"))
		return
	}

	offset, err := strconv.ParseInt(c.Query("offset"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse offset")
	}
	limit, err := strconv.ParseInt(c.Query("limit"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse limit")
		limit = DefaultPaginationLimit
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)
	res, err := client.RestaurantsPerSubzone(outgoingCtx, &inventory.RestaurantsRequest{
		CityId:    cityID,
		SubzoneId: []int64{subzoneID},
		Offset:    offset,
		Limit:     limit,
	})

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", "upstream error"))
		return
	}

	if res.Error != nil {
		log.WithField("err_code", res.Error.Code).Error("error response from ads-service. ", res.Error.String())
		c.AbortWithStatusJSON(http.StatusInternalServerError, res)
		return
	}

	c.JSON(http.StatusOK, res)
}

func GetClicksByRestaurants(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := inventory.NewInventoryClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var restaurantClicksRequest ads.RestaurantClicksRequest
	err := c.ShouldBindJSON(&restaurantClicksRequest)

	if err != nil {
		log.Errorf("could not bind request: %s", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}

	res, err := client.ViewClicksPerRestaurant(outgoingCtx, &inventory.RestaurantClicksRequest{
		RestaurantId:      restaurantClicksRequest.RestaurantID,
		CampaignStartTime: restaurantClicksRequest.CampaignStartTime,
		CampaignEndTime:   restaurantClicksRequest.CampaignEndTime,
		CityId:            restaurantClicksRequest.CityID,
	})

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", "upstream error"))
		return
	}

	if res.Error != nil {
		log.WithField("err_code", res.Error.Code).Error("error response from ads-service. ", res.Error.String())
		c.AbortWithStatusJSON(http.StatusInternalServerError, res)
		return
	}

	c.JSON(http.StatusOK, res)
}

func GetImpressionsByRestaurants(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := inventory.NewInventoryClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var restaurantImpressionsRequest ads.RestaurantImpressionsRequest
	err := c.ShouldBindJSON(&restaurantImpressionsRequest)

	if err != nil {
		log.Errorf("could not bind request: %s", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}

	res, err := client.ViewImpressionsPerRestaurant(outgoingCtx, &inventory.RestaurantImpressionsRequest{
		EntityId:          strconv.FormatInt(restaurantImpressionsRequest.EntityID, 10),
		CampaignStartTime: restaurantImpressionsRequest.CampaignStartTime,
		CampaignEndTime:   restaurantImpressionsRequest.CampaignEndTime,
		CityId:            restaurantImpressionsRequest.CityID,
		UniqueEstimateId:  restaurantImpressionsRequest.UniqueEstimateID,
		ProductId:         restaurantImpressionsRequest.ProductID,
	})

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", "upstream error"))
		return
	}

	if res.Error != nil {
		log.WithField("err_code", res.Error.Code).Error("error response from ads-service. ", res.Error.String())
		c.AbortWithStatusJSON(http.StatusInternalServerError, res)
		return
	}

	c.JSON(http.StatusOK, res)
}
