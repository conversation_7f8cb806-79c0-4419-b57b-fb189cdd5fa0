package ads

import (
	"net/http"

	adspb "github.com/Zomato/ads-service-client-golang/ads"
	"github.com/Zomato/ads-service-client-golang/permissions"
	zconfig "github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"

	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
)

func GetUserPermissions(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	response, err := client.GetUserPermissions(outgoingCtx, &permissions.UserPermissionsRequest{
		UserId: userID,
	})
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}
