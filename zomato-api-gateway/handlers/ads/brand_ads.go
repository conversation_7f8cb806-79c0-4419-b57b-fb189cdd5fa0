package ads

import (
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"strings"

	adspb "github.com/Zomato/ads-service-client-golang/ads"
	"github.com/Zomato/ads-service-client-golang/brand_ads"
	zconfig "github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	internalads "github.com/Zomato/zomato-api-gateway/internal/ads"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/array"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	adsmodel "github.com/Zomato/zomato-api-gateway/models/brand_ads"
	"github.com/gin-gonic/gin"
	"google.golang.org/protobuf/encoding/protojson"
)

var CrystalAdsDefaultRoles = []string{
	internalads.BrandAdsDashboardDefaultRole,
	internalads.BrandAdsDashboardAdminRole,
	internalads.CrystalInternalDefaultRole,
	internalads.CrystalInternalAdminRole,
	internalads.ResAdsDashboardDefaultRole,
	internalads.ResAdsDashboardGraphicsRole,
	internalads.ResAdsDashboardAdminRole,
}

func UpsertBrandAdsConfig(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if !isValidUserID(c, userID) {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	jsonData, err := io.ReadAll(c.Request.Body)
	if err != nil {
		log.WithError(err).Errorf("Failed to read body",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	var upsertBrandAdsConfigRequest brand_ads.CreateBrandAdsConfigRequest
	err = protojson.Unmarshal(jsonData, &upsertBrandAdsConfigRequest)
	if err != nil {
		log.WithError(err).Error("Failed to bind request to JSON: %s", err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	err = validateUpsertBrandAdsConfigRequest(&upsertBrandAdsConfigRequest)
	if err != nil {
		log.WithError(err).Error("Invalid request")
		c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
		return
	}

	response, err := client.UpsertBrandAdsConfig(outgoingCtx, &upsertBrandAdsConfigRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func validateUpsertBrandAdsConfigRequest(req *brand_ads.CreateBrandAdsConfigRequest) error {
	if req.GetType() == brand_ads.ConfigType_UNDEFINED_CONFIG_TYPE {
		return errors.New("invalid config type")
	}
	if req.GetConfig() == nil {
		return errors.New("empty request")
	}
	return nil
}

func isValidUserID(c *gin.Context, userID int64) bool {
	enabledUserIDs := zconfig.GetIntSlice(c, "brand_ads.upsert_config_rpc.enabled_user_ids")
	return array.ContainsInt(enabledUserIDs, int(userID))
}

func UnmarshalCampaignAssetsData(data []byte) (*brand_ads.CampaignAssetsData, error) {
	var campaignAssetsData *adsmodel.CampaignAssetsData

	if err := json.Unmarshal(data, &campaignAssetsData); err != nil {
		return nil, err
	}

	cad := &brand_ads.CampaignAssetsData{}
	if campaignAssetsData != nil && campaignAssetsData.Assets != nil && len(campaignAssetsData.Assets) > 0 {
		tmpAssetData := make([]*brand_ads.AssetData, 0)
		for _, asset := range campaignAssetsData.Assets {
			tmpProperties := make([]*brand_ads.AssetPropertyWithValue, 0)
			if asset != nil && asset.Properties != nil {
				for _, property := range asset.Properties {
					if property != nil {
						dataType := strings.ToUpper(property.DataType)
						assetProperty := brand_ads.AssetProperty{
							Name:        property.Name,
							Type:        property.Type,
							DataType:    brand_ads.DataType(brand_ads.DataType_value[dataType]),
							IsMandatory: property.IsMandatory,
						}
						var subAssetDataWithValues []*brand_ads.SubAssetDataWithValues
						if dataType == brand_ads.DataType_SUB_ASSET_CONFIG.String() && len(property.SubAssets) > 0 {
							subAssetDataWithValues = UnmarshalSubAssetProperties(subAssetDataWithValues, property)
						}
						tmpProperties = append(tmpProperties, &brand_ads.AssetPropertyWithValue{
							Property:       &assetProperty,
							Value:          property.Value,
							SubAssetValues: subAssetDataWithValues,
						})
					}
				}
			}
			tmpAssetData = append(tmpAssetData, &brand_ads.AssetData{
				Name:       asset.Name,
				Type:       brand_ads.AssetType(brand_ads.AssetType_value[strings.ToUpper(asset.Type)]),
				Status:     brand_ads.AssetStatus(brand_ads.AssetStatus_value[strings.ToUpper(asset.Status)]),
				IsActive:   asset.IsActive,
				Properties: tmpProperties,
			})
		}
		cad = &brand_ads.CampaignAssetsData{
			CampaignId: campaignAssetsData.CampaignId,
			Assets:     tmpAssetData,
		}
	}

	return cad, nil
}

func UnmarshalSubAssetProperties(subAssetDataWithValues []*brand_ads.SubAssetDataWithValues, property *adsmodel.AssetProperty) []*brand_ads.SubAssetDataWithValues {
	subAssetDataWithValues = make([]*brand_ads.SubAssetDataWithValues, 0)
	for _, subAsset := range property.SubAssets {
		propertyWithValues := make([]*brand_ads.AssetPropertyWithValue, 0)
		for _, subAssetProperty := range subAsset.Properties {
			propertyWithValues = append(propertyWithValues, &brand_ads.AssetPropertyWithValue{
				Property: &brand_ads.AssetProperty{
					Name:        subAssetProperty.Name,
					Type:        subAssetProperty.Type,
					DataType:    brand_ads.DataType(brand_ads.DataType_value[strings.ToUpper(subAssetProperty.DataType)]),
					IsMandatory: subAssetProperty.IsMandatory,
				},
				Value: subAssetProperty.Value,
			})
		}
		subAssetDataWithValues = append(subAssetDataWithValues, &brand_ads.SubAssetDataWithValues{
			Properties: propertyWithValues,
		})
	}
	return subAssetDataWithValues
}

func UpdateAssets(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	jsonData, err := io.ReadAll(c.Request.Body)
	if err != nil {
		log.WithError(err).Errorf("Failed to read body",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	var updateAssetsRequest *brand_ads.CampaignAssetsData
	updateAssetsRequest, err = UnmarshalCampaignAssetsData(jsonData)
	if err != nil {
		log.WithError(err).Error("Failed to bind request to JSON: %s", err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.UpdateAssets(outgoingCtx, updateAssetsRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func convertValidation(req []*brand_ads.Validation) map[string]adsmodel.ValidationResponse {
	validationResponse := map[string]adsmodel.ValidationResponse{}
	for _, validation := range req {
		var value interface{}
		if validation.GetFloatValue() != nil {
			if validation.GetFloatValue().GetSingleFloatValue() != 0 {
				value = validation.GetFloatValue().GetSingleFloatValue()
			} else {
				value = validation.GetFloatValue().GetMultiFloatValues()
			}
		} else {
			if validation.GetStringValue().GetSingleStringValue() != "" {
				value = validation.GetStringValue().GetSingleStringValue()
			} else {
				value = validation.GetStringValue().GetMultiStringValues()
			}
		}
		validationResponse[validation.GetType()] = adsmodel.ValidationResponse{
			Name:  validation.GetName(),
			Value: value,
		}
	}
	return validationResponse
}

func convertToResponse(req *brand_ads.CampaignAssetsData) (*adsmodel.CampaignAssetResponse, error) {
	assetsResponse := make([]*adsmodel.AssetResponse, 0)
	for _, asset := range req.GetAssets() {
		assetsProperty := make([]*adsmodel.AssetPropertyResponse, 0)
		for _, assetProperty := range asset.GetProperties() {
			property := assetProperty.GetProperty()

			var subAssets []*adsmodel.SubAssetData
			if property.DataType == brand_ads.DataType_SUB_ASSET_CONFIG {
				subAssets = convertSubAssets(assetProperty.SubAssetValues)
			}

			assetsProperty = append(assetsProperty, &adsmodel.AssetPropertyResponse{
				Name:        property.GetName(),
				DataType:    strings.ToLower(property.GetDataType().String()),
				Type:        strings.ToLower(property.GetType()),
				Value:       assetProperty.GetValue(),
				Validations: convertValidation(property.GetValidations()),
				Options:     property.GetOptions(),
				IsMandatory: property.GetIsMandatory(),
				MinCount:    property.GetSubAssetConfig().GetMinCount(),
				MaxCount:    property.GetSubAssetConfig().GetMaxCount(),
				SubAssets:   subAssets,
			})
		}
		assetsResponse = append(assetsResponse, &adsmodel.AssetResponse{
			Name:       asset.GetName(),
			Type:       strings.ToLower(asset.GetType().String()),
			Status:     strings.ToLower(asset.GetStatus().String()),
			IsActive:   asset.IsActive,
			Properties: assetsProperty,
		})
	}
	response := &adsmodel.CampaignAssetResponse{
		CampaignId: req.GetCampaignId(),
		Assets:     assetsResponse,
		IsEditable: req.GetIsEditable(),
	}
	return response, nil
}

func convertSubAssets(values []*brand_ads.SubAssetDataWithValues) []*adsmodel.SubAssetData {
	subAssets := make([]*adsmodel.SubAssetData, 0)
	if values == nil || len(values) == 0 {
		return subAssets
	}

	for _, value := range values {
		if value.Properties == nil {
			continue
		}

		properties := make([]*adsmodel.AssetPropertyResponse, 0)
		for _, assetPropertyWithValue := range value.Properties {
			property := assetPropertyWithValue.Property
			if property == nil {
				continue
			}

			properties = append(properties, &adsmodel.AssetPropertyResponse{
				Name:        property.Name,
				DataType:    strings.ToLower(property.GetDataType().String()),
				Type:        property.Type,
				Value:       assetPropertyWithValue.Value,
				Validations: convertValidation(property.GetValidations()),
				Options:     property.GetOptions(),
				IsMandatory: property.GetIsMandatory(),
			})
		}

		subAssets = append(subAssets, &adsmodel.SubAssetData{
			Properties: properties,
		})
	}
	return subAssets
}

func GetAssets(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	jsonData, err := io.ReadAll(c.Request.Body)
	if err != nil {
		log.WithError(err).Errorf("Failed to read body",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	var getAssetRequest brand_ads.GetAssetRequest
	err = protojson.Unmarshal(jsonData, &getAssetRequest)
	if err != nil {
		log.WithError(err).Error("Failed to bind request to JSON: %s", err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	getAssetResponse, err := client.GetAssets(outgoingCtx, &getAssetRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, getAssetResponse)
		return
	}

	response, err := convertToResponse(getAssetResponse)
	if err != nil {
		log.WithError(err).Error("Failed to convert response data")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func getSegmentValueRequest(value interface{}, dataType brand_ads.DataType) (*brand_ads.SegmentValue, error) {
	segmentValue := brand_ads.SegmentValue{}

	valueJson, err := json.Marshal(value)
	if err != nil {
		return nil, err
	}

	switch dataType {
	case brand_ads.DataType_STRING, brand_ads.DataType_INT, brand_ads.DataType_CSV:
		var temp []string
		if err := json.Unmarshal(valueJson, &temp); err != nil {
			return nil, err
		}
		segmentValue = brand_ads.SegmentValue{
			DataType:    brand_ads.DataType_STRING,
			StringValue: temp,
		}
	case brand_ads.DataType_TIMESTAMP:
		var temp []*brand_ads.TIMERANGE
		if err := json.Unmarshal(valueJson, &temp); err != nil {
			return nil, err
		}
		segmentValue = brand_ads.SegmentValue{
			DataType:  brand_ads.DataType_TIMESTAMP,
			TimeValue: temp,
		}
	case brand_ads.DataType_ENUM:
		var temp []*brand_ads.OptionField
		if err := json.Unmarshal(valueJson, &temp); err != nil {
			return nil, err
		}
		segmentValue = brand_ads.SegmentValue{
			DataType:    brand_ads.DataType_ENUM,
			OptionValue: temp,
		}
	default:
		return nil, nil
	}

	return &segmentValue, nil
}

func UnmarshalCampaignTargetSegmentData(data []byte) (*brand_ads.CampaignTargetSegments, error) {
	var campaignTargetSegmentData *adsmodel.CampaignTargetSegmentRequest

	if err := json.Unmarshal(data, &campaignTargetSegmentData); err != nil {
		return nil, err
	}

	campaignTargetSegments := &brand_ads.CampaignTargetSegments{}

	if campaignTargetSegmentData != nil {
		campaignTargetSegments.CampaignId = campaignTargetSegmentData.CampaignId
	}

	if campaignTargetSegmentData != nil && campaignTargetSegmentData.TargetSegments != nil && len(campaignTargetSegmentData.TargetSegments) > 0 {
		tmpTargetSegmentData := []*brand_ads.TargetSegments{}
		for _, targetSegment := range campaignTargetSegmentData.TargetSegments {
			dataType := brand_ads.DataType(brand_ads.DataType_value[strings.ToUpper(targetSegment.DataType)])
			segmentValue, err := getSegmentValueRequest(targetSegment.Value, dataType)
			if err != nil {
				return nil, err
			}
			tmpTargetSegmentData = append(tmpTargetSegmentData, &brand_ads.TargetSegments{
				Type:          targetSegment.Type,
				Value:         segmentValue,
				OperatorValue: brand_ads.Operator(brand_ads.Operator_value[strings.ToUpper(targetSegment.OperatorValue)]),
			})
		}
		campaignTargetSegments.TargetSegments = tmpTargetSegmentData
	}
	return campaignTargetSegments, nil

}

func UpdateTargetSegments(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	jsonData, err := io.ReadAll(c.Request.Body)
	if err != nil {
		log.WithError(err).Errorf("Failed to read body",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	var updateAssetsRequest *brand_ads.CampaignTargetSegments
	updateAssetsRequest, err = UnmarshalCampaignTargetSegmentData(jsonData)
	if err != nil {
		log.WithError(err).Error("Failed to bind request to JSON: %s", err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.UpdateTargetSegments(outgoingCtx, updateAssetsRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func getSegmentValueResponse(req *brand_ads.SegmentValue) interface{} {
	if req == nil {
		return []string{}
	}

	switch req.GetDataType() {
	case brand_ads.DataType_STRING, brand_ads.DataType_INT, brand_ads.DataType_CSV:
		return req.GetStringValue()
	case brand_ads.DataType_TIMESTAMP:
		return req.GetTimeValue()
	case brand_ads.DataType_ENUM:
		return req.GetOptionValue()
	}

	return []string{}
}

func getValidOperatorsResponse(req []*brand_ads.OperatorField) []*adsmodel.ValidOperators {
	if req == nil {
		return []*adsmodel.ValidOperators{}
	}

	validOperators := []*adsmodel.ValidOperators{}
	for _, operator := range req {
		validOperators = append(validOperators, &adsmodel.ValidOperators{
			Name: operator.GetName(),
			Type: strings.ToLower(operator.GetType().String()),
		})
	}

	return validOperators
}

func makeTargetSegmentResponse(req *brand_ads.CampaignTargetSegments) (*adsmodel.CampaignTargetSegmentResponse, error) {
	targetSegmentResponse := []*adsmodel.TargetSegmentResponse{}
	for _, targetSegment := range req.GetTargetSegments() {
		targetSegmentResponse = append(targetSegmentResponse, &adsmodel.TargetSegmentResponse{
			Name:             targetSegment.GetName(),
			Type:             targetSegment.GetType(),
			DataType:         strings.ToLower(targetSegment.GetDataType().String()),
			SelectionType:    strings.ToLower(targetSegment.GetSelectionType().String()),
			Options:          targetSegment.GetOptions(),
			Value:            getSegmentValueResponse(targetSegment.GetValue()),
			OperatorValue:    strings.ToLower(targetSegment.GetOperatorValue().String()),
			IsProfileStore:   targetSegment.IsProfileStore,
			ProfileStoreKeys: targetSegment.GetProfileStoreKeys(),
			Priority:         targetSegment.GetPriority(),
			IsActive:         targetSegment.IsActive,
			ValidOperators:   getValidOperatorsResponse(targetSegment.GetValidOperators()),
		})
	}

	response := &adsmodel.CampaignTargetSegmentResponse{
		CampaignId:     req.GetCampaignId(),
		TargetSegments: targetSegmentResponse,
		IsEditable:     req.GetIsEditable(),
	}
	return response, nil
}

func GetTargetSegments(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	jsonData, err := io.ReadAll(c.Request.Body)
	if err != nil {
		log.WithError(err).Errorf("Failed to read body",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	var getTargetSegmentRequest brand_ads.GetTargetSegmentsRequest
	err = protojson.Unmarshal(jsonData, &getTargetSegmentRequest)
	if err != nil {
		log.WithError(err).Error("Failed to bind request to JSON: %s", err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	getTargetSegmentResponse, err := client.GetTargetSegments(outgoingCtx, &getTargetSegmentRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, getTargetSegmentResponse)
		return
	}

	response, err := makeTargetSegmentResponse(getTargetSegmentResponse)
	if err != nil {
		log.WithError(err).Error("Failed to convert response data")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func UpdateCampaignMetaData(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var updateCampaignMetaDataRequest brand_ads.UpdateCampaignMetaDataRequest
	err := c.ShouldBindJSON(&updateCampaignMetaDataRequest)
	if err != nil {
		log.WithError(err).Error("Failed to bind request to JSON: %s", err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.UpdateCampaignMetaData(outgoingCtx, &updateCampaignMetaDataRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}
