package ads

import (
	"encoding/json"
	"errors"
	"io"
	"io/ioutil"
	"net/http"
	"strconv"

	v2 "github.com/Zomato/ads-service-client-golang/v2"
	"github.com/Zomato/ads-service-client-golang/v2/diy"
	"github.com/Zomato/ads-service-client-golang/v2/tags"
	"github.com/Zomato/ads-service-client-golang/v2/trial_pack"
	zconfig "github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/Zomato/zomato-api-gateway/models/ads"
)

func GetAdsSetDetails(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := v2.NewCampaignServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	payloadBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		log.WithError(err).Errorf("Failed to get parse request: %s", err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	response, err := client.GetAdSetDetails(outgoingCtx, &v2.JsonRequest{
		Payload: string(payloadBytes),
	})
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	var respJSON interface{}
	err = json.Unmarshal([]byte(response.GetPayload()), &respJSON)
	if err != nil {
		log.WithError(err).Errorf("Failed to parse response: %s", err.Error())
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, respJSON)
}

func GetCampaignsView(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := v2.NewCampaignServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	payloadBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		log.WithError(err).Errorf("Failed to get parse request: %s", err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.GetCampaigns(outgoingCtx, &v2.JsonRequest{
		Payload: string(payloadBytes),
	})
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	if response.GetError() != nil {
		respErr := errors.New(response.GetError().Message)
		log.WithError(respErr).Error("Got error in the campaign view response")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	var respJSON interface{}
	err = json.Unmarshal([]byte(response.GetPayload()), &respJSON)
	if err != nil {
		log.WithError(err).Errorf("Failed to parse response: %s", err.Error())
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, respJSON)
}

func CreateCampaigns(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := v2.NewCampaignServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var createCampaignRequest v2.CreateAdsRequest
	err := c.ShouldBindJSON(&createCampaignRequest)
	if err != nil {
		log.WithError(err).Error("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.CreateAds(outgoingCtx, &createCampaignRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func CreatePackageTags(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := v2.NewCampaignServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var createPackageTagRequest tags.CreateTagsRequest
	err := c.ShouldBindJSON(&createPackageTagRequest)
	if err != nil {
		log.WithError(err).Error("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.CreateTags(outgoingCtx, &createPackageTagRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func UpdateCampaigns(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := v2.NewCampaignServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)
	jsonData, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		log.WithError(err).Errorf("Failed to read body",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	var updateCampaignRequest v2.UpdateAdsRequest
	err = protojson.Unmarshal(jsonData, &updateCampaignRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.UpdateAds(outgoingCtx, &updateCampaignRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetAdsSetObjectives(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := v2.NewCampaignServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	response, err := client.GetAdsSetObjectives(outgoingCtx, &v2.GetAdsSetObjectivesRequest{})
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetEntityProductCapacity(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := v2.NewCampaignServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var requestObject v2.GetEntityProductCapacityRequest
	err := c.ShouldBindJSON(&requestObject)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.GetEntityProductCapacity(outgoingCtx, &requestObject)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetBenefitsEstimates(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := v2.NewCampaignServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)
	var requestObject diy.GetDiyOneClickEstimatesRequest
	err := c.ShouldBindJSON(&requestObject)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	response, err := client.GetDiyOneClickEstimates(outgoingCtx, &requestObject)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func PauseAdFromOneSupport(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := v2.NewCampaignServiceClient(conn)
	jsonData, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		log.WithError(err).Errorf("Failed to read body",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	var requestObject *ads.PauseAdFromOneSupportRequest
	err = json.Unmarshal(jsonData, &requestObject)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	if requestObject.UserId == 0 || requestObject.CampaignId == 0 {
		log.Error("User or Campaign Id is required")
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, requestObject.UserId)

	updateAdsRequest := v2.UpdateAdsRequest{
		Campaigns: []*v2.CampaignUpdateRequest{
			{
				ExternalCampaignId: requestObject.CampaignId,
				Status:             v2.CampaignStatus_PAUSED,
			},
		},
	}
	response, err := client.UpdateAds(outgoingCtx, &updateAdsRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetTrialPackEstimates(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := v2.NewCampaignServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)
	resId, err := strconv.ParseInt(c.Query("res_id"), 10, 64)
	if err != nil {
		log.WithError(err).Errorf("Failed to parse res: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	response, err := client.GetTrialPackEstimates(outgoingCtx, &trial_pack.GetTrialPackEstimatesRequest{
		ResId: resId,
	})
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetPackageTagsForRes(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := v2.NewCampaignServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	resId, err := strconv.ParseInt(c.Query("res_id"), 10, 64)
	if err != nil {
		log.WithError(err).Error("Failed to parse resId: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	response, err := client.GetTagsForRes(outgoingCtx, &tags.GetTagsRequest{
		ResId: uint64(resId),
	})
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func EditCampaigns(c *gin.Context) {
	span := make(map[string]interface{})
	span["service"] = "ads"
	span["method"] = "EditCampaigns"
	log := logger.FromContext(c).WithFields(span)
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := v2.NewCampaignServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)
	var editCampaignRequest v2.EditCampaignsRequest
	err := c.ShouldBindJSON(&editCampaignRequest)
	for idx := range editCampaignRequest.Campaigns {
		curCampaign := editCampaignRequest.Campaigns[idx]
		if curCampaign == nil {
			continue
		}
		curCampaign.EditSource = v2.Sources_ADS_STUDIO_SINGLE
	}
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.EditCampaigns(outgoingCtx, &editCampaignRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func PoolCampaigns(c *gin.Context) {
	span := make(map[string]interface{})
	span["service"] = "ads"
	span["method"] = "PoolCampaigns"
	log := logger.FromContext(c).WithFields(span)
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := v2.NewCampaignServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)
	var poolCampaignsRequest v2.PoolCampaignsRequest
	err := c.ShouldBindJSON(&poolCampaignsRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.PoolCampaigns(outgoingCtx, &poolCampaignsRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		httpErrCode, _ := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(httpErrCode, response)
		return
	}
	c.JSON(http.StatusOK, response)
}
