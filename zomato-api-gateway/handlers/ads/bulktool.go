package ads

import (
	"net/http"

	bt "github.com/Zomato/ads-service-client-golang/bulk_operation"
	zconfig "github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/ads"
	bulkOpsInternal "github.com/Zomato/zomato-api-gateway/internal/ads/bulk_operation"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	dto "github.com/Zomato/zomato-api-gateway/models"
	adsdto "github.com/Zomato/zomato-api-gateway/models/ads"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

// entry point for adding a new bulk operation for processing
func StartOperation(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	killSwitchStartOperation := zconfig.GetBool(c, "ads.add_bulk_operation_rpc_kill_switch")
	if killSwitchGlobal || killSwitchStartOperation {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := bt.NewBulkOperationsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	req := &bt.AddBulkOperationRequest{}
	err := c.ShouldBindJSON(req)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.AddBulkOperation(outgoingCtx, req)

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

// Entry Point for getting bulk operations for the dashboard
func GetBulkOperations(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := bt.NewBulkOperationsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	req := &bt.GetBulkOperationsRequest{}
	err := c.ShouldBindJSON(req)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	response, err := client.GetBulkOperations(outgoingCtx, req)

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func UpdateBulkOperations(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	var req adsdto.UpdateBulkOperationsRequest
	err := c.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		log.Errorf("UpdateBulkOperations - request bind error: %+v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	resp, err := bulkOpsInternal.UpdateBulkOperations(c, &req)
	if err != nil {
		log.Errorf("UpdateBulkOperations - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(statusCode, message)
		return
	}
	c.JSON(http.StatusOK, adsdto.Response{
		Data: resp,
	})
}

func ValidateBulkOperation(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	var req adsdto.ValidateBulkOperationRequest
	err := c.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		log.Errorf("ValidateBulkOperation - request bind error: %+v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	resp, err := bulkOpsInternal.ValidateBulkOperation(c, &req)
	if err != nil {
		log.Errorf("ValidateBulkOperation - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(statusCode, message)
		return
	}
	c.JSON(http.StatusOK, adsdto.Response{
		Data: resp,
	})
}

func StartBulkOperation(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	id, err := GetIdFromQueryParam(c)
	if err != nil {
		log.Errorf("StartBulkOperation - GetIdFromQueryParam error: %+v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	resp, err := bulkOpsInternal.StartBulkOperation(c, id)
	if err != nil {
		log.Errorf("StartBulkOperation - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(statusCode, message)
		return
	}
	c.JSON(http.StatusOK, adsdto.Response{
		Data: resp,
	})
}

func GetBulkOperationTypes(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	resp, err := bulkOpsInternal.GetBulkOperationTypes(c)
	if err != nil {
		log.Errorf("StartBulkOperation - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(statusCode, message)
		return
	}
	c.JSON(http.StatusOK, adsdto.Response{
		Data: resp,
	})
}
