package ads

import (
	"github.com/Zomato/ads-service-client-golang/ads"
	"github.com/Zomato/ads-service-client-golang/campaign"
	"github.com/Zomato/go/config"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"net/http"

	"github.com/Zomato/go/logger"
	adsinternal "github.com/Zomato/zomato-api-gateway/internal/ads"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/models"
	adsModels "github.com/Zomato/zomato-api-gateway/models/ads"
	"github.com/gin-gonic/gin"
)

func GetAdCategories(c *gin.Context) {
	if IsAdsServiceMbbCreationFlow(c) {
		log := logger.FromContext(c).WithField("service", "ads")
		killSwitch := config.GetBool(c, "ads.dashboard_rpc_kill_switch")
		if killSwitch {
			c.AbortWithStatusJSON(
				http.StatusOK,
				models.StatusFailed("Kill Switch Enabled"),
			)
			return
		}
		conn := env.FromContext(c).AdsServiceConn()
		client := ads.NewAdsClient(conn)
		zomatoClient := api.GetClientFromContext(c)

		userID := zomatoClient.UserID()

		if userID == 0 {
			c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
			return
		}

		outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

		requestObject := &campaign.GetDiningMbbAdCategoriesRequest{}

		response, err := client.GetDiningMbbAdCategories(outgoingCtx, requestObject)
		if err != nil {
			log.WithError(err).Error("Failed to get response from ads-service")
			c.AbortWithStatusJSON(http.StatusInternalServerError, response)
			return
		}
		c.JSON(http.StatusOK, response)
	} else {
		c.JSON(http.StatusOK, adsinternal.GetAdCategories(c))
	}
}

func GetCampaignDurations(c *gin.Context) {
	getCampaignDurationsResponse := adsinternal.GetCampaignDurations(c)
	c.JSON(http.StatusOK, getCampaignDurationsResponse)
}

func CreateDiningMBBCampaign(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads").WithField("method", "CreateDiningMBBCampaign")
	var createCampaignRequest adsModels.CreateDiningMBBCampaignRequest
	err := c.ShouldBindJSON(&createCampaignRequest)
	if err != nil {
		log.Errorf("could not bind request: request := %+v, err := %+v", c.Request, err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}

	createCampaignResponse := adsinternal.CreateDiningMBBCampaign(c, &createCampaignRequest)
	c.JSON(http.StatusOK, createCampaignResponse)

}

func ValidateDiningMBBCampaign(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads").WithField("method", "ValidateDiningMBBCampaign")

	if IsAdsServiceMbbCreationFlow(c) {
		killSwitch := config.GetBool(c, "ads.dashboard_rpc_kill_switch")
		if killSwitch {
			c.AbortWithStatusJSON(
				http.StatusOK,
				models.StatusFailed("Kill Switch Enabled"),
			)
			return
		}
		conn := env.FromContext(c).AdsServiceConn()
		client := ads.NewAdsClient(conn)
		zomatoClient := api.GetClientFromContext(c)

		userID := zomatoClient.UserID()

		if userID == 0 {
			c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
			return
		}

		outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

		var requestObject *campaign.ValidateDiningMbbCampaignRequest
		err := c.ShouldBindJSON(&requestObject)
		if err != nil {
			log.Errorf("could not bind request: request := %+v, err := %+v", c.Request, err)
			c.AbortWithStatusJSON(
				http.StatusBadRequest,
				models.StatusFailed("Bad request - JSON binding error in incoming request"))
			return
		}
		response, err := client.ValidateDiningMbbCampaign(outgoingCtx, requestObject)
		if err != nil {
			log.WithError(err).Error("Failed to get response from ads-service")
			c.AbortWithStatusJSON(http.StatusInternalServerError, response)
			return
		}
		c.JSON(http.StatusOK, response)
	} else {
		var validateMBBCampaignRequest adsModels.ValidateMBBCampaignRequest
		err := c.ShouldBindJSON(&validateMBBCampaignRequest)
		if err != nil {
			log.Errorf("could not bind request: request := %+v, err := %+v", c.Request, err)
			c.AbortWithStatusJSON(
				http.StatusBadRequest,
				models.StatusFailed("Bad request - JSON binding error in incoming request"))
			return
		}
		validateMBBCampaignResponse := adsinternal.ValidateMBBCampaign(c, &validateMBBCampaignRequest)
		c.JSON(http.StatusOK, validateMBBCampaignResponse)
	}
}

func PushCampaignToAdsService(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads").WithField("method", "PushCampaignToAdsService")

	var pushMbbCampaignToAdsServiceRequest adsModels.PushMBBCampaignToAdsServiceRequest
	err := c.ShouldBindJSON(&pushMbbCampaignToAdsServiceRequest)
	if err != nil {
		log.Errorf("could not bind request: request := %+v, err := %+v", c.Request, err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}

	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	pushMbbCampaignToAdsServiceRequest.UpdatedBy = userID

	pushMbbCampaignToAdsServiceResponse := adsinternal.PushMBBCampaignToAdsService(c, &pushMbbCampaignToAdsServiceRequest)
	c.JSON(http.StatusOK, pushMbbCampaignToAdsServiceResponse)
}
