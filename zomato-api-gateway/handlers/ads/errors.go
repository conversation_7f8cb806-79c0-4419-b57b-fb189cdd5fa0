package ads

import (
	"net/http"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/Zomato/zomato-api-gateway/internal/ads"
)

var grpcToHttpCodesMap = map[codes.Code]int{
	codes.InvalidArgument:   http.StatusBadRequest,
	codes.PermissionDenied:  http.StatusUnauthorized,
	codes.Internal:          http.StatusInternalServerError,
	codes.Unavailable:       http.StatusServiceUnavailable,
	codes.NotFound:          http.StatusNotFound,
	codes.ResourceExhausted: http.StatusTooManyRequests,
	codes.DeadlineExceeded:  http.StatusGatewayTimeout,
}

func getHttpStatusCodeAndMessageFromError(err error) (int, string) {
	st, ok := status.FromError(err)
	if ok && grpcToHttpCodesMap[st.Code()] != 0 {
		if st.Code() == codes.DeadlineExceeded {
			return grpcToHttpCodesMap[st.Code()], ads.SomethingWentWrongMessage
		}
		return grpcToHttpCodesMap[st.Code()], st.Message()
	}
	return http.StatusInternalServerError, ads.SomethingWentWrongMessage
}
