package ads

import (
	"net/http"
	"strconv"

	adspb "github.com/Zomato/ads-service-client-golang/ads"
	"github.com/Zomato/ads-service-client-golang/merchant"
	zconfig "github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/gin-gonic/gin"
)

func GetMerchantDetails(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	merchantName := c.Query("merchant_name")
	if merchantName == "" {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "Merchant name is empty"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	response, err := client.GetMerchantsByName(outgoingCtx, &merchant.GetMerchantDetailsRequest{
		Merchant: merchantName,
	})
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetResIdsByChainID(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)
	chainID, err := strconv.ParseUint(c.Param("chain_id"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid chain_id"))
		return
	}

	response, err := client.GetResIdsByChainId(outgoingCtx, &merchant.GetResIdsByChainIdRequest{
		ChainID: chainID,
	})

	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetMerchantOfficeDetails(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	merchantID, err := strconv.ParseInt(c.Param("merchant_id"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid merchant_id"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	response, err := client.GetMerchantsOfficeByMerchantID(outgoingCtx, &merchant.GetMerchantOfficeRequest{
		MerchantID: merchantID,
	})
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}
