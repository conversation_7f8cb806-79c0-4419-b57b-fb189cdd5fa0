package ads

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/Zomato/ads-service-client-golang/ads"
	"github.com/Zomato/ads-service-client-golang/clusterads"
	adscommon "github.com/Zomato/ads-service-client-golang/common"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/location-service-client-golang/proto/city"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	adsmodel "github.com/Zomato/zomato-api-gateway/models/ads"
	"github.com/Zomato/zomato-api-gateway/models/location"
	"github.com/gin-gonic/gin"
)

func prepareGetClusterRequest(c *gin.Context) (*clusterads.GetClustersRequest, error) {
	clusterIDs := make([]string, 0)
	clusterIDString, clusterIdsPresent := c.GetQuery("cluster_ids")
	if clusterIdsPresent {
		clusterIDs = strings.Split(clusterIDString, ",")
	}
	if len(clusterIDs) != 0 {
		return &clusterads.GetClustersRequest{
			ClusterIds: clusterIDs,
		}, nil
	}

	cityIdQuery, ok := c.GetQuery("city_id")
	if !ok {
		return nil, errors.New("city_id parameter missing")
	}

	cityID, err := strconv.ParseInt(cityIdQuery, 10, 64)
	if (err != nil) || (cityID <= 0) {
		return nil, errors.New("non numeric city or city id value equal to or less than 0 passed")
	}

	property, ok := c.GetQuery("property")
	if !ok || property == "" {
		return nil, errors.New("property parameter missing")
	}

	propertyValue, ok := clusterads.ClusterProperty_value[property]
	if !ok {
		return nil, errors.New("invalid ad property type")
	}

	clusterType, ok := c.GetQuery("cluster_type")
	if !ok || clusterType == "" {
		clusterType = ""
	}

	clusterTypeValue := clusterads.ClusterType_TYPE_UNKNOWN
	if clusterType != "" {
		clusterTypeValueInt, ok := clusterads.ClusterType_value[clusterType]
		if !ok {
			return nil, errors.New("invalid cluster type")
		}
		clusterTypeValue = clusterads.ClusterType(clusterTypeValueInt)
	}

	return &clusterads.GetClustersRequest{
		CityId:      cityID,
		Property:    clusterads.ClusterProperty(propertyValue),
		ClusterType: clusterads.ClusterType(clusterTypeValue),
	}, nil
}

func GetClusters(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	conn := env.FromContext(c).AdsServiceConn()
	adsClient := ads.NewAdminClient(conn)

	getClustersRequest, err := prepareGetClusterRequest(c)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", fmt.Sprintf("Bad request: %v", err.Error())))
		return
	}

	res, err := adsClient.GetClusters(c, getClustersRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", fmt.Sprintf("upstream error: %v", err.Error())))
		return
	}

	if res.Error != nil {
		log.WithField("err_code", res.Error.Code).Error("error response from ads-service. ", res.Error.String())
		c.AbortWithStatusJSON(http.StatusInternalServerError, res)
		return
	}

	getClusterResponse := &adsmodel.GetClusterResponse{
		Clusters: make([]*adsmodel.GetClusterResponseCluster, 0),
	}
	if res.Clusters != nil {
		for _, cluster := range res.Clusters {
			intId, err := strconv.ParseInt(cluster.Id, 10, 64)
			if err != nil {
				intId = 0
			}

			responseCluster := &adsmodel.GetClusterResponseCluster{
				BaseCluster: adsmodel.BaseCluster{
					Name:          cluster.Name,
					Latitude:      cluster.Latitude,
					Longitude:     cluster.Longitude,
					CityID:        cluster.CityId,
					RatingCutoff:  cluster.MinRating,
					Price:         cluster.Price,
					Currency:      cluster.Currency,
					Enabled:       true,
					ClusterType:   clusterads.ClusterType.String(cluster.ClusterType),
					SubzoneIDS:    cluster.SubzoneIds,
					MaxFocAllowed: cluster.MaxFocAllowed,
				},
				Id:       intId,
				Property: cluster.Property,
			}

			getClusterResponse.Clusters = append(getClusterResponse.Clusters, responseCluster)
		}
	}

	c.JSON(http.StatusOK, getClusterResponse)
}

func GetClusterConfigs(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	conn := env.FromContext(c).LocationServiceConn()
	cityServiceClient := city.NewCityServiceClient(conn)

	indianCities, err := cityServiceClient.GetCityFromCountryID(c, &city.GetCityFromCountryIDRequest{
		CountryID: location.CountryIndia,
	})
	if err != nil {
		log.WithError(err).Error("Failed to get response from location-service for GetCityFromCountryID india")
	}

	uaeCities, err := cityServiceClient.GetCityFromCountryID(c, &city.GetCityFromCountryIDRequest{
		CountryID: location.CountryUAE,
	})
	if err != nil {
		log.WithError(err).Error("Failed to get response from location-service for GetCityFromCountryID UAE")
	}

	clusterDashboardConfigResponse := &adsmodel.ClusterDashboardConfigResponse{
		Cities:       make([]*adsmodel.ClusterDashboardConfigResponseCity, 0),
		AdProperties: make([]*adsmodel.ClusterDashboardConfigResponseAdProperty, 0),
	}

	cities := make([]*adsmodel.ClusterDashboardConfigResponseCity, 0)
	if indianCities != nil && indianCities.City != nil {
		for _, city := range indianCities.City {
			cities = append(cities, &adsmodel.ClusterDashboardConfigResponseCity{
				CityId: city.CityID,
				Name:   city.Name,
			})
		}
	}
	if uaeCities != nil && uaeCities.City != nil {
		for _, city := range indianCities.City {
			cities = append(cities, &adsmodel.ClusterDashboardConfigResponseCity{
				CityId: city.CityID,
				Name:   city.Name,
			})
		}
	}
	clusterDashboardConfigResponse.Cities = cities

	adProperties := make([]*adsmodel.ClusterDashboardConfigResponseAdProperty, 0)
	for value, enum := range clusterads.ClusterProperty_name {
		adProperties = append(adProperties, &adsmodel.ClusterDashboardConfigResponseAdProperty{
			Name:  enum,
			Value: int64(value),
		})
	}
	clusterDashboardConfigResponse.AdProperties = adProperties

	c.JSON(http.StatusOK, clusterDashboardConfigResponse)
}

func AddClusters(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")

	var createClustersRequest adsmodel.AddClusterRequest
	err := c.ShouldBindJSON(&createClustersRequest)
	if err != nil {
		log.Errorf("could not bind request: request := %+v, err := %+v", createClustersRequest, err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}

	var addClusterRequest clusterads.CreateClustersRequest
	for _, cluster := range createClustersRequest.Clusters {
		addRequstCluster := &clusterads.CreateClustersRequest_Cluster{
			Name:         cluster.Name,
			Latitude:     cluster.Latitude,
			Longitude:    cluster.Longitude,
			CityId:       int64(cluster.CityID),
			RatingCutoff: float64(cluster.RatingCutoff),
			Property:     clusterads.ClusterProperty(clusterads.ClusterProperty_value[cluster.Property]),
			Price:        cluster.Price,
			Currency:     adscommon.Currency(adscommon.Currency_value[cluster.Currency]),
			Enabled:      cluster.Enabled,
			SubzoneIds:   cluster.SubzoneIDS,
			ClusterType:  clusterads.ClusterType(clusterads.ClusterType_value[cluster.ClusterType]),
		}
		if cluster.MaxFocAllowed != 0 {
			addRequstCluster.MaxFocAllowed = cluster.MaxFocAllowed
		}

		addClusterRequest.Clusters = append(addClusterRequest.Clusters, addRequstCluster)
	}

	conn := env.FromContext(c).AdsServiceConn()
	adsAdminClient := ads.NewAdminClient(conn)
	res, err := adsAdminClient.CreateClusters(c, &addClusterRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", "upstream error"))
		return
	}

	if res.Error != nil {
		log.WithField("err_code", res.Error.Code).Error("error response from ads-service. ", res.Error.String())
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailed(res.Error.Message))
		return
	}

	c.JSON(http.StatusOK, models.StatusSuccess(""))
}

func UpdateCluster(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")

	var editClusterReqData adsmodel.EditClusterRequest
	err := c.ShouldBindJSON(&editClusterReqData)
	if err != nil {
		log.Errorf("could not bind request: request := %+v, err := %+v", editClusterReqData, err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}

	req := &clusterads.UpdateClusterRequest{
		Id:           fmt.Sprintf("%v", editClusterReqData.Id),
		Name:         editClusterReqData.Name,
		Latitude:     editClusterReqData.Latitude,
		Longitude:    editClusterReqData.Longitude,
		CityId:       int64(editClusterReqData.CityID),
		RatingCutoff: float64(editClusterReqData.RatingCutoff),
		Price:        editClusterReqData.Price,
		Currency:     adscommon.Currency(adscommon.Currency_value[editClusterReqData.Currency]),
		Enabled:      editClusterReqData.Enabled,
		SubzoneIds:   editClusterReqData.SubzoneIDS,
		ClusterType:  clusterads.ClusterType(clusterads.ClusterType_value[editClusterReqData.ClusterType]),
	}
	if editClusterReqData.MaxFocAllowed != 0 {
		req.MaxFocAllowed = editClusterReqData.MaxFocAllowed
	}

	conn := env.FromContext(c).AdsServiceConn()
	adsAdminClient := ads.NewAdminClient(conn)
	res, err := adsAdminClient.UpdateCluster(c, req)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service for update cluster")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", "upstream error"))
		return
	}

	if res.Error != nil {
		log.WithField("err_code", res.Error.Code).Error("error response from ads-service for update cluster. ", res.Error.String())
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailed(res.Error.Message))
		return
	}

	c.JSON(http.StatusOK, models.StatusSuccess(""))
}
