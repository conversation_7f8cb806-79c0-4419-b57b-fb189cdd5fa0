package ads

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	bid "github.com/Zomato/ads-service-client-golang/bidding"
	zconfig "github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/gin-gonic/gin"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	DefaultBidsPaginationLimit = 10
	DefaultBidOffset           = 0
)

func CreateBid(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "create-bid",
	}
	log := logger.FromContext(c).WithFields(span)

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := bid.NewBiddingClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	req := &bid.CreateBiddingRequest{}
	err := c.ShouldBindJSON(req)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.CreateBid(outgoingCtx, req)

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}

func UpdateBid(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "update-bid",
	}
	log := logger.FromContext(c).WithFields(span)

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := bid.NewBiddingClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	req := &bid.UpdateBiddingRequest{}
	err := c.ShouldBindJSON(req)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.UpdateBid(outgoingCtx, req)

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", err.Error()))
		return
	}

	c.JSON(http.StatusOK, response)
}

func BiddingConfig(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "bidding-config",
	}
	log := logger.FromContext(c).WithFields(span)

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := bid.NewBiddingClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	keyword := c.Query("keyword")
	action, err := strconv.ParseInt(c.Query("action"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse action_type")
		action = 0
	}
	cityId, err := strconv.ParseInt(c.Query("city_id"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse city_id")
		cityId = 0
	}
	resId, err := strconv.ParseUint(c.Query("res_id"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse res_id")
		resId = 0
	}
	keywordType, err := strconv.ParseUint(c.Query("keyword_type"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse keyword_type")
		keywordType = 0
	}
	targetType, err := strconv.ParseUint(c.Query("target_type"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse target_type")
		targetType = 0
	}
	var startDate, endDate *timestamppb.Timestamp
	startDateEpoch, err := strconv.ParseInt(c.Query("start_date"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse start_date epoch")
	}
	if startDateEpoch > 0 {
		startDateTs := time.Unix(startDateEpoch, 0)
		startDate = timestamppb.New(startDateTs)
	}
	endDateEpoch, err := strconv.ParseInt(c.Query("end_date"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse end_date epoch")
	}
	if endDateEpoch > 0 {
		endDateTs := time.Unix(endDateEpoch, 0)
		endDate = timestamppb.New(endDateTs)
	}
	req := &bid.GetBiddingConfigRequest{
		Keyword: keyword, Action: bid.Action(action), CityId: cityId, ResId: resId, TargetType: bid.BidTargetType(targetType),
		StartDate:   startDate,
		EndDate:     endDate,
		KeywordType: bid.KeywordType(keywordType),
	}

	response, err := client.GetBiddingConfig(outgoingCtx, req)

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", err.Error()))
		return
	}

	c.JSON(http.StatusOK, response)
}

func TopBids(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "top-bids",
	}
	log := logger.FromContext(c).WithFields(span)

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := bid.NewBiddingClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	city := c.Query("city")
	cityId, err := strconv.ParseInt(c.Query("city_id"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse city_id")
		cityId = 0
	}
	var startDate, endDate *timestamppb.Timestamp
	startDateEpoch, err := strconv.ParseInt(c.Query("start_date"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse start_date epoch")
	}
	keywordType, err := strconv.ParseUint(c.Query("keyword_type"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse keyword_type")
		keywordType = 0
	}
	if startDateEpoch > 0 {
		startDateTs := time.Unix(startDateEpoch, 0)
		startDate = timestamppb.New(startDateTs)
	}
	endDateEpoch, err := strconv.ParseInt(c.Query("end_date"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse end_date epoch")
	}
	if endDateEpoch > 0 {
		endDateTs := time.Unix(endDateEpoch, 0)
		endDate = timestamppb.New(endDateTs)
	}
	req := &bid.GetTopBidsRequest{CityName: city, CityId: cityId, StartDate: startDate, EndDate: endDate,
		KeywordType: bid.KeywordType(keywordType)}

	response, err := client.GetTopBids(outgoingCtx, req)

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}

func SingleBid(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "single-bid",
	}
	log := logger.FromContext(c).WithFields(span)

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := bid.NewBiddingClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	biddingId := c.Query("bidding_id")
	if biddingId == "" {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "bidding_id parameter is required"))
		return
	}
	bidFlowTypeStr := c.Query("bid_flow_type")
	bidFlowType, err := strconv.ParseInt(bidFlowTypeStr, 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse bid_flow_type")
		bidFlowType = 0
	}
	var startDate, endDate *timestamppb.Timestamp
	startDateEpoch, err := strconv.ParseInt(c.Query("start_date"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse start_date epoch")
	}
	if startDateEpoch > 0 {
		startDateTs := time.Unix(startDateEpoch, 0)
		startDate = timestamppb.New(startDateTs)
	}
	endDateEpoch, err := strconv.ParseInt(c.Query("end_date"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse end_date epoch")
	}
	if endDateEpoch > 0 {
		endDateTs := time.Unix(endDateEpoch, 0)
		endDate = timestamppb.New(endDateTs)
	}
	req := &bid.GetSingleBidRequest{BiddingId: biddingId, BidFlowType: bid.BidCreationFlowType(bidFlowType), StartDate: startDate, EndDate: endDate}

	response, err := client.GetSingleBid(outgoingCtx, req)

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}

func MultipleBids(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "multiple-bids",
	}
	log := logger.FromContext(c).WithFields(span)

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := bid.NewBiddingClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)
	status := c.Query("status")
	offset, err := strconv.ParseInt(c.Query("offset"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse offset")
		offset = DefaultBidOffset
	}

	limit, err := strconv.ParseInt(c.Query("limit"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse limit")
		limit = DefaultBidsPaginationLimit
	}

	var endDateFrom, endDateTo *timestamppb.Timestamp
	endDateFromEpoch, err := strconv.ParseInt(c.Query("end_date_from"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse end_date_from epoch")
	}
	endDateToEpoch, err := strconv.ParseInt(c.Query("end_date_to"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse end_date_to epoch")
	}

	if endDateFromEpoch > 0 {
		endDateFromTs := time.Unix(endDateFromEpoch, 0)
		endDateFrom = timestamppb.New(endDateFromTs)
	}
	if endDateToEpoch > 0 {
		endDateToTs := time.Unix(endDateToEpoch, 0)
		endDateTo = timestamppb.New(endDateToTs)
	}

	var startDateFrom, startDateTo *timestamppb.Timestamp
	startDateFromEpoch, err := strconv.ParseInt(c.Query("start_date_from"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse start_date_from epoch")
	}
	startDateToEpoch, err := strconv.ParseInt(c.Query("start_date_to"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse start_date_to epoch")
	}

	if startDateFromEpoch > 0 {
		startDateFromTs := time.Unix(startDateFromEpoch, 0)
		startDateFrom = timestamppb.New(startDateFromTs)
	}
	if startDateToEpoch > 0 {
		startDateToTs := time.Unix(startDateToEpoch, 0)
		startDateTo = timestamppb.New(startDateToTs)
	}

	biddingIdsParam := c.Query("bidding_ids")
	biddingIds := make([]string, 0)
	if len(biddingIdsParam) > 0 {
		biddingIds = strings.Split(biddingIdsParam, ",")
	}

	statusesParam := c.Query("statuses")
	statuses := make([]string, 0)
	if len(statusesParam) > 0 {
		statuses = strings.Split(statusesParam, ",")
	}

	cityIdsParam := c.Query("city_ids")
	cityIdsStr := make([]string, 0)
	if len(cityIdsParam) > 0 {
		cityIdsStr = strings.Split(cityIdsParam, ",")
	}
	cityIds := make([]int64, 0)
	for idx := range cityIdsStr {
		cityIdStr := cityIdsStr[idx]
		cityId, err := strconv.ParseInt(cityIdStr, 10, 64)
		if err != nil {
			log.WithError(err).Warnf("Failed to parse cityId: %s", cityIdStr)
			continue
		}
		cityIds = append(cityIds, cityId)
	}

	createdByEmailParam := c.Query("created_by_emails")
	createdByEmails := make([]string, 0)
	if len(createdByEmailParam) > 0 {
		createdByEmails = strings.Split(createdByEmailParam, ",")
	}

	keywordsParam := c.Query("keywords")
	keywords := make([]string, 0)
	if len(keywordsParam) > 0 {
		keywords = strings.Split(keywordsParam, ",")
	}

	brandNamesParam := c.Query("brand_names")
	brandNames := make([]string, 0)
	if len(brandNamesParam) > 0 {
		brandNames = strings.Split(brandNamesParam, ",")
	}

	req := &bid.GetMultipleBidsRequest{
		Status:          status,
		Limit:           int32(limit),
		Offset:          int32(offset),
		EndDateFrom:     endDateFrom,
		EndDateTo:       endDateTo,
		StartDateFrom:   startDateFrom,
		StartDateTo:     startDateTo,
		Statuses:        statuses,
		BiddingIds:      biddingIds,
		CityIds:         cityIds,
		CreatedByEmails: createdByEmails,
		Keywords:        keywords,
		BrandNames:      brandNames,
	}

	response, err := client.GetMultipleBids(outgoingCtx, req)

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}

func KeywordCityProperty(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "keyword-city-property",
	}
	log := logger.FromContext(c).WithFields(span)

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := bid.NewBiddingClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)
	city := c.Query("city")
	keyword := c.Query("keyword")
	cityId, err := strconv.ParseInt(c.Query("city_id"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse city_id")
		cityId = 0
	}
	targetType, err := strconv.ParseInt(c.Query("target_type"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse city_id")
		targetType = 0
	}
	keywordType, err := strconv.ParseUint(c.Query("keyword_type"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse keyword_type")
		keywordType = 0
	}
	req := &bid.GetKeywordCityPropertyRequest{
		CityName:    city,
		Keyword:     keyword,
		CityId:      cityId,
		TargetType:  bid.BidTargetType(targetType),
		KeywordType: bid.KeywordType(keywordType),
	}

	response, err := client.GetKeywordCityProperty(outgoingCtx, req)

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func ValidateBidCsv(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "validate-bid-csv",
	}
	log := logger.FromContext(c).WithFields(span)

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := bid.NewBiddingClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	req := &bid.ValidateBidCsvRequest{}
	err := c.ShouldBindJSON(req)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.ValidateBidCsv(outgoingCtx, req)

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}

func QueryKeywords(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "query-keywords",
	}
	log := logger.FromContext(c).WithFields(span)

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := bid.NewBiddingClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	query := c.Query("q")
	cityID, err := strconv.ParseInt(c.Query("city_id"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse city_id")
		cityID = 0
	}
	resID, err := strconv.ParseInt(c.Query("res_id"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse res_id")
		resID = 0
	}
	targetType, err := strconv.ParseInt(c.Query("target_type"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse res_id")
		targetType = 0
	}
	req := &bid.QueryKeywordRequest{
		Query:      query,
		CityId:     cityID,
		ResId:      uint64(resID),
		TargetType: bid.BidTargetType(targetType),
	}
	response, err := client.QueryKeywords(outgoingCtx, req)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}
