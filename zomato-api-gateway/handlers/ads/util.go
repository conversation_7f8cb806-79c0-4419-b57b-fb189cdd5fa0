package ads

import (
	"context"
	"errors"
	"strconv"
	"time"

	"github.com/Zomato/zomato-api-gateway/internal/ads"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/middlewares"

	"github.com/Zomato/go/grpc/metadata"
	"github.com/gin-gonic/gin"

	"github.com/Zomato/zomato-api-gateway/models/user"
)

const (
	UserIDHeaderKey string = "user-id"
	TraceIdKey      string = "trace-id"
)

var headerList = []string{
	"forceserver",
	"x-city-id",
	"x-client-id",
	"x-request-id",
	"x-amzn-trace-id",
	"x-zomato-app-version",
	"x-zomato-beta-app-version",
	"x-zomato-uuid",
}

func NewError(code string, message string) map[string]map[string]string {
	return map[string]map[string]string{
		"error": map[string]string{
			"code":    code,
			"message": message,
		},
	}
}

func metadataWithHeaders(c *gin.Context) map[string]string {
	headersMap := map[string]string{}

	for _, h := range headerList {
		if val := c.GetHeader(h); val != "" {
			headersMap[h] = val
		}
	}
	// If no request_id in header, use the one from context
	if val := c.GetHeader("x-request-id"); val == "" {
		headersMap["x-request-id"] = middlewares.GetRequestID(c)
	}

	return headersMap
}

func NewMetaDataWithAuthorisationHeaders(c *gin.Context, userID int64) context.Context {
	headersMap := metadataWithHeaders(c)
	lang := c.GetHeader(user.Lang)
	jumboSessionID := c.GetHeader(user.JumboSessionID)
	cityID := c.GetHeader(user.CityID)
	userAgent := c.GetHeader(user.UserAgent)
	clientID := c.GetHeader(user.ClientId)

	headersMap[UserIDHeaderKey] = strconv.FormatInt(userID, 10)
	headersMap[user.Lang] = lang
	headersMap[user.ClientId] = clientID
	headersMap[user.JumboSessionID] = jumboSessionID
	headersMap[user.CityID] = cityID
	headersMap[user.XUserAgent] = userAgent
	headersMap[user.UserIP] = c.ClientIP()
	headersMap[api.AuthorizationCtx] = api.GetAuthTokenFromContext(c) // For Authz Sidecar Integration
	headersMap[TraceIdKey] = middlewares.GetAmazonTraceID(c)
	NewMetaData := metadata.New(headersMap)
	return metadata.NewOutgoingContext(c, NewMetaData)
}

func validateStartAndEndDateForAdsPlatform(startDate string, endDate string) error {
	effectiveStartDate, err := time.Parse(ads.DateFormat, startDate)
	if err != nil {
		return errors.New("start date is not valid")
	}

	effectiveEndDate, err := time.Parse(ads.DateFormat, endDate)
	if err != nil {
		return errors.New("end date is not valid")
	}

	if effectiveStartDate.After(effectiveEndDate) {
		return errors.New("start date is after end date")
	}
	return nil

}

func GetIdFromQueryParam(ctx *gin.Context) (string, error) {
	id, found := ctx.GetQuery("id")
	if !found {
		return "", errors.New("id not found")
	}
	return id, nil
}

func GetEntityIdFromQueryParam(ctx *gin.Context) (uint64, error) {
	entityId, found := ctx.GetQuery("entity_id")
	if !found {
		return 0, errors.New("entity_id not found")
	}
	return strconv.ParseUint(entityId, 10, 64)
}

func GetEntityTypeFromQueryParam(ctx *gin.Context) (string, error) {
	entityType, found := ctx.GetQuery("entity_type")
	if !found {
		return "", errors.New("entity_type not found")
	}
	return entityType, nil
}
