package ads

import (
	"net/http"

	"github.com/Zomato/go/logger"
	adsinternal "github.com/Zomato/zomato-api-gateway/internal/ads"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/models"
	adsModels "github.com/Zomato/zomato-api-gateway/models/ads"
	"github.com/gin-gonic/gin"
)

func UpdateLeads(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads").WithField("method", "UpdateLeads")

	var updateLeadsRequest adsModels.UpdateLeadsRequest
	err := c.ShouldBind<PERSON>(&updateLeadsRequest)
	if err != nil {
		log.Errorf("could not bind request: request := %+v, err := %+v", c.Request, err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}

	updateLeadsResponse := adsinternal.UpdateLeads(c, &updateLeadsRequest)
	c.<PERSON>(http.StatusOK, updateLeadsResponse)
}

func UpdateCpl(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads").WithField("method", "UpdateCpl")

	var updateCplRequest adsModels.UpdateCplRequest
	err := c.ShouldBindJSON(&updateCplRequest)
	if err != nil {
		log.Errorf("could not bind request: request := %+v, err := %+v", c.Request, err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}

	updateCplResponse := adsinternal.UpdateCpl(c, &updateCplRequest)
	c.JSON(http.StatusOK, updateCplResponse)
}

func GetLeads(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads").WithField("method", "UpdateLeads")

	var updateLeadsRequest adsModels.GetLeadsRequest
	err := c.ShouldBindJSON(&updateLeadsRequest)
	if err != nil {
		log.Errorf("could not bind request: request := %+v, err := %+v", c.Request, err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}

	updateLeadsResponse := adsinternal.GetLeads(c, &updateLeadsRequest)
	c.JSON(http.StatusOK, updateLeadsResponse)
}

func GetEntityDetails(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads").WithField("method", "UpdateCpl")

	var getEntityDetailsRequest adsModels.GetEntityDetailsRequest
	err := c.ShouldBindJSON(&getEntityDetailsRequest)
	if err != nil {
		log.Errorf("could not bind request: request := %+v, err := %+v", c.Request, err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}

	getEntityDetailsResponse := adsinternal.GetEntityDetails(c, &getEntityDetailsRequest)
	c.JSON(http.StatusOK, getEntityDetailsResponse)
}

func UpdateDiningCampaignStatus(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads").WithField("method", "UpdateDiningCampaignStatus")

	var updateDiningCampaignStatusRequest adsModels.UpdateDiningCampaignStatusRequest
	err := c.ShouldBindJSON(&updateDiningCampaignStatusRequest)
	if err != nil {
		log.Errorf("could not bind request: request := %+v, err := %+v", c.Request, err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}

	zClient := api.GetClientFromContext(c)
	userId := zClient.UserID()
	updateDiningCampaignStatusRequest.UpdatedBy = userId

	getEntityDetailsResponse := adsinternal.UpdateDiningCampaignStatus(c, &updateDiningCampaignStatusRequest)
	c.JSON(http.StatusOK, getEntityDetailsResponse)
}
