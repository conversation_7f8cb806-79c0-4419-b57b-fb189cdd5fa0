package ads

import (
	"net/http"
	"strconv"

	"strings"

	hpBanner "github.com/Zomato/ads-service-client-golang/hp_banner"
	zconfig "github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/gin-gonic/gin"
)

const (
	DefaulHpBannersPaginationLimit = 10
	DefaultHpBannerOffset          = 0
)

func CreateHpBanner(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "create-hp-banner",
	}

	log := logger.FromContext(c).WithFields(span)

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := hpBanner.NewHpBannerClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	req := &hpBanner.CreateHpBannerRequest{}
	err := c.ShouldBindJSON(req)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.CreateHpBanner(outgoingCtx, req)

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}

func HpBannerConfig(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "hp-banner-config",
	}
	log := logger.FromContext(c).WithFields(span)

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := hpBanner.NewHpBannerClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)
	cityIdStr := c.DefaultQuery("city_id", "0")
	cityId, err := strconv.ParseUint(cityIdStr, 10, 64)
	if err != nil {
		log.WithError(err).Error("Failed to parse city_id from request")
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "city_id should be integer"))
		return
	}
	chainId, err := strconv.ParseUint(c.Query("chain_id"), 10, 64)
	if err != nil {
		log.WithError(err).Error("Failed to parse chain_id from request")
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "chain_id should be integer"))
		return
	}
	weekNo, err := strconv.ParseUint(c.Query("week_no"), 10, 32)
	if err != nil {
		log.Debugf("Failed to parse week_no from request")
	}
	weekNo_64, err := strconv.ParseUint(c.Query("week_no_64"), 10, 64)
	if err != nil {
		log.Debugf("Failed to parse week_no_64 from request")
	}

	req := &hpBanner.GetHpBannerConfigRequest{
		CityId:    cityId,
		ChainId:   chainId,
		WeekNo:    uint32(weekNo),
		WeekNo_64: weekNo_64,
	}

	response, err := client.GetHpBannerConfig(outgoingCtx, req)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}

func MultipleHpBanners(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "multiple-hp-banners",
	}
	log := logger.FromContext(c).WithFields(span)

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := hpBanner.NewHpBannerClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	statusesParam := c.Query("statuses")
	statuses := make([]string, 0)
	if len(statusesParam) > 0 {
		statuses = strings.Split(statusesParam, ",")
	}

	cityIdsParam := c.Query("city_ids")
	cityIdsStr := make([]string, 0)
	if len(cityIdsParam) > 0 {
		cityIdsStr = strings.Split(cityIdsParam, ",")
	}
	cityIds := make([]uint64, 0)
	for idx := range cityIdsStr {
		cityIdStr := cityIdsStr[idx]
		cityId, err := strconv.ParseUint(cityIdStr, 10, 64)
		if err != nil {
			log.WithError(err).Warnf("Failed to parse cityId: %s", cityIdStr)
			continue
		}
		cityIds = append(cityIds, cityId)
	}

	weekNosParam := c.Query("week_nos")
	weekNosStr := make([]string, 0)
	if len(weekNosParam) > 0 {
		weekNosStr = strings.Split(weekNosParam, ",")
	}
	weekNos := make([]uint32, 0)
	for idx := range weekNosStr {
		weekNoStr := weekNosStr[idx]
		weekNo, err := strconv.ParseUint(weekNoStr, 10, 64)
		if err != nil {
			log.WithError(err).Warnf("Failed to parse weekNo: %s", weekNoStr)
			continue
		}
		weekNos = append(weekNos, uint32(weekNo))
	}

	weekNosParam_64 := c.Query("week_nos_64")
	weekNosStr_64 := make([]string, 0)
	if len(weekNosParam_64) > 0 {
		weekNosStr_64 = strings.Split(weekNosParam_64, ",")
	}
	weekNos_64 := make([]uint64, 0)
	for idx := range weekNosStr_64 {
		weekNoStr_64 := weekNosStr_64[idx]
		weekNo_64, err := strconv.ParseUint(weekNoStr_64, 10, 64)
		if err != nil {
			log.WithError(err).Warnf("Failed to parse weekNo_64: %s", weekNoStr_64)
			continue
		}
		weekNos_64 = append(weekNos_64, weekNo_64)
	}

	createdByEmailParam := c.Query("created_by_emails")
	createdByEmails := make([]string, 0)
	if len(createdByEmailParam) > 0 {
		createdByEmails = strings.Split(createdByEmailParam, ",")
	}

	chainIdsParam := c.Query("chain_ids")
	chainIdsStr := make([]string, 0)
	if len(chainIdsParam) > 0 {
		chainIdsStr = strings.Split(chainIdsParam, ",")
	}
	chainIds := make([]uint64, 0)
	for idx := range chainIdsStr {
		chainIdStr := chainIdsStr[idx]
		chainId, err := strconv.ParseUint(chainIdStr, 10, 64)
		if err != nil {
			log.WithError(err).Warnf("Failed to parse chainId: %s", chainIdStr)
			continue
		}
		chainIds = append(chainIds, chainId)
	}

	hpBannerIdsParam := c.Query("hp_banner_ids")
	hpBannerIds := make([]string, 0)
	if len(hpBannerIdsParam) > 0 {
		hpBannerIds = strings.Split(hpBannerIdsParam, ",")
	}

	offset, err := strconv.ParseInt(c.Query("offset"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse offset")
		offset = DefaultHpBannerOffset
	}

	limit, err := strconv.ParseInt(c.Query("limit"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse limit")
		limit = DefaulHpBannersPaginationLimit
	}

	req := &hpBanner.GetMultipleHpBannerRequest{
		Statuses:        statuses,
		CityIds:         cityIds,
		WeekNos:         weekNos,
		WeekNos_64:      weekNos_64,
		CreatedByEmails: createdByEmails,
		ChainIds:        chainIds,
		HpBannerIds:     hpBannerIds,
		Limit:           int32(limit),
		Offset:          int32(offset),
	}

	response, err := client.GetMultipleHpBanners(outgoingCtx, req)

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetHpBannerSummary(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "get-hp-banner-summary",
	}
	log := logger.FromContext(c).WithFields(span)

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := hpBanner.NewHpBannerClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	statusesParam := c.Query("statuses")
	statuses := make([]string, 0)
	if len(statusesParam) > 0 {
		statuses = strings.Split(statusesParam, ",")
	}

	cityIdsParam := c.Query("city_ids")
	cityIdsStr := make([]string, 0)
	if len(cityIdsParam) > 0 {
		cityIdsStr = strings.Split(cityIdsParam, ",")
	}
	cityIds := make([]uint64, 0)
	for idx := range cityIdsStr {
		cityIdStr := cityIdsStr[idx]
		cityId, err := strconv.ParseUint(cityIdStr, 10, 64)
		if err != nil {
			log.WithError(err).Warnf("Failed to parse cityId: %s", cityIdStr)
			continue
		}
		cityIds = append(cityIds, cityId)
	}

	weekNosParam := c.Query("week_nos")
	weekNosStr := make([]string, 0)
	if len(weekNosParam) > 0 {
		weekNosStr = strings.Split(weekNosParam, ",")
	}
	weekNos := make([]uint64, 0)
	for idx := range weekNosStr {
		weekNoStr := weekNosStr[idx]
		weekNo, err := strconv.ParseUint(weekNoStr, 10, 64)
		if err != nil {
			log.WithError(err).Warnf("Failed to parse weekNo: %s", weekNoStr)
			continue
		}
		weekNos = append(weekNos, uint64(weekNo))
	}


	createdByEmailParam := c.Query("created_by_emails")
	createdByEmails := make([]string, 0)
	if len(createdByEmailParam) > 0 {
		createdByEmails = strings.Split(createdByEmailParam, ",")
	}

	chainIdsParam := c.Query("chain_ids")
	chainIdsStr := make([]string, 0)
	if len(chainIdsParam) > 0 {
		chainIdsStr = strings.Split(chainIdsParam, ",")
	}
	chainIds := make([]uint64, 0)
	for idx := range chainIdsStr {
		chainIdStr := chainIdsStr[idx]
		chainId, err := strconv.ParseUint(chainIdStr, 10, 64)
		if err != nil {
			log.WithError(err).Warnf("Failed to parse chainId: %s", chainIdStr)
			continue
		}
		chainIds = append(chainIds, chainId)
	}

	hpBannerIdsParam := c.Query("hp_banner_ids")
	hpBannerIds := make([]string, 0)
	if len(hpBannerIdsParam) > 0 {
		hpBannerIds = strings.Split(hpBannerIdsParam, ",")
	}


	req := &hpBanner.GetHpBannerSummaryRequest{
		Statuses:        statuses,
		CityIds:         cityIds,
		WeekNos:         weekNos,
		CreatedByEmails: createdByEmails,
		ChainIds:        chainIds,
		HpBannerIds:     hpBannerIds,
	}

	response, err := client.GetHpBannerSummary(outgoingCtx, req)

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}

func EditHpBanner(c *gin.Context) {
	span := map[string]interface{}{
		"service": "ads",
		"method":  "edit-hp-banner",
	}

	log := logger.FromContext(c).WithFields(span)

	killSwitchGlobal := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitchGlobal {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	client := hpBanner.NewHpBannerClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	req := &hpBanner.EditHpBannerRequest{}
	err := c.ShouldBindJSON(req)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.EditHpBanner(outgoingCtx, req)

	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}
