package ads

import (
	"net/http"

	"github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"

	"github.com/Zomato/zomato-api-gateway/internal/ads"
	"github.com/Zomato/zomato-api-gateway/internal/ads/platform"
	dto "github.com/Zomato/zomato-api-gateway/models"
	adsdto "github.com/Zomato/zomato-api-gateway/models/ads"
)

func GetEntityDetailsById(ctx *gin.Context) {
	resp, err := platform.GetEntityDetails(ctx)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetEntityDetails - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func GetSections(ctx *gin.Context) {
	resp, err := platform.GetSections(ctx)
	if err != nil {
		logger.FromContext(ctx).<PERSON><PERSON><PERSON>("GetSections - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func GetAggregatedAdsPerformance(ctx *gin.Context) {
	var req adsdto.GetAggregatedAdsPerformanceRequest
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetAggregatedAdsPerformance - request bind error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	err = validateStartAndEndDateForAdsPlatform(req.StartDate, req.EndDate)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetAggregatedAdsPerformance - validateStartAndEndDateForAdsPlatform error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	resp, err := platform.GetAggregatedAdsPerformance(ctx, &req)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetAggregatedAdsPerformance - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, adsdto.Response{
		Data: resp,
	})
}

func GetAdsPerformanceTrendChart(ctx *gin.Context) {
	var req adsdto.GetAdsPerformanceTrendChartRequest
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetAdsPerformanceTrendChart - request bind error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	err = validateStartAndEndDateForAdsPlatform(req.StartDate, req.EndDate)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetAggregatedAdsPerformance - validateStartAndEndDateForAdsPlatform error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	resp, err := platform.GetAdsPerformanceTrendChart(ctx, &req)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetAdsPerformanceTrendChart - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, adsdto.Response{
		Data: resp,
	})
}

func GetProducts(ctx *gin.Context) {
	resp, err := platform.GetProductData(ctx)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetProducts - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, adsdto.Response{
		Data: resp,
	})
}

func GetFilters(ctx *gin.Context) {
	getFilterReq := prepareGetFilterRequest(ctx)
	logger.FromContext(ctx).Debugf("getFilterReq:%v", getFilterReq)

	getFilterResp, err := platform.GetFilters(ctx, getFilterReq)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetFilters - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, getFilterResp)
}

func prepareGetFilterRequest(ctx *gin.Context) *adsdto.GetFiltersRequest {
	page := ctx.Query("page")
	adGroupId := ctx.Query("ad_group_id")
	return &adsdto.GetFiltersRequest{
		Page:      adsdto.Page(page),
		AdGroupID: adGroupId,
	}
}

func GetStep(ctx *gin.Context) {
	var req adsdto.GetStepRequest
	err := ctx.ShouldBindQuery(&req)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetStep - request bind error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	resp, err := platform.GetStep(ctx, &req)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetStep - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, adsdto.Response{
		Data: resp,
	})
}

func DownloadAdsPerformance(ctx *gin.Context) {
	var req adsdto.DownloadAdsPerformanceRequest
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		logger.FromContext(ctx).Errorf("DownloadAdsPerformance - request bind error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	err = validateStartAndEndDateForAdsPlatform(req.StartDate, req.EndDate)
	if err != nil {
		logger.FromContext(ctx).Errorf("DownloadAdsPerformance - validateStartAndEndDateForAdsPlatform error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	resp, err := platform.DownloadAdsPerformance(ctx, &req)
	if err != nil {
		logger.FromContext(ctx).Errorf("DownloadAdsPerformance - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, adsdto.Response{
		Status: string(ads.HTTPSuccessResponse),
		Code:   http.StatusOK,
		Data:   resp,
	})
}

func GetAdsPerformancePieChart(ctx *gin.Context) {
	var req adsdto.GetAdsPerformancePieChartRequest
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetAdsPerformancePieChart - request bind error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	err = validateStartAndEndDateForAdsPlatform(req.StartDate, req.EndDate)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetAdsPerformancePieChart - validateStartAndEndDateForAdsPlatform error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	resp, err := platform.GetAdsPerformancePieChart(ctx, &req)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetAdsPerformancePieChart - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, adsdto.Response{
		Status: string(ads.HTTPSuccessResponse),
		Code:   http.StatusOK,
		Data:   resp,
	})
}

func GetAdsPerformanceMealtimeBarChart(ctx *gin.Context) {
	var req adsdto.GetAdsPerformanceMealtimeBarChartRequest
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetAdsPerformanceMealtimeBarChart - request bind error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	err = validateStartAndEndDateForAdsPlatform(req.StartDate, req.EndDate)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetAdsPerformanceMealtimeBarChart - validateStartAndEndDateForAdsPlatform error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	resp, err := platform.GetAdsPerformanceMealtimeBarChart(ctx, &req)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetAdsPerformanceMealtimeBarChart - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, adsdto.Response{
		Status: string(ads.HTTPSuccessResponse),
		Code:   http.StatusOK,
		Data:   resp,
	})
}

func GetEstimates(ctx *gin.Context) {
	var req adsdto.GetEstimatesRequest
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetEstimates - request bind error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	resp, err := platform.GetEstimates(ctx, &req)
	if err != nil {
		logger.FromContext(ctx).Errorf("GetEstimates - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, adsdto.Response{
		Data: resp,
	})
}

func DownloadAdsPerformanceStatus(ctx *gin.Context) {
	id, err := GetIdFromQueryParam(ctx)
	if err != nil {
		logger.FromContext(ctx).Errorf("DownloadAdsPerformanceStatus invalid request", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	req := adsdto.DownloadAdsPerformanceStatusRequest{
		Id: id,
	}
	resp, err := platform.DownloadAdsPerformanceStatus(ctx, &req)
	if err != nil {
		logger.FromContext(ctx).Errorf("DownloadAdsPerformanceStatus - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, adsdto.Response{
		Status: string(ads.HTTPSuccessResponse),
		Code:   http.StatusOK,
		Data:   resp,
	})
}

func SaveDraft(ctx *gin.Context) {
	var req adsdto.SaveDraftRequest
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		logger.FromContext(ctx).Errorf("SaveDraft - request bind error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	resp, err := platform.SaveDraft(ctx, &req)
	if err != nil {
		logger.FromContext(ctx).Errorf("SaveDraft - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func CreateAds(ctx *gin.Context) {
	var req adsdto.CreateAdGroupRequest
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		logger.FromContext(ctx).Errorf("CreateAds - request bind error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	resp, err := platform.CreateAd(ctx, &req)
	if err != nil {
		logger.FromContext(ctx).Errorf("CreateAds - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, adsdto.Response{
		Data: resp,
	})
}

func ValidateCustomBudgetCsv(ctx *gin.Context) {
	var req adsdto.ValidateCustomBudgetCsvRequest
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		logger.FromContext(ctx).Errorf("ValidateCustomBudgetCsv - request bind error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	resp, err := platform.ValidateBudgetCsv(ctx, req)
	if err != nil {
		logger.FromContext(ctx).Errorf("ValidateCustomBudgetCsv - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, adsdto.Response{
		Data: resp,
	})
}

func SingleAdGroup(ctx *gin.Context) {
	id, err := GetIdFromQueryParam(ctx)
	if err != nil {
		logger.FromContext(ctx).Errorf("SingleAdGroup invalid request", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	req := &adsdto.SingleAdGroupRequest{
		ExternalAdGroupId: id,
	}
	resp, err := platform.SingleAdGroup(ctx, req)
	if err != nil {
		logger.FromContext(ctx).Errorf("SingleAdGroup - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, adsdto.Response{
		Status: string(ads.HTTPSuccessResponse),
		Code:   http.StatusOK,
		Data:   resp,
	})
}

func MultipleAdGroups(ctx *gin.Context) {
	var req adsdto.MultipleAdGroupRequest
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		logger.FromContext(ctx).Errorf("MultipleAdGroups - request bind error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	resp, err := platform.MultipleAdGroups(ctx, &req)
	if err != nil {
		logger.FromContext(ctx).Errorf("MultipleAdGroups - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, adsdto.Response{
		Data: resp,
	})
}
