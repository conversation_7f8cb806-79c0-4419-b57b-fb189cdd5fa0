package ads

import (
	"net/http"

	"github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"

	"github.com/Zomato/zomato-api-gateway/internal/ads"
	dto "github.com/Zomato/zomato-api-gateway/models"
	adsdto "github.com/Zomato/zomato-api-gateway/models/ads"
)

func UpdateKV(ctx *gin.Context) {
	var req adsdto.UpdateKVRequest
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		logger.FromContext(ctx).Errorf("UpdateKV - request bind error: %+v", err)
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(ads.InvalidRequestMessage))
		return
	}
	err = ads.UpdateKV(ctx, &req)
	if err != nil {
		logger.FromContext(ctx).E<PERSON>rf("UpdateKV - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(statusCode, message)
		return
	}
	ctx.JSON(http.StatusOK, adsdto.Response{})
}
