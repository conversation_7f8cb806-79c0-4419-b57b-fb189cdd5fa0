package ads

import (
	"net/http"

	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/ads/tracking"
	adsdto "github.com/Zomato/zomato-api-gateway/models/ads"
	"github.com/gin-gonic/gin"
)

func CampaignsFetchHandler(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")

	var campaignFetchReq adsdto.CampaignsFetchRequest
	err := c.ShouldBindBodyWithJSON(&campaignFetchReq)
	if err != nil {
		log.WithError(err).Erro<PERSON>("[CampaignsFetchHandler] Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := tracking.CampaignsFetch(c, &campaignFetchReq)
	if err != nil {
		log.Errorf("[CampaignsFetchHandler] - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(statusCode, message)
		return
	}

	c.JSON(http.StatusOK, response)
}

func CampaignsSummaryHandler(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")

	var campaignSummaryReq adsdto.CampaignsSummaryRequest
	err := c.ShouldBindBodyWithJSON(&campaignSummaryReq)
	if err != nil {
		log.WithError(err).Errorf("[CampaignsSummaryHandler] Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := tracking.CampaignsSummary(c, &campaignSummaryReq)
	if err != nil {
		log.Errorf("[CampaignsSummaryHandler] - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(statusCode, message)
		return
	}

	c.JSON(http.StatusOK, response)
}

func CampaignsActionHandler(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")

	var campaignActionReq adsdto.CampaignsActionRequest
	err := c.ShouldBindBodyWithJSON(&campaignActionReq)
	if err != nil {
		log.WithError(err).Errorf("[CampaignsActionHandler] Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := tracking.CampaignsAction(c, &campaignActionReq)
	if err != nil {
		log.Errorf("[CampaignsActionHandler] - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(statusCode, message)
		return
	}

	c.JSON(http.StatusOK, response)
}

func TrackingDownloadHandler(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")

	var trackingDownloadReq adsdto.TrackingDownloadRequest
	err := c.ShouldBindBodyWithJSON(&trackingDownloadReq)
	if err != nil {
		log.WithError(err).Errorf("[TrackingDownloadHandler] Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := tracking.TrackingDownload(c, &trackingDownloadReq)
	if err != nil {
		log.Errorf("[TrackingDownloadHandler] - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(statusCode, message)
		return
	}

	c.JSON(http.StatusOK, response)
}
