package ads

import (
	"net/http"

	"github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
	adsdto "github.com/Zomato/zomato-api-gateway/models/ads"
	approvalInternal "github.com/Zomato/zomato-api-gateway/internal/ads/approvals"
)

func AdminUpdateApprovals(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")

	var adminUpdateApprovalsReq adsdto.AdminUpdateApprovalsRequest
	err := c.ShouldBindBodyWithJSON(&adminUpdateApprovalsReq)
	if err != nil {
		log.WithError(err).<PERSON><PERSON><PERSON>("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	if len(adminUpdateApprovalsReq.ApprovalGroups) == 0 {
		log.Errorf("AdminUpdateApprovals - No approval groups provided in request")
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := approvalInternal.AdminUpdateApprovals(c, &adminUpdateApprovalsReq)
	if err != nil {
		log.Errorf("AdminUpdateApprovals - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(statusCode, message)
		return
	}

	c.JSON(http.StatusOK, response)
}
