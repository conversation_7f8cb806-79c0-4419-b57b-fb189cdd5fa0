package ads

import (
	"net/http"

	"github.com/Zomato/ads-service-client-golang/ads"
	"github.com/Zomato/ads-service-client-golang/cache"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/gin-gonic/gin"
)

func GetCache(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads").With<PERSON>ield("method", "GetCache")

	var getCacheRequest cache.GetCacheRequest
	err := c.ShouldBindJ<PERSON>N(&getCacheRequest)
	if err != nil {
		log.Errorf("could not bind request: request := %+v, err := %+v", c.Request, err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	adsClient := ads.NewAdsClient(conn)
	res, err := adsClient.GetCacheValue(c, &getCacheRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", "Could not get cache value for this key"))
		return
	}

	c.JSON(http.StatusOK, res)
}

func ClearCache(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads").WithField("method", "ClearCache")

	var clearCacheRequest cache.ClearCacheRequest
	err := c.ShouldBindJSON(&clearCacheRequest)
	if err != nil {
		log.Errorf("could not bind request: request := %+v, err := %+v", c.Request, err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}

	conn := env.FromContext(c).AdsServiceConn()
	adsClient := ads.NewAdsClient(conn)
	res, err := adsClient.ClearCacheValue(c, &clearCacheRequest)
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, NewError("500", "Unable to clear cache for this key"))
		return
	}

	c.JSON(http.StatusOK, res)
}
