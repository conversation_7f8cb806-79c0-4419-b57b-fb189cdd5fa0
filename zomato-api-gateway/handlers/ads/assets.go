package ads

import (
	"encoding/json"
	"net/http"

	"github.com/Zomato/cart-service/pkg/log"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/ads"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	dto "github.com/Zomato/zomato-api-gateway/models"
	adsdto "github.com/Zomato/zomato-api-gateway/models/ads"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

func GetAllAssetsForAdsStudio(ctx *gin.Context) {
	assetsData, err := ads.GetAdsAssetsFromCommsSvc(ctx)
	if err != nil {
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, dto.StatusFailed(http.StatusText(http.StatusInternalServerError)))
	}
	response := make(map[string][]interface{})
	for assetType, data := range assetsData {
		var obj []interface{}
		unmarshalErr := json.Unmarshal([]byte(data), &obj)
		if unmarshalErr != nil {
			logger.FromContext(ctx).Errorf("unable to unmarshal string : %v, err: %v", data, err)
			continue
		}
		response[assetType] = obj
	}
	ctx.JSON(http.StatusOK, response)
}

func AcknowledgeAsset(ctx *gin.Context) {
	// Bind JSON using request model
	var req adsdto.AcknowledgeRequest
	if err := ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		log.ErrorfWithContext(ctx, "could not bind request: %s", err)
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			dto.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}
	if req.EntityType == adsdto.SegmentTypeRestaurant {
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed("restaurant entity type is not supported at the moment"))
		return
	}
	zomatoClient := api.GetClientFromContext(ctx)
	loggedInUser := zomatoClient.UserID()
	if req.EntityType == adsdto.SegmentTypeUser && req.EntityId != loggedInUser {
		ctx.AbortWithStatusJSON(http.StatusBadRequest, dto.StatusFailed(http.StatusText(http.StatusBadRequest)))
		return
	}
	err := ads.AcknowledgeAsset(ctx, &req)
	if err != nil {
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, dto.StatusFailed(http.StatusText(http.StatusInternalServerError)))
		return
	}
	ctx.JSON(http.StatusOK, dto.StatusSuccess("asset acknowledged"))
}
