package ads

import (
	"net/http"
	"testing"

	"github.com/Zomato/go/grpc/status"
	"google.golang.org/grpc/codes"
)

func Test_getHttpStatusCodeAndMessageFromError(t *testing.T) {
	type args struct {
		err error
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{
			name: "status not found",
			args: args{
				err: status.Error(codes.NotFound, "not found"),
			},
			want: http.StatusNotFound,
		},
		{
			name: "invalid arg",
			args: args{
				err: status.Error(codes.InvalidArgument, "invalid arg"),
			},
			want: http.StatusBadRequest,
		},
		{
			name: "aborted",
			args: args{
				err: status.Error(codes.Aborted, "aborted"),
			},
			want: http.StatusInternalServerError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got, _ := getHttpStatusCodeAndMessageFromError(tt.args.err); got != tt.want {
				t.Errorf("getHttpStatusCodeAndMessageFromError() = %v, want %v", got, tt.want)
			}
		})
	}
}
