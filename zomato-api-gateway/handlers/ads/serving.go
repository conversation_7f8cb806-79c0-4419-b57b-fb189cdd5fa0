package ads

import (
	"encoding/json"
	"errors"
	"net/http"
	"strconv"
	"time"

	"github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/webclient"
	"github.com/Zomato/zomato-api-gateway/models/ads"
	"github.com/gin-gonic/gin"
)

const (
	adsRailAPIPath       = "/api-internal/ads/menu"
	UserDefinedLatitude  = "x-user-defined-lat"
	UserDefinedLongitude = "x-user-defined-long"
	CityID               = "x-city-id"
)

func GetSimilarAdsRail(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	log.Debug("GetSimilarAdsRail request")

	if !config.GetBool(c, "ads.similar_ads_rail_enabled") {
		c.JSON(
			http.StatusOK,
			struct{}{},
		)
		return
	}

	webReq, err := buildWebSimilarAdRailRequest(c)
	if err != nil {
		log.WithError(err).Error("GetSimilarAdsRail bad request")
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}
	if webReq == nil {
		log.Error("Failed to parse GetSimilarAdsRail request")
		c.AbortWithStatusJSON(http.StatusBadRequest, "Failed to parse GetSimilarAdsRail request")
		return
	}
	response, err := webReq.ExecuteWithTimeout(c, time.Millisecond*time.Duration(config.GetInt64(c, "ads.similar_ads_rail_timeout_ms")))
	if err != nil {
		log.WithError(err).Error("Failed to fetch ads rail from web")
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}
	if response.StatusCode != http.StatusOK {
		log.Error("Failed to fetch ads rail from web")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response.GetBody())
		return
	}
	log.Debug(response.GetBody())

	var jsonResponse interface{}
	if err := json.Unmarshal(response.GetResponseBody(), &jsonResponse); err != nil {
		log.WithError(err).Error("Failed to unmarshal ads rail response from web")
		c.AbortWithStatusJSON(http.StatusInternalServerError, "Failed to parse ads rail response")
	}

	c.JSON(
		http.StatusOK,
		jsonResponse,
	)
}

func buildWebSimilarAdRailRequest(c *gin.Context) (*webclient.Request, error) {
	log := logger.FromContext(c).WithField("service", "ads")
	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()

	adsRequest := &ads.GetSimilarAdsRailRequest{}
	if err := c.ShouldBindJSON(&adsRequest); err != nil {
		return nil, err
	}

	// validate request
	if adsRequest.ResID == "" {
		log.Error("ResID not found")
		return nil, errors.New("empty leader res ID")
	}

	if adsRequest.CellID == "" {
		log.Error("CellID not found")
		return nil, errors.New("empty user location data")
	}

	if adsRequest.PlaceID == "" {
		log.Error("PlaceID not found")
		return nil, errors.New("empty user location data")
	}

	userLat := ""
	if val := c.GetHeader(UserDefinedLatitude); val != "" {
		userLat = val
	} else {
		log.Error("User Latitude not found")
		return nil, errors.New("empty user location data")
	}

	userLong := ""
	if val := c.GetHeader(UserDefinedLongitude); val != "" {
		userLong = val
	} else {
		log.Error("User Longitude not found")
		return nil, errors.New("empty user location data")
	}

	cityId := ""
	if val := c.GetHeader(CityID); val != "" {
		cityId = val
	} else {
		log.Error("CityID not found")
		return nil, errors.New("empty user location data")
	}

	webReq := webclient.NewZomatoWebRequest(c, http.MethodPost, adsRailAPIPath)

	for _, header := range webclient.RequiredHeaders {
		headerValue := c.GetHeader(header)
		if headerValue != "" {
			webReq.SetHeader(header, headerValue)
		}
	}

	webReq.SetHeader("Content-Type", "application/x-www-form-urlencoded")
	webReq.SetPostParam("rail_type", adsRequest.Type)
	webReq.SetPostParam("user_id", strconv.FormatInt(userID, 10))
	webReq.SetPostParam("city_id", cityId)
	webReq.SetPostParam("leader_res_id", adsRequest.ResID)
	webReq.SetPostParam("user_lat", userLat)
	webReq.SetPostParam("user_long", userLong)
	webReq.SetPostParam("cell_id", adsRequest.CellID)
	webReq.SetPostParam("display_page", adsRequest.Type)
	webReq.SetPostParam("delivery_subzone_id", adsRequest.PlaceID)
	webReq.SetHostHeader(config.GetString(c, "api_internal.host"))

	return webReq, nil
}
