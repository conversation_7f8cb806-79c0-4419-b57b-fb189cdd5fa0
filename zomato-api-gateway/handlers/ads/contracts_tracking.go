package ads

import (
	"net/http"

	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/ads/tracking"
	adsdto "github.com/Zomato/zomato-api-gateway/models/ads"
	"github.com/gin-gonic/gin"
)

func ContractsFetchHandler(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")

	var contractFetchReq adsdto.ContractsFetchRequest
	err := c.ShouldBindBodyWithJSON(&contractFetchReq)
	if err != nil {
		log.WithError(err).Erro<PERSON>("[ContractsFetchHandler] Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := tracking.ContractsFetch(c, &contractFetchReq)
	if err != nil {
		log.Errorf("[ContractsFetchHandler] - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(statusCode, message)
		return
	}

	c.JSON(http.StatusOK, response)
}

func ContractsSummaryHandler(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")

	var contractSummaryReq adsdto.ContractsSummaryRequest
	err := c.ShouldBindBodyWithJSON(&contractSummaryReq)
	if err != nil {
		log.WithError(err).Errorf("[ContractsSummaryHandler] Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := tracking.ContractsSummary(c, &contractSummaryReq)
	if err != nil {
		log.Errorf("[ContractsSummaryHandler] - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(statusCode, message)
		return
	}

	c.JSON(http.StatusOK, response)
}
