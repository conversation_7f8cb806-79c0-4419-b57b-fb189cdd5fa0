package ads

import (
	"net/http"

	"github.com/Zomato/go/logger"
	adsinternal "github.com/Zomato/zomato-api-gateway/internal/ads"
	"github.com/Zomato/zomato-api-gateway/models"
	adsModels "github.com/Zomato/zomato-api-gateway/models/ads"
	"github.com/gin-gonic/gin"
)

func GetDiningAdCampaigns(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads").WithField("method", "GetDiningAdCampaigns")

	var getDiningAdCampaignsRequest adsModels.GetDiningAdCampaignsRequest
	err := c.ShouldBindJ<PERSON>(&getDiningAdCampaignsRequest)
	if err != nil {
		log.Errorf("could not bind request: request := %+v, err := %+v", c.Request, err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}

	getDiningCampaignsResponse := adsinternal.GetDiningAdCampaigns(c, &getDiningAdCampaignsRequest)
	c.JSON(http.StatusOK, getDiningCampaignsResponse)
}

func GetDiningCampaignsFilters(c *gin.Context) {
	getDiningCampaignsFiltersResponse := adsinternal.GetDiningAdCampaignsFilters(c)
	c.JSON(http.StatusOK, getDiningCampaignsFiltersResponse)
}

func UpdateCampaign(c *gin.Context) {
	var updateDiningCampaignRequest adsModels.UpdateCampaignRequest
	err := c.ShouldBindJSON(&updateDiningCampaignRequest)
	if err != nil {
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Bad request - JSON binding error in incoming request"))
		return
	}
	for _, update := range updateDiningCampaignRequest.Updates {
		switch update.Action {
		case adsModels.UpdateCluster:
			err := adsinternal.UpdateDiningCampaign(c, updateDiningCampaignRequest.CampaignId, update.Properties)
			if err != nil {
				c.AbortWithStatusJSON(
					http.StatusInternalServerError,
					models.StatusFailed("Internal server error - updating campaign"))
				return
			}
			break
		default:
			continue
		}
	}

	c.JSON(http.StatusOK, models.StatusSuccess("Campaigns updated successfully"))
	return
}
