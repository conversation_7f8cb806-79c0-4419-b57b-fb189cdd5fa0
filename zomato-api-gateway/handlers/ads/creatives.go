package ads

import (
	"net/http"
	"strconv"

	ads "github.com/Zomato/ads-service-client-golang/ads"
	cpb "github.com/Zomato/ads-service-client-golang/creatives"
	zconfig "github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"

	internalAds "github.com/Zomato/zomato-api-gateway/internal/ads"
	"github.com/Zomato/zomato-api-gateway/internal/ads/creatives"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	adsdto "github.com/Zomato/zomato-api-gateway/models/ads"
)

func CreateCreative(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := ads.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var createCreativeRequest cpb.CreateCreativeRequest
	err := c.ShouldBindJSON(&createCreativeRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.CreateCreative(outgoingCtx, &createCreativeRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetCreative(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := ads.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var getCreativeRequest cpb.GetCreativeRequest
	err := c.ShouldBindJSON(&getCreativeRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.GetCreative(outgoingCtx, &getCreativeRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func EditCreative(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := ads.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var editCreativeRequest cpb.EditCreativeRequest
	err := c.ShouldBindJSON(&editCreativeRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.EditCreative(outgoingCtx, &editCreativeRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func UpdateCreativeStatus(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := ads.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var updateCreativeStatusRequest cpb.UpdateCreativeStatusRequest
	err := c.ShouldBindJSON(&updateCreativeStatusRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.UpdateCreativeStatus(outgoingCtx, &updateCreativeStatusRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetCatalogueItemsForCreatives(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)
	resID, err := strconv.ParseInt(c.Query("res_id"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid res_id"))
		return
	}
	response, err := internalAds.GetCatalogueItemsForCreatives(c, resID, outgoingCtx)
	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		httpErrCode, _ := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(httpErrCode, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetFeaturedImage(ctx *gin.Context) {
	log := logger.FromContext(ctx).WithField("service", "ads")
	killswitch := zconfig.GetBool(ctx, "ads.dashboard_rpc_kill_switch")
	if killswitch {
		ctx.AbortWithStatusJSON(http.StatusOK, models.StatusFailed("Kill Switch Enabled"))
		return
	}
	entityId, err := GetEntityIdFromQueryParam(ctx)
	if err != nil {
		log.WithError(err).Errorf("Failed to get entity_id from query")
		ctx.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid entity_id"))
		return
	}
	entityType, err := GetEntityTypeFromQueryParam(ctx)
	if err != nil {
		log.WithError(err).Errorf("Failed to get entity_type from query")
		ctx.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid entity_type"))
		return
	}
	if entityId == 0 || entityType == "" {
		log.WithField("entity_id", entityId).WithField("entity_type", entityType).Errorf("Invalid entity_id or entity_type")
		ctx.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid entity_id or entity_type"))
		return
	}

	zomatoClient := api.GetClientFromContext(ctx)
	userID := zomatoClient.UserID()
	getFeaturedImageReq := adsdto.GetFeaturedImageRequest{
		EntityID:   entityId,
		EntityType: entityType,
		UserId:     uint64(userID),
	}

	response, err := creatives.GetFeaturedImage(ctx, &getFeaturedImageReq)
	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		httpErrCode, _ := getHttpStatusCodeAndMessageFromError(err)
		ctx.AbortWithStatusJSON(httpErrCode, response)
		return
	}
	ctx.JSON(http.StatusOK, response)
}
