package ads

import (
	"github.com/Zomato/zomato-api-gateway/internal/ads/platform"
	"net/http"
	"strconv"

	adspb "github.com/Zomato/ads-service-client-golang/ads"
	"github.com/Zomato/ads-service-client-golang/manager"
	zconfig "github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/gin-gonic/gin"
)

func GetManagerDetails(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := adspb.NewAdsClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	entityType := c.Param("entity_type")
	if entityType == "" {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "Entity type is empty"))
		return
	}

	entityID, err := strconv.ParseUint(c.Param("entity_id"), 10, 64)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, NewError("400", "invalid entity_id"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	response, err := client.GetManagerDetails(outgoingCtx, &manager.GetManagerDetailsRequest{
		EntityType: entityType,
		EntityID:   entityID,
		EntityIds:  []uint64{entityID},
	})
	if err != nil {
		log.WithError(err).Error("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetDiningManagerDetails(c *gin.Context) {
	resp, err := platform.GetManagerDetails(c)
	if err != nil {
		logger.FromContext(c).Errorf("GetDiningManagerDetails - failed %+v", err)
		statusCode, message := getHttpStatusCodeAndMessageFromError(err)
		c.AbortWithStatusJSON(statusCode, message)
		return
	}
	c.JSON(http.StatusOK, resp)

}
