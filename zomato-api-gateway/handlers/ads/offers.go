package ads

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	v2 "github.com/Zomato/ads-service-client-golang/v2"
	"github.com/Zomato/ads-service-client-golang/v2/offers"
	zconfig "github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/gin-gonic/gin"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func GetOffers(c *gin.Context) {
	span := make(map[string]interface{})
	span["service"] = "ads"
	span["method"] = "GetOffers"
	log := logger.FromContext(c).WithFields(span)
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := offers.NewOfferServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	budget, err := strconv.ParseInt(c.Query("budget"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse budget")
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	Budget := uint64(budget)
	cpx, err := strconv.ParseFloat(c.Query("cpx"), 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse cpx")
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	source, err := strconv.ParseInt(c.Query("source"), 10, 32)
	if err != nil {
		log.WithError(err).Warn("Failed to parse source")
		source = 0
	}
	Source := v2.Sources(source)

	keysParams := c.Query("keys")
	keys := strings.Split(keysParams, ",")

	var startDate, endDate *timestamppb.Timestamp
	startDateEpoch, err := strconv.ParseInt(c.Query("start_date"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse start date epoch")
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	endDateEpoch, err := strconv.ParseInt(c.Query("end_date"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse end_date epoch")
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	if startDateEpoch > 0 {
		startDateUnix := time.Unix(startDateEpoch, 0)
		startDate = timestamppb.New(startDateUnix)
	}

	if endDateEpoch > 0 {
		endDateUnix := time.Unix(startDateEpoch, 0)
		endDate = timestamppb.New(endDateUnix)
	}

	level := c.Query("level")
	productId, err := strconv.ParseInt(c.Query("product_id"), 10, 64)
	if err != nil {
		log.WithError(err).Warn("Failed to parse  product id")
	}
	productID := uint32(productId)

	req := offers.GetOffersRequest{
		Budget:    Budget,
		Cpx:       cpx,
		ProductId: productID,
		Source:    Source,
		Key:       keys,
		StartDate: startDate,
		EndDate:   endDate,
		Level:     level,
	}

	response, err := client.GetOffers(outgoingCtx, &req)
	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func ApplyOffer(c *gin.Context) {
	span := make(map[string]interface{})
	span["service"] = "ads"
	span["method"] = "GetOffers"
	log := logger.FromContext(c).WithFields(span)
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := offers.NewOfferServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	if userID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, NewError("403", "unauthorised"))
		return
	}

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)
	var applyOfferRequest offers.ApplyOfferRequest
	err := c.ShouldBindJSON(&applyOfferRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.ApplyOffer(outgoingCtx, &applyOfferRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}
