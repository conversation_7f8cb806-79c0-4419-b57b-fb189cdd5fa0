package ads

import (
	"net/http"

	"github.com/Zomato/ads-service-client-golang/v2/diy"
	zconfig "github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/gin-gonic/gin"
)

func CreateDiyCreative(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := diy.NewDiyCreativeServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var createDiyCreativeRequest diy.CreateDiyCreativeRequest
	err := c.ShouldBindJSON(&createDiyCreativeRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.CreateDiyCreative(outgoingCtx, &createDiyCreativeRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetDiyCreative(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := diy.NewDiyCreativeServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()

	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var getDiyCreativeRequest diy.GetDiyCreativeRequest
	err := c.ShouldBindQuery(&getDiyCreativeRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.GetDiyCreative(outgoingCtx, &getDiyCreativeRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}

func UpdateDiyCreative(c *gin.Context) {
	log := logger.FromContext(c).WithField("service", "ads")
	killSwitch := zconfig.GetBool(c, "ads.dashboard_rpc_kill_switch")
	if killSwitch {
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return
	}
	conn := env.FromContext(c).AdsServiceConn()
	client := diy.NewDiyCreativeServiceClient(conn)
	zomatoClient := api.GetClientFromContext(c)

	userID := zomatoClient.UserID()
	outgoingCtx := NewMetaDataWithAuthorisationHeaders(c, userID)

	var updateDiyCreativeRequest diy.UpdateDiyCreativeRequest
	err := c.ShouldBindJSON(&updateDiyCreativeRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to bind request to JSON: %s",
			err.Error())
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	response, err := client.UpdateDiyCreative(outgoingCtx, &updateDiyCreativeRequest)
	if err != nil {
		log.WithError(err).Errorf("Failed to get response from ads-service")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response)
		return
	}
	c.JSON(http.StatusOK, response)
}
