package aerobar

import (
	"context"
	"errors"
	"net/http"
	"os"
	"testing"

	aerobarProto "github.com/Zomato/aerobar-service-client-golang/aerobar"
	"github.com/Zomato/go/config"
	"github.com/Zomato/go/i18n"
	log "github.com/Zomato/go/logger"
	aerobarService "github.com/Zomato/zomato-api-gateway/internal/aerobar"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/internal/version"
	aerobarmodel "github.com/Zomato/zomato-api-gateway/models/aerobar"
	"github.com/Zomato/zomato-api-gateway/models/sushi"
	"github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
)

func NewContextForTest() (context.Context, config.TestProvider) {
	ctx, testProvider, err := config.NewTestProvider()
	if err != nil {
		panic(err)
	}

	return ctx, testProvider
}

func initialize() {
	ctx, _ := NewContextForTest()
	_ = os.Setenv("CONFIG_SOURCE", "local:default,global-config|sre,global-config|dataplatform")
	wd, _ := os.Getwd()
	_ = os.Chdir("../../")
	config.Init()

	_ = os.Chdir(wd)
	// configure logging
	err := log.Initialize(
		log.Formatter(config.GetString(ctx, "log.format")),
		log.Level(config.GetString(ctx, "log.level")),
	)
	if err != nil {
		log.WithError(err).Panic("unable to initialise logger")
	}

	// initialise localization
	err = i18n.Initialize(i18n.WithRootPath("../../localization"))
	if err != nil {
		log.WithError(err).Error("unable to initialise localization")
	}

	gin.SetMode(gin.TestMode)
}

func TestGetAerobarsFromService(t *testing.T) {
	initialize()
	ctx, r := gin.CreateTestContext(nil)
	// Get the test context and provider to set config values
	configCtx, testProvider := NewContextForTest()
	// Set the config value to return true for reviews_revamp_enabled
	testProvider.MustSet("aerobar_service.get_aerobar_details.reviews_revamp_enabled", true)

	r.ContextWithFallback = true
	ctx.Request = (&http.Request{}).WithContext(configCtx)
	// Set up the environment in the context
	env := env.NewEnv()
	ctx.Set("env", env)
	// Set up a mock Zomato client in the context to avoid panic in GetClientFromContext

	// Create a mock client
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	newVer, _ := version.Make("827") // Version above the required 826 threshold
	betaVer, _ := version.Make("1.0.0-beta")
	mockClient := api.NewClient(
		api.ClientTypeApp,
		"zomato_android_v2",
		"test-uuid",
		"guest-user-id",
		newVer,
		betaVer,
		123) // UserID

	ctx.Set(api.ZomatoClientCtx, mockClient)

	type args struct {
		req *AerobarModel
	}

	type want struct {
		data []*aerobarmodel.AerobarData
		err  string
	}

	truePtr := func(b bool) *bool { return &b }
	falsePtr := func(b bool) *bool { return &b }
	stringPtr := func(s string) *string { return &s }

	tests := []struct {
		name                  string
		args                  args
		mockedAerobarResponse *aerobarProto.GetAerobarDetailsResponse
		mockAerobarError      error
		want                  want
	}{
		{
			name: "ServiceError",
			args: args{
				req: &AerobarModel{
					UserID:            123,
					IsUserZoman:       true,
					IsAndroidConsumer: true,
					AppVersion:        "1.0.0",
					AppBetaVersion:    "1.0.0-beta",
					AerobarService:    GetMockAerobarServiceImpl(t),
				},
			},
			mockAerobarError: errors.New("service error"),
			want: want{
				data: nil,
				err:  "service error",
			},
		},
		{
			name: "NilAerobarService",
			args: args{
				req: &AerobarModel{
					UserID:            123,
					IsUserZoman:       true,
					IsAndroidConsumer: true,
					AppVersion:        "1.0.0",
					AppBetaVersion:    "1.0.0-beta",
					AerobarService:    nil,
				},
			},
			want: want{
				data: nil,
				err:  "aerobar service is nil",
			},
		},
		{
			name: "EmptyServiceResponse",
			args: args{
				req: &AerobarModel{
					UserID:            123,
					IsUserZoman:       true,
					IsAndroidConsumer: true,
					AppVersion:        "1.0.0",
					AppBetaVersion:    "1.0.0-beta",
					AerobarService:    GetMockAerobarServiceImpl(t),
				},
			},
			mockedAerobarResponse: &aerobarProto.GetAerobarDetailsResponse{
				Status: true,
				Data:   []*aerobarProto.AerobarDetails{},
			},
			want: want{
				data: []*aerobarmodel.AerobarData{
					{
						AerobarType: "transactional",
						MaxToShow:   0,
						Items:       []*aerobarmodel.Item{},
					},
					{
						AerobarType: "content",
						MaxToShow:   10,
						Items:       []*aerobarmodel.Item{},
					},
					{
						AerobarType: "promotional",
						MaxToShow:   5,
						Items:       []*aerobarmodel.Item{},
					},
				},
				err: "",
			},
		},
		{
			name: "TemplateTypeFiltering",
			args: args{
				req: &AerobarModel{
					UserID:            123,
					IsUserZoman:       true,
					IsAndroidConsumer: true,
					AppVersion:        "1.0.0",
					AppBetaVersion:    "1.0.0-beta",
					AerobarService:    GetMockAerobarServiceImpl(t),
				},
			},
			mockedAerobarResponse: &aerobarProto.GetAerobarDetailsResponse{
				Status: true,
				Data: []*aerobarProto.AerobarDetails{
					{
						AerobarId:      "2357060_6_4",
						Deeplink:       "zomato://t/2357060",
						Title:          "Subway",
						FirstDeeplink:  "zomato://t/2357060",
						LottieUrl:      "https://b.zmtcdn.com/data/o2_assets/cooking2.json",
						SecondDeeplink: "zomato://t/2357060",
						TapDeeplink:    "zomato://t/2357060",
						Type:           1,
						Cta2:           &aerobarProto.Cta{PersistOnTap: true},
						Cta1:           &aerobarProto.Cta{PersistOnTap: true},
						Body:           &aerobarProto.Cta{PersistOnTap: true},
						AerobarType:    aerobarProto.AerobarType_AEROBAR_TYPE_TRANSACTIONAL,
						TemplateType:   "unsupported_template", // This template won't be included in the result
						SubTitle: &aerobarProto.SubTitle{
							Text: &aerobarProto.TextLabel{
								Key: "zm-online-ordering-aerobar-order-being-prepared-subtitle-text",
							},
						},
					},
				},
			},
			want: want{
				data: []*aerobarmodel.AerobarData{
					{
						AerobarType: "transactional",
						MaxToShow:   0,
						Items:       []*aerobarmodel.Item{},
					},
					{
						AerobarType: "content",
						MaxToShow:   10,
						Items:       []*aerobarmodel.Item{},
					},
					{
						AerobarType: "promotional",
						MaxToShow:   5,
						Items:       []*aerobarmodel.Item{},
					},
				},
				err: "",
			},
		},
		{
			name: "DarkModeEnabled",
			args: args{
				req: &AerobarModel{
					UserID:            123,
					IsUserZoman:       true,
					IsAndroidConsumer: true,
					AppVersion:        "1.0.0",
					AppBetaVersion:    "1.0.0-beta",
					IsDarkModeEnabled: true,
					AerobarService:    GetMockAerobarServiceImpl(t),
				},
			},
			mockedAerobarResponse: &aerobarProto.GetAerobarDetailsResponse{
				Status: true,
				Data: []*aerobarProto.AerobarDetails{
					{
						AerobarId:       "2357060_6_4",
						Deeplink:        "zomato://t/2357060",
						Title:           "Subway",
						FirstDeeplink:   "zomato://t/2357060",
						FirstActionText: "Order",
						LottieUrl:       "https://b.zmtcdn.com/data/o2_assets/cooking2.json",
						SecondDeeplink:  "zomato://t/2357060",
						TapDeeplink:     "zomato://t/2357060",
						Type:            1,
						Cta2:            &aerobarProto.Cta{PersistOnTap: true},
						Cta1:            &aerobarProto.Cta{PersistOnTap: true},
						Body:            &aerobarProto.Cta{PersistOnTap: true},
						AerobarType:     aerobarProto.AerobarType_AEROBAR_TYPE_TRANSACTIONAL,
						TemplateType:    "online_ordering",
						SubTitle: &aerobarProto.SubTitle{
							Text: &aerobarProto.TextLabel{
								Key: "zm-online-ordering-aerobar-order-being-prepared-subtitle-text",
							}, SuffixIcon: &aerobarProto.SuffixIcon{
								Code: "e875", Color: &aerobarProto.Color{
									Tint: "500",
									Type: "red",
								}, Size: 12,
							},
						}, LottieV2: &aerobarProto.LottieV2{
							DarkModeUrl: "https://b.zmtcdn.com/data/file_assets/dark_mode_lottie.json",
						},
						IconV2: &aerobarProto.IconV2{
							DarkModeImageUrl: "https://b.zmtcdn.com/data/file_assets/dark_mode_icon.png",
						},
					},
				},
			},
			want: want{
				data: []*aerobarmodel.AerobarData{
					{
						Items: []*aerobarmodel.Item{
							{
								AerobarID: "2357060_6_4",
								Subtitle: &sushi.Subtitle{
									Text: "Preparing your order", SuffixIcon: &sushi.Icon{
										Code: "e875",
										Color: &sushi.Color{
											Tint: "500",
											Type: "red",
											ThemeBucket: &sushi.ThemeBucket{
												Token: sushi.TokenColorIconBrandOnlyLight,
											},
										},
										Size: 12,
									},
								},
								Title:           "Subway",
								LottieURL:       "https://b.zmtcdn.com/data/o2_assets/cooking2.json",
								Deeplink:        "zomato://t/2357060",
								FirstDeeplink:   "zomato://t/2357060",
								FirstActionText: stringPtr("Order"),
								SecondDeeplink:  "zomato://t/2357060",
								LeftAnimation: &sushi.Animation{
									URL:         "https://b.zmtcdn.com/data/o2_assets/cooking2.json",
									AspectRatio: 1, ThemeConfig: []*sushi.ThemeConfig{
										{
											Theme:         sushi.ThemeDark,
											ComponentType: sushi.ComponentTypeAnimation,
											Animation: &sushi.Animation{
												URL:         "https://b.zmtcdn.com/data/file_assets/dark_mode_lottie.json",
												AspectRatio: 1},
										},
									},
								},
								LeftImage: &sushi.Image{
									URL:         "",
									AspectRatio: 1,
									ThemeConfig: []*sushi.ThemeConfig{
										{
											Theme:         sushi.ThemeDark,
											ComponentType: sushi.ComponentTypeImage,
											Image: &sushi.Image{
												URL:         "https://b.zmtcdn.com/data/file_assets/dark_mode_icon.png",
												AspectRatio: 1,
											},
										},
									},
								},
								TapDeeplink: "zomato://t/2357060",
								Type:        1,
								TrackingData: []*sushi.TrackingData{
									{
										TableName: "jevent",
										Payload:   "{\"var1\":\"2357060_6_4\",\"var2\":\"Subway\",\"var5\":\"online_ordering\"}", EventNames: &sushi.EventNames{
											Impression: "{\"ename\":\"AerobarImpression\"}",
										},
									}, {
										TableName: "jevent",
										Payload:   "{\"var1\":\"2357060_6_4\",\"var2\":\"Subway\",\"var5\":\"online_ordering\"}",
										EventNames: &sushi.EventNames{
											Tap: "{\"ename\":\"AerobarTapped\"}",
										},
									},
								}, Cta1: &aerobarmodel.Cta{
									PersistOnTap:   truePtr(true),
									IgnoreCooldown: falsePtr(false), IgnoreMaxCount: falsePtr(false)},
								Cta2: &aerobarmodel.Cta{
									PersistOnTap:   truePtr(true),
									IgnoreCooldown: falsePtr(false),
									IgnoreMaxCount: falsePtr(false),
								},
								Body: &aerobarmodel.Cta{
									PersistOnTap:   truePtr(true),
									IgnoreCooldown: falsePtr(false),
									IgnoreMaxCount: falsePtr(false),
								},
								CooldownPeriod: &aerobarmodel.CooldownPeriod{
									Tap:        0,
									Impression: 0,
								},
								MaxShowCount: &aerobarmodel.MaxShowCount{
									Tap:        0,
									Impression: 0,
								},
								AerobarType: "transactional",
							},
						}, AerobarType: "transactional",
						MaxToShow: 1,
					},
					{
						AerobarType: "content",
						MaxToShow:   1,
						Items:       []*aerobarmodel.Item{},
					},
					{
						AerobarType: "promotional",
						MaxToShow:   0, Items: []*aerobarmodel.Item{},
					},
				}, err: "",
			},
		},
		{
			name: "ContentTypeAerobar",
			args: args{
				req: &AerobarModel{
					UserID:            123,
					IsUserZoman:       true,
					IsAndroidConsumer: true,
					AppVersion:        "1.0.0",
					AppBetaVersion:    "1.0.0-beta",
					AerobarService:    GetMockAerobarServiceImpl(t),
				},
			},
			mockedAerobarResponse: &aerobarProto.GetAerobarDetailsResponse{
				Status: true,
				Data: []*aerobarProto.AerobarDetails{
					{
						AerobarId:      "content_123",
						Deeplink:       "zomato://content/123",
						Title:          "Latest Article",
						FirstDeeplink:  "zomato://content/123",
						LottieUrl:      "https://b.zmtcdn.com/data/content_lottie.json",
						TapDeeplink:    "zomato://content/123",
						Type:           2,
						Cta1:           &aerobarProto.Cta{PersistOnTap: false, IgnoreCooldown: true},
						Cta2:           &aerobarProto.Cta{PersistOnTap: false, IgnoreMaxCount: true},
						Body:           &aerobarProto.Cta{PersistOnTap: false},
						AerobarType:    aerobarProto.AerobarType_AEROBAR_TYPE_CONTENT,
						TemplateType:   "dining",
						MaxShowCount:   &aerobarProto.TapAndImpression{Tap: 5, Impression: 10},
						CooldownPeriod: &aerobarProto.TapAndImpression{Tap: 3600, Impression: 120},
					},
				},
			},
			want: want{
				data: []*aerobarmodel.AerobarData{
					{
						AerobarType: "transactional",
						MaxToShow:   0,
						Items:       []*aerobarmodel.Item{},
					},
					{
						AerobarType: "content",
						MaxToShow:   10,
						Items: []*aerobarmodel.Item{
							{
								AerobarID:       "content_123",
								Title:           "Latest Article",
								LottieURL:       "https://b.zmtcdn.com/data/content_lottie.json",
								Deeplink:        "zomato://content/123",
								FirstDeeplink:   "zomato://content/123",
								FirstActionText: stringPtr(""),
								LeftAnimation: &sushi.Animation{
									URL:         "https://b.zmtcdn.com/data/content_lottie.json",
									AspectRatio: 1},
								TapDeeplink: "zomato://content/123",
								Type:        2,
								TrackingData: []*sushi.TrackingData{
									{
										TableName: "jevent",
										Payload:   "{\"var1\":\"content_123\",\"var2\":\"Latest Article\",\"var5\":\"dining\"}",
										EventNames: &sushi.EventNames{
											Impression: "{\"ename\":\"AerobarImpression\"}",
										},
									}, {
										TableName: "jevent",
										Payload:   "{\"var1\":\"content_123\",\"var2\":\"Latest Article\",\"var5\":\"dining\"}",
										EventNames: &sushi.EventNames{
											Tap: "{\"ename\":\"AerobarTapped\"}",
										},
									},
								},
								Cta1: &aerobarmodel.Cta{
									PersistOnTap:   falsePtr(false),
									IgnoreCooldown: truePtr(true),
									IgnoreMaxCount: falsePtr(false),
								},
								Cta2: &aerobarmodel.Cta{
									PersistOnTap:   falsePtr(false),
									IgnoreCooldown: falsePtr(false),
									IgnoreMaxCount: truePtr(true),
								},
								Body: &aerobarmodel.Cta{
									PersistOnTap:   falsePtr(false),
									IgnoreCooldown: falsePtr(false),
									IgnoreMaxCount: falsePtr(false),
								},
								CooldownPeriod: &aerobarmodel.CooldownPeriod{
									Tap:        3600,
									Impression: 120,
								},
								MaxShowCount: &aerobarmodel.MaxShowCount{
									Tap:        5,
									Impression: 10,
								}, AerobarType: "content",
								Subtitle: &sushi.Subtitle{}},
						}},
					{
						AerobarType: "promotional",
						MaxToShow:   5,
						Items:       []*aerobarmodel.Item{},
					},
				},
				err: "",
			},
		},
		{
			name: "SuccessfulResponse",
			args: args{
				req: &AerobarModel{
					UserID:            123,
					IsUserZoman:       true,
					IsAndroidConsumer: true,
					AppVersion:        "1.0.0",
					AppBetaVersion:    "1.0.0-beta",
					AerobarService:    GetMockAerobarServiceImpl(t),
				},
			},
			mockedAerobarResponse: &aerobarProto.GetAerobarDetailsResponse{
				Status: true,
				Data: []*aerobarProto.AerobarDetails{
					{
						AerobarId:      "2357060_6_4",
						Deeplink:       "zomato://t/2357060",
						Title:          "Subway",
						FirstDeeplink:  "zomato://t/2357060",
						LottieUrl:      "https://b.zmtcdn.com/data/o2_assets/cooking2.json",
						SecondDeeplink: "zomato://t/2357060",
						TapDeeplink:    "zomato://t/2357060",
						Type:           1,
						Cta2:           &aerobarProto.Cta{PersistOnTap: true},
						Cta1:           &aerobarProto.Cta{PersistOnTap: true},
						Body:           &aerobarProto.Cta{PersistOnTap: true},
						AerobarType:    aerobarProto.AerobarType_AEROBAR_TYPE_TRANSACTIONAL,
						TemplateType:   "online_ordering",
						SubTitle: &aerobarProto.SubTitle{
							Text: &aerobarProto.TextLabel{
								Key: "zm-online-ordering-aerobar-order-being-prepared-subtitle-text"},
							SuffixIcon: &aerobarProto.SuffixIcon{
								Code: "e875",
								Color: &aerobarProto.Color{Tint: "500",
									Type: "red",
								}, Size: 12,
							},
						}, LottieV2: &aerobarProto.LottieV2{
							DarkModeUrl: "https://b.zmtcdn.com/data/file_assets/ec6a98cae9e973de4c490ba3865290a31724238472.json"},
					},
				},
			},
			want: want{
				data: []*aerobarmodel.AerobarData{
					{
						Items: []*aerobarmodel.Item{
							{
								AerobarID: "2357060_6_4",
								Subtitle: &sushi.Subtitle{
									Text: "Preparing your order",
									SuffixIcon: &sushi.Icon{
										Code: "e875",
										Color: &sushi.Color{
											Tint: "500",
											Type: "red",
										},
										Size: 12,
									},
								},
								Title:           "Subway",
								LottieURL:       "https://b.zmtcdn.com/data/o2_assets/cooking2.json",
								Deeplink:        "zomato://t/2357060",
								FirstDeeplink:   "zomato://t/2357060",
								FirstActionText: new(string),
								SecondDeeplink:  "zomato://t/2357060", LeftAnimation: &sushi.Animation{
									URL:         "https://b.zmtcdn.com/data/o2_assets/cooking2.json",
									AspectRatio: 1,
								},
								TapDeeplink: "zomato://t/2357060",
								Type:        1,
								TrackingData: []*sushi.TrackingData{
									{
										TableName: "jevent",
										Payload:   "{\"var1\":\"2357060_6_4\",\"var2\":\"Subway\",\"var5\":\"online_ordering\"}",
										EventNames: &sushi.EventNames{
											Impression: "{\"ename\":\"AerobarImpression\"}",
										},
									}, {
										TableName: "jevent",
										Payload:   "{\"var1\":\"2357060_6_4\",\"var2\":\"Subway\",\"var5\":\"online_ordering\"}",
										EventNames: &sushi.EventNames{
											Tap: "{\"ename\":\"AerobarTapped\"}",
										},
									},
								},
								Cta1: &aerobarmodel.Cta{
									PersistOnTap:   truePtr(true),
									IgnoreCooldown: falsePtr(false),
									IgnoreMaxCount: falsePtr(false),
								},
								Cta2: &aerobarmodel.Cta{
									PersistOnTap:   truePtr(true),
									IgnoreCooldown: falsePtr(false),
									IgnoreMaxCount: falsePtr(false),
								},
								Body: &aerobarmodel.Cta{
									PersistOnTap:   truePtr(true),
									IgnoreCooldown: falsePtr(false),
									IgnoreMaxCount: falsePtr(false),
								},
								CooldownPeriod: &aerobarmodel.CooldownPeriod{
									Tap:        0,
									Impression: 0,
								},
								MaxShowCount: &aerobarmodel.MaxShowCount{
									Tap:        0,
									Impression: 0,
								},
								AerobarType: "transactional",
							},
						}, 
						AerobarType: "transactional",
						MaxToShow: 1,
					}, 
					{
						AerobarType: "content",
						MaxToShow:   1,
						Items:       []*aerobarmodel.Item{},
					},
					{
						AerobarType: "promotional",
						MaxToShow:   0,
						Items:       []*aerobarmodel.Item{},
					},
				},
				err: "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCtrl := gomock.NewController(t)
			defer mockCtrl.Finish()

			// Only set up mocks for non-nil requests and tests that need service calls
			if tt.args.req != nil {
				// Don't set mock for NilAerobarService test
				if tt.name != "NilAerobarService" {
					mockAerobarServiceImpl := aerobarService.NewMockAerobarService(mockCtrl)
					tt.args.req.AerobarService = mockAerobarServiceImpl

					// Only set up expectations for tests that need service calls
					if tt.mockedAerobarResponse != nil || tt.mockAerobarError != nil {
						mockAerobarServiceImpl.EXPECT().GetAerobarDetails(ctx, gomock.Any()).
							Return(tt.mockedAerobarResponse, tt.mockAerobarError)
					}
				}

				var data []*aerobarmodel.AerobarData
				respFromService, err := tt.args.req.getAerobarsFromService(ctx)
				if err == nil {
					data, _ = tt.args.req.prepareAerobarDetails(ctx, respFromService)
				}

				if tt.want.err != "" {
					assert.EqualError(t, err, tt.want.err)
				} else {
					assert.NoError(t, err)
				}
				assert.Equal(t, tt.want.data, data)
			}
		})
	}
}

func GetMockAerobarServiceImpl(t *testing.T) *aerobarService.MockAerobarService {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	mockAerobarServiceImpl := aerobarService.NewMockAerobarService(mockCtrl)
	return mockAerobarServiceImpl
}
