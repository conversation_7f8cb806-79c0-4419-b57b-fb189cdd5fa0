package aerobar

type AerobarState string

type Group int

const (
	groupStateUnspecified Group = 0
	groupStateCreated     Group = 1
	groupStateActive      Group = 2
	groupStateLocked      Group = 3
	groupStateArchived    Group = 4
	groupStateDeleted     Group = 5
	groupStateOrderPlaced Group = 6
)

const (
	// MQTT constants
	channelAerobar            = "aerobar:%s"
	channelAerobarReplace     = "aerobar_replace:%s"
	channelHashSalt           = "zoma:pubsub:v1:%s"
	providerMqtt              = "mqtt"
	channelTypeAerobar        = "aerobar"
	subscriberChannelTimeDiff = 10 // 10 seconds

	getAerobarsFromWebTimeout   = "aerobar_service.get_aerobars_from_web.timeout"
	isGetAerobarsFromWebEnabled = "aerobar_service.get_aerobars_from_web.enabled"
	aerobarCallSource           = "x-aerobar-call-source"

	// Aerobar request constants
	hasUserRatedCurrentVersionKey   = "has_user_rated_current_version"
	appRatingImpressionTimestampKey = "app_rating_impression_timestamp"
	cityIDKey                       = "city_id"
	latKey                          = "lat"
	lonKey                          = "lon"
	isGroupOrderKey                 = "get_group_orders"
	appThemeHeader                  = "HTTP_X_APP_THEME"

	// Aerobar constants
	aerobarTypeTransactional      = "transactional"
	aerobarTypeContent            = "content"
	aerobarTypePromotional        = "promotional"
	sourceAerobar                 = "aerobar"
	snippetTypeAerobarType2       = "aerobar_type_2"
	bgToFgThreshold               = 10
	aerobarSubtypePremiumReferral = "premium_referral"

	tapCooldownPeriodContent        = 172800
	impressionCooldownPeriodContent = 60
	tapMaxShowCountContent          = 2
	impressionMaxShowCountContent   = 48

	tapCooldownPeriodPromotional              = 172800
	impressionCooldownPeriodPromotional       = 60
	tapMaxShowCountPromotional                = 2
	impressionMaxShowCountPromotional         = 48
	templateTypeBlinkitO2                     = "blinkit_online_ordering"
	proPackageDiningEventsLogger              = "pro_package_dining_aerobar_events_logger"
	aerobarEventsTopic                        = "zomato.aerobar-service.aerobar-events"
	templateTypeTR                            = "table_reservation"
	templateTypeOrderSharing                  = "online_order_sharing"
	templateTypeO2                            = "online_ordering"
	templateTypeDining                        = "dining"
	templateTypeDiningProPackage              = "dining_pro_package"
	templateTypeCalling                       = "calling"
	templateTypeOrderPlanner                  = "order_planner"
	ArrivingSoonThreshold               int64 = 3

	// aerobar states
	AerobarStateInvalid             AerobarState = ""
	AerobarStateOnTime              AerobarState = "state_1"
	AerobarStateDelayed             AerobarState = "state_2"
	noOfAerobarRightContainerStates              = 2

	maxRatingLottie        = "https://b.zmtcdn.com/data/o2_assets/26307dcc74173f41e1ab7b98a39170f91712126480.lottie"
	rightAnimationDuration = 10000

	// Aerobar click action contacts
	saveRatingAPIUrl          = "gw/feedback/save-rating"
	addDetailedFeedbackAPIUrl = "gw/feedback/add-detailed-feedback"

	RightContainerArrivingSoonTitle        = "arriving"
	RightContainerArrivingSoonSubtitle     = "now"
	RightContainerArrivingInTitle          = "arriving in"
	RightContainerArrivingInSubtitle       = "%d mins"
	RightContainerArrivingByTitle          = "arriving by"
	RightContainerArrivingOnTitle          = "arriving on"
	RightContainerArrivingTomorrowSubtitle = "Tomorrow"

	// tracking constants
	tableNameJevent = "jevent"

	// Aerobar tracking event constants
	trackingEventImpression = "impression"
	trackingEventTap        = "tap"

	MaxToShowContent     = 10
	MaxToShowPromotional = 5

	themeDark                               = "dark"
	componentTypeAnimation                  = "animation"
	introductionAnimationUrl                = "https://b.zmtcdn.com/data/o2_assets/6b721e4928475ef63e4e91271e37f33d1735810611.lottie"
	introductionAnimationVegUrl             = "https://b.zmtcdn.com/data/o2_assets/9190775e97838fa3538eac1955792b1e1735834692.lottie"
	introductionDarkAnimationUrl            = "https://b.zmtcdn.com/data/o2_assets/378415a20c94e4bc288155bf9e2156b61735810686.lottie"
	introductionDarkAnimationVegUrl         = "https://b.zmtcdn.com/data/o2_assets/d998caea0783be0b682df01139d6607b1735834714.lottie"
	introductionAnimationFallbackUrl        = "https://b.zmtcdn.com/data/o2_assets/cbbb8a1a65e2cdd3e6131781805a11b41735878913.png?output-format=webp"
	introductionAnimationFallbackVegUrl     = "https://b.zmtcdn.com/data/o2_assets/aa958c2fe676d06761a8b0962a78d1e81735878944.png?output-format=webp"
	introductionAnimationFallbackDarkUrl    = "https://b.zmtcdn.com/data/o2_assets/b4aae28fd345f7bd584bde38bf9802041735878968.png?output-format=webp"
	introductionAnimationFallbackDarkVegUrl = "https://b.zmtcdn.com/data/o2_assets/bed8256439854d63c65e951e4ab2fdd91735878983.png?output-format=webp"
	introductionBgImageUrl                  = "https://b.zmtcdn.com/data/o2_assets/2ff7f68b12c845f6b17f57048b78992f1732099881.png?output-format=webp"
	mroCheckoutConfigStateEligible          = "eligible"
	mroCheckoutConfigStateNotEligible       = "not_eligible"
	eventTypeMultiCartIntroduction          = "multi_cart_introduction"
	eventTypeMultiCartCreationButtonTooltip = "multi_cart_creation_button_tooltip"
	genericTooltipDataId                    = "aerobar_disabled_checkout_tooltip"

	numberOfPromotionalAerobarsToBeGenerated = 3
	recentOrderTimeFrame                     = 4 * 60 * 60 // 4 hours
	businessIdentifierPro                    = "PRO"
	businessIdentifierPackages               = "PRO_PACKAGES"
	appRatingAerobar                         = "APP_RATING"
	financeSurveyAerobar                     = "FINANCE_SURVEY"
	gourmetAerobar                           = "GOURMET"
	audioInstructionAerobar                  = "AUDIO_INSTRUCTION_AEROBAR"
	trAerobars                               = "TR_AEROBARS"
	zomaland                                 = "ZOMALAND"
	trRatingsBooking                         = "TR_RATINGS_BOOKING"
	trSeatedBookings                         = "TR_SEATED_BOOKINGS"
	trBookings                               = "TR_BOOKINGS"
	diningAerobarForActiveTable              = "DINING_AEROBAR_FOR_ACTIVE_TABLE"
	proSuperpowerAerobar                     = "PRO_SUPERPOWER_AEROBAR"
	mobileLoginAerobar                       = "MOBILE_LOGIN"
	npsAerobar                               = "NPS_AEROBAR"
	redAerobars                              = "RED_AEROBARS"
	redRenewalAerobar                        = "RED_RENEWAL_AEROBAR"
	redGenericAerobar                        = "RED_GENERIC_AEROBAR"
	redVisitRatingAerobar                    = "RED_VISIT_RATING_AEROBAR"
	redReferralAerobar                       = "RED_REFERRAL_AEROBAR"
	nutritionAerobars                        = "NUTRITION_AEROBARS"
	editionAerobar                           = "EDITION_AEROBAR"
	cutleryAerobar                           = "CUTLERY_AEROBAR"
	diningOrderRatingAerobars                = "DINING_ORDER_RATING_AEROBARS"
	pasCardTokeniztionAerobars               = "PAS_CARD_TOKENIZTION_AEROBARS"
	whatsappOptinAerobar                     = "WHATSAPP_OPTIN_AEROBAR"
	customerRiderChatMessageAerobars         = "CUSTOMER_RIDER_CHAT_MESSAGE_AEROBARS"
	o2SavedCartAerobar                       = "o2_saved_cart"
	zplAerobar                               = "ZPL_AEROBAR"
	blinkitOrderAerobar                      = "BLINKIT_ORDER_AEROBAR"
	orderSharingAerobar                      = "ORDER_SHARING_AEROBAR"
	appUpdateAerobar                         = "APP_UPDATE_AEROBAR"
	talabatAerobar                           = "TALABAT_AEROBAR"
	loginMethodUpdateAerobar                 = "LOGIN_METHOD_UPDATE_AEROBAR"
	birthdayUpdateAerobar                    = "BIRTHDAY_UPDATE_AEROBAR"
	tapDeeplink                              = "gw/feedback/add-detailed-feedback"
	ratingChangeDeeplink                     = "gw/feedback/save-rating"
	reviewsTableName                         = "review_tracking_events"
	shareMoreFeedbackEname                   = "O2_PRIVATE_RATING_ADD_MORE_FEEDBACK_BUTTON_TAP"
	ratingTapEname                           = "O2_PRIVATE_RATING_AEROBAR_RATING_TAP"
	recommendationYesTapped                  = "RecommendationAerobarYesTapped"
	recommendationNoTapped                   = "RecommendationAerobarNoTapped"
	persistenceType0                         = 0
	persistenceType1                         = 1
	persistenceType2                         = 2
	loggerChannel                            = "aerobar_improvements_logs"
	orderAerobarLoggerChannel                = "order_aerobar_logs"
	aerobarMissingKeyLoggerChannel           = "aerobar_missing_key_logs"
	outputStatusSuccess                      = "success"
	outputMessageSuccessful                  = "successful"
	aerobarOrderCountDefaultLimit            = 10
	aerobarPastOrdersThresholdTime           = 7 // 7 days
	targetGroup                              = "TG"
	cartTypeAerobar                          = "aerobar"
	cartConfigModeFullPage                   = "full_page"
	businessIdentifierO2                     = "online_ordering"
	template1                                = "template_1"
	template2                                = "template_2"
	template3                                = "template_3"
	template4                                = "template_4"
	redirectToMenu                           = "menu"
	ctaRedirectToCart                        = "cart"
	ctaRedirectToGroupOrder                  = "group_order"
	redirectToCart                           = "cart"
	shakeTypeAnimation1                      = "shake_type_1"
	psSourceName                             = "api-gateway-aerobar-api"
	o2Delivery                               = "o2_delivery"
	sessionMaxImpressionCount                = 1
	initialUIDataOpenDelayMilliSec           = 1000
	uiDataOpenDurationMilliSec               = 500
)

var (
	promotionalAerobarSortedList = []string{
		editionAerobar,
		financeSurveyAerobar,
		gourmetAerobar,
		proSuperpowerAerobar,
		mobileLoginAerobar,
		redAerobars,
		cutleryAerobar,
		whatsappOptinAerobar,
		zplAerobar,
		zomaland,
		appUpdateAerobar,
		talabatAerobar,
		birthdayUpdateAerobar,
	}
	localAerobarList = []string{
		o2SavedCartAerobar,
	}
	ineligibleGroupStatesForMqttData = []Group{
		groupStateDeleted,
		groupStateArchived,
		groupStateUnspecified,
		groupStateOrderPlaced,
	}
)
