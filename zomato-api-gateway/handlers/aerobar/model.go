package aerobar

import (
	aerobarService "github.com/Zomato/zomato-api-gateway/internal/aerobar"
)

type AerobarModel struct {
	IsUserZoman                  bool
	IsGroupOrder                 bool
	IsVegModeEnabled             bool
	UserID                       int64
	IsDarkModeEnabled            bool
	IsIOSConsumer                bool
	IsAndroidConsumer            bool
	AppVersion                   string
	AppBetaVersion               string
	IsPureVeg                    bool
	GuestUserID                  string
	AerobarService               aerobarService.AerobarService
}

type OrderDetails struct {
	Status         bool  `json:"status"`
	OrderID        int64 `json:"order_id"`
	OrderStatus    int64 `json:"order_status"`
	DeliveryStatus int64 `json:"delivery_status"`
}

type TimeSlot struct {
	StartTime uint64 `json:"start_time,omitempty"`
	EndTime   uint64 `json:"end_time,omitempty"`
}

type PostBody struct {
	TransactionID string `json:"transaction_id"`
	Experience    string `json:"experience"`
	Source        string `json:"source"`
}
