package aerobar

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/wrapperspb"

	log "github.com/Zomato/go/logger"

	reviewtrackingevents "github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/reviews/reviewtrackingevents"
	"github.com/Zomato/zomato-api-gateway/handlers/go_out/tabs/utils"
	"github.com/Zomato/zomato-api-gateway/models/sushi"
)

const (
	TRACKING_EVENT_AEROBAR_IMPRESSION = "AerobarImpression"
	TRACKING_EVENT_AEROBAR_TAP        = "AerobarTapped"
	reviewTrackingEventsTableName     = "review_tracking_events"
)

func (req *AerobarModel) getTrackingData(aerobarID, title, businessIdentifier string, businessMeta map[string]interface{}) []*sushi.TrackingData {
	trackingData := []*sushi.TrackingData{}
	businessMetaByte, err := json.Marshal(businessMeta)
	if err != nil {
		log.WithError(err).Error("[Aerobar] error in marshalling businessMeta for GetOfferSwitchPopupTracking function")
	}

	jeventPayloadStruct := sushi.JeventPayload{
		Var1: aerobarID,
	}

	if !utils.IsEmptyString(title) {
		jeventPayloadStruct.Var2 = title
	}

	if !utils.IsEmptyString(businessIdentifier) {
		jeventPayloadStruct.Var5 = businessIdentifier
	}

	if businessMeta != nil && !utils.IsEmptyString(string(businessMetaByte)) {
		jeventPayloadStruct.Var6 = string(businessMetaByte)
	}

	jeventPayload, err := sushi.GetJEventPayload(jeventPayloadStruct)

	if err != nil {
		log.WithError(err).Error("[Aerobar] error in getJeventPayload for GetOfferSwitchPopupTracking function")
		return []*sushi.TrackingData{}
	}

	// Setting impression tracking data
	impressionTracking := &sushi.TrackingData{
		TableName: tableNameJevent,
		EventNames: &sushi.EventNames{
			Impression: getEventName(TRACKING_EVENT_AEROBAR_IMPRESSION),
		},
		Payload: jeventPayload,
	}

	trackingData = append(trackingData, impressionTracking)

	// Setting click/tap tracking data
	tapTracking := &sushi.TrackingData{
		TableName: tableNameJevent,
		EventNames: &sushi.EventNames{
			Tap: getEventName(TRACKING_EVENT_AEROBAR_TAP),
		},
		Payload: jeventPayload,
	}

	trackingData = append(trackingData, tapTracking)

	return trackingData
}

func getEventName(eventName string) string {
	ename, err := sushi.GetJEventName(eventName)
	if err != nil {
		return ""
	}
	return ename
}

func GetReviewTrackingEventsTrackingData(
	ctx *gin.Context,
	impressionEvent, tapEvent, textFieldFilledEvent, transactionID string,
) []*sushi.TrackingData {
	log := log.FromContext(ctx)

	trackingPayload := &reviewtrackingevents.ReviewTrackingEvents{
		TransactionId:  wrapperspb.String(transactionID),
	}

	eventNames, err := getEventNames(impressionEvent, tapEvent, textFieldFilledEvent)
	if err != nil {
		log.WithError(err).Errorf("[Aerobar] Error while getting event names for impressionEvent: %+v, and tapEvent: %+v", impressionEvent, tapEvent)
	}

	payload, payloadErr := getReviewTrackingEventPayload(trackingPayload)
	if payloadErr != nil {
		log.WithError(payloadErr).Errorf("[Aerobar] Error in getting payload for %#v", trackingPayload)
	}

	return []*sushi.TrackingData{
		{
			TableName:  reviewTrackingEventsTableName,
			EventNames: eventNames,
			Payload:    payload,
		}}
}

func getReviewTrackingEventPayload(payload *reviewtrackingevents.ReviewTrackingEvents) (string, error) {
	response, err := protojson.Marshal(payload)
	if err != nil {
		return "", err
	}

	return string(response), nil
}

func getEventNames(impressionEvent, tapEvent, textFieldFilledEvent string) (eventNames *sushi.EventNames, err error) {
	eventNames = &sushi.EventNames{}

	if len(impressionEvent) > 0 {
		eventNames.Impression, err = sushi.GetEventNameForTracking(impressionEvent)
		if err != nil {
			return nil, err
		}
	}

	if len(tapEvent) > 0 {
		eventNames.Tap, err = sushi.GetEventNameForTracking(tapEvent)
		if err != nil {
			return nil, err
		}
	}

	if len(textFieldFilledEvent) > 0 {
		eventNames.TextFieldFilled2, err = sushi.GetEventNameForTracking(textFieldFilledEvent)
		if err != nil {
			return nil, err
		}
	}

	return eventNames, nil
}
