package aerobar

import (
	"bytes"
	"encoding/json"
	"net/http"

	pb "github.com/Zomato/aerobar-service-client-golang/aerobar"
	"github.com/Zomato/go/config"
	"github.com/Zomato/go/i18n"
	logger "github.com/Zomato/go/logger"

	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	aerobarModels "github.com/Zomato/zomato-api-gateway/models/aerobar"
	"github.com/gin-gonic/gin"
)

func UpdateTemplate(ctx *gin.Context) {
	log := logger.FromContext(ctx)
	env := env.FromContext(ctx)

	var req aerobarModels.UpdateTemplateRequest
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		log.Errorf("could not bind request: %s", err)
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)),
		)
		return
	}

	zomatoClient := api.GetClientFromContext(ctx)
	userID := zomatoClient.UserID()

	var userHasUpdateTemplatePermission bool
	allowedUserIDs := config.GetIntSlice(ctx, "aerobar_service.template_operation.allowed_user_ids")
	for _, allowedUserID := range allowedUserIDs {
		if userID == int64(allowedUserID) {
			userHasUpdateTemplatePermission = true
		}
	}
	if !userHasUpdateTemplatePermission {
		log.Errorf("Unauthorized users cannot perform template operations")
		ctx.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusFailed(i18n.Translate(ctx, "unauthorized-user-for-template-operations")))
		return
	}

	if isBlank(req.TemplateID) {
		log.Errorf("Invalid template_id")
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)),
		)
		return
	}

	conn := env.AerobarServiceConn()
	aerobarClient := pb.NewAerobarClient(conn)

	switch req.ActionType {
	case publishActionType:
		templateObj := &pb.PublishTemplateRequest{
			TemplateId: req.TemplateID,
		}
		_, err := aerobarClient.PublishTemplate(ctx, templateObj)
		if err != nil {
			log.WithError(err).Errorf("Error in publishing template for templateID %v ", req.TemplateID)
			ctx.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
			return
		}
	case draftActionType:
		templateObj := &pb.UpdateTemplateRequest{
			TemplateId: req.TemplateID,
			MarkDraft:  true,
		}
		_, err := aerobarClient.UpdateTemplate(ctx, templateObj)
		if err != nil {
			log.WithError(err).Errorf("Error in drafting template for templateID %v ", req.TemplateID)
			ctx.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
			return
		}
	case updateTemplateSelectionRuleActionType:
		if isBlank(req.TemplateSelectionRule) {
			log.Errorf("Invalid template_selection_rule")
			ctx.AbortWithStatusJSON(
				http.StatusBadRequest,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)),
			)
			return
		}
		templateObj := &pb.UpdateTemplateRequest{
			TemplateId:            req.TemplateID,
			TemplateSelectionRule: req.TemplateSelectionRule,
		}
		_, err := aerobarClient.UpdateTemplate(ctx, templateObj)
		if err != nil {
			log.WithError(err).Errorf("Error in updating template selection rule template for templateID %v ", req.TemplateID)
			ctx.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
			return
		}
	case updatePriorityActionType:
		if req.Priority == 0 {
			log.Errorf("Invalid priority")
			ctx.AbortWithStatusJSON(
				http.StatusBadRequest,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)),
			)
			return
		}
		templateObj := &pb.UpdateTemplateRequest{
			TemplateId: req.TemplateID,
			Priority:   req.Priority,
		}
		_, err := aerobarClient.UpdateTemplate(ctx, templateObj)
		if err != nil {
			log.WithError(err).Errorf("Error in updating priority for templateID %v ", req.TemplateID)
			ctx.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
			return
		}
	case updateTemplateTypeActionType:
		if isBlank(req.TemplateType) {
			log.Errorf("Invalid template type")
			ctx.AbortWithStatusJSON(
				http.StatusBadRequest,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)),
			)
			return
		}
		templateObj := &pb.UpdateTemplateRequest{
			TemplateId:   req.TemplateID,
			TemplateType: req.TemplateType,
		}
		_, err := aerobarClient.UpdateTemplate(ctx, templateObj)
		if err != nil {
			log.WithError(err).Errorf("Error in updating template type for templateID %v ", req.TemplateID)
			ctx.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
			return
		}
	case updateAerobarDetailsActionType:
		if req.AerobarDetails == nil {
			log.Errorf("Invalid aerobar_details object")
			ctx.AbortWithStatusJSON(
				http.StatusBadRequest,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)),
			)
			return
		}

		getTemplateObj := &pb.GetTemplateRequest{
			TemplateId: req.TemplateID,
		}
		response, err := aerobarClient.GetTemplate(ctx, getTemplateObj)
		if err != nil {
			log.Errorf("could not get template: %s", err)
			ctx.AbortWithStatusJSON(http.StatusInternalServerError, response)
			return
		}
		if (!response.Status) || isBlank(response.TemplateData.TemplateType) {
			log.Errorf("response is empty, this template doesn't exist")
			ctx.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
			return
		}

		marshalledRequestAerobarDetails, err := json.Marshal(&req.AerobarDetails)
		if err != nil {
			log.Errorf("could not marshall updatedAerobarDetails : %s", err)
			ctx.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
			return
		}

		var rpcDataMap map[string]interface{}
		var requestDataMap map[string]interface{}
		dec := json.NewDecoder(bytes.NewReader(marshalledRequestAerobarDetails))
		dec.DisallowUnknownFields()
		if err = dec.Decode(&requestDataMap); err != nil {
			log.Errorf("could not decode marshalledAerobarDetails into Map", err)
			ctx.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
			return
		}

		if err = json.Unmarshal([]byte(response.GetTemplateData().GetAerobarDetails()), &rpcDataMap); err != nil {
			log.Errorf("could not unmarshall into rpcMap", err)
			ctx.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
			return
		}

		mergedMap := mergeMaps(rpcDataMap, requestDataMap)

		mergedJSONString, err := json.Marshal(mergedMap)
		if err != nil {
			log.Errorf("could not marshall into jsonMap", err)
			ctx.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
			return
		}

		var finalAerobarDetails *pb.AerobarDetails
		err = json.Unmarshal((mergedJSONString), &finalAerobarDetails)
		if err != nil {
			log.Errorf("could not unmarshall finalAerobarDetails", err)
			ctx.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
			return
		}
		updateTemplateObj := &pb.UpdateTemplateRequest{
			TemplateId:     req.TemplateID,
			AerobarDetails: finalAerobarDetails,
		}

		_, err = aerobarClient.UpdateTemplate(ctx, updateTemplateObj)
		if err != nil {
			log.WithError(err).Errorf("Error in updating aerobar details object for templateID %v ", req.TemplateID)
			ctx.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
			return
		}
	default:
		log.Errorf("Invalid action type")
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)),
		)
		return
	}
	ctx.JSON(http.StatusOK, models.StatusSuccess("template-updated-successfully"))
}

func CreateTemplate(ctx *gin.Context) {
	log := logger.FromContext(ctx)
	env := env.FromContext(ctx)

	var req aerobarModels.CreateTemplateRequest
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		log.Errorf("could not bind request: %s", err)
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)),
		)
		return
	}

	zomatoClient := api.GetClientFromContext(ctx)
	userID := zomatoClient.UserID()

	var userHasCreateTemplatePermission bool
	allowedUserIDs := config.GetIntSlice(ctx, "aerobar_service.template_operation.allowed_user_ids")
	for _, allowedUserID := range allowedUserIDs {
		if userID == int64(allowedUserID) {
			userHasCreateTemplatePermission = true
		}
	}
	if !userHasCreateTemplatePermission {
		log.Errorf("Unauthorized users cannot perform template operations")
		ctx.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusFailed(i18n.Translate(ctx, "unauthorized-user-for-template-operations")))
		return
	}

	if req.AerobarDetails == nil || isBlank(req.TemplateSelectionRule) || isBlank(req.TemplateType) || req.Priority == 0 {
		log.Errorf("invalid input for creating template")
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)),
		)
		return
	}
	conn := env.AerobarServiceConn()
	templateclient := pb.NewAerobarClient(conn)
	marshalledReq, err := json.Marshal(&req)
	if err != nil {
		log.WithError(err).Errorf("could not marshall templateObj")
		ctx.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
		return
	}
	var templateDetails *pb.CreateTemplateRequest
	err = json.Unmarshal([]byte(marshalledReq), &templateDetails)
	if err != nil {
		log.WithError(err).Errorf("could not unmarshall final create template object")
		ctx.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
		return
	}

	_, err = templateclient.CreateTemplate(ctx, templateDetails)
	if err != nil {
		log.WithError(err).Errorf("Error in creating template")
		ctx.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(ctx, StatusFailedErrorStringKey)))
		return
	}

	ctx.JSON(http.StatusOK, models.StatusSuccess("template-created-successfully"))
}

func mergeMaps(rpcDataMap, requestDataMap map[string]interface{}) map[string]interface{} { //nolint: ignore-recursion
	mergedMap := make(map[string]interface{})

	for key, value := range rpcDataMap {
		mergedMap[key] = value
	}

	for key, value := range requestDataMap {
		if value != nil {
			if nestedMap, ok := value.(map[string]interface{}); ok {
				if _, ok := mergedMap[key]; !ok {
					mergedMap[key] = make(map[string]interface{})
				}
				if mergedNestedMap, ok := mergedMap[key].(map[string]interface{}); ok {
					mergedMap[key] = mergeMaps(mergedNestedMap, nestedMap)
				}
			} else {
				mergedMap[key] = value
			}
		}
	}
	return mergedMap
}
