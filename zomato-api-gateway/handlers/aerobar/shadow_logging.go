package aerobar

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/Zomato/go/logger"
	events "github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/aerobarservice/aerobarservicemigrationevents"
	"github.com/Zomato/zomato-api-gateway/internal/utils"
	aerobarmodel "github.com/Zomato/zomato-api-gateway/models/aerobar"
	"github.com/gin-gonic/gin"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

func LogAerobarMismatch(c *gin.Context, gwResp *aerobarmodel.AerobarResponse, webResp *aerobarmodel.AerobarResponse) []*events.AerobarMismatchEvent {
	// log aerobar mismatch
	mismatchEventArray := make([]*events.AerobarMismatchEvent, 0)
	if gwResp == nil && webResp == nil {
		return mismatchEventArray
	}

	if gwResp == nil {
		webJson, err := json.Marshal(webResp)
		if err != nil {
			logger.Error("[Aerobar] Failed to marshal web response using json", err)
		} else {
			mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
				Key:                     wrapperspb.String("Response"),
				ApiGatewayResponseValue: wrapperspb.String("nil"),
				WebResponseValue:        wrapperspb.String(string(webJson)),
			})
		}
		return mismatchEventArray
	}

	if webResp == nil {
		gwJson, err := json.Marshal(gwResp)
		if err != nil {
			logger.Error("[Aerobar] Failed to marshal gw response using json", err)
		} else {
			mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
				Key:                     wrapperspb.String("Response"),
				ApiGatewayResponseValue: wrapperspb.String(string(gwJson)),
				WebResponseValue:        wrapperspb.String("nil"),
			})
		}
		return mismatchEventArray
	}

	// Compare each field of AerobarResponse explicitly
	if len(gwResp.Data) != len(webResp.Data) {
		mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
			Key:                     wrapperspb.String("Data"),
			ApiGatewayResponseValue: wrapperspb.String("Length mismatch"),
			WebResponseValue:        wrapperspb.String("Length mismatch"),
		})
		return mismatchEventArray
	}

	for i := range gwResp.Data {
		if gwResp.Data[i] == nil && webResp.Data[i] == nil {
			continue
		}
		if gwResp.Data[i] == nil {
			webJson, err := json.Marshal(webResp.Data[i])
			if err != nil {
				logger.Error("[Aerobar] Failed to marshal web response data using json", err)
			} else {
				mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
					Key:                     wrapperspb.String("Data"),
					ApiGatewayResponseValue: wrapperspb.String("nil"),
					WebResponseValue:        wrapperspb.String(string(webJson)),
				})
			}
			continue
		}

		if webResp.Data[i] == nil {
			gwJson, err := json.Marshal(gwResp.Data[i])
			if err != nil {
				logger.Error("[Aerobar] Failed to marshal gw response data using json", err)
			} else {
				mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
					Key:                     wrapperspb.String("Data"),
					ApiGatewayResponseValue: wrapperspb.String(string(gwJson)),
					WebResponseValue:        wrapperspb.String("nil"),
				})
			}
			continue
		}

		if gwResp.Data[i].AerobarType != webResp.Data[i].AerobarType {
			mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
				Key:                     wrapperspb.String("Data.AerobarType"),
				ApiGatewayResponseValue: wrapperspb.String(gwResp.Data[i].AerobarType),
				WebResponseValue:        wrapperspb.String(webResp.Data[i].AerobarType),
			})
		}

		if gwResp.Data[i].MaxToShow != webResp.Data[i].MaxToShow {
			mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
				Key:                     wrapperspb.String("Data.MaxToShow"),
				ApiGatewayResponseValue: wrapperspb.String(utils.ConvertInt64ToString(int64(gwResp.Data[i].MaxToShow))),
				WebResponseValue:        wrapperspb.String(utils.ConvertInt64ToString(int64(webResp.Data[i].MaxToShow))),
			})
		}

		if len(gwResp.Data[i].Items) != len(webResp.Data[i].Items) {
			mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
				Key:                     wrapperspb.String("Data.Items"),
				ApiGatewayResponseValue: wrapperspb.String("Length mismatch: " +strconv.Itoa(len(gwResp.Data[i].Items))),
				WebResponseValue:        wrapperspb.String("Length mismatch: " +strconv.Itoa(len(webResp.Data[i].Items))),
			})
		} else {
			for j := range gwResp.Data[i].Items {
				if gwResp.Data[i].Items[j] == nil && webResp.Data[i].Items[j] == nil {
					continue
				}
				if gwResp.Data[i].Items[j] == nil {
					webJson, err := json.Marshal(webResp.Data[i].Items[j])
					if err != nil {
						logger.Error("[Aerobar] Failed to marshal web response item using json", err)
					} else {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items"),
							ApiGatewayResponseValue: wrapperspb.String("nil"),
							WebResponseValue:        wrapperspb.String(string(webJson)),
						})
					}
					continue
				}

				if webResp.Data[i].Items[j] == nil {
					gwJson, err := json.Marshal(gwResp.Data[i].Items[j])
					if err != nil {
						logger.Error("[Aerobar] Failed to marshal gw response item using json", err)
					} else {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items"),
							ApiGatewayResponseValue: wrapperspb.String(string(gwJson)),
							WebResponseValue:        wrapperspb.String("nil"),
						})
					}
					continue
				}

				gwRespItem := gwResp.Data[i].Items[j]
				webRespItem := webResp.Data[i].Items[j]

				if gwRespItem.AerobarID != webRespItem.AerobarID {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.AerobarID"),
						ApiGatewayResponseValue: wrapperspb.String(gwRespItem.AerobarID),
						WebResponseValue:        wrapperspb.String(webRespItem.AerobarID),
					})
				}

				if gwRespItem.Title != webRespItem.Title {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.Title"),
						ApiGatewayResponseValue: wrapperspb.String(gwRespItem.Title),
						WebResponseValue:        wrapperspb.String(webRespItem.Title),
					})
				}

				if gwRespItem.Description != webRespItem.Description {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.Description"),
						ApiGatewayResponseValue: wrapperspb.String(gwRespItem.Description),
						WebResponseValue:        wrapperspb.String(webRespItem.Description),
					})
				}

				if normalizelink(gwRespItem.Deeplink) != normalizelink(webRespItem.Deeplink) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.Deeplink"),
						ApiGatewayResponseValue: wrapperspb.String(gwRespItem.Deeplink),
						WebResponseValue:        wrapperspb.String(webRespItem.Deeplink),
					})
				}

				if HashStruct(gwResp.Data[i].Items[j].Subtitle) != HashStruct(webResp.Data[i].Items[j].Subtitle) {
					gwValue := MarshalToString(gwResp.Data[i].Items[j].Subtitle)
					webValue := MarshalToString(webResp.Data[i].Items[j].Subtitle)
					if !(webValue == "{}" && gwValue == "null") {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items.Subtitle"),
							ApiGatewayResponseValue: wrapperspb.String(gwValue),
							WebResponseValue:        wrapperspb.String(webValue),
						})
					}
				}

				if gwResp.Data[i].Items[j].AerobarStatus != webResp.Data[i].Items[j].AerobarStatus {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.AerobarStatus"),
						ApiGatewayResponseValue: wrapperspb.String(utils.ConvertInt64ToString(gwResp.Data[i].Items[j].AerobarStatus)),
						WebResponseValue:        wrapperspb.String(utils.ConvertInt64ToString(webResp.Data[i].Items[j].AerobarStatus)),
					})
				}

				if gwResp.Data[i].Items[j].Icon != webResp.Data[i].Items[j].Icon {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.Icon"),
						ApiGatewayResponseValue: wrapperspb.String(gwResp.Data[i].Items[j].Icon),
						WebResponseValue:        wrapperspb.String(webResp.Data[i].Items[j].Icon),
					})
				}

				if gwResp.Data[i].Items[j].FirstActionText != nil || webResp.Data[i].Items[j].FirstActionText != nil {
					if DereferenceString(gwResp.Data[i].Items[j].FirstActionText) != DereferenceString(webResp.Data[i].Items[j].FirstActionText) {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items.FirstActionText"),
							ApiGatewayResponseValue: wrapperspb.String(DereferenceString(gwResp.Data[i].Items[j].FirstActionText)),
							WebResponseValue:        wrapperspb.String(DereferenceString(webResp.Data[i].Items[j].FirstActionText)),
						})
					}
				}

				if gwResp.Data[i].Items[j].FirstIsActionIcon != webResp.Data[i].Items[j].FirstIsActionIcon {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.FirstIsActionIcon"),
						ApiGatewayResponseValue: wrapperspb.String(utils.ConvertInt64ToString(gwResp.Data[i].Items[j].FirstIsActionIcon)),
						WebResponseValue:        wrapperspb.String(utils.ConvertInt64ToString(webResp.Data[i].Items[j].FirstIsActionIcon)),
					})
				}

				if gwResp.Data[i].Items[j].SecondDeeplink != webResp.Data[i].Items[j].SecondDeeplink {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.SecondDeeplink"),
						ApiGatewayResponseValue: wrapperspb.String(gwResp.Data[i].Items[j].SecondDeeplink),
						WebResponseValue:        wrapperspb.String(webResp.Data[i].Items[j].SecondDeeplink),
					})
				}

				if gwResp.Data[i].Items[j].SecondActionText != webResp.Data[i].Items[j].SecondActionText {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.SecondActionText"),
						ApiGatewayResponseValue: wrapperspb.String(gwResp.Data[i].Items[j].SecondActionText),
						WebResponseValue:        wrapperspb.String(webResp.Data[i].Items[j].SecondActionText),
					})
				}

				if gwResp.Data[i].Items[j].IsRatingSnippetVisible != webResp.Data[i].Items[j].IsRatingSnippetVisible {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.IsRatingSnippetVisible"),
						ApiGatewayResponseValue: wrapperspb.String(utils.ConvertBoolToString(gwResp.Data[i].Items[j].IsRatingSnippetVisible)),
						WebResponseValue:        wrapperspb.String(utils.ConvertBoolToString(webResp.Data[i].Items[j].IsRatingSnippetVisible)),
					})
				}

				if gwResp.Data[i].Items[j].SecondIsActionIcon != webResp.Data[i].Items[j].SecondIsActionIcon {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.SecondIsActionIcon"),
						ApiGatewayResponseValue: wrapperspb.String(utils.ConvertInt64ToString(gwResp.Data[i].Items[j].SecondIsActionIcon)),
						WebResponseValue:        wrapperspb.String(utils.ConvertInt64ToString(webResp.Data[i].Items[j].SecondIsActionIcon)),
					})
				}

				if gwResp.Data[i].Items[j].IconBottomRightImage != webResp.Data[i].Items[j].IconBottomRightImage {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.IconBottomRightImage"),
						ApiGatewayResponseValue: wrapperspb.String(gwResp.Data[i].Items[j].IconBottomRightImage),
						WebResponseValue:        wrapperspb.String(webResp.Data[i].Items[j].IconBottomRightImage),
					})
				}

				if gwResp.Data[i].Items[j].LottieURL != webResp.Data[i].Items[j].LottieURL {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.LottieURL"),
						ApiGatewayResponseValue: wrapperspb.String(gwResp.Data[i].Items[j].LottieURL),
						WebResponseValue:        wrapperspb.String(webResp.Data[i].Items[j].LottieURL),
					})
				}
				if gwResp.Data[i].Items[j].FirstDeeplink != webResp.Data[i].Items[j].FirstDeeplink {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.FirstDeeplink"),
						ApiGatewayResponseValue: wrapperspb.String(gwResp.Data[i].Items[j].FirstDeeplink),
						WebResponseValue:        wrapperspb.String(webResp.Data[i].Items[j].FirstDeeplink),
					})
				}
				if cleanLink(gwResp.Data[i].Items[j].TapDeeplink) != cleanLink(webResp.Data[i].Items[j].TapDeeplink) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.TapDeeplink"),
						ApiGatewayResponseValue: wrapperspb.String(gwResp.Data[i].Items[j].TapDeeplink),
						WebResponseValue:        wrapperspb.String(webResp.Data[i].Items[j].TapDeeplink),
					})
				}
				if gwResp.Data[i].Items[j].Type != webResp.Data[i].Items[j].Type {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.Type"),
						ApiGatewayResponseValue: wrapperspb.String(utils.ConvertInt64ToString(gwResp.Data[i].Items[j].Type)),
						WebResponseValue:        wrapperspb.String(utils.ConvertInt64ToString(webResp.Data[i].Items[j].Type)),
					})
				}
				if gwResp.Data[i].Items[j].AerobarType != webResp.Data[i].Items[j].AerobarType {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.AerobarType"),
						ApiGatewayResponseValue: wrapperspb.String(gwResp.Data[i].Items[j].AerobarType),
						WebResponseValue:        wrapperspb.String(webResp.Data[i].Items[j].AerobarType),
					})
				}
				if gwResp.Data[i].Items[j].Subtype != webResp.Data[i].Items[j].Subtype {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.Subtype"),
						ApiGatewayResponseValue: wrapperspb.String(gwResp.Data[i].Items[j].Subtype),
						WebResponseValue:        wrapperspb.String(webResp.Data[i].Items[j].Subtype),
					})
				}
				if HashStruct(gwResp.Data[i].Items[j].TopTitle) != HashStruct(webResp.Data[i].Items[j].TopTitle) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.TopTitle"),
						ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.Data[i].Items[j].TopTitle)),
						WebResponseValue:        wrapperspb.String(MarshalToString(webResp.Data[i].Items[j].TopTitle)),
					})
				}
				if HashStruct(gwResp.Data[i].Items[j].LeftAnimation) != HashStruct(webResp.Data[i].Items[j].LeftAnimation) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.LeftAnimation"),
						ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.Data[i].Items[j].LeftAnimation)),
						WebResponseValue:        wrapperspb.String(MarshalToString(webResp.Data[i].Items[j].LeftAnimation)),
					})
				}
				if HashStruct(gwResp.Data[i].Items[j].RightContainer) != HashStruct(webResp.Data[i].Items[j].RightContainer) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.RightContainer"),
						ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.Data[i].Items[j].RightContainer)),
						WebResponseValue:        wrapperspb.String(MarshalToString(webResp.Data[i].Items[j].RightContainer)),
					})
				}
				if HashStruct(gwResp.Data[i].Items[j].CooldownPeriod) != HashStruct(webResp.Data[i].Items[j].CooldownPeriod) {
					gwValue := MarshalToString(gwResp.Data[i].Items[j].CooldownPeriod)
					webValue := MarshalToString(webResp.Data[i].Items[j].CooldownPeriod)
					// Don't report if this specific case is encountered
					if !(webValue == "null" && gwValue == "{}") {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items.CooldownPeriod"),
							ApiGatewayResponseValue: wrapperspb.String(gwValue),
							WebResponseValue:        wrapperspb.String(webValue),
						})
					}
				}
				if HashStruct(gwResp.Data[i].Items[j].MaxShowCount) != HashStruct(webResp.Data[i].Items[j].MaxShowCount) {
					gwValue := MarshalToString(gwResp.Data[i].Items[j].MaxShowCount)
					webValue := MarshalToString(webResp.Data[i].Items[j].MaxShowCount)
					// Don't report if this specific case is encountered
					if !(webValue == "null" && gwValue == "{}") {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items.MaxShowCount"),
							ApiGatewayResponseValue: wrapperspb.String(gwValue),
							WebResponseValue:        wrapperspb.String(webValue),
						})
					}
				}
				if isEitherNilButNotBoth(gwResp.Data[i].Items[j].RatingConfig, webResp.Data[i].Items[j].RatingConfig) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.RatingConfig"),
						ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.Data[i].Items[j].RatingConfig)),
						WebResponseValue:        wrapperspb.String(MarshalToString(webResp.Data[i].Items[j].RatingConfig)),
					})
				} else if gwResp.Data[i].Items[j].RatingConfig != nil && webResp.Data[i].Items[j].RatingConfig != nil {
					gwRatingConfig := gwResp.Data[i].Items[j].RatingConfig
					webRatingConfig := webResp.Data[i].Items[j].RatingConfig
					if HashStruct(gwRatingConfig.RatingChangeClickAction) != HashStruct(webRatingConfig.RatingChangeClickAction) {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items.RatingConfig.RatingChangeClickAction"),
							ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwRatingConfig.RatingChangeClickAction)),
							WebResponseValue:        wrapperspb.String(MarshalToString(webRatingConfig.RatingChangeClickAction)),
						})
					}
					if len(gwRatingConfig.RatingChangeSecondaryClickAction) != len(webRatingConfig.RatingChangeSecondaryClickAction) {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items.RatingConfig.RatingChangeSecondaryClickAction"),
							ApiGatewayResponseValue: wrapperspb.String("Length mismatch: " + strconv.Itoa(len(gwRatingConfig.RatingChangeSecondaryClickAction))),
							WebResponseValue:        wrapperspb.String("Length mismatch: " + strconv.Itoa(len(webRatingConfig.RatingChangeSecondaryClickAction))),
						})
					} else {
						for k := range gwRatingConfig.RatingChangeSecondaryClickAction {
							if HashStruct(gwRatingConfig.RatingChangeSecondaryClickAction[k]) != HashStruct(webRatingConfig.RatingChangeSecondaryClickAction[k]) {
								mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
									Key:                     wrapperspb.String(fmt.Sprintf("Data.Items.RatingConfig.RatingChangeSecondaryClickAction[%d]", k)),
									ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwRatingConfig.RatingChangeSecondaryClickAction[k])),
									WebResponseValue:        wrapperspb.String(MarshalToString(webRatingConfig.RatingChangeSecondaryClickAction[k])),
								})
							}
						}
					}
					if HashStruct(gwRatingConfig.MaxRatingLottie) != HashStruct(webRatingConfig.MaxRatingLottie) {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items.RatingConfig.MaxRatingLottie"),
							ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwRatingConfig.MaxRatingLottie)),
							WebResponseValue:        wrapperspb.String(MarshalToString(webRatingConfig.MaxRatingLottie)),
						})
					}
					if HashStruct(gwRatingConfig.Subtitle) != HashStruct(webRatingConfig.Subtitle) {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items.RatingConfig.Subtitle"),
							ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwRatingConfig.Subtitle)),
							WebResponseValue:        wrapperspb.String(MarshalToString(webRatingConfig.Subtitle)),
						})
					}
					if HashStruct(gwRatingConfig.RightButtonAnimationConfig) != HashStruct(webRatingConfig.RightButtonAnimationConfig) {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items.RatingConfig.RightButtonAnimationConfig"),
							ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwRatingConfig.RightButtonAnimationConfig)),
							WebResponseValue:        wrapperspb.String(MarshalToString(webRatingConfig.RightButtonAnimationConfig)),
						})
					}
					if len(gwRatingConfig.TrackingData) != len(webRatingConfig.TrackingData) {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items.RatingConfig.TrackingData"),
							ApiGatewayResponseValue: wrapperspb.String("Length mismatch: " + strconv.Itoa(len(gwRatingConfig.TrackingData))),
							WebResponseValue:        wrapperspb.String("Length mismatch: " + strconv.Itoa(len(webRatingConfig.TrackingData))),
						})
					} else {
						for k := range gwRatingConfig.TrackingData {
							if HashStruct(gwRatingConfig.TrackingData[k]) != HashStruct(webRatingConfig.TrackingData[k]) {
								mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
									Key:                     wrapperspb.String(fmt.Sprintf("Data.Items.RatingConfig.TrackingData[%d]", k)),
									ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwRatingConfig.TrackingData[k])),
									WebResponseValue:        wrapperspb.String(MarshalToString(webRatingConfig.TrackingData[k])),
								})
							}
						}
					}
					if HashStruct(gwRatingConfig.TopTitle) != HashStruct(webRatingConfig.TopTitle) {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items.RatingConfig.TopTitle"),
							ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwRatingConfig.TopTitle)),
							WebResponseValue:        wrapperspb.String(MarshalToString(webRatingConfig.TopTitle)),
						})
					}
				}
				if isEitherNilButNotBoth(gwResp.Data[i].Items[j].TapClickAction, webResp.Data[i].Items[j].TapClickAction) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.TapClickAction"),
						ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.Data[i].Items[j].TapClickAction)),
						WebResponseValue:        wrapperspb.String(MarshalToString(webResp.Data[i].Items[j].TapClickAction)),
					})
				} else if gwResp.Data[i].Items[j].TapClickAction != nil && webResp.Data[i].Items[j].TapClickAction != nil {
					gwAction := gwResp.Data[i].Items[j].TapClickAction
					webAction := webResp.Data[i].Items[j].TapClickAction

					if gwAction.Type != webAction.Type {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items.TapClickAction.Type"),
							ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwAction.Type)),
							WebResponseValue:        wrapperspb.String(MarshalToString(webAction.Type)),
						})
					}
					if gwAction.OpenRatingFormBottomSheet != nil || webAction.OpenRatingFormBottomSheet != nil {
						if HashStruct(gwAction.OpenRatingFormBottomSheet) != HashStruct(webAction.OpenRatingFormBottomSheet) {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String("Data.Items.TapClickAction.OpenRatingFormBottomSheet"),
								ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwAction.OpenRatingFormBottomSheet)),
								WebResponseValue:        wrapperspb.String(MarshalToString(webAction.OpenRatingFormBottomSheet)),
							})
						}
					}
					if gwAction.AddBottomView != nil || webAction.AddBottomView != nil {
						if HashStruct(gwAction.AddBottomView) != HashStruct(webAction.AddBottomView) {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String("Data.Items.TapClickAction.AddBottomView"),
								ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwAction.AddBottomView)),
								WebResponseValue:        wrapperspb.String(MarshalToString(webAction.AddBottomView)),
							})
						}
					}
					if isEitherNilButNotBoth(gwAction.APICallOnTap, webAction.APICallOnTap) {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("Data.Items.TapClickAction.APICallOnTap"),
							ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwAction.APICallOnTap)),
							WebResponseValue:        wrapperspb.String(MarshalToString(webAction.APICallOnTap)),
						})
					} else if gwAction.APICallOnTap != nil && webAction.APICallOnTap != nil {
						if gwAction.APICallOnTap.URL != webAction.APICallOnTap.URL {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String("Data.Items.TapClickAction.APICallOnTap.URL"),
								ApiGatewayResponseValue: wrapperspb.String(gwAction.APICallOnTap.URL),
								WebResponseValue:        wrapperspb.String(webAction.APICallOnTap.URL),
							})
						}
						if gwAction.APICallOnTap.Type != webAction.APICallOnTap.Type {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String("Data.Items.TapClickAction.APICallOnTap.Type"),
								ApiGatewayResponseValue: wrapperspb.String(gwAction.APICallOnTap.Type),
								WebResponseValue:        wrapperspb.String(webAction.APICallOnTap.Type),
							})
						}
						if gwAction.APICallOnTap.PostBody != "" || webAction.APICallOnTap.PostBody != "" {
							if HashStructNormalized(gwAction.APICallOnTap.PostBody) != HashStructNormalized(webAction.APICallOnTap.PostBody) {
								mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
									Key:                     wrapperspb.String("Data.Items.TapClickAction.APICallOnTap.PostBody"),
									ApiGatewayResponseValue: wrapperspb.String(gwAction.APICallOnTap.PostBody),
									WebResponseValue:        wrapperspb.String(webAction.APICallOnTap.PostBody),
								})
							}
						}
						if gwAction.APICallOnTap.RequestEncodingType != webAction.APICallOnTap.RequestEncodingType {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String("Data.Items.TapClickAction.APICallOnTap.RequestEncodingType"),
								ApiGatewayResponseValue: wrapperspb.String(gwAction.APICallOnTap.RequestEncodingType),
								WebResponseValue:        wrapperspb.String(webAction.APICallOnTap.RequestEncodingType),
							})
						}
					}
				}
				if HashStruct(gwResp.Data[i].Items[j].LeftImage) != HashStruct(webResp.Data[i].Items[j].LeftImage) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.LeftImage"),
						ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.Data[i].Items[j].LeftImage)),
						WebResponseValue:        wrapperspb.String(MarshalToString(webResp.Data[i].Items[j].LeftImage)),
					})
				}
				if HashStruct(gwResp.Data[i].Items[j].Cta1) != HashStruct(webResp.Data[i].Items[j].Cta1) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.Cta1"),
						ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.Data[i].Items[j].Cta1)),
						WebResponseValue:        wrapperspb.String(MarshalToString(webResp.Data[i].Items[j].Cta1)),
					})
				}
				if HashStruct(gwResp.Data[i].Items[j].Cta2) != HashStruct(webResp.Data[i].Items[j].Cta2) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.Cta2"),
						ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.Data[i].Items[j].Cta2)),
						WebResponseValue:        wrapperspb.String(MarshalToString(webResp.Data[i].Items[j].Cta2)),
					})
				}
				if HashStruct(gwResp.Data[i].Items[j].Body) != HashStruct(webResp.Data[i].Items[j].Body) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.Body"),
						ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.Data[i].Items[j].Body)),
						WebResponseValue:        wrapperspb.String(MarshalToString(webResp.Data[i].Items[j].Body)),
					})
				}
				if len(gwResp.Data[i].Items[j].TrackingData) != len(webResp.Data[i].Items[j].TrackingData) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("Data.Items.TrackingData"),
						ApiGatewayResponseValue: wrapperspb.String("Length mismatch: " + strconv.Itoa(len(gwResp.Data[i].Items[j].TrackingData))),
						WebResponseValue:        wrapperspb.String("Length mismatch: " + strconv.Itoa(len(webResp.Data[i].Items[j].TrackingData))),
					})
				} else {
					for k := range gwResp.Data[i].Items[j].TrackingData {
						if HashStruct(gwResp.Data[i].Items[j].TrackingData[k]) != HashStruct(webResp.Data[i].Items[j].TrackingData[k]) {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String(fmt.Sprintf("Data.Items.TrackingData[%d]", k)),
								ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.Data[i].Items[j].TrackingData[k])),
								WebResponseValue:        wrapperspb.String(MarshalToString(webResp.Data[i].Items[j].TrackingData[k])),
							})
						}
					}
				}
			}
		}
	}

	if gwResp.SubscriberChannelType != webResp.SubscriberChannelType {
		mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
			Key:                     wrapperspb.String("SubscriberChannelType"),
			ApiGatewayResponseValue: wrapperspb.String(gwResp.SubscriberChannelType),
			WebResponseValue:        wrapperspb.String(webResp.SubscriberChannelType),
		})
	}

	if len(gwResp.SubscriberChannelName) != len(webResp.SubscriberChannelName) {
		mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
			Key:                     wrapperspb.String("SubscriberChannelName"),
			ApiGatewayResponseValue: wrapperspb.String("Length mismatch"),
			WebResponseValue:        wrapperspb.String("Length mismatch"),
		})
	} else {
		for i := range gwResp.SubscriberChannelName {
			if gwResp.SubscriberChannelName[i] != webResp.SubscriberChannelName[i] {
				mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
					Key:                     wrapperspb.String("SubscriberChannelName"),
					ApiGatewayResponseValue: wrapperspb.String(gwResp.SubscriberChannelName[i]),
					WebResponseValue:        wrapperspb.String(webResp.SubscriberChannelName[i]),
				})
			}
		}
	}
	if gwResp.ServerTimestamp <= 0 || webResp.ServerTimestamp <= 0 {
		mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
			Key:                     wrapperspb.String("ServerTimestamp"),
			ApiGatewayResponseValue: wrapperspb.String(strconv.FormatInt(gwResp.ServerTimestamp, 10)),
			WebResponseValue:        wrapperspb.String(strconv.FormatInt(webResp.ServerTimestamp, 10)),
		})
	}
	if isEitherNilButNotBoth(gwResp.SubscriberChannel, webResp.SubscriberChannel) {
		mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
			Key:                     wrapperspb.String("SubscriberChannel"),
			ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.SubscriberChannel)),
			WebResponseValue:        wrapperspb.String(MarshalToString(webResp.SubscriberChannel)),
		})
	} else if gwResp.SubscriberChannel != nil && webResp.SubscriberChannel != nil {
		if gwResp.SubscriberChannel.Type != webResp.SubscriberChannel.Type {
			mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
				Key:                     wrapperspb.String("SubscriberChannel.Type"),
				ApiGatewayResponseValue: wrapperspb.String(gwResp.SubscriberChannel.Type),
				WebResponseValue:        wrapperspb.String(webResp.SubscriberChannel.Type),
			})
		}
		if len(gwResp.SubscriberChannel.Name) != len(webResp.SubscriberChannel.Name) {
			mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
				Key:                     wrapperspb.String("SubscriberChannel.Name"),
				ApiGatewayResponseValue: wrapperspb.String("Length mismatch"),
				WebResponseValue:        wrapperspb.String("Length mismatch"),
			})
		} else {
			for i := range gwResp.SubscriberChannel.Name {
				if gwResp.SubscriberChannel.Name[i] != webResp.SubscriberChannel.Name[i] {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("SubscriberChannel.Name"),
						ApiGatewayResponseValue: wrapperspb.String(gwResp.SubscriberChannel.Name[i]),
						WebResponseValue:        wrapperspb.String(webResp.SubscriberChannel.Name[i]),
					})
				}
			}
		}
		if gwResp.SubscriberChannel.Qos != webResp.SubscriberChannel.Qos {
			mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
				Key:                     wrapperspb.String("SubscriberChannel.Qos"),
				ApiGatewayResponseValue: wrapperspb.String(utils.ConvertInt64ToString(int64(gwResp.SubscriberChannel.Qos))),
				WebResponseValue:        wrapperspb.String(utils.ConvertInt64ToString(int64(webResp.SubscriberChannel.Qos))),
			})
		}
		if gwResp.SubscriberChannel.Client.Username != webResp.SubscriberChannel.Client.Username {
			mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
				Key:                     wrapperspb.String("SubscriberChannel.Client.Username"),
				ApiGatewayResponseValue: wrapperspb.String(gwResp.SubscriberChannel.Client.Username),
				WebResponseValue:        wrapperspb.String(webResp.SubscriberChannel.Client.Username),
			})
		}
		if gwResp.SubscriberChannel.Client.Password != webResp.SubscriberChannel.Client.Password {
			mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
				Key:                     wrapperspb.String("SubscriberChannel.Client.Password"),
				ApiGatewayResponseValue: wrapperspb.String(gwResp.SubscriberChannel.Client.Password),
				WebResponseValue:        wrapperspb.String(webResp.SubscriberChannel.Client.Password),
			})
		}
		if gwResp.SubscriberChannel.Client.Keepalive != webResp.SubscriberChannel.Client.Keepalive {
			mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
				Key:                     wrapperspb.String("SubscriberChannel.Client.Keepalive"),
				ApiGatewayResponseValue: wrapperspb.String(utils.ConvertInt64ToString(int64(gwResp.SubscriberChannel.Client.Keepalive))),
				WebResponseValue:        wrapperspb.String(utils.ConvertInt64ToString(int64(webResp.SubscriberChannel.Client.Keepalive))),
			})
		}
	}

	if len(gwResp.LocalAerobars) != len(webResp.LocalAerobars) {
		mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
			Key:                     wrapperspb.String("LocalAerobars"),
			ApiGatewayResponseValue: wrapperspb.String("Length mismatch"),
			WebResponseValue:        wrapperspb.String("Length mismatch"),
		})
	} else {
		for i := range gwResp.LocalAerobars {
			if gwResp.LocalAerobars[i].Type != webResp.LocalAerobars[i].Type {
				mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
					Key:                     wrapperspb.String("LocalAerobars.Type"),
					ApiGatewayResponseValue: wrapperspb.String(gwResp.LocalAerobars[i].Type),
					WebResponseValue:        wrapperspb.String(webResp.LocalAerobars[i].Type),
				})
			}
			if isEitherNilButNotBoth(gwResp.LocalAerobars[i].O2SavedCart, webResp.LocalAerobars[i].O2SavedCart) {
				mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
					Key:                     wrapperspb.String("LocalAerobars.O2SavedCart"),
					ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.LocalAerobars[i].O2SavedCart)),
					WebResponseValue:        wrapperspb.String(MarshalToString(webResp.LocalAerobars[i].O2SavedCart)),
				})
			} else if gwResp.LocalAerobars[i].O2SavedCart != nil && webResp.LocalAerobars[i].O2SavedCart != nil {
				if len(gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates) != len(webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.RedirectionTemplates"),
						ApiGatewayResponseValue: wrapperspb.String("Length mismatch"),
						WebResponseValue:        wrapperspb.String("Length mismatch"),
					})
				} else {
					for j := range gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates {
						if gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Type != webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Type {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.RedirectionTemplates.Type"),
								ApiGatewayResponseValue: wrapperspb.String(gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Type),
								WebResponseValue:        wrapperspb.String(webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Type),
							})
						}
						if gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].RedirectTo != webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].RedirectTo {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.RedirectionTemplates.RedirectTo"),
								ApiGatewayResponseValue: wrapperspb.String(gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].RedirectTo),
								WebResponseValue:        wrapperspb.String(webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].RedirectTo),
							})
						}
						if gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].CtaRedirectTo != webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].CtaRedirectTo {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.RedirectionTemplates.CtaRedirectTo"),
								ApiGatewayResponseValue: wrapperspb.String(gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].CtaRedirectTo),
								WebResponseValue:        wrapperspb.String(webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].CtaRedirectTo),
							})
						}

						if gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params != nil || webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params != nil {
							if gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params == nil || webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params == nil {
								mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
									Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.RedirectionTemplates.Params"),
									ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params)),
									WebResponseValue:        wrapperspb.String(MarshalToString(webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params)),
								})
								continue
							}

							if gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.CartType != webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.CartType {
								mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
									Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.RedirectionTemplates.Params.CartType"),
									ApiGatewayResponseValue: wrapperspb.String(gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.CartType),
									WebResponseValue:        wrapperspb.String(webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.CartType),
								})
							}
							if gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.CartConfigMode != webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.CartConfigMode {
								mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
									Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.RedirectionTemplates.Params.CartConfigMode"),
									ApiGatewayResponseValue: wrapperspb.String(gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.CartConfigMode),
									WebResponseValue:        wrapperspb.String(webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.CartConfigMode),
								})
							}
							if gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.SearchSource != webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.SearchSource {
								mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
									Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.RedirectionTemplates.Params.SearchSource"),
									ApiGatewayResponseValue: wrapperspb.String(gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.SearchSource),
									WebResponseValue:        wrapperspb.String(webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.SearchSource),
								})
							}
							if gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.ShouldOpenMenu != webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.ShouldOpenMenu {
								mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
									Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.RedirectionTemplates.Params.ShouldOpenMenu"),
									ApiGatewayResponseValue: wrapperspb.String(utils.ConvertBoolToString(gwResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.ShouldOpenMenu)),
									WebResponseValue:        wrapperspb.String(utils.ConvertBoolToString(webResp.LocalAerobars[i].O2SavedCart.RedirectionTemplates[j].Params.ShouldOpenMenu)),
								})
							}
						}
					}
				}
				if len(gwResp.LocalAerobars[i].O2SavedCart.GroupOrders) != len(webResp.LocalAerobars[i].O2SavedCart.GroupOrders) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.GroupOrders"),
						ApiGatewayResponseValue: wrapperspb.String("Length mismatch"),
						WebResponseValue:        wrapperspb.String("Length mismatch"),
					})
				} else {
					for j := range gwResp.LocalAerobars[i].O2SavedCart.GroupOrders {
						if gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].GroupOrderID != webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].GroupOrderID {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.GroupOrders.GroupOrderID"),
								ApiGatewayResponseValue: wrapperspb.String(gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].GroupOrderID),
								WebResponseValue:        wrapperspb.String(webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].GroupOrderID),
							})
						}
						if gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].RestaurantID != webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].RestaurantID {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.GroupOrders.RestaurantID"),
								ApiGatewayResponseValue: wrapperspb.String(gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].RestaurantID),
								WebResponseValue:        wrapperspb.String(webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].RestaurantID),
							})
						}

						// Add more field comparisons for GroupOrders as needed
						if gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].ShouldRemoveAerobar != webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].ShouldRemoveAerobar {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.GroupOrders.GroupOrderStatus"),
								ApiGatewayResponseValue: wrapperspb.String(utils.ConvertBoolToString(gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].ShouldRemoveAerobar)),
								WebResponseValue:        wrapperspb.String(utils.ConvertBoolToString(webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].ShouldRemoveAerobar)),
							})
						}

						if gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData != webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData {
							if gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData == nil || webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData == nil {
								mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
									Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.GroupOrders.MqttData"),
									ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData)),
									WebResponseValue:        wrapperspb.String(MarshalToString(webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData)),
								})
							} else {
								if gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Type != webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Type {
									mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
										Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.GroupOrders.MqttData.Type"),
										ApiGatewayResponseValue: wrapperspb.String(gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Type),
										WebResponseValue:        wrapperspb.String(webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Type),
									})
								}
								if HashStruct(gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Name) != HashStruct(webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Name) {
									mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
										Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.GroupOrders.MqttData.Name"),
										ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Name)),
										WebResponseValue:        wrapperspb.String(MarshalToString(webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Name)),
									})
								}
								if gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Qos != webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Qos {
									mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
										Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.GroupOrders.MqttData.Value"),
										ApiGatewayResponseValue: wrapperspb.String(utils.ConvertInt64ToString(int64(gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Qos))),
										WebResponseValue:        wrapperspb.String(utils.ConvertInt64ToString(int64(webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Qos))),
									})
								}
								if HashStruct(gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Client) != HashStruct(webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Client) {
									mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
										Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.GroupOrders.MqttData.Client"),
										ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Client)),
										WebResponseValue:        wrapperspb.String(MarshalToString(webResp.LocalAerobars[i].O2SavedCart.GroupOrders[j].MqttData.Client)),
									})
								}
							}
						}
					}
				}

				if isEitherNilButNotBoth(gwResp.LocalAerobars[i].O2SavedCart.MROConfig, webResp.LocalAerobars[i].O2SavedCart.MROConfig) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.MROConfig"),
						ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.LocalAerobars[i].O2SavedCart.MROConfig)),
						WebResponseValue:        wrapperspb.String(MarshalToString(webResp.LocalAerobars[i].O2SavedCart.MROConfig)),
					})
				} else if gwResp.LocalAerobars[i].O2SavedCart.MROConfig != nil && webResp.LocalAerobars[i].O2SavedCart.MROConfig != nil {
					gwConfig := gwResp.LocalAerobars[i].O2SavedCart.MROConfig
					webConfig := webResp.LocalAerobars[i].O2SavedCart.MROConfig
					if isEitherNilButNotBoth(gwConfig.CheckoutConfig, webConfig.CheckoutConfig) {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.MROConfig.CheckoutConfig"),
							ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwConfig.CheckoutConfig)),
							WebResponseValue:        wrapperspb.String(MarshalToString(webConfig.CheckoutConfig)),
						})
					} else if gwConfig.CheckoutConfig != nil && webConfig.CheckoutConfig != nil {
						if len(gwConfig.CheckoutConfig.MroConfigStates) != len(webConfig.CheckoutConfig.MroConfigStates) {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.MROConfig.CheckoutConfig.MroConfigStates"),
								ApiGatewayResponseValue: wrapperspb.String("Length mismatch: " + strconv.Itoa(len(gwConfig.CheckoutConfig.MroConfigStates))),
								WebResponseValue:        wrapperspb.String("Length mismatch: " + strconv.Itoa(len(webConfig.CheckoutConfig.MroConfigStates))),
							})
						} else {
							for j := range gwConfig.CheckoutConfig.MroConfigStates {
								if HashStruct(gwConfig.CheckoutConfig.MroConfigStates[j]) != HashStruct(webConfig.CheckoutConfig.MroConfigStates[j]) {
									mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
										Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.MROConfig.CheckoutConfig.MroConfigStates"),
										ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwConfig.CheckoutConfig.MroConfigStates[j])),
										WebResponseValue:        wrapperspb.String(MarshalToString(webConfig.CheckoutConfig.MroConfigStates[j])),
									})
								}
							}
						}

						if HashStruct(gwConfig.EducationInfo) != HashStruct(webConfig.EducationInfo) {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String("LocalAerobars.O2SavedCart.MROConfig.EducationInfo"),
								ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwConfig.EducationInfo)),
								WebResponseValue:        wrapperspb.String(MarshalToString(webConfig.EducationInfo)),
							})
						}
					}
				}
			}
		}
	}

	if isEitherNilButNotBoth(gwResp.ConfigData, webResp.ConfigData) {
		mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
			Key:                     wrapperspb.String("ConfigData"),
			ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.ConfigData)),
			WebResponseValue:        wrapperspb.String(MarshalToString(webResp.ConfigData)),
		})
	} else if gwResp.ConfigData != nil && webResp.ConfigData != nil {
		if gwResp.ConfigData.BgToFgThreshold != webResp.ConfigData.BgToFgThreshold {
			mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
				Key:                     wrapperspb.String("ConfigData.BgToFgThreshold"),
				ApiGatewayResponseValue: wrapperspb.String(utils.ConvertInt64ToString(gwResp.ConfigData.BgToFgThreshold)),
				WebResponseValue:        wrapperspb.String(utils.ConvertInt64ToString(webResp.ConfigData.BgToFgThreshold)),
			})
		}
		if isEitherNilButNotBoth(gwResp.ConfigData.UIConfig, webResp.ConfigData.UIConfig) {
			mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
				Key:                     wrapperspb.String("ConfigData.UIConfig"),
				ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.ConfigData.UIConfig)),
				WebResponseValue:        wrapperspb.String(MarshalToString(webResp.ConfigData.UIConfig)),
			})
		} else if gwResp.ConfigData.UIConfig != nil && webResp.ConfigData.UIConfig != nil {
			if isEitherNilButNotBoth(gwResp.ConfigData.UIConfig.LayoutConfig, webResp.ConfigData.UIConfig.LayoutConfig) {
				mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
					Key:                     wrapperspb.String("ConfigData.UIConfig.LayoutConfig"),
					ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.ConfigData.UIConfig.LayoutConfig)),
					WebResponseValue:        wrapperspb.String(MarshalToString(webResp.ConfigData.UIConfig.LayoutConfig)),
				})
			} else if gwResp.ConfigData.UIConfig.LayoutConfig != nil && webResp.ConfigData.UIConfig.LayoutConfig != nil {
				if HashStruct(gwResp.ConfigData.UIConfig.LayoutConfig) != HashStruct(webResp.ConfigData.UIConfig.LayoutConfig) {
					mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
						Key:                     wrapperspb.String("ConfigData.UIConfig.LayoutConfig"),
						ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwResp.ConfigData.UIConfig.LayoutConfig)),
						WebResponseValue:        wrapperspb.String(MarshalToString(webResp.ConfigData.UIConfig.LayoutConfig)),
					})
				}
			}
		}

		webStateConfigMap := make(map[string]*aerobarmodel.StateConfig)
		matchedWebStateConfigs := make(map[string]bool)

		for _, webStateConfig := range webResp.ConfigData.StateConfig {
			if webStateConfig != nil && webStateConfig.AerobarID != "" {
				webStateConfigMap[webStateConfig.AerobarID] = webStateConfig
			}
		}
		for _, gwStateConfig := range gwResp.ConfigData.StateConfig {
			if gwStateConfig == nil {
				continue
			}
			// Matching webResp by AerobarID
			webStateConfig, found := webStateConfigMap[gwStateConfig.AerobarID]
			if !found {
				mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
					Key:                     wrapperspb.String(fmt.Sprintf("ConfigData.StateConfig.AerobarID=%s", gwStateConfig.AerobarID)),
					ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwStateConfig)),
					WebResponseValue:        wrapperspb.String("Not found"),
				})
				continue
			}
			matchedWebStateConfigs[gwStateConfig.AerobarID] = true
			if len(gwStateConfig.States) != len(webStateConfig.States) {
				mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
					Key:                     wrapperspb.String(fmt.Sprintf("ConfigData.StateConfig[AerobarID=%s].States.Length", gwStateConfig.AerobarID)),
					ApiGatewayResponseValue: wrapperspb.String(utils.ConvertInt64ToString(int64(len(gwStateConfig.States)))),
					WebResponseValue:        wrapperspb.String(utils.ConvertInt64ToString(int64(len(webStateConfig.States)))),
				})
			} else {
				for j := 0; j < len(gwStateConfig.States); j++ {
					if gwStateConfig.States[j] == nil || webStateConfig.States[j] == nil {
						if isEitherNilButNotBoth(gwStateConfig.States[j], webStateConfig.States[j]) {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String(fmt.Sprintf("ConfigData.StateConfig[AerobarID=%s].States[%d]", gwStateConfig.AerobarID, j)),
								ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwStateConfig.States[j])),
								WebResponseValue:        wrapperspb.String(MarshalToString(webStateConfig.States[j])),
							})
						}
						continue
					}
					if gwStateConfig.States[j].State != webStateConfig.States[j].State {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String(fmt.Sprintf("ConfigData.StateConfig[AerobarID=%s].States[%d].State", gwStateConfig.AerobarID, j)),
							ApiGatewayResponseValue: wrapperspb.String(gwStateConfig.States[j].State),
							WebResponseValue:        wrapperspb.String(webStateConfig.States[j].State),
						})
					}
					// Compare Data
					gwData := gwStateConfig.States[j].Data
					webData := webStateConfig.States[j].Data
					if (gwData == nil && webData != nil) || (gwData != nil && webData == nil) {
						mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
							Key:                     wrapperspb.String(fmt.Sprintf("ConfigData.StateConfig[AerobarID=%s].States[%d].Data", gwStateConfig.AerobarID, j)),
							ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwData)),
							WebResponseValue:        wrapperspb.String(MarshalToString(webData)),
						})
					} else if gwData != nil && webData != nil {
						if HashStruct(gwData) != HashStruct(webData) {
							mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
								Key:                     wrapperspb.String(fmt.Sprintf("ConfigData.StateConfig[AerobarID=%s].States[%d].Data", gwStateConfig.AerobarID, j)),
								ApiGatewayResponseValue: wrapperspb.String(MarshalToString(gwData)),
								WebResponseValue:        wrapperspb.String(MarshalToString(webData)),
							})
						}
					}
				}
			}
		}
		// Report any webResp StateConfig items that weren't matched
		for _, webStateConfig := range webResp.ConfigData.StateConfig {
			if webStateConfig != nil && webStateConfig.AerobarID != "" && !matchedWebStateConfigs[webStateConfig.AerobarID] {
				mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
					Key:                     wrapperspb.String(fmt.Sprintf("ConfigData.StateConfig.AerobarID=%s", webStateConfig.AerobarID)),
					ApiGatewayResponseValue: wrapperspb.String("Not found"),
					WebResponseValue:        wrapperspb.String(MarshalToString(webStateConfig)),
				})
			}
		}
	}

	if gwResp.IsMultiSavedCartAerobarEnabled != webResp.IsMultiSavedCartAerobarEnabled {
		mismatchEventArray = append(mismatchEventArray, &events.AerobarMismatchEvent{
			Key:                     wrapperspb.String("IsMultiSavedCartAerobarEnabled"),
			ApiGatewayResponseValue: wrapperspb.String(utils.ConvertBoolToString(gwResp.IsMultiSavedCartAerobarEnabled)),
			WebResponseValue:        wrapperspb.String(utils.ConvertBoolToString(webResp.IsMultiSavedCartAerobarEnabled)),
		})
	}

	return mismatchEventArray
}

// DereferenceString safely dereferences a string pointer. If the pointer is nil, it returns an empty string.
func DereferenceString(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// MarshalToString marshals an object to a JSON string. If marshalling fails, it returns an empty string.
func MarshalToString(v interface{}) string {
	data, err := json.Marshal(v)
	if err != nil {
		logger.Error("[Aerobar] Failed to marshal object to string", err)
		return ""
	}
	return string(data)
}

// HashStruct generates a hash for a given struct by serializing it to JSON and computing its MD5 hash.
func HashStruct(v interface{}) string {
	data, err := json.Marshal(v)
	if err != nil {
		logger.Error("[Aerobar] Failed to marshal struct to JSON", err)
		return ""
	}
	return fmt.Sprintf("%x", md5.Sum(data))
}

func HashStructNormalized(s string) string {
	var normalized interface{}
	err := json.Unmarshal([]byte(s), &normalized)
	if err != nil {
		logger.Error("[Aerobar] Failed to unmarshal JSON string", err)
		return ""
	}

	normalizedData, err := json.Marshal(normalized)
	if err != nil {
		logger.Error("[Aerobar] Failed to marshal normalized struct to JSON", err)
		return ""
	}
	return fmt.Sprintf("%x", md5.Sum(normalizedData))
}

func isEitherNilButNotBoth(a interface{}, b interface{}) bool {
	if a == nil && b == nil {
		return false
	}
	if a == nil || b == nil {
		return true
	}
	return false
}

func normalizelink(s string) string {
    return strings.ReplaceAll(s, "&amp;", "&")
}

func cleanLink(s string) string {
    if unquoted, err := strconv.Unquote(`"` + s + `"`); err == nil {
        s = unquoted
    }
    // Remove all whitespace, tabs, and newlines
    s = strings.ReplaceAll(s, " ", "")
    s = strings.ReplaceAll(s, "\t", "")
    s = strings.ReplaceAll(s, "\n", "")
    s = strings.ReplaceAll(s, "\r", "")
    return s
}
