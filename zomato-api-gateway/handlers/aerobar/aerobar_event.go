package aerobar

import (
	"github.com/gin-gonic/gin"

	"github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/pkg/datadog"
	"github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/aerobar"
)

type AerobarEvent struct {
	event *aerobar.AerobarEvent
}

func NewAerobarEvent() *AerobarEvent {
	return &AerobarEvent{}
}

func (m *AerobarEvent) SetEvent(event *aerobar.AerobarEvent) {
	m.event = event
}

func (m *AerobarEvent) Send(c *gin.Context) {
	if !config.GetBool(c, "aerobar_service.aerobar_event_enqueuer.enabled") {
		return
	}

	defer datadog.StartSegmentWithContext(c, "handlers.aerobar.events.send_aerobar_event").Finish()

	event := m.event

	logger.Debugf("[Aerobar Event] event data: %+v", event)
	err := enqueueMessage(c, event)
	if err != nil {
		logger.WithError(err).Error("failed to enqueue aerobar event")
		datadog.NoticeError(c, err)
	}

	logger.Debugf("pushed aerobar event")
}
