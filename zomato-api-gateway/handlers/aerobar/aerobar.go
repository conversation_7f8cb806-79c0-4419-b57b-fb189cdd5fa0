package aerobar

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"html"
	"time"

	"github.com/Zomato/go/config"
	"github.com/Zomato/go/i18n"
	"github.com/Zomato/go/tracer"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/array"
	"github.com/Zomato/zomato-api-gateway/internal/dark_mode"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	ps "github.com/Zomato/zomato-api-gateway/internal/profile_store_service"
	"github.com/Zomato/zomato-api-gateway/internal/user"
	"github.com/Zomato/zomato-api-gateway/internal/utils"
	"github.com/Zomato/zomato-api-gateway/internal/webclient"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/Zomato/zomato-api-gateway/pkg/datadog"
	"github.com/Zomato/zomato-api-gateway/pkg/jumbo_v2"

	aerobarProto "github.com/Zomato/aerobar-service-client-golang/aerobar"
	"github.com/Zomato/go/logger"
	jumbo_event "github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/event"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/aerobarservice/aerobarservicemigrationevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/reviews/reviewtrackingevents"

	aerobarService "github.com/Zomato/zomato-api-gateway/internal/aerobar"
	"github.com/Zomato/zomato-api-gateway/internal/live_order_service/service/common"
	"github.com/Zomato/zomato-api-gateway/internal/live_order_service/service/order"
	"github.com/Zomato/zomato-api-gateway/internal/live_order_service/service/request"
	"github.com/Zomato/zomato-api-gateway/internal/live_order_service/service/response"
	aerobarmodel "github.com/Zomato/zomato-api-gateway/models/aerobar"
	"github.com/Zomato/zomato-api-gateway/models/sushi"
	"github.com/gin-gonic/gin"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

var o2Templates = []string{templateTypeO2, templateTypeCalling}

var reqHeadersForOldAPI = []string{
	"x-appsflyer-uid",
	"x-present-lat",
	"x-perf-class",
	"x-user-defined-lat",
	"x-bluetooth-on",
	"x-jumbo-session-id",
	"user-agent",
	"x-device-language",
	"x-disable-non-image-rich-media",
	"x-rider-installed",
	"x-district-installed",
	"x-zomato-client-id",
	"x-present-long",
	"x-client-id",
	"x-network-type",
	"x-zomato-uuid",
	"x-app-language",
	"x-firebase-instance-id",
	"x-device-pixel-ratio",
	"x-o2-city-id",
	"x-android-id",
	"x-zomato-app-version-code",
	"accept",
	"x-present-horizontal-accuracy",
	"x-request-id",
	"x-zomato-app-version",
	"x-zomato-beta-app-version",
	"x-city-id",
	"x-device-width",
	"pragma",
	"x-zomato-access-token",
	"x-vpn-active",
	"x-device-height",
	"x-user-defined-long",
	"x-installer-package-name",
	"x-blinkit-installed",
	"x-accessibility-dynamic-text-scale-factor",
	"x-zomato-api-key",
	"x-dv-token",
	"x-zomato-is-metric",
	"user-bucket",
	"user-high-priority",
	"is-akamai-video-optimisation-enabled",
	"x-app-theme",
	"x-app-appearance",
	"x-system-appearance",
	"x-location-token",
	"x-accessibility-voice-over-enabled",
	"cookie",
	"forceserver",
}

func GetAerobars(c *gin.Context) {
	if isGetAerobarsFromGatewayEnabledForUser(c) {
		zomatoClient := api.GetClientFromContext(c)
		userID := zomatoClient.UserID()
		log := logger.FromContext(c).WithFields(map[string]interface{}{
			"user_id": userID,
		})
		log.Debugf("[Aerobar] GetAerobar called from gateway")

		aerobarResp, err := GetAerobarsFromGW(c)
		if err != nil {
			log.Errorf("[Aerobar] Error in getting aerobar response: %v", err)
			c.JSON(http.StatusInternalServerError,
				models.StatusFailed("Internal server error"))
			return
		}

		c.JSON(http.StatusOK, aerobarResp)
		return
	}

	webResponse, err := GetAerobarResponseFromWeb(c)
	log.Debugf("[Aerobar] - web response: %v", webResponse)
	if err != nil {
		log.WithError(err).Error("[Aerobar] - error in fetch web response")
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(c, "something-went-wrong")),
		)
		return
	}

	c.JSON(http.StatusOK, webResponse)
}

func GetAerobarsFromGW(c *gin.Context) (*aerobarmodel.AerobarResponse, error) {
	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()

	log := logger.FromContext(c).WithFields(map[string]interface{}{
		"user_id": userID,
	})
	log.Debugf("[Aerobar] GetAerobar called from gateway")

	aerobarReq := setAerobarModelData(c)
	if aerobarReq == nil {
		log.Errorf("[Aerobar] Error in getting aerobar request data")
		return nil, errors.New("aerobar request is nil")
	}

	aerobarResp, err := aerobarReq.GetAerobarDetails(c)
	if err != nil {
		log.Errorf("[Aerobar] Error in getting aerobar response: %v", err)
		return nil, err
	}

	if aerobarResp == nil {
		log.Errorf("[Aerobar] Error in getting aerobar response: %v", err)
		return nil, errors.New("aerobar response is nil")
	}

	return aerobarResp, nil
}

func GetAerobarResponseFromWeb(c *gin.Context) (interface{}, error) {
	req := webclient.NewZomatoWebRequest(c, http.MethodGet, config.GetString(c, "aerobar_service.get_aerobars_from_web.web_endpoint"))
	for _, header := range reqHeadersForOldAPI {
		headerValue := c.GetHeader(header)
		if !isBlank(headerValue) {
			req.SetHeader(header, headerValue)
		}
	}

	req.SetHeader(aerobarCallSource, "api-internal")

	req.SetRawQuery(c.Request.URL.RawQuery)
	log.Debugf("[Aerobar] - request: %+v", *req)

	timeout := config.GetDuration(c, getAerobarsFromWebTimeout)
	response, err := req.ExecuteWithTimeout(c, timeout)
	if err != nil {
		log.WithError(err).Error("[Aerobar] Failed to fetch response from web")
		return nil, err
	}

	var aerobarJsonResponse interface{}
	if err = json.Unmarshal(response.GetResponseBody(), &aerobarJsonResponse); err != nil {
		log.WithError(err).Error("[Aerobar] Failed to unmarshal aerobar response from web")
		return nil, err
	}

	log.Debugf("[Aerobar] - aerobarJsonResponse: %+v", aerobarJsonResponse)

	if isJumboLoggingEnabledForUser(c) {
		zomatoClient := api.GetClientFromContext(c)
		userID := zomatoClient.UserID()

		cityIdStr := c.Query(cityIDKey)
		cityId := int64(0)
		if !utils.IsEmptyString(cityIdStr) {
			cityId, err = utils.ConvertStringToInt64(cityIdStr)
			if err != nil {
				log.WithError(err).Errorf("[Aerobar] error while parsing city id, received: %s", cityIdStr)
			}
		}

		log := logger.FromContext(c).WithFields(map[string]interface{}{
			"user_id": userID,
		})

		var webJsonResp *aerobarmodel.AerobarResponse
		if err = json.Unmarshal(response.GetResponseBody(), &webJsonResp); err != nil {
			log.WithError(err).Error("[Aerobar] Failed to unmarshal aerobar response from web into Aerobar Response model")
		} else {
			log.Debugf("[Aerobar] GetAerobar called from gateway")
			aerobarResp, err := GetAerobarsFromGW(c)
			if err != nil {
				log.Errorf("[Aerobar] Error in getting aerobar response: %v", err)
			}

			// log jumbo event
			mismatchEvents := LogAerobarMismatch(c, aerobarResp, webJsonResp)
			if len(mismatchEvents) > 0 && cityId > 0 {
				for _, item := range mismatchEvents {
					if item == nil {
						continue
					}
					log.Debugf("[Aerobar] [Jumbo] - aerobar mismatch: %+v", *item)
				}
				webResponse, err := json.Marshal(webJsonResp)
				if err != nil {
					log.WithError(err).Error("[Aerobar] Failed to send to jumbo, error in marshalling web response")
				}
				gatewayResponse, err := json.Marshal(aerobarResp)
				if err != nil {
					log.WithError(err).Error("[Aerobar] Failed to send to jumbo, error in marshalling gateway response")
				}
				jumboPayload := &aerobarservicemigrationevents.AerobarServiceMigrationEvents{
					EventName:             aerobarservicemigrationevents.EventName_AEROBAR_MISMATCH_LOGGING,
					StatusCode:            wrapperspb.Int32(int32(response.StatusCode)),
					Request:               wrapperspb.String(c.Request.URL.RawQuery),
					WebResponse:           wrapperspb.String(string(webResponse)),
					ApiGatewayResponse:    wrapperspb.String(string(gatewayResponse)),
					AerobarMismatchEvents: mismatchEvents,
				}
				sendToJumbo(c, jumboPayload)
			}
		}
	}

	return aerobarJsonResponse, err
}

func sendToJumbo(c *gin.Context, item *aerobarservicemigrationevents.AerobarServiceMigrationEvents) {
	zomatoClient := api.GetClientFromContext(c)
	log := logger.FromContext(c).WithField("user_id", zomatoClient.UserID())

	if item == nil {
		return
	}

	var (
		source = jumbo_event.Source_android
		url    string
	)

	if zomatoClient.IsIOSConsumer() {
		source = jumbo_event.Source_ios
	}

	if c.Request != nil && c.Request.URL != nil {
		url = c.Request.URL.RequestURI()
	}

	jumboHeader := jumbo_v2.NewHeader(
		c,
		source,
		zomatoClient.UUID(),
		0,
	)

	err := jumbo_v2.SendEvent(c, item, jumboHeader, url)
	if err != nil {
		log.WithError(err).Errorf("[Aerobar] error in sending log to jumbo v2:")
	}
}

func isJumboLoggingEnabledForUser(c *gin.Context) bool {
	if !config.GetBool(c, "aerobar_service.get_aerobars_from_web.jumbo_logging.enabled") {
		return false
	}

	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()

	if !config.GetBool(c, isGetAerobarsFromWebEnabled) {
		return true
	}

	enabledUserPercentage := config.GetInt64(c, "aerobar_service.get_aerobars_from_web.jumbo_logging.enabled_user_percentage")
	if enabledUserPercentage == 100 {
		return true
	}

	if userID == 0 {
		return config.GetBool(c, "aerobar_service.get_aerobars_from_web.jumbo_logging.enabled_for_guest_user_ids")
	}

	user, err := user.FetchUserProfileFromContext(c, userID)
	if err != nil {
		datadog.NoticeError(c, err)
		logger.FromContext(c).WithError(err).Error("[Aerobar] error while fetching user profile")
		return false
	}

	enabledUserIDs := config.GetIntSlice(c, "aerobar_service.get_aerobars_from_web.jumbo_logging.enabled_user_ids")
	if array.ContainsInt(enabledUserIDs, int(userID)) {
		return true
	}

	enabledForEternals := config.GetBool(c, "aerobar_service.get_aerobars_from_web.jumbo_logging.enabled_for_eternals")
	if enabledForEternals && (user.GetIsEternal() || user.GetIsZoman()) {
		return true
	}

	enabledUserModFactor := config.GetInt64(c, "aerobar_service.get_aerobars_from_gateway.enabled_user_mod_factor")
	modValue := (userID * enabledUserModFactor) % 100
	return modValue < enabledUserPercentage
}

func (req *AerobarModel) GetTextLabelString(c *gin.Context, textLabel *aerobarProto.TextLabel, defaultValue string) string {
	if textLabel != nil && !utils.IsEmptyString(textLabel.GetKey()) {
		values := textLabel.GetValues()
		finalValues := make([]string, len(values))
		for i, val := range values {
			finalValues[i] = val
		}
		textLabelKey := textLabel.GetKey()
		if req.IsPureVeg {
			switch textLabelKey {
			case "zm-online-ordering-aerobar-order-being-prepared-cod-subtitle-text":
				textLabelKey = "zm-online-ordering-aerobar-order-being-prepared-cod-subtitle-text-green"
				if req.IsDarkModeEnabled {
					textLabelKey = "zm-online-ordering-aerobar-order-prepared-cod-subtitle-dark-mode-text"
				}
			case "zm-online-ordering-aerobar-on-the-way-cod-subtitle-text":
				textLabelKey = "zm-online-ordering-aerobar-on-the-way-cod-subtitle-text-green"
				if req.IsDarkModeEnabled {
					textLabelKey = "zm-online-ordering-aerobar-on-the-way-cod-subtitle-dark-mode-text"
				}
			}
		} else {
			switch textLabelKey {
			case "zm-online-ordering-aerobar-order-being-prepared-cod-subtitle-text":
				if req.IsDarkModeEnabled {
					textLabelKey = "zm-online-ordering-aerobar-order-prepared-cod-subtitle-dark-mode-text"
				}
			case "zm-online-ordering-aerobar-on-the-way-cod-subtitle-text":
				if req.IsDarkModeEnabled {
					textLabelKey = "zm-online-ordering-aerobar-on-the-way-cod-subtitle-dark-mode-text"
				}
			}
		}
		return i18n.Translate(c, textLabelKey, finalValues...)
	}
	return defaultValue
}

func (req *AerobarModel) GetAerobarDetails(c *gin.Context) (*aerobarmodel.AerobarResponse, error) {
	if req == nil {
		return nil, errors.New("aerobar request is nil")
	}

	respFromService, err := req.getAerobarsFromService(c)
	if err != nil {
		return nil, err
	}

	resp, err := req.prepareAerobarDetails(c, respFromService)
	if err != nil {
		return nil, err
	}

	// add local config function here
	localAerobarResponse := req.getLocalAerobars(c)

	// set values for subscriber channel
	aerobarResponse := &aerobarmodel.AerobarResponse{
		Data:                  resp,
		SubscriberChannelType: channelTypeAerobar,
		ServerTimestamp:       time.Now().Unix(),
		SubscriberChannelName: []string{
			getUpdateChannelName(req.UserID, req.GuestUserID),
			getReplaceChannelName(req.UserID, req.GuestUserID),
		},
		SubscriberChannel: getSubscriberChannelName(
			c, getUpdateChannelName(req.UserID, req.GuestUserID),
			getReplaceChannelName(req.UserID, req.GuestUserID),
		),
		LocalAerobars:                  localAerobarResponse,
		IsMultiSavedCartAerobarEnabled: true,
		ConfigData:                     getAerobarConfigData(c, req.IsDarkModeEnabled, resp),
	}

	aerobarResponse.Status = models.Status{
		Status:  "success",
		Message: "successful",
	}

	return aerobarResponse, nil
}

func getUpdateChannelName(userID int64, guestUserID string) string {
	if userID != 0 {
		return fmt.Sprintf(channelAerobar, getEncryptedChannelID(utils.ConvertInt64ToString(userID)))
	}
	return fmt.Sprintf(channelAerobar, getEncryptedChannelID(guestUserID))
}

func getReplaceChannelName(userID int64, guestUserID string) string {
	if userID != 0 {
		return fmt.Sprintf(channelAerobarReplace, getEncryptedChannelID(utils.ConvertInt64ToString(userID)))
	}
	return fmt.Sprintf(channelAerobarReplace, getEncryptedChannelID(guestUserID))
}

func getEncryptedChannelID(id string) string {
	return fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf(channelHashSalt, id))))
}

func getSubscriberChannelName(c *gin.Context, channel, channel2 string) *aerobarmodel.SubscriberChannel {
	return &aerobarmodel.SubscriberChannel{
		Type:   channelTypeAerobar,
		Name:   []string{channel, channel2},
		Qos:    0,
		Time:   time.Now().Unix() - subscriberChannelTimeDiff,
		Client: getClientSettings(c),
	}
}

func getClientSettings(c *gin.Context) aerobarmodel.Client {
	return aerobarmodel.Client{
		Username:  config.GetString(c, "aerobar_service.mqtt_data.username"),
		Password:  config.GetString(c, "aerobar_service.mqtt_data.password"),
		Keepalive: config.GetInt(c, "aerobar_service.mqtt_data.keepalive"),
	}
}

func (req *AerobarModel) getAerobarsFromService(c *gin.Context) (*aerobarProto.GetAerobarDetailsResponse, error) {
	if req == nil {
		return nil, errors.New("aerobar request for service is nil")
	}

	appType := aerobarProto.AppType_APP_TYPE_INVALID
	if req.IsAndroidConsumer {
		appType = aerobarProto.AppType_APP_TYPE_CONSUMER_ANDROID
	} else if req.IsIOSConsumer {
		appType = aerobarProto.AppType_APP_TYPE_CONSUMER_IOS
	}

	if req.AerobarService == nil {
		return nil, errors.New("aerobar service is nil")
	}

	aerobarServiceResponse, err := req.AerobarService.GetAerobarDetails(c, &aerobarProto.GetAerobarDetailsRequest{
		UserId:      req.UserID,
		Uuid:        req.GuestUserID, // uuid on service side = GuestUserID on app side
		Tenant:      aerobarProto.Tenant_TENANT_ZOMATO,
		IsUserZoman: req.IsUserZoman,
		ClientDetails: &aerobarProto.ClientDetails{
			AppType:        appType,
			AppVersion:     req.AppVersion,
			AppBetaVersion: req.AppBetaVersion,
		},
	})

	if err != nil {
		return nil, err
	}

	log := logger.FromContext(c).WithFields(map[string]interface{}{
		"user_id": req.UserID,
	})

	log.Debugf("[Aerobar] - service response: %+v", aerobarServiceResponse)
	return aerobarServiceResponse, nil
}

func (req *AerobarModel) prepareAerobarDetails(c *gin.Context, aerobarServiceResponse *aerobarProto.GetAerobarDetailsResponse) ([]*aerobarmodel.AerobarData, error) {
	aerobarItems := make([]*aerobarmodel.Item, 0)
	if aerobarServiceResponse.Status == true {
		log := logger.FromContext(c).WithFields(map[string]interface{}{
			"user_id": req.UserID,
		})
		for _, aerobarData := range aerobarServiceResponse.GetData() {
			enabledTemplates := config.GetStringSlice(c, "aerobar_service.reads_allowed_templates")
			if !utils.ArrayContains(enabledTemplates, aerobarData.GetTemplateType()) {
				continue
			}

			firstActionText := req.GetTextLabelString(c, aerobarData.GetFirstActionTextLabel(), aerobarData.GetFirstActionText())
			aerobarItem := &aerobarmodel.Item{
				AerobarID:              aerobarData.GetAerobarId(),
				AerobarStatus:          int64(0), // Not used on app side
				Icon:                   aerobarData.GetIcon(),
				Title:                  req.GetTextLabelString(c, aerobarData.GetTitleLabel(), aerobarData.GetTitle()),
				Description:            req.GetTextLabelString(c, aerobarData.GetDescriptionLabel(), aerobarData.GetDescription()),
				FirstDeeplink:          html.UnescapeString(aerobarData.GetFirstDeeplink()),
				FirstActionText:        &firstActionText,
				FirstIsActionIcon:      aerobarData.GetFirstIsActionIcon(),
				SecondDeeplink:         html.UnescapeString(aerobarData.GetSecondDeeplink()),
				SecondActionText:       aerobarData.GetSecondActionText(),
				SecondIsActionIcon:     aerobarData.GetSecondIsActionIcon(),
				IconBottomRightImage:   aerobarData.GetIconBottomRightImage(),
				IsRatingSnippetVisible: aerobarData.GetIsRatingSnippetVisible(),
				MaxShowCount: req.getMaxShowCount(
					aerobarData.GetMaxShowCount().GetTap(),
					aerobarData.GetMaxShowCount().GetImpression(),
					aerobarData.GetAerobarType()),
				CooldownPeriod: req.getCooldown(
					aerobarData.GetCooldownPeriod().GetTap(),
					aerobarData.GetCooldownPeriod().GetImpression(),
					aerobarData.GetAerobarType()),
				TapDeeplink: html.UnescapeString(aerobarData.GetTapDeeplink()),
				Deeplink:    html.UnescapeString(aerobarData.GetDeeplink()),
				LottieURL:   aerobarData.GetLottieUrl(),
				Type:        aerobarData.GetType(),
				AerobarType: req.getAerobarType(aerobarData.GetAerobarType()),
				LeftAnimation: &sushi.Animation{
					URL:         aerobarData.GetLottieUrl(),
					AspectRatio: 1,
				},
				RightContainer: req.getRightContainer(c, aerobarData),
				TrackingData:   req.getTrackingData(aerobarData.GetAerobarId(), req.GetTextLabelString(c, aerobarData.GetTitleLabel(), aerobarData.GetTitle()), aerobarData.GetTemplateType(), nil),
				Cta1:           req.getCtaFlags(aerobarData.GetCta1()),
				Cta2:           req.getCtaFlags(aerobarData.GetCta2()),
				Body:           req.getCtaFlags(aerobarData.GetBody()),
			}

			if aerobarItem.RightContainer != nil && utils.IsEmptyString(aerobarData.GetSubtype()) {
				aerobarItem.FirstActionText = nil
			}

			if req.isReviewsAerobarRevampEnabled(c) && aerobarData.GetIsRatingSnippetVisible() && aerobarData.GetTemplateType() == templateTypeO2 {
				orderDetails := getOrderDetailsFromAerobarID(aerobarData.GetAerobarId())
				orderID := orderDetails.OrderID

				postBody := PostBody{
					TransactionID: strconv.FormatInt(orderID, 10),
					Experience:    o2Delivery,
					Source:        sourceAerobar,
				}
				postBodyStr, err := json.Marshal(postBody)
				if err != nil {
					log.WithError(err).Error("[Aerobar] error while marshalling post body")
				}
				
				log.Debugf("[Aerobar] - Adding rating config")
				aerobarItem.RatingConfig = req.getRatingConfig(c, string(postBodyStr), aerobarData.GetIsRecommendationSnippetVisible(), postBody.TransactionID)
				if !aerobarData.GetIsRecommendationSnippetVisible() {
					log.Debugf(("[Aerobar] - Adding recommendation bottom sheet"))
					aerobarItem.TapClickAction = req.getOpenRatingFormBottomSheetClickAction(string(postBodyStr))
				} else {
					log.Debugf("[Aerobar] - Removing recommendataion bottom sheet")
				}
			}

			if req.IsDarkModeEnabled {
				aerobarItem.LeftImage = getLeftImage(aerobarData)
				aerobarItem.LeftAnimation = getLeftAnimation(aerobarData)
			}

			if aerobarData.GetTopTitle() != nil {
				topTitle := req.getToptitle(c, aerobarData.GetTopTitle(), aerobarData.GetSubtype())
				aerobarItem.TopTitle = topTitle
			}

			aerobarItem.Subtitle = req.getSubtitle(c, aerobarData.GetSubTitle(), aerobarData.GetSubtype(), aerobarData.GetDescriptionLabel(), aerobarData.GetDescription())

			// premium referral aerobar
			log.Debugf("[Aerobar] - aerobarData subtype: %v", aerobarData.GetSubtype())
			aerobarItem.Subtype = aerobarData.GetSubtype()
			if aerobarData.GetSubtype() == aerobarSubtypePremiumReferral {
				aerobarItem.TapClickAction = getTapClickAction(aerobarData)
			}
			aerobarItems = append(aerobarItems, aerobarItem)
		}
	}

	return getAerobarItemBuckets(aerobarItems), nil
}

func getAerobarItemBuckets(aerobarItems []*aerobarmodel.Item) []*aerobarmodel.AerobarData {
	transactionalItems := []*aerobarmodel.Item{}
	contentItems := []*aerobarmodel.Item{}

	for _, item := range aerobarItems {
		switch item.AerobarType {
		case aerobarTypeTransactional:
			transactionalItems = append(transactionalItems, item)
		default:
			contentItems = append(contentItems, item)
		}
	}

	aerobarData := []*aerobarmodel.AerobarData{}

	maxToShowTransactional := len(transactionalItems)
	maxToShowContent := MaxToShowContent

	if maxToShowTransactional > 0 {
		maxToShowContent = int(math.Min(float64(maxToShowContent), 1))
	}

	if len(transactionalItems) > 0 {
		aerobarData = append(aerobarData, &aerobarmodel.AerobarData{
			AerobarType: aerobarTypeTransactional,
			MaxToShow:   maxToShowTransactional,
			Items:       transactionalItems,
		})
	} else {
		aerobarData = append(aerobarData, &aerobarmodel.AerobarData{
			AerobarType: aerobarTypeTransactional,
			MaxToShow:   0,
			Items:       []*aerobarmodel.Item{},
		})
	}

	if len(contentItems) > 0 {
		aerobarData = append(aerobarData, &aerobarmodel.AerobarData{
			AerobarType: aerobarTypeContent,
			MaxToShow:   maxToShowContent,
			Items:       contentItems,
		})
	} else {
		aerobarData = append(aerobarData, &aerobarmodel.AerobarData{
			AerobarType: aerobarTypeContent,
			MaxToShow:   maxToShowContent,
			Items:       []*aerobarmodel.Item{},
		})
	}

	maxToShowPromotional := MaxToShowPromotional
	if len(transactionalItems) > 0 {
		maxToShowPromotional = 0
	}
	aerobarData = append(aerobarData, &aerobarmodel.AerobarData{
		AerobarType: aerobarTypePromotional,
		MaxToShow:   maxToShowPromotional,
		Items:       []*aerobarmodel.Item{},
	})

	return aerobarData
}

func getTapClickAction(aerobarData *aerobarProto.AerobarDetails) *sushi.ClickAction {
	if aerobarData.GetTapClickAction() == nil {
		return nil
	}

	if aerobarData.GetTapClickAction().GetType() != sushi.SuccessActionAPICallOnTap {
		return nil
	}

	var parsedPostBody map[string]interface{}
	postBody := aerobarData.GetTapClickAction().GetApiCallOnTap().GetPostBody()
	if !utils.IsEmptyString(postBody) {
		err := json.Unmarshal([]byte(postBody), &parsedPostBody)
		if err == nil {
			str, err := json.Marshal(parsedPostBody)
			if err == nil {
				postBody = string(str)
			}
		}
	}

	return &sushi.ClickAction{
		Type: sushi.ActionType(aerobarData.GetTapClickAction().GetType()),
		APICallOnTap: &sushi.APICallOnTap{
			URL:                 aerobarData.GetTapClickAction().GetApiCallOnTap().GetUrl(),
			Type:                string(sushi.APITypePOST),
			PostBody:            postBody,
			RequestEncodingType: "application/x-www-form-urlencoded",
		},
	}
}

func getLeftImage(aerobarData *aerobarProto.AerobarDetails) *sushi.Image {
	if aerobarData.GetIconV2() != nil && !utils.IsEmptyString(aerobarData.GetIconV2().GetDarkModeImageUrl()) {
		finalImageIcon := aerobarData.GetIcon()
		if !utils.IsEmptyString(aerobarData.GetIconV2().GetImageUrl()) {
			finalImageIcon = aerobarData.GetIconV2().GetImageUrl()
		}
		finalImage := &sushi.Image{
			URL:         finalImageIcon,
			AspectRatio: 1,
		}
		themeConfigIcon := &sushi.ThemeConfig{
			Theme:         sushi.ThemeDark,
			ComponentType: sushi.ComponentTypeImage,
		}
		themeConfigImage := &sushi.Image{
			URL:         aerobarData.GetIconV2().GetDarkModeImageUrl(),
			AspectRatio: 1,
		}
		themeConfigIcon.Image = themeConfigImage
		finalImage.ThemeConfig = []*sushi.ThemeConfig{themeConfigIcon}
		return finalImage
	}
	return nil
}

func getLeftAnimation(aerobarData *aerobarProto.AerobarDetails) *sushi.Animation {
	if aerobarData.GetLottieV2() != nil && !utils.IsEmptyString(aerobarData.GetLottieV2().GetDarkModeUrl()) {
		finalLottie := aerobarData.GetLottieUrl()
		if !utils.IsEmptyString(aerobarData.GetLottieV2().GetUrl()) {
			finalLottie = aerobarData.GetLottieV2().GetUrl()
		}
		finalAnimation := &sushi.Animation{
			URL:         finalLottie,
			AspectRatio: 1,
		}
		themeConfigIcon := &sushi.ThemeConfig{
			Theme:         sushi.ThemeDark,
			ComponentType: sushi.ComponentTypeAnimation,
		}
		themeConfigAnimation := &sushi.Animation{
			URL:         aerobarData.GetLottieV2().GetDarkModeUrl(),
			AspectRatio: 1,
		}
		themeConfigIcon.Animation = themeConfigAnimation
		finalAnimation.ThemeConfig = []*sushi.ThemeConfig{themeConfigIcon}
		return finalAnimation
	}
	return nil
}

func getOrderDetailsFromAerobarID(aerobarID string) OrderDetails {
	response := OrderDetails{
		Status: false,
	}

	aerobarIDArr := strings.Split(aerobarID, "_")
	if len(aerobarIDArr) < 3 {
		return response
	}

	orderID, err := utils.ConvertStringToInt64(aerobarIDArr[0])
	if err != nil {
		logger.WithField("aerobar_id", aerobarID).Error("[Aerobar] Error in converting string to int64")
	}
	orderStatus, err := utils.ConvertStringToInt64(aerobarIDArr[1])
	if err != nil {
		logger.WithField("aerobar_id", aerobarID).Error("[Aerobar] Error in converting string to int64")
	}
	deliveryStatus, err := utils.ConvertStringToInt64(aerobarIDArr[2])
	if err != nil {
		logger.WithField("aerobar_id", aerobarID).Error("[Aerobar] Error in converting string to int64")
	}

	response.Status = true
	response.OrderID = orderID
	response.OrderStatus = orderStatus
	response.DeliveryStatus = deliveryStatus

	return response
}

func (req *AerobarModel) isReviewsAerobarRevampEnabled(c *gin.Context) bool {
	client := api.GetClientFromContext(c)
	if !config.GetBool(c, "aerobar_service.get_aerobar_details.reviews_revamp_enabled") {
		return false
	}

	if !client.SupportsResRatingV3Flow() {
		return false
	}

	return true
}

func (req *AerobarModel) getAerobarType(aerobarType aerobarProto.AerobarType) string {
	switch aerobarType {
	case aerobarProto.AerobarType_AEROBAR_TYPE_TRANSACTIONAL:
		return aerobarTypeTransactional
	case aerobarProto.AerobarType_AEROBAR_TYPE_CONTENT:
		return aerobarTypeContent
	case aerobarProto.AerobarType_AEROBAR_TYPE_PROMOTIONAL:
		return aerobarTypePromotional
	default:
		return ""
	}
}

func (req *AerobarModel) getCooldown(tapCooldown, impressionCooldown int64, aerobarType aerobarProto.AerobarType) *aerobarmodel.CooldownPeriod {
	coolDownPeriod := &aerobarmodel.CooldownPeriod{}
	switch aerobarType {
	case aerobarProto.AerobarType_AEROBAR_TYPE_CONTENT:
		coolDownPeriod.Tap = tapCooldownPeriodContent
		coolDownPeriod.Impression = impressionCooldownPeriodContent
		if tapCooldown != 0 {
			coolDownPeriod.Tap = tapCooldown
		}
		if impressionCooldown != 0 {
			coolDownPeriod.Impression = impressionCooldown
		}
	case aerobarProto.AerobarType_AEROBAR_TYPE_PROMOTIONAL:
		coolDownPeriod.Tap = tapCooldownPeriodPromotional
		coolDownPeriod.Impression = impressionCooldownPeriodPromotional
		if tapCooldown != 0 {
			coolDownPeriod.Tap = tapCooldown
		}
		if impressionCooldown != 0 {
			coolDownPeriod.Impression = impressionCooldown
		}
	default:
		if tapCooldown == 0 && impressionCooldown == 0 {
			return coolDownPeriod
		}
		return &aerobarmodel.CooldownPeriod{
			Tap:        tapCooldown,
			Impression: impressionCooldown,
		}
	}

	return coolDownPeriod
}

func (req *AerobarModel) getMaxShowCount(tapMaxShow, impressionMaxShow int64, aerobarType aerobarProto.AerobarType) *aerobarmodel.MaxShowCount {
	maxShowCount := &aerobarmodel.MaxShowCount{}
	switch aerobarType {
	case aerobarProto.AerobarType_AEROBAR_TYPE_CONTENT:
		maxShowCount.Tap = tapMaxShowCountContent
		maxShowCount.Impression = impressionMaxShowCountContent
		if tapMaxShow != 0 {
			maxShowCount.Tap = tapMaxShow
		}
		if impressionMaxShow != 0 {
			maxShowCount.Impression = impressionMaxShow
		}
	case aerobarProto.AerobarType_AEROBAR_TYPE_PROMOTIONAL:
		maxShowCount.Tap = tapMaxShowCountPromotional
		maxShowCount.Impression = impressionMaxShowCountPromotional
		if tapMaxShow != 0 {
			maxShowCount.Tap = int64(math.Min(float64(tapMaxShow), float64(tapMaxShowCountPromotional)))
		}

		if impressionMaxShow != 0 {
			maxShowCount.Impression = int64(math.Min(float64(impressionMaxShow), float64(impressionMaxShowCountPromotional)))
		}
	default:
		return &aerobarmodel.MaxShowCount{
			Tap:        tapMaxShow,
			Impression: impressionMaxShow,
		}
	}
	return maxShowCount
}

func (req *AerobarModel) getCtaFlags(cta *aerobarProto.Cta) *aerobarmodel.Cta {
	defaultFalse := false
	if cta == nil {
		return &aerobarmodel.Cta{
			PersistOnTap:   &defaultFalse,
			IgnoreCooldown: &defaultFalse,
			IgnoreMaxCount: &defaultFalse,
		}
	}
	persistOnTap := cta.GetPersistOnTap()
	ignoreCooldown := cta.GetIgnoreCooldown()
	ignoreMaxCount := cta.GetIgnoreMaxCount()
	return &aerobarmodel.Cta{
		PersistOnTap:   &persistOnTap,
		IgnoreCooldown: &ignoreCooldown,
		IgnoreMaxCount: &ignoreMaxCount,
	}
}

func (req *AerobarModel) getToptitle(c *gin.Context, toptitle *aerobarProto.SubTitle, subType string) *sushi.Subtitle {
	if toptitle == nil {
		return nil
	}

	titleText := req.GetTextLabelString(c, toptitle.GetText(), "")
	finalTitle := &sushi.Subtitle{
		Text: titleText,
	}

	if toptitle.GetColor() != nil && !utils.IsEmptyString(toptitle.GetColor().GetType()) {
		titleColor := &sushi.Color{
			Tint: sushi.ColorTint(toptitle.GetColor().GetTint()),
			Type: sushi.ColorType(toptitle.GetColor().GetType()),
		}

		if req.IsDarkModeEnabled {
			token := sushi.TokenColorTextSecondary
			if toptitle.GetColor().GetThemeBucket() != nil && !utils.IsEmptyString(toptitle.GetColor().GetThemeBucket().GetToken()) {
				token = sushi.Token(toptitle.GetColor().GetThemeBucket().GetToken())
			}
			titleColor.ThemeBucket = &sushi.ThemeBucket{
				Token: token,
			}
		}
		finalTitle.Color = titleColor
	}

	if toptitle.GetFont() != nil {
		finalTitle.Font = &sushi.Font{
			Weight: sushi.FontWeight(toptitle.GetFont().GetWeight()),
			Size:   sushi.FontSize(toptitle.GetFont().GetSize()),
		}
	}

	return finalTitle
}

func (req *AerobarModel) getSubtitle(c *gin.Context, subtitle *aerobarProto.SubTitle, subType string, descriptionLabel *aerobarProto.TextLabel, description string) *sushi.Subtitle {
	if subtitle == nil {
		descriptionText := req.GetTextLabelString(c, descriptionLabel, description)
		return &sushi.Subtitle{
			Text: descriptionText,
		}
	}

	subtitleText := req.GetTextLabelString(c, subtitle.GetText(), "")
	finalSubtitle := &sushi.Subtitle{
		Text: subtitleText,
	}

	if !utils.IsEmptyString(subtitle.GetColor().GetType()) {
		finalSubtitle.Color = &sushi.Color{
			Tint: sushi.ColorTint(subtitle.GetColor().GetTint()),
			Type: sushi.ColorType(subtitle.GetColor().GetType()),
		}

		if !utils.IsEmptyString(subType) {
			finalSubtitle.Color.ThemeBucket = &sushi.ThemeBucket{
				Token: sushi.TokenColorTextBrand,
			}
		}
	}

	if !utils.IsEmptyString(subtitle.GetFont().GetSize()) {
		finalSubtitle.Font = &sushi.Font{
			Size:   sushi.FontSize(subtitle.GetFont().GetSize()),
			Weight: sushi.FontWeight(subtitle.GetFont().GetWeight()),
		}
	}

	if subtitle.GetSuffixIcon() != nil {
		finalSubtitle.SuffixIcon = &sushi.Icon{
			Code: sushi.IconCode(subtitle.GetSuffixIcon().GetCode()),
			Size: sushi.IconSize(subtitle.GetSuffixIcon().GetSize()),
		}

		if subtitle.GetSuffixIcon().GetColor() != nil {
			finalSubtitle.SuffixIcon.Color = &sushi.Color{
				Tint: sushi.ColorTint(subtitle.GetSuffixIcon().GetColor().GetTint()),
				Type: sushi.ColorType(subtitle.GetSuffixIcon().GetColor().GetType()),
			}
		}
	}

	finalSubtitle.IsMarkdown = subtitle.GetIsMarkdown()

	if req.IsDarkModeEnabled {
		log := logger.FromContext(c).WithFields(map[string]interface{}{
			"user_id": req.UserID,
		})
		log.Debugf("dark mode enabled")
		if finalSubtitle.SuffixIcon != nil && finalSubtitle.SuffixIcon.Color != nil {
			finalSubtitle.SuffixIcon.Color.ThemeBucket = &sushi.ThemeBucket{
				Token: sushi.TokenColorIconBrandOnlyLight,
			}
			if !utils.IsEmptyString(subType) {
				finalSubtitle.SuffixIcon.Color.ThemeBucket = &sushi.ThemeBucket{
					Token: sushi.TokenColorIconBrand,
				}
			}
		}
	}

	if req.IsPureVeg {
		if finalSubtitle.SuffixIcon != nil && finalSubtitle.SuffixIcon.Color != nil {
			finalSubtitle.SuffixIcon.Color = &sushi.Color{
				Tint: sushi.ColorTint600,
				Type: sushi.ColorGreen,
			}
			themeBucketVeg := &sushi.ThemeBucket{
				Token: sushi.TokenColorIconBrandOnlyLight,
			}
			if !utils.IsEmptyString(subType) {
				themeBucketVeg = &sushi.ThemeBucket{
					Token: sushi.TokenColorIconBrand,
				}
			}
			if req.IsDarkModeEnabled {
				finalSubtitle.SuffixIcon.Color.ThemeBucket = themeBucketVeg
			}
		}
	}

	return finalSubtitle
}

func setAerobarModelData(c *gin.Context) *AerobarModel {
	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()
	guestUserID := zomatoClient.GuestUserID()
	isUserZoman := false
	isPureVeg := false
	log := logger.FromContext(c).WithFields(map[string]interface{}{
		"user_id": userID,
	})
	if userID != 0 {
		userProfile, err := user.FetchUserProfileFromContext(c, userID)
		if err != nil {
			datadog.NoticeError(c, err)
			log.WithError(err).Error("error while fetching user profile")
		} else {
			isUserZoman = userProfile.IsZoman
			isPureVeg = userProfile.IsPureVeg
		}
	}

	isGroupOrder := c.Query(isGroupOrderKey)

	var err error
	isGroupOrderBool := false
	if !utils.IsEmptyString(isGroupOrder) {
		isGroupOrderBool, err = strconv.ParseBool(isGroupOrder)
		if err != nil {
			datadog.NoticeError(c, err)
			log.WithError(err).Errorf("error while parsing group order, received %s", isGroupOrder)
		}
	}

	isVegModeEnabled := utils.IsPureVegThemeEnabled(c)
	isIOSConsumer := zomatoClient.IsIOSConsumer()
	isAndroidConsumer := zomatoClient.IsAndroidConsumer()

	// Create and return the AerobarModel object
	return &AerobarModel{
		UserID:                       userID,
		IsUserZoman:                  isUserZoman,
		IsGroupOrder:                 isGroupOrderBool,
		IsVegModeEnabled:             isVegModeEnabled,
		IsDarkModeEnabled:            dark_mode.IsDarkModeOn(c),
		IsIOSConsumer:                isIOSConsumer,
		IsAndroidConsumer:            isAndroidConsumer,
		AppVersion:                   zomatoClient.Version().String(),
		AppBetaVersion:               zomatoClient.BetaVersion().String(),
		IsPureVeg:                    isPureVeg,
		GuestUserID:                  guestUserID,
		AerobarService:               aerobarService.NewAerobarServiceImpl(),
	}
}

func (req *AerobarModel) getRatingConfig(c *gin.Context, postBodyStr string, isRecommendationAerobarEnabled bool, transactionID string) *aerobarmodel.RatingConfig {
	// convert postbody to string
	ratingChangedClickAction := &sushi.ClickAction{
		Type: sushi.ActionTypeRatingChangedClick,
		RatingChanged: &sushi.RatingChanged{
			URL:      saveRatingAPIUrl,
			PostBody: string(postBodyStr),
		},
	}

	openRatingFormBottomSheet := req.getOpenRatingFormBottomSheetClickAction(string(postBodyStr))
	reviewsTrackingData := GetReviewTrackingEventsTrackingData(
		c,
		"",
		reviewtrackingevents.EName_O2_PRIVATE_RATING_AEROBAR_RATING_TAP.String(),
		"",
		transactionID,
	)

	ratingChangeSecondaryClickAction := &sushi.ClickAction{
		Type: sushi.ActionTypeAddBottomView,
		AddBottomView: &sushi.AddBottomView{
			Button: &sushi.Button{
				TrackingData: GetReviewTrackingEventsTrackingData(
					c,
					"",
					reviewtrackingevents.EName_O2_PRIVATE_RATING_ADD_MORE_FEEDBACK_BUTTON_TAP.String(),
					"",
					transactionID,
				),
				Text: i18n.Translate(c, "zreviews-share-more-feedback-text"),
				Type: sushi.ButtonTypeText,
				SuffixIcon: &sushi.Icon{
					Code: sushi.IconRightTriangle2,
				},
				ClickAction: openRatingFormBottomSheet,
				Size:        sushi.ButtonSizeMedium,
			},
		},
	}

	ratingChangeSecondaryClickActions := []*sushi.ClickAction{
		ratingChangeSecondaryClickAction,
	}

	ratingConfig := &aerobarmodel.RatingConfig{
		RatingChangeClickAction:          ratingChangedClickAction,
		RatingChangeSecondaryClickAction: ratingChangeSecondaryClickActions,
		MaxRatingLottie: &sushi.Animation{
			URL: maxRatingLottie,
		},
		RightButtonAnimationConfig: &sushi.Animation{
			Duration: rightAnimationDuration,
			OutlineColor: &sushi.Color{
				Tint: sushi.ColorTint500,
				Type: sushi.ColorGrey,
			},
		},
		TrackingData: reviewsTrackingData,
	}

	if isRecommendationAerobarEnabled {
		ratingConfig.TopTitle = &sushi.Label{
			Text: i18n.Translate(c, "zm-online-ordering-rating-aerobar-top-title-text"),
		}
	} else {
		ratingConfig.Subtitle = &sushi.Label{
			Text: i18n.Translate(c, "zreviews-aerobar-rating-given-text"),
		}
	}

	return ratingConfig
}

func (req *AerobarModel) getOpenRatingFormBottomSheetClickAction(postBodyStr string) *sushi.ClickAction {
	openRatingFormBottomSheetBGColor := &sushi.Color{
		Tint: sushi.ColorTint050,
		Type: sushi.ColorIndigo,
	}

	if req.IsDarkModeEnabled {
		openRatingFormBottomSheetBGColor.ThemeBucket = &sushi.ThemeBucket{
			Token: sushi.TokenColorBackgroundSecondary,
		}
	}

	return &sushi.ClickAction{
		Type: sushi.ActionTypeOpenRatingFormBottomSheet,
		OpenRatingFormBottomSheet: &sushi.OpenRatingFormBottomSheet{
			APIData: &sushi.APIData{
				URL:      addDetailedFeedbackAPIUrl,
				PostBody: postBodyStr,
			},
			ShowSnippetAsSections: true,
			BgColor:               openRatingFormBottomSheetBGColor,
		},
	}
}

func (req *AerobarModel) getRightContainer(c *gin.Context, aerobarData *aerobarProto.AerobarDetails) *sushi.RightContainer {
	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()

	log := logger.FromContext(c).WithFields(map[string]interface{}{
		"user_id": userID,
	})
	log.Debugf("[Aerobar] Getting right container")
	isDarkModeEnabled := req.IsDarkModeEnabled
	rightContainerFromService := aerobarData.GetRightContainer()
	if rightContainerFromService != nil {
		return req.getServiceRightContainer(c, aerobarData)
	}
	if aerobarData.GetTemplateType() != templateTypeO2 {
		log.Debugf("[Aerobar] Template type is not o2")
		return nil
	}

	orderDetails := getOrderDetailsFromAerobarID(aerobarData.GetAerobarId())
	if orderDetails.Status && orderDetails.OrderStatus == int64(aerobarService.ORDER_STATUS_LINKED) {
		orderID := orderDetails.OrderID
		orderTrackingDetails, err := getOrderTrackingDetails(c, int64(orderID))
		if err != nil {
			log.Errorf("error while fetching order tracking details %v", err)
			return nil
		}
		deliveryETA := orderTrackingDetails.GetSmoothenEta()
		// eta range
		isEtaRangeActive := orderTrackingDetails.GetIsEtaRangeActive()
		etaLowerBound := orderTrackingDetails.GetEtaLowerBound()
		etaUpperBound := orderTrackingDetails.GetEtaUpperBound()
		isOrderArrivingSoon := uint64(orderTrackingDetails.GetRiderArrivingDropAt().GetSeconds()) > 0
		orderStatus := orderTrackingDetails.GetOrderStatus()
		deliveryStatus := orderTrackingDetails.GetDeliveryStatus()
		orderDelayStatus := orderTrackingDetails.GetOrderDelayStatus()
		isOrderPickedByDropWalker := orderTrackingDetails.GetIsOrderPickedByDropWalker()
		isLongKPTOrder := orderTrackingDetails.GetIsLongKptOrder()
		scheduledSlots := getScheduledSlot(orderTrackingDetails.GetScheduledSlot())
		isScheduledOrder := orderTrackingDetails.GetIsScheduledOrder()
		isSingleLegWalkerOrder := orderTrackingDetails.GetIsSingleLegWalkerOrder()
		isTakeawayOrder := orderTrackingDetails.GetIsTakeawayOrder()
		isLogs := orderTrackingDetails.GetIsLogs()
		isTrackable := orderTrackingDetails.GetIsTrackable()
		if deliveryETA < 0 {
			log.Debugf("[Aerobar] Delivery ETA is less than 0")
			return nil
		}

		if isTakeawayOrder {
			log.Debugf("[Aerobar] Order is a takeaway order")
			return nil
		}

		if !isLogs && !isTrackable {
			log.Debugf("[Aerobar] Order is non logs and not trackable")
			return nil
		}

		if !isLogs && isTrackable && orderTrackingDetails.GetSingleOrderStatus() != order.StatusOnTheWay {
			log.Debugf("[Aerobar] Order is non logs and trackable and right container is not required")
			return nil
		}

		containerTitle := getRightContainerTitleText(c, false)
		containerSubtitle := getRightContainerSubtitleText(c, deliveryETA, false, isEtaRangeActive, etaLowerBound, etaUpperBound)

		isOrderPickedUpByWalker := isOrderPickedByDropWalker
		if isSingleLegWalkerOrder && deliveryStatus == int32(aerobarService.ORDER_DELIVERY_STATUS_ON_THE_WAY) {
			isOrderPickedUpByWalker = true
		}

		if isOrderPickedUpByWalker || isOrderArrivingSoon {
			containerTitle = getRightContainerTitleText(c, true)
			containerSubtitle = getRightContainerSubtitleText(c, deliveryETA, true, isEtaRangeActive, etaLowerBound, etaUpperBound)
		}

		if orderStatus == int32(aerobarService.ORDER_STATUS_LINKED) &&
			deliveryStatus != int32(aerobarService.ORDER_DELIVERY_STATUS_ON_THE_WAY) &&
			isLongKPTOrder {
			rightContainer := getContainerContentForLongKPTOrder(c, scheduledSlots)
			containerSubtitle = rightContainer.Subtitle.Text
			containerTitle = rightContainer.Title.Text
		}

		rightContainerStates := getRightContainerStates(isDarkModeEnabled)

		if len(rightContainerStates) < noOfAerobarRightContainerStates {
			log.Debugf("[Aerobar] Right container states are less than %d", noOfAerobarRightContainerStates)
			return nil
		}

		rightContainerState1 := rightContainerStates[0]
		rightContainerState2 := rightContainerStates[1]

		rightContainerTitle := getRightContainerLabel(containerTitle, rightContainerState1.Data.RightContainer.Title)
		rightContainerSubtitle := getRightContainerLabel(containerSubtitle, rightContainerState1.Data.RightContainer.Subtitle)
		rightContainerBorderColor := getRightContainerColor(rightContainerState1.Data.RightContainer.BorderColor)
		rightContainerBGColor := getRightContainerColor(rightContainerState1.Data.RightContainer.BgColor)

		if orderDelayStatus == order.DelayStatusExtremeDelay || orderDelayStatus == order.DelayStatusSlightDelay {
			rightContainerTitle = getRightContainerLabel(containerTitle, rightContainerState2.Data.RightContainer.Title)
			rightContainerSubtitle = getRightContainerLabel(containerSubtitle, rightContainerState2.Data.RightContainer.Subtitle)
			rightContainerBorderColor = getRightContainerColor(rightContainerState2.Data.RightContainer.BorderColor)
			rightContainerBGColor = getRightContainerColor(rightContainerState2.Data.RightContainer.BgColor)
		}

		if (orderStatus == int32(aerobarService.ORDER_STATUS_LINKED) ||
			orderStatus == int32(aerobarService.ORDER_STATUS_SENT_TO_LOGISTICS_PARTNER)) &&
			isScheduledOrder &&
			deliveryStatus == int32(aerobarService.ORDER_DELIVERY_STATUS_PLACED) {
			return nil
		}

		log.Debugf("[Aerobar] Returning right container for order ID: %d", orderID)

		return &sushi.RightContainer{
			Title:       rightContainerTitle,
			Subtitle:    rightContainerSubtitle,
			BorderColor: rightContainerBorderColor,
			BgColor:     rightContainerBGColor,
		}
	}

	return nil
}

func (req *AerobarModel) getServiceRightContainer(c *gin.Context, aerobarData *aerobarProto.AerobarDetails) *sushi.RightContainer {
	isDarkModeEnabled := req.IsDarkModeEnabled
	rightContainerFromService := aerobarData.GetRightContainer()
	rightContainer := &sushi.RightContainer{}
	if rightContainerFromService.GetBorderColor() != nil {
		rightContainerBorderColor := &sushi.Color{
			Type: sushi.ColorType(rightContainerFromService.GetBorderColor().GetType()),
			Tint: sushi.ColorTint(rightContainerFromService.GetBorderColor().GetTint()),
		}

		if isDarkModeEnabled {
			token := sushi.TokenColorButtonPrimaryBackground
			if rightContainerFromService.GetBorderColor().GetThemeBucket() != nil {
				token = sushi.Token(rightContainerFromService.GetBorderColor().GetThemeBucket().GetToken())
			}
			rightContainerBorderColor.ThemeBucket = &sushi.ThemeBucket{
				Token: token,
			}
		}
		rightContainer.BorderColor = rightContainerBorderColor
	}

	if rightContainerFromService.GetBgColor() != nil {
		rightContainerBgColor := &sushi.Color{
			Type: sushi.ColorType(rightContainerFromService.GetBgColor().GetType()),
			Tint: sushi.ColorTint(rightContainerFromService.GetBgColor().GetTint()),
		}

		if isDarkModeEnabled {
			token := sushi.TokenColorButtonPrimaryBackground
			if rightContainerFromService.GetBgColor().GetThemeBucket() != nil {
				token = sushi.Token(rightContainerFromService.GetBgColor().GetThemeBucket().GetToken())
			}
			rightContainerBgColor.ThemeBucket = &sushi.ThemeBucket{
				Token: token,
			}
		}
		rightContainer.BgColor = rightContainerBgColor
	}

	rightContainerTitle := rightContainerFromService.GetTitle()
	rightContainerTitleText := req.GetTextLabelString(c, rightContainerTitle, "")
	if rightContainerTitle != nil {
		title := &sushi.Label{
			Text: rightContainerTitleText,
		}

		if rightContainerTitle.GetColor() != nil {
			rightContainerTitleColor := &sushi.Color{
				Type: sushi.ColorType(rightContainerTitle.GetColor().GetType()),
				Tint: sushi.ColorTint(rightContainerTitle.GetColor().GetTint()),
			}

			if isDarkModeEnabled {
				token := sushi.TokenColorButtonPrimaryLabel
				if rightContainerTitle.GetColor().GetThemeBucket() != nil {
					token = sushi.Token(rightContainerTitle.GetColor().GetThemeBucket().GetToken())
				}
				rightContainerTitleColor.ThemeBucket = &sushi.ThemeBucket{
					Token: token,
				}
			}
			title.Color = rightContainerTitleColor
		}

		if rightContainerTitle.GetFont() != nil {
			title.Font = &sushi.Font{
				Weight: sushi.FontWeight(rightContainerTitle.GetFont().GetWeight()),
				Size:   sushi.FontSize(rightContainerTitle.GetFont().GetSize()),
			}
		}
		rightContainer.Title = title
	}

	rightContainerSubTitle := rightContainerFromService.GetSubTitle()
	rightContainerSubTitleText := req.GetTextLabelString(c, rightContainerTitle, "")
	if rightContainerSubTitle != nil {
		subTitle := &sushi.Label{
			Text: rightContainerSubTitleText,
		}

		if rightContainerSubTitle.GetColor() != nil {
			rightContainerSubtitleColor := &sushi.Color{
				Type: sushi.ColorType(rightContainerSubTitle.GetColor().GetType()),
				Tint: sushi.ColorTint(rightContainerSubTitle.GetColor().GetTint()),
			}

			if isDarkModeEnabled {
				token := sushi.TokenColorButtonPrimaryLabel
				if rightContainerSubTitle.GetColor().GetThemeBucket() != nil {
					token = sushi.Token(rightContainerSubTitle.GetColor().GetThemeBucket().GetToken())
				}
				rightContainerSubtitleColor.ThemeBucket = &sushi.ThemeBucket{
					Token: token,
				}
			}
			subTitle.Color = rightContainerSubtitleColor
		}

		if rightContainerSubTitle.GetFont() != nil {
			subTitle.Font = &sushi.Font{
				Weight: sushi.FontWeight(rightContainerSubTitle.GetFont().GetWeight()),
				Size:   sushi.FontSize(rightContainerSubTitle.GetFont().GetSize()),
			}
		}
		rightContainer.Subtitle = subTitle
	}

	return rightContainer
}

func getRightContainerLabel(text string, labelData *sushi.Label) *sushi.Label {
	return &sushi.Label{
		Text: text,
		Font: &sushi.Font{
			Weight: sushi.FontWeight(labelData.Font.Weight),
			Size:   sushi.FontSize(labelData.Font.Size),
		},
		Color: &sushi.Color{
			Type: sushi.ColorType(labelData.Color.Type),
			Tint: sushi.ColorTint(labelData.Color.Tint),
			ThemeBucket: &sushi.ThemeBucket{
				Token: labelData.Color.ThemeBucket.Token,
			},
		},
	}
}

func getRightContainerColor(colorData *sushi.Color) *sushi.Color {
	return &sushi.Color{
		Type: sushi.ColorType(colorData.Type),
		Tint: sushi.ColorTint(colorData.Tint),
		ThemeBucket: &sushi.ThemeBucket{
			Token: colorData.ThemeBucket.Token,
		},
	}
}

func getAerobarConfigData(c *gin.Context, isDarkModeEnabled bool, aerobarData []*aerobarmodel.AerobarData) *aerobarmodel.ConfigData {
	stateConfigs := getAerobarStateConfig(c, isDarkModeEnabled, aerobarData)
	return &aerobarmodel.ConfigData{
		StateConfig: stateConfigs,
		UIConfig: &aerobarmodel.UIConfig{
			LayoutConfig: &sushi.LayoutConfig{
				SnippetType: snippetTypeAerobarType2,
			},
		},
		BgToFgThreshold: bgToFgThreshold,
	}
}

func getAerobarStateConfig(c *gin.Context, isDarkModeEnabled bool, aerobarData []*aerobarmodel.AerobarData) []*aerobarmodel.StateConfig {
	states := getRightContainerStates(isDarkModeEnabled)
	stateConfigs := []*aerobarmodel.StateConfig{}
	if len(states) > 0 {
		for _, aerobar := range aerobarData {
			if aerobar == nil {
				continue
			}
			for _, item := range aerobar.Items {
				stateConfig := &aerobarmodel.StateConfig{
					AerobarID: item.AerobarID,
					States:    states,
				}
				stateConfigs = append(stateConfigs, stateConfig)
			}
		}
	}

	return stateConfigs
}

func getRightContainerStates(isDarkModeEnabled bool) []*aerobarmodel.State {
	return getStates(isDarkModeEnabled)
}

func getStates(isDarkModeEnabled bool) []*aerobarmodel.State {
	title1 := &sushi.Label{
		Font: &sushi.Font{
			Weight: sushi.FontRegular,
			Size:   sushi.FontSize100,
		},
		Color: &sushi.Color{
			Type: sushi.ColorWhite,
			Tint: sushi.ColorTint500,
		},
	}
	if isDarkModeEnabled {
		title1.Color.ThemeBucket = &sushi.ThemeBucket{
			Token: sushi.TokenColorBaseWhite500,
		}
	}
	title1.Text = ""

	subtitle1 := &sushi.Label{
		Font: &sushi.Font{
			Weight: sushi.FontSemiBold,
			Size:   sushi.FontSize300,
		},
		Color: &sushi.Color{
			Type: sushi.ColorWhite,
			Tint: sushi.ColorTint500,
		},
	}
	if isDarkModeEnabled {
		subtitle1.Color.ThemeBucket = &sushi.ThemeBucket{
			Token: sushi.TokenColorBaseWhite500,
		}
	}
	subtitle1.Text = ""

	borderColor1 := &sushi.Color{
		Type: sushi.ColorGreen,
		Tint: sushi.ColorTint500,
	}
	bgColor1 := &sushi.Color{
		Type: sushi.ColorGreen,
		Tint: sushi.ColorTint500,
	}
	if isDarkModeEnabled {
		borderColor1.ThemeBucket = &sushi.ThemeBucket{
			Token: sushi.TokenColorSurfaceSuccess,
		}
		bgColor1.ThemeBucket = &sushi.ThemeBucket{
			Token: sushi.TokenColorSurfaceSuccess,
		}
	}

	rightContainer1 := &sushi.RightContainer{
		Title:       title1,
		Subtitle:    subtitle1,
		BorderColor: borderColor1,
		BgColor:     bgColor1,
	}

	title2 := &sushi.Label{
		Font: &sushi.Font{
			Weight: sushi.FontRegular,
			Size:   sushi.FontSize200,
		},
		Color: &sushi.Color{
			Type: sushi.ColorGrey,
			Tint: sushi.ColorTint900,
		},
	}
	if isDarkModeEnabled {
		title2.Color.ThemeBucket = &sushi.ThemeBucket{
			Token: sushi.TokenColorTextPrimary,
		}
	}
	title2.Text = ""

	subtitle2 := &sushi.Label{
		Font: &sushi.Font{
			Weight: sushi.FontSemiBold,
			Size:   sushi.FontSize300,
		},
		Color: &sushi.Color{
			Type: sushi.ColorGrey,
			Tint: sushi.ColorTint900,
		},
	}
	if isDarkModeEnabled {
		subtitle2.Color.ThemeBucket = &sushi.ThemeBucket{
			Token: sushi.TokenColorTextPrimary,
		}
	}
	subtitle2.Text = ""

	borderColor2 := &sushi.Color{
		Type: sushi.ColorGrey,
		Tint: sushi.ColorTint300,
	}
	bgColor2 := &sushi.Color{
		Type: sushi.ColorWhite,
		Tint: sushi.ColorTint500,
	}
	if isDarkModeEnabled {
		borderColor2.ThemeBucket = &sushi.ThemeBucket{
			Token: sushi.TokenColorBorderIntense,
		}
		bgColor2.ThemeBucket = &sushi.ThemeBucket{
			Token: sushi.TokenColorSurfaceSecondary,
		}
	}

	rightContainer2 := &sushi.RightContainer{
		Title:       title2,
		Subtitle:    subtitle2,
		BorderColor: borderColor2,
		BgColor:     bgColor2,
	}

	state1 := &aerobarmodel.State{
		State: string(AerobarStateOnTime),
		Data: &aerobarmodel.StateData{
			RightContainer: rightContainer1,
		},
	}

	state2 := &aerobarmodel.State{
		State: string(AerobarStateDelayed),
		Data: &aerobarmodel.StateData{
			RightContainer: rightContainer2,
		},
	}
	states := []*aerobarmodel.State{state1, state2}
	return states
}

func getContainerContentForLongKPTOrder(ctx context.Context, scheduledSlots *TimeSlot) *sushi.RightContainer {
	log := logger.FromContext(ctx).WithFields(map[string]interface{}{
		"slot_timing": scheduledSlots,
	})

	rightContainer := &sushi.RightContainer{}

	if scheduledSlots == nil || scheduledSlots.StartTime <= 0 || scheduledSlots.EndTime <= 0 {
		log.Error("[Aerobar] invalid slot timing")
		tracer.NoticeError(ctx, fmt.Errorf("invalid slot timing for long kpt order"))
		return rightContainer
	}

	startTime := int64(scheduledSlots.StartTime)
	endTime := int64(scheduledSlots.EndTime)

	currentTime := time.Now().Unix()
	timeDiff := (startTime - currentTime) / 60

	if timeDiff <= 60 {
		return rightContainer
	}

	loc, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		log.Errorf("[Aerobar] error loading location")
		return rightContainer
	}

	currentDate := time.Unix(currentTime, 0).In(loc).Truncate(24 * time.Hour)
	endDate := time.Unix(endTime, 0).In(loc).Truncate(24 * time.Hour)

	daysDiff := int(endDate.Sub(currentDate).Hours() / 24)
	if daysDiff < 0 {
		log.Errorf("[Aerobar] invalid slot timing")
		tracer.NoticeError(ctx, fmt.Errorf("invalid slot timing for long kpt order"))
		return rightContainer
	}

	if daysDiff == 0 {
		rightContainer.Title = &sushi.Label{
			Text: RightContainerArrivingByTitle,
		}
		rightContainer.Subtitle = &sushi.Label{
			Text: getSlotString(ctx, startTime, endTime),
		}

		return rightContainer

	}

	if daysDiff == 1 {
		rightContainer.Title = &sushi.Label{
			Text: RightContainerArrivingSoonTitle,
		}

		rightContainer.Subtitle = &sushi.Label{
			Text: RightContainerArrivingTomorrowSubtitle,
		}
		return rightContainer
	}

	rightContainer.Title = &sushi.Label{
		Text: RightContainerArrivingOnTitle,
	}
	rightContainer.Subtitle = &sushi.Label{
		Text: time.Unix(endTime, 0).Format("02 Jan"),
	}
	return rightContainer
}

// getSlotString formats the time slot string.
func getSlotString(ctx context.Context, startTime, endTime int64) string {
	loc, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		logger.FromContext(ctx).WithError(err).Error("[Aerobar] error loading location")
		return ""
	}
	start := time.Unix(startTime, 0).In(loc)
	end := time.Unix(endTime, 0).In(loc)

	slotStart := start.Format("3")
	if start.Minute() != 0 {
		slotStart = start.Format("3:04")
	}

	slotEnd := end.Format("3 PM")
	if end.Minute() != 0 {
		slotEnd = end.Format("3:04 PM")
	}

	return fmt.Sprintf("%s-%s", slotStart, slotEnd)
}

func getRightContainerTitleText(c *gin.Context, isOrderArrivingSoon bool) string {
	if isOrderArrivingSoon {
		return i18n.Translate(c, "zm-aerobar-crystal-right-container-title-arriving")
	}
	return i18n.Translate(c, "zm-aerobar-crystal-right-container-title")
}

func getRightContainerSubtitleText(c *gin.Context, deliveryETA int32, isOrderArrivingSoon bool, isETARangeActive bool, etaLowerBound int32, etaUpperBound int32) string {
	if isOrderArrivingSoon {
		return i18n.Translate(c, "zm-aerobar-crystal-right-container-subtitle-arriving-soon")
	}

	if isETARangeActive {
		if etaLowerBound > 0 && etaUpperBound > 0 && etaLowerBound < etaUpperBound {
			return i18n.Translate(c, "zm-aerobar-crystal-right-container-subtitle-arriving-in-range", utils.ConvertInt64ToString(int64(etaLowerBound)), utils.ConvertInt64ToString(int64(etaUpperBound)))
		}
	}
	deliveryETA = int32(math.Max(float64(ArrivingSoonThreshold), float64(deliveryETA)))
	return i18n.Translate(c, "zm-aerobar-crystal-right-container-subtitle", utils.ConvertInt64ToString(int64(deliveryETA)))
}

func getOrderTrackingDetails(c *gin.Context, orderID int64) (*response.GetOrderTrackingDetailsResponse, error) {
	en := env.FromContext(c)
	resp, err := en.LiveOrderService().GetOrderTrackingDetails(
		c,
		&request.GetOrderTrackingDetailsRequest{
			OrderId: orderID,
		},
	)

	if err != nil {
		logger.FromContext(c).WithError(err).Errorf("los GetOrderTrackingDetails rpc failed, err: %+v", err)
		return nil, err
	}

	if resp == nil {
		logger.FromContext(c).Errorf("los GetOrderTrackingDetails rpc response is nil")
		return nil, errors.New("internal server error")
	}

	return resp, nil
}

func getScheduledSlot(timeSlot *common.TimeSlot) *TimeSlot {
	if timeSlot == nil {
		return nil
	}
	return &TimeSlot{
		StartTime: uint64(timeSlot.GetStartTime().GetSeconds()),
		EndTime:   uint64(timeSlot.GetEndTime().GetSeconds()),
	}
}

func isGetAerobarsFromGatewayEnabledForUser(c *gin.Context) bool {
	if !config.GetBool(c, "aerobar_service.get_aerobars_from_gateway.enabled") {
		return false
	}
	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()

	enabledUserPercentage := config.GetInt64(c, "aerobar_service.get_aerobars_from_gateway.enabled_user_percentage")
	if enabledUserPercentage == 100 {
		return true
	}

	enabledUserIDs := config.GetIntSlice(c, "aerobar_service.get_aerobars_from_gateway.enabled_user_ids")
	if array.ContainsInt(enabledUserIDs, int(userID)) {
		return true
	}

	if userID == 0 {
		return config.GetBool(c, "aerobar_service.get_aerobars_from_gateway.enabled_for_guest_user_ids")
	}

	user, err := user.FetchUserProfileFromContext(c, userID)
	if err != nil {
		datadog.NoticeError(c, err)
		logger.FromContext(c).WithError(err).Error("error while fetching user profile")
		return false
	}

	enabledForZomans := config.GetBool(c, "aerobar_service.get_aerobars_from_gateway.enabled_for_zomans")
	if enabledForZomans && user.GetIsZoman() {
		return true
	}

	enabledForEternals := config.GetBool(c, "aerobar_service.get_aerobars_from_gateway.enabled_for_eternals")
	if enabledForEternals && user.GetIsEternal() {
		return true
	}

	enabledForEternalLinked := config.GetBool(c, "aerobar_service.get_aerobars_from_gateway.enabled_for_eternal_linked")
	if enabledForEternalLinked && ps.IsEternalLinkedUser(c, userID, psSourceName) {
		return true
	}

	enabledUserModFactor := config.GetInt64(c, "aerobar_service.get_aerobars_from_gateway.enabled_user_mod_factor")
	modValue := (userID * enabledUserModFactor) % 100
	return modValue < enabledUserPercentage
}
