package aerobar

import (
	"time"

	"github.com/Zomato/go/config"
	"github.com/Zomato/go/i18n"

	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/array"
	cartv1 "github.com/Zomato/zomato-api-gateway/internal/cart/aggregates/cart"
	"github.com/Zomato/zomato-api-gateway/internal/cart/aggregates/connections"
	"github.com/Zomato/zomato-api-gateway/internal/cart/aggregates/customer"
	"github.com/Zomato/zomato-api-gateway/internal/cart/aggregates/group"
	"github.com/Zomato/zomato-api-gateway/internal/cart/lib"
	"github.com/Zomato/zomato-api-gateway/internal/cart/registry"
	"github.com/Zomato/zomato-api-gateway/internal/cart/upstreams/group/requests"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/internal/utils"
	aerobarmodel "github.com/Zomato/zomato-api-gateway/models/aerobar"
	"github.com/Zomato/zomato-api-gateway/models/sushi"
	"github.com/gin-gonic/gin"
)

var stateNameToGroupStateMap map[registry.StateName]Group = map[registry.StateName]Group{
	registry.StateNameUnSpecified: groupStateUnspecified,
	registry.StateNameCreated:     groupStateCreated,
	registry.StateNameActive:      groupStateActive,
	registry.StateNameLocked:      groupStateLocked,
	registry.StateNameArchived:    groupStateArchived,
}

func (req *AerobarModel) getLocalAerobars(c *gin.Context) []*aerobarmodel.LocalAerobar {
	return []*aerobarmodel.LocalAerobar{req.getCartParams(c, o2SavedCartAerobar)}
}

func (req *AerobarModel) getCartParams(c *gin.Context, aerobarType string) *aerobarmodel.LocalAerobar {
	if !array.Contains(localAerobarList, aerobarType) {
		return nil
	}

	cartParams := &aerobarmodel.Params{
		CartType:       cartTypeAerobar,
		CartConfigMode: cartConfigModeFullPage,
	}

	data := &aerobarmodel.O2SavedCart{
		RedirectionTemplates: req.getRedirectionTemplates(c, cartParams),
	}

	if config.GetBool(c, "aerobar_service.group_ordering.enabled") && req.IsGroupOrder {
		data.GroupOrders = req.getGroupOrdersData(c)
	}

	if req.shouldEnableMRO(c, int(req.UserID)) {
		data.MROConfig = req.getMROConfig(c)
	}

	return &aerobarmodel.LocalAerobar{
		Type:        aerobarType,
		O2SavedCart: data,
	}
}

func (req *AerobarModel) shouldEnableMRO(c *gin.Context, userID int) bool {
	if !config.GetBool(c, "aerobar_service.aerobars.local_aerobars.cart.mro.rollout.enabled") {
		return false
	}
	if userID == 0 {
		return false
	}

	testUsers := config.GetIntSlice(c, "aerobar_service.aerobars.local_aerobars.cart.mro.rollout.test_user_ids")
	if array.ContainsInt(testUsers, userID) {
		return true
	}

	enabledPercentage := config.GetInt(c, "aerobar_service.aerobars.local_aerobars.cart.mro.rollout.enabled_percentage")
	return (userID % 100) < enabledPercentage
}

func (req *AerobarModel) getRedirectionTemplates(c *gin.Context, params *aerobarmodel.Params) []*aerobarmodel.RedirectionTemplate {
	if params == nil {
		return nil
	}

	response := []*aerobarmodel.RedirectionTemplate{}
	templateObj1 := &aerobarmodel.RedirectionTemplate{
		Type:       template1,
		RedirectTo: redirectToMenu,
	}
	response = append(response, templateObj1)

	templateObj2 := &aerobarmodel.RedirectionTemplate{
		Type:       template2,
		RedirectTo: redirectToMenu,
	}

	enabled := req.isGetRedirectionTemplatesEnabled(c)
	if enabled {
		templateObj2.RedirectTo = redirectToCart
		templateObj2.Params = params
	}
	response = append(response, templateObj2)

	if config.GetBool(c, "aerobar_service.aerobars.o2_aerobar.local_aerobar_menu_redirection.enabled") {
		params.ShouldOpenMenu = false
	}
	templateObj3 := &aerobarmodel.RedirectionTemplate{
		Type:          template3,
		RedirectTo:    redirectToMenu,
		CtaRedirectTo: ctaRedirectToCart,
		Params:        params,
	}
	response = append(response, templateObj3)

	params = &aerobarmodel.Params{
		SearchSource: cartTypeAerobar}
	templateObj4 := &aerobarmodel.RedirectionTemplate{
		Type:          template4,
		RedirectTo:    redirectToMenu,
		CtaRedirectTo: ctaRedirectToGroupOrder,
		Params:        params,
	}
	response = append(response, templateObj4)
	return response
}

func (req *AerobarModel) getGroupOrdersData(c *gin.Context) []*aerobarmodel.GroupOrder {
	log := logger.FromContext(c).WithFields(map[string]interface{}{
		"user_id":             req.UserID,
		"group_order_aerobar": "getGroupOrdersData",
	})

	groupOrders := make([]*aerobarmodel.GroupOrder, 0)
	groupOrderingService := env.FromContext(c).GroupOrderingService()
	customerID := customer.NewCustomerIdFromId(utils.ConvertInt64ToString(req.UserID))

	ctx := lib.NewCartContextForGroupOrdering(c)
	request := requests.NewGetGroupsForMembersRequest(customerID, map[registry.FeatureSupportKey]bool{})
	groups, err := groupOrderingService.GetGroupsForMember(ctx, request)

	if err.IsNotEmpty() {
		log.WithError(err.ToErr()).WithField("customerID", customerID).Error("[Aerobar] failed to fetch cart for group")
		return nil
	}

	if groups == nil || len(groups.Groups()) == 0 {
		return groupOrders
	}
	log.Debug("[Aerobar] Groups fetched for member:", groups.Groups())
	timestamp := time.Now().Unix()

	for _, group := range groups.GroupsWithCarts() {
		cart, err := cartv1.ToCart(group.Cart())
		if err.IsNotEmpty() {
			log.Errorln("[Aerobar] Error fetching cart for group:", err)
			continue
		}

		resID := getStoreIdFromCart(cart)
		state := getGroupState(group.Group())
		groupID := getGroupId(group.Group())

		if resID == "" || groupID == "" {
			continue
		}

		if isGroupStateDeleted(state) {
			continue
		}

		shouldRemoveAerobar := false
		if state == groupStateOrderPlaced {
			shouldRemoveAerobar = true
		}

		mqttData := &aerobarmodel.MqttData{}

		if isEligibleStateForMqttData(state) {
			groupMqttConnections := getGroupMqttConnections(group.Connections())
			if groupMqttConnections != nil {
				mqttData = &aerobarmodel.MqttData{
					Type: string(groupMqttConnections.Topics()[0].TopicType()),
					Name: []string{groupMqttConnections.Topics()[0].TopicName()},
					Qos:  int(groupMqttConnections.Topics()[0].Qos()),
					Time: timestamp,
					Client: &aerobarmodel.Client{
						Username:  groupMqttConnections.Authentication().Username(),
						Password:  groupMqttConnections.Authentication().Password(),
						Keepalive: int(group.Connections().Connections()[0].KeepaliveTime()),
					},
				}
			}
		}

		groupOrder := &aerobarmodel.GroupOrder{
			GroupOrderID:        groupID,
			RestaurantID:        resID,
			ShouldRemoveAerobar: shouldRemoveAerobar}

		groupOrder.MqttData = mqttData
		groupOrders = append(groupOrders, groupOrder)
	}
	return groupOrders
}

func (req *AerobarModel) getMROConfig(c *gin.Context) *aerobarmodel.MROConfig {
	return &aerobarmodel.MROConfig{
		CheckoutConfig: &aerobarmodel.CheckoutConfig{
			MroConfigStates: req.getMROCheckoutConfigStates(c),
		},
		EducationInfo: req.getMROEducationInfo(c),
	}
}

func (req *AerobarModel) getMROCheckoutConfigStates(c *gin.Context) []*aerobarmodel.MROConfigState {
	var mroConfigState []*aerobarmodel.MROConfigState
	mroConfigState = append(mroConfigState, req.getMROCheckoutConfigForEligibleState(c))
	client := api.GetClientFromContext(c)
	if client.SupportsMroIneligibleShakeAction() {
		mroConfigState = append(mroConfigState, req.getUpdatedMROCheckoutConfigForIneligibleState(c))
	} else {
		mroConfigState = append(mroConfigState, req.getMROCheckoutConfigForIneligibleState(c))
	}
	return mroConfigState
}

func (req *AerobarModel) getMROEducationInfo(c *gin.Context) *aerobarmodel.EducationInfo {
	fallbackDarkImageUrl := introductionAnimationFallbackDarkUrl
	fallbackImageUrl := introductionAnimationFallbackUrl
	animationUrl := introductionAnimationUrl
	darkAnimationUrl := introductionDarkAnimationUrl
	if req.IsVegModeEnabled {
		fallbackDarkImageUrl = introductionAnimationFallbackDarkVegUrl
		fallbackImageUrl = introductionAnimationFallbackVegUrl
		animationUrl = introductionAnimationVegUrl
		darkAnimationUrl = introductionDarkAnimationVegUrl
	}

	fallbackDarkImage := &sushi.Image{
		URL:         fallbackDarkImageUrl,
		AspectRatio: 1.406,
	}
	darkAnimation := &sushi.Animation{
		URL:           darkAnimationUrl,
		AspectRatio:   1.406,
		RepeatCount:   0,
		FallbackImage: fallbackDarkImage,
	}
	darkTheme := &sushi.ThemeConfig{
		Theme:         sushi.ThemeDark,
		ComponentType: sushi.ComponentTypeAnimation,
		Animation:     darkAnimation,
	}

	fallbackImage := &sushi.Image{
		URL:         fallbackImageUrl,
		AspectRatio: 1.406,
	}
	animation := &sushi.Animation{
		URL:           animationUrl,
		AspectRatio:   1.406,
		RepeatCount:   0,
		FallbackImage: fallbackImage,
		ThemeConfig:   []*sushi.ThemeConfig{darkTheme},
	}
	uiData := &sushi.UIConfig{
		Animation:  animation,
		BgGradient: req.getMROEducationGradient(),
	}

	globalMaxImpressionCount := config.GetInt(c, "aerobar_service.aerobars.local_aerobars.cart.mro.introduction.global_impression_count")
	educationInfo := &aerobarmodel.EducationInfo{
		UIConfig: uiData,
		EducationConfigData: &aerobarmodel.EducationConfigData{
			GlobalMaxImpressionCount:       globalMaxImpressionCount,
			SessionMaxImpressionCount:      sessionMaxImpressionCount,
			InitialUIDataOpenDelayMilliSec: initialUIDataOpenDelayMilliSec,
			UIDataOpenDurationMilliSec:     uiDataOpenDurationMilliSec,
		},
	}

	if isNewUserBasedOnOrderFrequencyProperty(c, req.UserID) {
		return nil
	}

	return educationInfo
}

func (req *AerobarModel) getMROEducationGradient() *sushi.Gradient {
	return &sushi.Gradient{
		Colors: []*sushi.Color{
			(&sushi.Color{Type: sushi.ColorPink, Tint: sushi.ColorTint100}).
				SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorSurfaceSelection}),
			(&sushi.Color{Type: sushi.ColorCider, Tint: sushi.ColorTint050}).
				SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorBackgroundPrimary}),
		},
	}
}

func (req *AerobarModel) getMROCheckoutConfigForEligibleState(c *gin.Context) *aerobarmodel.MROConfigState {
	color := &sushi.Color{
		Type: sushi.ColorRed,
		Tint: sushi.ColorTint500,
	}
	if req.IsVegModeEnabled {
		color = &sushi.Color{
			Type: sushi.ColorGreen,
			Tint: sushi.ColorTint500,
		}
	}
	eligibleStateButton := &aerobarmodel.MROButton{
		Text: i18n.Translate(c, "zm-multi-cart-creation-button-text"),
		Size: sushi.ButtonSizeMedium,
		Color: color.SetThemeBucket(&sushi.ThemeBucket{
			Token: sushi.TokenColorButtonGhostLabel,
		}),
		SuffixIcon:  &sushi.Icon{Code: sushi.IconRightArrowThin},
		ClickAction: &sushi.ClickAction{Type: sushi.ActionTypeOpenMultiCartAerobar},
		IsDisabled:  false,
	}

	buttonBgColor := &sushi.Color{
		Type: sushi.ColorWhite,
		Tint: sushi.ColorTint500,
	}
	buttonBorderColor := &sushi.Color{
		Type: sushi.ColorGrey,
		Tint: sushi.ColorTint500,
	}
	eligibleStateButton.Type = sushi.ButtonTypeOutlined
	eligibleStateButton.Font = &sushi.Font{Weight: sushi.FontBold, Size: sushi.FontSize300}
	eligibleStateButton.BgColor = buttonBgColor.SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorSurfacePrimary})
	eligibleStateButton.BorderColor = buttonBorderColor.SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorBorderModerate})

	color = &sushi.Color{
		Type: sushi.ColorWhite,
		Tint: sushi.ColorTint500,
	}
	tooltipContainerTitle := &sushi.Label{
		Text:       i18n.Translate(c, "zm-multi-cart-creation-button-tooltip-education-message"),
		Color:      color.SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorTextInverse}),
		Font:       &sushi.Font{Weight: sushi.FontRegular, Size: sushi.FontSize200},
		IsMarkdown: 1,
	}
	borderColor := &sushi.Color{
		Type: sushi.ColorBlack,
		Tint: sushi.ColorTint500,
	}
	bgColor := &sushi.Color{
		Type: sushi.ColorBlack,
		Tint: sushi.ColorTint500,
	}
	tooltipContainer := &aerobarmodel.ToolTipContainer{
		Title:       tooltipContainerTitle,
		BorderColor: borderColor.SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorSurfaceInverse}),
		BgColor:     bgColor.SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorSurfaceInverse}), CornerRadius: 12,
	}
	globalMaxImpressionCount := config.GetInt(c, "aerobar_service.aerobars.local_aerobars.cart.mro.introduction.tooltip_global_impression_count")
	tooltip := &aerobarmodel.Tooltip{
		TooltipContainer:          tooltipContainer,
		SessionMaxImpressionCount: 1,
		GlobalMaxImpressionCount:  globalMaxImpressionCount,
	}

	eligibleState := &aerobarmodel.MROConfigState{
		State:                    mroCheckoutConfigStateEligible,
		Button:                   eligibleStateButton,
		Tooltip:                  tooltip,
		ShouldShouldButtonShadow: true,
	}

	return eligibleState
}

func (req *AerobarModel) getUpdatedMROCheckoutConfigForIneligibleState(c *gin.Context) *aerobarmodel.MROConfigState {
	genericTooltipDataConfig := &sushi.GenericTooltipConfig{
		Delay:                 100,
		ShowDimmingBackground: false,
		Alignment:             string(sushi.AlignmentBottom),
	}

	color := (&sushi.Color{
		Type: sushi.ColorWhite,
		Tint: sushi.ColorTint500,
	}).SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorTextInverse})
	bgColor := (&sushi.Color{
		Type: sushi.ColorBlack,
		Tint: sushi.ColorTint500,
	}).SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorSurfaceInverse})
	icon := &sushi.Icon{
		Code:  sushi.IconCrossCircle,
		Color: (&sushi.Color{Type: sushi.ColorRed, Tint: sushi.ColorTint500}).SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorIconInverse}),
	}
	genericTooltipData := &sushi.ShowGenericTooltipData{
		Type: sushi.ToolTipType2,
		TooltipType2: &sushi.Label{
			Title: &sushi.Label{
				Text:  i18n.Translate(c, "zm-multi-cart-creation-button-unserviceable-message"),
				Color: color,
				Font:  &sushi.Font{Weight: sushi.FontRegular, Size: sushi.FontSize200},
			},
			BgColor:    bgColor,
			ShowAnchor: true,
			Direction:  sushi.DirectionSouth,
			Icon:       icon,
		},
	}

	showGenericTooltip := &sushi.ShowGenericTooltip{
		ID:     genericTooltipDataId,
		Config: genericTooltipDataConfig,
		Data:   genericTooltipData,
	}

	ineligibleStateClickAction := &sushi.SuccessAction{
		Type:                        string(sushi.ActionTypeShowGenericTooltip),
		ShouldPerformHapticFeedback: true,
		ShowGenericTooltip:          showGenericTooltip,
	}
	color = &sushi.Color{
		Type: sushi.ColorRed,
		Tint: sushi.ColorTint500,
	}
	errorBorderData := &sushi.Border{
		Width: 1,
		Colors: []*sushi.Color{
			color.SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorBorderError}),
		},
	}

	color = &sushi.Color{Type: sushi.ColorGrey, Tint: sushi.ColorTint500}

	triggerShakeAnimation := &sushi.SuccessAction{
		Type: string(sushi.ActionTypeTriggerShakeAnimation),
		TriggerShakeAnimation: &sushi.TriggerShakeAnimation{
			Data: &sushi.TriggerShakeAnimationData{
				Type: shakeTypeAnimation1,
				ShakeType1: &sushi.ShakeType1{
					BorderData: errorBorderData,
				},
			},
		},
	}

	ineligibleStateButton := &aerobarmodel.MROButton{
		Text:       i18n.Translate(c, "zm-multi-cart-creation-button-text"),
		Size:       sushi.ButtonSizeMedium,
		Color:      color.SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorButtonGhostLabelDisabled}),
		SuffixIcon: &sushi.Icon{Code: sushi.IconRightArrowThin},
		IsDisabled: true,
		ClickAction: &sushi.ClickAction{
			Type:       sushi.ActionTypeActionList,
			ActionList: []*sushi.SuccessAction{ineligibleStateClickAction, triggerShakeAnimation},
		},
	}

	buttonBgColor := (&sushi.Color{
		Type: sushi.ColorWhite,
		Tint: sushi.ColorTint500,
	}).SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorSurfacePrimary})
	buttonBorderColor := (&sushi.Color{
		Type: sushi.ColorGrey,
		Tint: sushi.ColorTint500,
	}).SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorBorderModerate})
	ineligibleStateButton.Type = sushi.ButtonTypeOutlined
	ineligibleStateButton.Font = &sushi.Font{Weight: sushi.FontBold, Size: sushi.FontSize300}
	ineligibleStateButton.BgColor = buttonBgColor
	ineligibleStateButton.BorderColor = buttonBorderColor

	ineligibleState := &aerobarmodel.MROConfigState{
		State:                    mroCheckoutConfigStateNotEligible,
		Button:                   ineligibleStateButton,
		ShouldShouldButtonShadow: true,
	}
	return ineligibleState
}

func (req *AerobarModel) getMROCheckoutConfigForIneligibleState(c *gin.Context) *aerobarmodel.MROConfigState {
	genericTooltipDataConfig := &sushi.GenericTooltipConfig{
		Delay:                 100,
		ShowDimmingBackground: true,
		Alignment:             string(sushi.AlignmentTop),
	}

	color := (&sushi.Color{
		Type: sushi.ColorWhite,
		Tint: sushi.ColorTint500,
	}).SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorTextInverse})
	bgColor := (&sushi.Color{
		Type: sushi.ColorBlack,
		Tint: sushi.ColorTint500,
	}).SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorSurfaceInverse})

	genericTooltipData := &sushi.ShowGenericTooltipData{
		Type: sushi.ToolTipType2,
		TooltipType2: &sushi.Label{
			Title: &sushi.Label{
				Text:  i18n.Translate(c, "zm-multi-cart-creation-button-unserviceable-message"),
				Color: color,
				Font:  &sushi.Font{Weight: sushi.FontRegular, Size: sushi.FontSize200},
			},
			BgColor:    bgColor,
			ShowAnchor: true,
		},
	}

	showGenericTooltip := &sushi.ShowGenericTooltip{
		ID:     genericTooltipDataId,
		Config: genericTooltipDataConfig,
		Data:   genericTooltipData,
	}
	ineligibleStateClickAction := &sushi.SuccessAction{
		Type:                        string(sushi.ActionTypeShowGenericTooltip),
		ShouldPerformHapticFeedback: true,
		ShowGenericTooltip:          showGenericTooltip,
	}
	ineligibleStateButton := &aerobarmodel.MROButton{
		Text: i18n.Translate(c, "zm-multi-cart-creation-button-text"),
		Size: sushi.ButtonSizeMedium,
		Color: (&sushi.Color{
			Type: sushi.ColorGrey,
			Tint: sushi.ColorTint500,
		}).SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorButtonGhostLabelDisabled}),
		SuffixIcon: &sushi.Icon{Code: sushi.IconRightArrowThin},
		IsDisabled: true,
		ClickAction: &sushi.ClickAction{
			Type:       sushi.ActionTypeActionList,
			ActionList: []*sushi.SuccessAction{ineligibleStateClickAction},
		},
	}
	buttonBgColor := &sushi.Color{
		Type: sushi.ColorWhite,
		Tint: sushi.ColorTint500,
	}
	buttonBorderColor := &sushi.Color{
		Type: sushi.ColorGrey,
		Tint: sushi.ColorTint500,
	}
	ineligibleStateButton.Type = sushi.ButtonTypeOutlined
	ineligibleStateButton.Font = &sushi.Font{Weight: sushi.FontBold, Size: sushi.FontSize300}
	ineligibleStateButton.BgColor = buttonBgColor.SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorSurfacePrimary})
	ineligibleStateButton.BorderColor = buttonBorderColor.SetThemeBucket(&sushi.ThemeBucket{Token: sushi.TokenColorBorderModerate})

	ineligibleState := &aerobarmodel.MROConfigState{
		State:                    mroCheckoutConfigStateNotEligible,
		Button:                   ineligibleStateButton,
		ShouldShouldButtonShadow: true,
	}
	return ineligibleState
}

func (req *AerobarModel) isGetRedirectionTemplatesEnabled(c *gin.Context) bool {
	if config.GetBool(c, "aerobar_service.aerobars.local_aerobars.consumer_app.aerobar.enabled") {
		return true
	}

	if array.ContainsInt(config.GetIntSlice(c, "aerobar_service.aerobars.local_aerobars.consumer_app.aerobar.enabled_user_ids"), int(req.UserID)) {
		return true
	}

	return false
}

func isEligibleStateForMqttData(state Group) bool {
	return !array.Contains(ineligibleGroupStatesForMqttData, state)
}

func isGroupStateDeleted(state Group) bool {
	return state == groupStateArchived || state == groupStateDeleted
}

func getGroupState(state group.BaseGroup) Group {
	if state.IsEmpty() {
		return groupStateUnspecified
	}

	groupState := state.State()
	if groupState.IsEmpty() {
		return groupStateUnspecified
	}

	currentState := groupState.CurrentState()
	if currentState.IsEmpty() {
		return groupStateUnspecified
	}

	stateNode := currentState.StateNode()
	if stateNode.IsEmpty() {
		return groupStateUnspecified
	}

	stateName := stateNode.Name()
	stateType := stateNode.StateType()
	grpState := groupStateUnspecified
	if mappedState, ok := stateNameToGroupStateMap[stateName]; ok {
		grpState = mappedState
	}

	if stateName == registry.StateNameArchived {
		if stateType == registry.StateTypePositiveTerminal {
			grpState = groupStateOrderPlaced
		} else if stateType == registry.StateTypeNegativeTerminal {
			grpState = groupStateArchived
		}
	}
	return grpState
}

func getGroupId(group group.BaseGroup) string {
	if group.IsEmpty() {
		return ""
	}

	groupId := group.GroupId()
	if groupId.IsEmpty() {
		return ""
	}

	return groupId.Id()
}

func getStoreIdFromCart(cart *cartv1.Cart) string {
	if cart.IsEmpty() {
		return ""
	}

	fulfilment := cart.Fulfilment()
	if fulfilment.IsEmpty() {
		return ""
	}

	merchant := fulfilment.Merchant()
	if merchant.IsEmpty() {
		return ""
	}

	merchantStore := merchant.Store()
	if merchantStore.IsEmpty() {
		return ""
	}

	store := merchantStore.Store()
	if store.IsEmpty() {
		return ""
	}

	storeId := store.StoreId()
	if storeId.IsEmpty() {
		return ""
	}

	return storeId.Id()
}

func getGroupMqttConnections(connections *connections.Connections) *connections.ConnectionDetails {
	if connections.IsEmpty() {
		return nil
	}

	connectionSlice := connections.Connections()
	if len(connectionSlice) == 0 {
		return nil
	}

	connection := connectionSlice[0]
	if connection.IsEmpty() {
		return nil
	}

	connectionDetails := connection.ConnectionDetails()
	if connectionDetails.IsEmpty() {
		return nil
	}

	return connectionDetails
}

func BoolPtr(val bool) *bool {
	return &val
}
