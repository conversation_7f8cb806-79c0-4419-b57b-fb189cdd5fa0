package aerobar

import (
	"strings"

	"github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	ps "github.com/Zomato/zomato-api-gateway/internal/profile_store_service"
	"github.com/gin-gonic/gin"
)

const (
	publishActionType                     = "publish"
	draftActionType                       = "draft"
	updateTemplateSelectionRuleActionType = "update_template_selection_rule"
	updateAerobarDetailsActionType        = "update_aerobar_details"
	updatePriorityActionType              = "update_priority"
	updateTemplateTypeActionType          = "update_template_type"
	StatusFailedErrorStringKey            = "something-went-wrong"
)

func isBlank(s string) bool {
	return len(strings.TrimSpace(s)) == 0
}

func isNewUserBasedOnOrderFrequencyProperty(c *gin.Context, userID int64) bool {
	if !config.GetBool(c, "tabbed_home.disable_blocker_items_for_new_user") {
		return false
	}

	if userID <= 0 {
		return false
	}

	enabledPercentage := config.GetInt64(c, "tabbed_home.disable_blocker_items_for_new_user_percentage")
	enabledUserModFactor := int64(563)
	modValue := (userID * enabledUserModFactor) % 100
	if modValue >= enabledPercentage {
		return false
	}

	userSegment := ps.GetUserOrderFrequencySegment(c, userID, "local_aerobar")
	switch userSegment {
	case "", ps.UserOrderFrequencyNew, ps.UserOrderFrequencyLapsed60, ps.UserOrderFrequencyLapsed180, "{}":
		logger.FromContext(c).Debugf("[LOCAL_AEROBAR] - user segment: %s for userid: %d", userSegment, userID)
		return true
	}
	return false
}
