package aerobar

import (
	"context"
	"fmt"

	log "github.com/Zomato/go/logger"

	zenqueue "github.com/Zomato/go/enqueue"
	"github.com/Zomato/go/enqueue/message"
	"github.com/Zomato/zomato-api-gateway/internal/pubsub/kafka/enqueue"
	pubsubMessage "github.com/Zomato/zomato-api-gateway/internal/pubsub/message"
	"github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/aerobar"
)

const (
	AerobarEventTopic = "zomato.aerobar-service.aerobar-events"
)

var (
	aerobarEventEnqueuer zenqueue.Enqueuer
)

func InitialiseAerobarEventEnqueuer(ctx context.Context, topic, cluster string) {
	enqueuer, err := enqueue.GetAsyncTopicEnqueuer(ctx, topic, cluster)
	if err != nil {
		log.WithError(err).Error("Could not initialise aerobar event enqueuer")
		return
	}

	aerobarEventEnqueuer = enqueuer
}

func enqueueMessage(ctx context.Context, m *aerobar.AerobarEvent) error {
	msg, err := pubsubMessage.New(m)
	if err != nil {
		return err
	}

	messageInfo := enqueue.EnqueuerMessageInfo{
		Message:       message.Proto(msg),
		Topic:         AerobarEventTopic,
		TopicEnqueuer: aerobarEventEnqueuer,
		MessageKey:    fmt.Sprintf("%s_%s", m.GetTemplateType(), m.GetEventId()),
	}

	err = enqueue.EnqueueAsyncMsgToKafka(ctx, messageInfo)
	if err != nil {
		return err
	}
	return nil
}
