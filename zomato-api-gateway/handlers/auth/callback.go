package auth

import (
	"net/http"
	"time"

	"github.com/Zomato/auth-service-client-golang/pkg/auth"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	models "github.com/Zomato/zomato-api-gateway/models"
	"github.com/gin-gonic/gin"
)

const (
	cookieExpiration time.Duration = 365 * 24 * time.Hour
)

// CallbackHanlder exchanges token with zat by making rpc call to auth service
// and sets it in cookie in response
func CallbackHandler(c *gin.Context) {

	env := env.FromContext(c)
	log := logger.FromContext(c)

	token := c.PostForm("token")
	if len(token) == 0 {
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("token is required"),
		)
		return
	}

	authServiceConn := env.AuthServiceConn()
	authClient := auth.NewAuthServiceClient(authServiceConn)

	authResp, err := authClient.GetZomatoAccessToken(
		c,
		&auth.GetZomatoAccessTokenRequest{
			Token: token,
		},
	)
	if err != nil || authResp.GetResponse().GetStatus() == auth.Status_FAILURE {
		log.WithError(err).Error("error exchanging token with zat")
		c.AbortWithStatusJSON(
			http.StatusBadGateway,
			models.StatusFailed("something went wrong"),
		)
		return
	}

	zat := authResp.GetZatData()
	http.SetCookie(c.Writer, &http.Cookie{
		Name:     zat.GetName(),
		Value:    zat.GetValue(),
		Path:     "/",
		Expires:  time.Now().Add(cookieExpiration),
		Secure:   true,
		HttpOnly: true,
	})

	ttaz := authResp.GetTtazData()
	http.SetCookie(c.Writer, &http.Cookie{
		Name:    ttaz.GetName(),
		Value:   ttaz.GetValue(),
		Path:    "/",
		Expires: time.Now().Add(cookieExpiration),
		Secure:  true,
	})

	c.JSON(
		http.StatusOK,
		models.StatusSuccess("callback successful"),
	)
}
