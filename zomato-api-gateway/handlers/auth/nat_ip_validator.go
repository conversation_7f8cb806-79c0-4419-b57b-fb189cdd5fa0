package auth

import (
	"net/http"

	"github.com/Zomato/go/config"
	models "github.com/Zomato/zomato-api-gateway/models"
	"github.com/gin-gonic/gin"
)

func IsOneSupportAutomationWorkerIP(c *gin.Context) {
	ip := c.GetHeader(trueClientIPHeader)
	whitelistedIps := config.GetStringSlice(c, "one_support.automation_worker_nat_ips")
	for i := range whitelistedIps {
		if ip == whitelistedIps[i] {
			return
		}
	}
	c.AbortWithStatusJSON(
		http.StatusUnauthorized,
		models.StatusFailed(ip),
	)
	return
}
