package auth

import (
	"errors"
	"net/http"
	"strings"

	authpkg "github.com/Zomato/auth-service-client-golang/pkg/auth"
	authsvc "github.com/Zomato/auth-service-client-golang/pkg/login"
	"github.com/Zomato/go/grpc/metadata"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	models "github.com/Zomato/zomato-api-gateway/models"
	authmodels "github.com/Zomato/zomato-api-gateway/models/auth"
	"github.com/gin-gonic/gin"
)

const (
	CLIENT_ID             = "X-Client-Id"
	APP_VERSION           = "X-Zomato-App-Version"
	CITY_ID               = "X-City-Id"
	UUID                  = "X-Zomato-UUID"
	LANG                  = "X-App-Language"
	FORCESERVER           = "forceserver"
	USER_AGENT            = "User-Agent"
	X_USER_AGENT          = "X-User-Agent"
	USER_IP               = "X-IP-USER"
	COOKIE                = "cookie"
	AGENT_NOT_AUTHORIZED  = "agent not authorized"
	AKAMAI_EDGESCAPE      = "x-akamai-edgescape"
	TRUE_CLIENT_IP_HEADER = "True-Client-IP"
)

func LoginWithAppleHandler(c *gin.Context) {

	log := logger.FromContext(c)
	env := env.FromContext(c)

	var req authmodels.AppleLoginRequest

	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Errorf("could not bind request: %s", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Something went wrong. Please try again later!"))
		return
	}

	var conn = env.AuthServiceConn()

	client := authsvc.NewLoginServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	authResp, err := client.LoginWithApple(ctx,
		&authsvc.LoginWithAppleRequest{
			Code:    req.Code,
			IdToken: req.IdToken,
			Name:    req.Name,
			Email:   req.Email,
			State:   req.State,
		},
	)

	if err != nil {
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Something went wrong. Please try again later!"))
		return
	}

	appleLoginResponse := getAppleLoginResponse(authResp)
	c.JSON(http.StatusOK, appleLoginResponse)

}

func DisconnectAppleHandler(ctx *gin.Context) {
	client := api.GetClientFromContext(ctx)

	userID := client.UserID()

	if userID == 0 {
		ctx.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusFailed("Not Authorised!"),
		)
		return
	}

	err := disconnectApple(ctx, userID)
	if err != nil {
		ctx.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed("Something went wrong. Please try again later!"),
		)
		return
	}

	ctx.JSON(http.StatusOK, models.StatusSuccess("Your apple account is disconnected"))

}

func getAppleLoginResponse(resp *authsvc.LoginWithAppleResponse) (response authmodels.AppleLoginResponse) {
	appleLoginResponse := authmodels.AppleLoginResponse{}
	appleLoginResponse.Status = resp.Status
	appleLoginResponse.Message = resp.Message
	appleLoginResponse.AccessToken = resp.AccessToken
	appleLoginResponse.UserID = resp.UserId
	appleLoginResponse.IsNewUser = resp.IsNewUser
	return appleLoginResponse
}

func prepareHeaders(c *gin.Context) metadata.MD {

	appVersion := c.GetHeader(APP_VERSION)
	clientID := c.GetHeader(CLIENT_ID)
	cityID := c.GetHeader(CITY_ID)
	uuID := c.GetHeader(UUID)
	lang := c.GetHeader(LANG)
	forceServer := c.GetHeader(FORCESERVER)
	userAgent := c.GetHeader(USER_AGENT)
	cookie := c.GetHeader(COOKIE)
	userIP := c.ClientIP()
	akamaiEdgescape := c.GetHeader(AKAMAI_EDGESCAPE)
	trueUserIp := c.GetHeader(TRUE_CLIENT_IP_HEADER)

	headersMap := map[string]string{
		APP_VERSION:           appVersion,
		CLIENT_ID:             clientID,
		CITY_ID:               cityID,
		UUID:                  uuID,
		LANG:                  lang,
		FORCESERVER:           forceServer,
		X_USER_AGENT:          userAgent,
		USER_IP:               userIP,
		COOKIE:                cookie,
		AKAMAI_EDGESCAPE:      akamaiEdgescape,
		api.AuthorizationCtx:  api.GetAuthTokenFromContext(c),
		TRUE_CLIENT_IP_HEADER: trueUserIp,
	}

	return metadata.New(headersMap)
}

func disconnectApple(c *gin.Context, userID int64) error {
	env := env.FromContext(c)

	log := logger.FromContext(c).WithFields(map[string]interface{}{
		"user_id": userID,
	})

	var conn = env.AuthServiceConn()

	client := authsvc.NewLoginServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	authResp, err := client.DisconnectApple(
		ctx,
		&authsvc.DisconnectAppleRequest{
			UserId: userID,
		},
	)

	if err != nil {
		log.WithFields(map[string]interface{}{
			"error": err,
		}).Error("Apple Disconnect: error while disconnecting apple")
		return err
	}

	if !authResp.Status {
		return errors.New("failed to disconnect")
	}

	log.Info("Apple Disconnect: disconnected apple successfully")
	return nil
}

func IsUserLoggedIn(ctx *gin.Context) {
	client := api.GetClientFromContext(ctx)

	userID := client.UserID()

	if userID == 0 {
		ctx.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusFailed("Not Authorised!"),
		)
		return
	}
}

func IsAgentAuthorized(ctx *gin.Context) {
	log := logger.FromContext(ctx)
	env := env.FromContext(ctx)

	var conn = env.AuthServiceConn()
	authClient := authpkg.NewAuthServiceClient(conn)

	var permissions []string
	jwt := ExtractToken(ctx.GetHeader("jwt-token"))
	if len(jwt) == 0 {
		err := errors.New(AGENT_NOT_AUTHORIZED)
		log.WithError(err).Error("no jwt-token header")
		ctx.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusFailed("You are not authorized to perform this action."),
		)
		return
	}

	jwtValidationResponse, err := authClient.DecodeJWTToken(ctx,
		&authpkg.DecodeJWTTokenRequest{
			Token: jwt,
		},
	)

	if err != nil {
		log.WithError(err).Error("error validating the jwt")
		ctx.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusFailed("You are not authorized to perform this action."),
		)
		return
	}

	payload := jwtValidationResponse.GetPayload()
	if payload == nil {
		err = errors.New(AGENT_NOT_AUTHORIZED)
		log.WithError(err).Error("error validating the jwt : nil payload")
		ctx.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusFailed("You are not authorized to perform this action."),
		)
		return
	}

	permissions = payload.GetPermissions()
	userID := payload.GetUserID()

	if userID != 0 && permissions != nil {
		for _, permission := range permissions {
			if permission == "ALCOBEV_ID_APPROVER" || permission == "ALCOBEV_ID_APPROVER_ADMIN" {
				return
			}
		}
	}
	if userID == 0 {
		err = errors.New(AGENT_NOT_AUTHORIZED)
		log.WithError(err).Error("userID is 0")
	} else if permissions == nil {
		err = errors.New(AGENT_NOT_AUTHORIZED)
		log.WithError(err).Error("nil permissions")
	} else {
		err = errors.New(AGENT_NOT_AUTHORIZED)
		log.WithError(err).Error("agent doesnt have permission")
	}
	ctx.AbortWithStatusJSON(
		http.StatusUnauthorized,
		models.StatusFailed("You are not authorized to perform this action."),
	)
	return
}

func ExtractToken(bearToken string) string {
	strArr := strings.Split(bearToken, " ")
	if len(strArr) == 2 && strArr[0] == "Bearer" {
		return strArr[1]
	}
	return ""
}
