package auth

import (
	"net/http"
	"strconv"

	"github.com/Zomato/auth-service-client-golang/pkg/auth"
	authroles "github.com/Zomato/auth-service-client-golang/pkg/userroles"
	"github.com/Zomato/go/grpc/metadata"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/internal/user"
	models "github.com/Zomato/zomato-api-gateway/models"
	authmodels "github.com/Zomato/zomato-api-gateway/models/auth"
	"github.com/gin-gonic/gin"
)

type ValidateResponse struct {
	IsLoggedIn    bool               `json:"is_logged_in"`
	UserID        int                `json:"user_id"`
	Roles         []*authmodels.Role `json:"roles,omitempty"`
	Name          string             `json:"name,omitempty"`
	ProfilePicURI string             `json:"profile_pic_uri,omitempty"`
	Email         string             `json:"email,omitempty"`
	Mobile        string             `json:"mobile,omitempty"`
}

// ValidateHandler validates if the user is logged in and the session is valid
// based on access tokens in the cookie / headers
func ValidateHandler(c *gin.Context) {
	var (
		err                  error
		fetchUserProfileFlag bool
	)
	log := logger.FromContext(c)
	env := env.FromContext(c)

	fetchUserProfile := c.Query("fetch_user_profile")
	if fetchUserProfile != "" {
		fetchUserProfileFlag, err = strconv.ParseBool(fetchUserProfile)
		if err != nil {
			log.WithError(err).Errorf("error in converting fetch_user_profile to bool, fetch_user_profile:- %+v", fetchUserProfile)
			c.AbortWithStatusJSON(
				http.StatusBadRequest,
				models.StatusSomethingWentWrong(),
			)
			return
		}
	}

	authClient := auth.NewAuthServiceClient(env.AuthServiceConn())

	zomatoAccessToken := c.GetHeader("x-zomato-access-token")
	if zomatoAccessToken == "" {
		zomatoAccessToken, err = c.Cookie("zat") // browser will send zat in cookie
		if err != nil && err != http.ErrNoCookie {
			log.WithError(err).Error("error accessing zat cookie")
		}
	}

	zomatoClientID := c.GetHeader("x-zomato-client-id")
	if zomatoClientID == "" {
		zomatoClientID, err = c.Cookie("cid") // browser will send cid in cookie
		if err != nil && err != http.ErrNoCookie {
			log.WithError(err).Error("error accessing cid cookie")
		}
	}

	if zomatoClientID == "" || zomatoAccessToken == "" {
		log.Debug("missing client_id / access_token in the request")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.FailedResponse(ValidateResponse{}, "missing authorization credentials"),
		)
		return
	}

	zomatoTokenDetails := &auth.ZomatoAccessToken{
		AccessToken: zomatoAccessToken,
		ClientId:    zomatoClientID,
	}
	authTokenResp, err := authClient.GetAccessTokenDetails(c,
		&auth.AccessTokenDetailRequest{
			Token: zomatoTokenDetails,
		},
	)

	if err != nil {
		log.WithError(err).Error("error fetching auth details from access token")
		c.AbortWithStatusJSON(
			http.StatusBadGateway,
			models.FailedResponse(ValidateResponse{}, "could not authenticate"),
		)
		return
	}

	tokenDetails := authTokenResp.GetAccessTokenDetails()
	if !tokenDetails.GetIsValid() {
		log.WithError(err).Debug("access token is invalid")
		c.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.FailedResponse(ValidateResponse{}, "invalid credentials"),
		)
		return
	}

	c.Set(api.AuthorizationCtx, tokenDetails.GetIdToken())

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	authRolesClient := authroles.NewUserRolesServiceClient(env.AuthServiceConn())
	response, err := authRolesClient.ListUserRoles(ctx, &authroles.ListUserRolesRequest{})
	if err != nil {
		log.WithError(err).Error("error while fetching user roles")
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.FailedResponse(ValidateResponse{}, "something went wrong"),
		)
		return
	}

	userID := int(tokenDetails.GetUserId())

	var (
		name, profileImage, userEmail, userMobile string
	)
	if fetchUserProfileFlag {
		userProfile, err := user.FetchUserProfileByUserID(c, int64(userID))
		if err != nil {
			log.WithField("user_id", userID).WithError(err).Error("error while fetching user profile")
			c.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusSomethingWentWrong(),
			)
			return
		}
		name = userProfile.Name
		profileImage = userProfile.GetProfileImageUri()
		userEmail = userProfile.GetEmail()
		userMobile = userProfile.GetMobile()
	}

	c.JSON(http.StatusOK,
		models.SuccessResponse(ValidateResponse{
			IsLoggedIn:    true,
			UserID:        userID,
			Roles:         prepareRolesResponse(response),
			Name:          name,
			ProfilePicURI: profileImage,
			Email:         userEmail,
			Mobile:        userMobile,
		}, ""),
	)
}

func prepareRolesResponse(response *authroles.ListUserRolesResponse) []*authmodels.Role {
	var roles []*authmodels.Role
	for _, role := range response.GetRoles() {
		roles = append(roles, &authmodels.Role{
			Name: role.GetName(),
		})
	}
	return roles
}
