package auth

import (
	"fmt"
	"github.com/Zomato/zomato-api-gateway/pkg/utils"
	"net/http"

	"github.com/Zomato/go/config"
	"github.com/Zomato/go/i18n"
	"github.com/Zomato/go/logger"
	models "github.com/Zomato/zomato-api-gateway/models"
	"github.com/gin-gonic/gin"
)

const (
	akamaiLoginEndpoint = "/login/akamai"
)

func AkamaiCallbackHandler(c *gin.Context) {
	// This is a temporary workaround for Akamai bug of not redirecting to the application redirect_uri. To be removed after Akamai bug is fixed
	// Separate handler is created to better handle or modify request redirect if needed rather than directly redirecting from router
	redirectURL := fmt.Sprint(config.GetString(c, "akamai.redirect_host"), akamaiLoginEndpoint)
	code, _ := c.<PERSON>uer<PERSON>("code")
	state, _ := c.GetQuery("state")
	if utils.IsBlank(code) || utils.IsBlank(state) {
		logger.Errorf("code or state is empty")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(i18n.Translate(c, "something-went-wrong")),
		)
		return
	}
	fullURL := fmt.Sprintf("%s?code=%s&state=%s", redirectURL, code, state)
	c.Redirect(http.StatusTemporaryRedirect, fullURL)
}
