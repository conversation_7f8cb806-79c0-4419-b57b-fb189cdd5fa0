package onetap

import (
	"fmt"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

const (
	DefaultProfileImage = "web/assets/2267aec184e096c98c46a1565a5563661664945464.png"
	CdnImagePrefix      = "https://b.zmtcdn.com/"
)

func getImageCDNUrl(url string) string {
	return fmt.Sprintf("%v%v", CdnImagePrefix, url)
}

func toTitle(s string) string {
	titleCaser := cases.Title(language.English)
	titleString := titleCaser.String(s)
	return titleString
}
