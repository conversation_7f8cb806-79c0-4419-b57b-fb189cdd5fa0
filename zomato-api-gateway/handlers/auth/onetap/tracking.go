package onetap

import (
	"context"

	"github.com/Zomato/go/logger"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/authservice/apploginevents"
	"github.com/Zomato/zomato-api-gateway/models/sushi"
	"google.golang.org/protobuf/encoding/protojson"
)

const (
	appLoginEventsTableName = "app_login_events"
)

func getOnetapEventsTrackingData(ctx context.Context, trackingPayload *apploginevents.AppLoginEvents, impressionEvent, tapEvent string) []*sushi.TrackingData {
	log := logger.FromContext(ctx)

	eventNames, err := getEventNames(impressionEvent, tapEvent)
	if err != nil {
		log.WithError(err).Errorf("[Onetap] Error while getting event names for impressionEvent: %+v, and tapEvent: %+v", impressionEvent, tapEvent)
	}

	payload, payloadErr := getAppLoginEventPayload(trackingPayload)
	if payloadErr != nil {
		log.WithError(payloadErr).Errorf("[Onetap] Error in getting payload for %#v", trackingPayload)
	}

	return []*sushi.TrackingData{
		{
			TableName:  appLoginEventsTableName,
			EventNames: eventNames,
			Payload:    payload,
		},
	}
}

func getAppLoginEventPayload(payload *apploginevents.AppLoginEvents) (string, error) {
	response, err := protojson.Marshal(payload)
	if err != nil {
		return "", err
	}

	return string(response), nil
}

func getEventNames(impressionEvent, tapEvent string) (eventNames *sushi.EventNames, err error) {
	eventNames = &sushi.EventNames{}

	if len(impressionEvent) > 0 {
		eventNames.Impression, err = sushi.GetEventNameForTracking(impressionEvent)
		if err != nil {
			return nil, err
		}
	}

	if len(tapEvent) > 0 {
		eventNames.Tap, err = sushi.GetEventNameForTracking(tapEvent)
		if err != nil {
			return nil, err
		}
	}

	return eventNames, nil
}
