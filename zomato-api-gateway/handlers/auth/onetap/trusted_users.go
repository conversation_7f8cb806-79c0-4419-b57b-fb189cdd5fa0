package onetap

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/Zomato/auth-service-client-golang/pkg/auth"
	"github.com/Zomato/go/config"
	"github.com/Zomato/go/grpc/metadata"
	"github.com/Zomato/go/i18n"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/authservice/apploginevents"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	models "github.com/Zomato/zomato-api-gateway/models"
	authmodels "github.com/Zomato/zomato-api-gateway/models/auth/onetap"
	"github.com/Zomato/zomato-api-gateway/models/sushi"
	"github.com/Zomato/zomato-api-gateway/pkg/utils"
	"github.com/gin-gonic/gin"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	CLIENT_ID               = "X-Client-Id"
	APP_VERSION             = "X-Zomato-App-Version"
	UUID                    = "X-Zomato-UUID"
	FORCESERVER             = "forceserver"
	USER_AGENT              = "User-Agent"
	X_USER_AGENT            = "X-User-Agent"
	USER_IP                 = "X-IP-USER"
	CITY_ID                 = "X-City-Id"
	COOKIE                  = "cookie"
	JUMBO_SESSION_ID_HEADER = "x-jumbo-session-id"
)

func GetTrustedOnetapUsersHandler(c *gin.Context) {

	log := logger.FromContext(c)
	apiClient := api.GetClientFromContext(c)

	if shouldSkipOnetapForClient(c, apiClient) {
		// send default 200
		c.JSON(http.StatusOK, models.StatusSuccess(i18n.Translate(c, "success-title-text")))
		return
	}

	if !config.GetBool(c, "auth_service.onetap.get_trusted_users.enabled") || !apiClient.SupportsOnetapLogin() {
		// send default 200
		c.JSON(http.StatusOK, models.StatusSuccess(i18n.Translate(c, "success-title-text")))
		return
	}

	var req authmodels.GetTrustedOneTapUsersRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.WithError(err).Error("could not bind request")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			i18n.Translate(c, "something-went-wrong"),
		)
		return
	}

	log = log.WithField("request", req)

	if err := validateGetTrustedUsersRequest(c, &req); err != nil {
		log.WithError(err).Error("validation failed")
		c.JSON(http.StatusBadRequest, i18n.Translate(c, "something-went-wrong"))
		return
	}

	request := prepareTrustedOnetapUsersRequest(c, &req)
	env := env.FromContext(c)
	conn := env.AuthServiceConn()
	client := auth.NewAuthServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)
	ctxWithTimeout, cancel := context.WithTimeout(
		ctx,
		config.GetDuration(ctx, "auth_service.onetap.get_trusted_users.timeout"),
	)
	defer cancel()

	response, err := client.GetTrustedOnetapUsers(ctxWithTimeout, request)
	if err != nil {
		log.WithError(err).Error("failed to fetch trusted users")
		c.JSON(http.StatusInternalServerError, i18n.Translate(c, "something-went-wrong"))
		return
	}

	trustedUsersResponse := buildTrustedUsersResponse(c, response)
	c.JSON(http.StatusOK, trustedUsersResponse)
}

func RemoveTrustedOnetapUserHandler(c *gin.Context) {
	log := logger.FromContext(c)
	apiClient := api.GetClientFromContext(c)

	if shouldSkipOnetapForClient(c, apiClient) {
		c.JSON(http.StatusOK, models.StatusSuccess(i18n.Translate(c, "success-title-text")))
		return
	}

	if !config.GetBool(c, "auth_service.onetap.remove_trusted_user.enabled") || !apiClient.SupportsOnetapLogin() {
		// send default 200
		c.JSON(http.StatusOK, models.StatusSuccess(i18n.Translate(c, "success-title-text")))
		return
	}

	var req authmodels.RemoveTrustedOneTapUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.WithError(err).Error("failed to bind query")
		c.JSON(http.StatusBadRequest, i18n.Translate(c, "something-went-wrong"))
		return
	}

	log = log.WithField("request", req)

	if err := validateRemoveTrustedUserRequest(c, &req); err != nil {
		log.WithError(err).Error("validation failed")
		c.JSON(http.StatusBadRequest, i18n.Translate(c, "something-went-wrong"))
		return
	}

	request := prepareRemoveTrustedOnetapUserRequest(c, &req)
	env := env.FromContext(c)
	conn := env.AuthServiceConn()
	client := auth.NewAuthServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)
	ctxWithTimeout, cancel := context.WithTimeout(
		ctx,
		config.GetDuration(ctx, "auth_service.onetap.remove_trusted_user.timeout"),
	)
	defer cancel()

	response, err := client.RemoveTrustedOnetapUser(ctxWithTimeout, request)
	if err != nil {
		log.WithError(err).Error("failed to remove trusted user")
		c.JSON(http.StatusInternalServerError, i18n.Translate(c, "something-went-wrong"))
		return
	}

	if response == nil || !response.GetStatus() {
		log.Errorf("Failed to remove trusted user, response is nil or status is false")
		c.JSON(http.StatusInternalServerError, i18n.Translate(c, "something-went-wrong"))
		return
	}

	buildRemoveTrustedUserResponse := buildRemoveTrustedUserResponse(c, response)

	c.JSON(http.StatusOK, buildRemoveTrustedUserResponse)
}

func shouldSkipOnetapForClient(c *gin.Context, apiClient *api.Client) bool {
	if (apiClient.IsAndroidBeta() || apiClient.IsAndroidConsumer()) && !config.GetBool(c, "auth_service.onetap.enable_for_android") {
		return true
	}

	if apiClient.IsIOSConsumer() && !config.GetBool(c, "auth_service.onetap.enable_for_ios") {
		return true
	}

	return false
}

func validateGetTrustedUsersRequest(c *gin.Context, req *authmodels.GetTrustedOneTapUsersRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if utils.IsBlank(c.GetHeader(UUID)) {
		return fmt.Errorf("missing UUID header")
	}

	return nil
}

func validateRemoveTrustedUserRequest(c *gin.Context, req *authmodels.RemoveTrustedOneTapUserRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if utils.IsBlank(req.MobileToken) {
		return fmt.Errorf("missing mobile token")
	}
	if utils.IsBlank(req.DeviceToken) {
		return fmt.Errorf("missing device token")
	}
	if utils.IsBlank(c.GetHeader(UUID)) {
		return fmt.Errorf("missing UUID header")
	}

	return nil
}

func prepareTrustedOnetapUsersRequest(c *gin.Context, req *authmodels.GetTrustedOneTapUsersRequest) *auth.GetTrustedOnetapUsersRequest {
	return &auth.GetTrustedOnetapUsersRequest{
		DeviceId:    c.GetHeader(UUID),
		DeviceToken: req.DeviceToken,
	}
}

func prepareRemoveTrustedOnetapUserRequest(c *gin.Context, req *authmodels.RemoveTrustedOneTapUserRequest) *auth.RemoveTrustedOnetapUserRequest {
	return &auth.RemoveTrustedOnetapUserRequest{
		DeviceToken:     req.DeviceToken,
		EncryptedMobile: req.MobileToken,
		DeviceId:        c.GetHeader(UUID),
	}
}

func buildRemoveTrustedUserResponse(c *gin.Context, response *auth.RemoveTrustedOnetapUserResponse) *authmodels.RemoveTrustedOneTapUserResponse {
	accounts := make([]*authmodels.TrustedUserAccount, 0, len(response.GetUsers()))

	for idx, user := range response.GetUsers() {
		if user == nil {
			continue
		}

		account := buildTrustedUserAccount(c, user, idx)
		accounts = append(accounts, account)
	}

	data := buildTrustedUsersDataResponse(c, accounts, response.GetIsRememberThisDeviceEnabled())

	return &authmodels.RemoveTrustedOneTapUserResponse{
		Status:  "success",
		Message: i18n.Translate(c, "zm-auth-onetap-trusted-user-remove-success"),
		Data:    data,
	}
}

func buildTrustedUsersResponse(c *gin.Context, response *auth.GetTrustedOnetapUsersResponse) *authmodels.GetTrustedOneTapUsersResponse {
	accounts := make([]*authmodels.TrustedUserAccount, 0, len(response.GetUsers()))

	for idx, user := range response.GetUsers() {
		if user == nil {
			continue
		}

		account := buildTrustedUserAccount(c, user, idx)
		accounts = append(accounts, account)
	}

	data := buildTrustedUsersDataResponse(c, accounts, response.GetIsRememberThisDeviceEnabled())

	return &authmodels.GetTrustedOneTapUsersResponse{
		Status:  "success",
		Message: i18n.Translate(c, "zm-auth-onetap-trusted-users-fetch-success"),
		Data:    data,
	}
}

func buildTrustedUsersDataResponse(c *gin.Context, accounts []*authmodels.TrustedUserAccount,
	isRememberThisDeviceEnabled bool,
) *authmodels.TrustedUsersData {

	data := &authmodels.TrustedUsersData{
		Header:      buildHeaderLabel(c),
		Accounts:    accounts,
		LoginOption: buildLoginOption(c),
	}

	if isRememberThisDeviceEnabled {
		data.RememberLoginCheckbox = buildRememberLoginCheckbox(c)
	}

	if len(accounts) > 2 {
		moreCount := len(accounts) - 1
		data.MoreAccounts = buildMoreAccountsButton(c, moreCount)
	}

	return data

}

func buildHeaderLabel(c *gin.Context) *sushi.Label {
	return &sushi.Label{
		Text: i18n.Translate(c, "zm-auth-onetap-trusted-users-choose-account-title"),
		Font: &sushi.Font{
			Size:   sushi.FontSize300,
			Weight: sushi.FontSemiBold,
		},
		Color: &sushi.Color{
			Type:        sushi.ColorGrey,
			Tint:        sushi.ColorTint700,
			ThemeBucket: sushi.NewThemeBucket(sushi.TokenColorBaseGrey700),
		},
	}
}

func buildTrustedUserAccount(c *gin.Context, user *auth.User, idx int) *authmodels.TrustedUserAccount {
	maskedMobile := maskMobileNumberWithIsdCode(user.GetMobile(), user.GetIsdCode())
	encryptedMobile := user.GetEncryptedMobile()

	response := &authmodels.TrustedUserAccount{
		Image: buildTrustedUserImage(user, idx),
		Title: &sushi.Label{
			Text: toTitle(user.GetName()),
			Font: &sushi.Font{
				Size:   sushi.FontSize300,
				Weight: sushi.FontSemiBold,
			},
			Color: &sushi.Color{
				Type:        sushi.ColorBlack,
				Tint:        sushi.ColorTint900,
				ThemeBucket: sushi.NewThemeBucket(sushi.TokenColorTextPrimary),
			},
		},
		Subtitle: &sushi.Label{
			Text: maskedMobile,
			Font: &sushi.Font{
				Size:   sushi.FontSize100,
				Weight: sushi.FontRegular,
			},
			Color: &sushi.Color{
				Type:        sushi.ColorGrey,
				Tint:        sushi.ColorTint600,
				ThemeBucket: sushi.NewThemeBucket(sushi.TokenColorTextQuaternary),
			},
		},
		ID: encryptedMobile,
		Icon: &sushi.Icon{
			Code: "e89f",
			Color: &sushi.Color{
				ThemeBucket: sushi.NewThemeBucket(sushi.TokenColorIconTertiary),
			},
			FontSize: sushi.FontSize400,
		},
		ClickAction: &sushi.ClickAction{
			Type: sushi.ActionTypeVerifyOneTapLogin,
			ID:   encryptedMobile,
		},
		RemoveAccount: buildRemoveAccountOption(c, encryptedMobile),
	}

	if config.GetBool(c, "auth_service.onetap.enable_tracking") {
		response.TrackingData = getOnetapEventsTrackingData(c, &apploginevents.AppLoginEvents{Accounts: []*apploginevents.AccountDetails{
			{
				MobileNumberEncrypted: wrapperspb.String(encryptedMobile),
			},
		}}, apploginevents.EventName_ONETAP_ACCOUNT_IMPRESSION.String(), apploginevents.EventName_ONETAP_ACCOUNT_TAP.String())
	}
	return response
}

func buildTrustedUserImage(user *auth.User, idx int) *sushi.Image {
	image := &sushi.Image{
		BgColor: &sushi.Color{
			ThemeBucket: sushi.NewThemeBucket(getThemeBucketForBackgroundColor(idx)),
		},
		Width:  40,
		Height: 40,
		Type:   sushi.ImageTypeCircle,
	}

	userProfileImageUri := user.GetProfileImageUri()

	if utils.IsBlank(userProfileImageUri) || userProfileImageUri == getImageCDNUrl(DefaultProfileImage) {
		image.OverlayTextData = &sushi.Label{
			Text: getFirstNameInitial(user.GetName()),
			Color: &sushi.Color{
				ThemeBucket: sushi.NewThemeBucket(getThemeBucketForOverlayText(idx)),
			},
			Font: &sushi.Font{
				Size:   sushi.FontSize500,
				Weight: sushi.FontMedium,
			},
			ShouldRoundForHeight: false,
		}
	} else {
		image.URL = userProfileImageUri
	}

	return image
}

func buildLoginOption(c *gin.Context) *authmodels.LoginOption {

	response := &authmodels.LoginOption{
		Title: &sushi.Label{
			Text: i18n.Translate(c, "zm-auth-onetap-choose-another-sign-in-method"),
			Font: &sushi.Font{
				Size:   sushi.FontSize300,
				Weight: sushi.FontSemiBold,
			},
			Color: &sushi.Color{
				Type:        sushi.ColorBlue,
				Tint:        sushi.ColorTint500,
				ThemeBucket: sushi.NewThemeBucket(sushi.TokenColorButtonGhostLabel),
			},
			SuffixIcon: &sushi.Icon{
				Code: "e821",
				Color: &sushi.Color{
					ThemeBucket: sushi.NewThemeBucket(sushi.TokenColorIconBrand),
				},
			},
		},
	}

	if config.GetBool(c, "auth_service.onetap.enable_tracking") {
		response.TrackingData = getOnetapEventsTrackingData(c, &apploginevents.AppLoginEvents{},
			apploginevents.EventName_USE_ANOTHER_SIGN_IN_METHOD_IMPRESSION.String(),
			apploginevents.EventName_USE_ANOTHER_SIGN_IN_METHOD_TAP.String())
	}

	return response
}

func buildRemoveAccountOption(c *gin.Context, encryptedMobile string) *sushi.Label {
	response := &sushi.Label{
		Title: &sushi.Label{
			Text: i18n.Translate(c, "zm-auth-onetap-remove-account"),
			Font: &sushi.Font{
				Size:   sushi.FontSize200,
				Weight: sushi.FontRegular,
			},
			Color: &sushi.Color{
				ThemeBucket: sushi.NewThemeBucket(sushi.TokenColorTextPrimary),
			},
		},
		BgColor: &sushi.Color{
			ThemeBucket: sushi.NewThemeBucket(sushi.TokenColorSurfaceSecondary),
		},
	}

	if config.GetBool(c, "auth_service.onetap.enable_tracking") {
		response.TrackingData = getOnetapEventsTrackingData(c, &apploginevents.AppLoginEvents{
			Accounts: []*apploginevents.AccountDetails{
				{
					MobileNumberEncrypted: wrapperspb.String(encryptedMobile),
				},
			},
		},
			apploginevents.EventName_REMOVE_ACCOUNT_IMPRESSION.String(),
			apploginevents.EventName_REMOVE_ACCOUNT_TAP.String(),
		)
	}

	return response
}

func buildRememberLoginCheckbox(c *gin.Context) *authmodels.RememberLoginCheckbox {
	response := &authmodels.RememberLoginCheckbox{
		Title: &sushi.Label{
			Text: i18n.Translate(c, "zm-auth-onetap-remember-login"),
			Font: &sushi.Font{
				Size:   sushi.FontSize200,
				Weight: sushi.FontRegular,
			},
			Color: &sushi.Color{
				Type:        sushi.ColorBlack,
				Tint:        sushi.ColorTint900,
				ThemeBucket: sushi.NewThemeBucket(sushi.TokenColorTextPrimary),
			},
		},
		IsChecked: true,
	}

	if config.GetBool(c, "auth_service.onetap.enable_tracking") {
		response.TrackingData = getOnetapEventsTrackingData(c, &apploginevents.AppLoginEvents{},
			apploginevents.EventName_REMEMBER_THIS_DEVICE_IMPRESSION.String(),
			apploginevents.EventName_REMEMBER_THIS_DEVICE_TAP.String(),
		)
	}

	return response
}

func buildMoreAccountsButton(c *gin.Context, count int) *authmodels.MoreAccounts {
	var text string
	if count == 1 {
		text = i18n.Translate(c, "zm-auth-onetap-trusted-users-more-accounts-single")
	} else {
		text = i18n.Translate(c, "zm-auth-onetap-trusted-users-more-accounts", fmt.Sprintf("%d", count))
	}

	response := &authmodels.MoreAccounts{
		Title: &sushi.Label{
			Text: text,
			Font: &sushi.Font{
				Size:   sushi.FontSize300,
				Weight: sushi.FontSemiBold,
			},
			Color: &sushi.Color{
				ThemeBucket: sushi.NewThemeBucket(sushi.TokenColorTextPrimary),
			},
		},
		Icon: &sushi.Icon{
			Code:     "e821",
			FontSize: sushi.FontSize400,
			Color: &sushi.Color{
				ThemeBucket: sushi.NewThemeBucket(sushi.TokenColorIconTertiary),
			},
		},
		ClickAction: &sushi.ClickAction{
			Type: sushi.ActionTypeSeeAllOneTapAccounts,
		},
		HeaderTitle: &sushi.Label{
			Text:      i18n.Translate(c, "zm-auth-onetap-trusted-users-choose-account-title"),
			Alignment: sushi.AlignmentCenter,
			Font: &sushi.Font{
				Size:   sushi.FontSize500,
				Weight: sushi.FontBold,
			},
			Color: &sushi.Color{
				ThemeBucket: sushi.NewThemeBucket(sushi.TokenColorTextPrimary),
			},
		},
	}

	if config.GetBool(c, "auth_service.onetap.enable_tracking") {
		response.TrackingData = getOnetapEventsTrackingData(c, &apploginevents.AppLoginEvents{},
			apploginevents.EventName_MORE_ACCOUNTS_IMPRESSION.String(),
			apploginevents.EventName_MORE_ACCOUNTS_TAP.String())
	}

	return response
}

func maskMobileNumberWithIsdCode(mobile string, isdCode int32) string {
	if len(mobile) < 4 {
		return mobile
	}
	middleDigits := mobile[1 : len(mobile)-1]

	switch isdCode {
	case 971:
		// UAE mobile numbers are typically 9 digits and start with 5
		// Show format like "+971 5XXXX X123"
		if len(mobile) >= 9 {
			return fmt.Sprintf("+%d %sXXXX X%s", isdCode, mobile[0:1], mobile[len(mobile)-3:])
		}
		return fmt.Sprintf("+%d X%sX", isdCode, middleDigits)
	case 91:
		if len(mobile) >= 10 {
			return fmt.Sprintf("+%d %sXXXX X%s", isdCode, mobile[0:1], mobile[len(mobile)-4:])
		}
		middleDigits := mobile[1 : len(mobile)-1]
		return fmt.Sprintf("+%d X%sX", isdCode, middleDigits)
	default:
		// Default format for other countries
		return fmt.Sprintf("+%d X%sX", isdCode, middleDigits)
	}
}

func prepareHeaders(c *gin.Context) metadata.MD {

	appVersion := c.GetHeader(APP_VERSION)
	clientID := c.GetHeader(CLIENT_ID)
	uuID := c.GetHeader(UUID)
	forceServer := c.GetHeader(FORCESERVER)
	userAgent := c.GetHeader(USER_AGENT)
	cookie := c.GetHeader(COOKIE)
	cityID := c.GetHeader(CITY_ID)
	jumboSessionID := c.GetHeader(JUMBO_SESSION_ID_HEADER)
	userIP := c.ClientIP()

	headersMap := map[string]string{
		APP_VERSION:             appVersion,
		CLIENT_ID:               clientID,
		UUID:                    uuID,
		FORCESERVER:             forceServer,
		X_USER_AGENT:            userAgent,
		USER_IP:                 userIP,
		COOKIE:                  cookie,
		CITY_ID:                 cityID,
		JUMBO_SESSION_ID_HEADER: jumboSessionID,
	}

	return metadata.New(headersMap)
}

func getFirstNameInitial(name string) string {
	firstNameInitial := "A"
	parts := strings.Split(name, " ")
	if len(parts) > 0 {
		firstNameInitial = strings.ToUpper(string(parts[0][0]))
	}
	return firstNameInitial
}

func getThemeBucketForOverlayText(val int) sushi.Token {
	availableTokens := []sushi.Token{sushi.TokenColorTextAccentGreenIntense, sushi.TokenColorTextAccentRedIntense, sushi.TokenColorTextAccentBlueIntense, sushi.TokenColorTextAccentYellowIntense, sushi.TokenColorTextAccentPurpleIntense}
	return availableTokens[val%len(availableTokens)]
}

func getThemeBucketForBackgroundColor(val int) sushi.Token {
	availableTokens := []sushi.Token{sushi.TokenColorSurfaceAccentGreen, sushi.TokenColorSurfaceAccentRed, sushi.TokenColorSurfaceAccentBlue, sushi.TokenColorSurfaceAccentYellow, sushi.TokenColorSurfaceAccentPurple}
	return availableTokens[val%len(availableTokens)]
}
