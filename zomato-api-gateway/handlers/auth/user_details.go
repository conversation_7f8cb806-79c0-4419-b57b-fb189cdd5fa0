package auth

import (
	"net/http"

	"github.com/Zomato/auth-service-client-golang/pkg/auth"
	"github.com/Zomato/go/config"
	"github.com/Zomato/go/grpc/metadata"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	authmodels "github.com/Zomato/zomato-api-gateway/models/auth"
	"github.com/Zomato/zomato-api-gateway/pkg/utils"
	"github.com/gin-gonic/gin"
)

var AuthEntityTypeMapper = map[authmodels.AuthEntityType]auth.AuthEntityType{
	authmodels.AuthEntityTypeUserID:  auth.AuthEntityType_AUTH_ENTITY_TYPE_USER_ID,
	authmodels.AuthEntityTypeEmail:   auth.AuthEntityType_AUTH_ENTITY_TYPE_EMAIL,
	authmodels.AuthEntityTypePhone:   auth.AuthEntityType_AUTH_ENTITY_TYPE_PHONE,
	authmodels.AuthEntityTypeInvalid: auth.AuthEntityType_AUTH_ENTITY_TYPE_INVALID,
}

func GetUserDetailsHandler(c *gin.Context) {
	if !config.GetBool(c, "auth_service.dashboard.user_details.enabled") {
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			"Couldn't fetch user details, feature not enabled",
		)
		return
	}
	log := logger.FromContext(c)
	env := env.FromContext(c)

	var req authmodels.GetUserDetailsByEntityRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Errorf("could not bind request, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			"something went wrong. Please try again later!",
		)
		return
	}

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	conn := env.AuthServiceConn()
	client := auth.NewAuthServiceClient(conn)
	authRequest := prepareGetUserByEntityRequest(&req)
	if authRequest == nil {
		log.Errorf("invalid user details")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			"invalid user details. Please try again later!",
		)
		return
	}
	response, err := client.GetUserDetailsByEntity(ctx, authRequest)
	if response == nil || err != nil {
		log.Errorf("error while getting user details, error: %v", err)
		prepareErrorResponse(c, err)
		return
	}
	if response.User == nil || response.User.UserId <= 0 {
		log.Errorf("user not found, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusOK,
			"user doesn't exists with given details",
		)
		return
	}
	userDetailsResponse := prepareGetUserDetailsByEntityResponse(response)

	c.JSON(http.StatusOK, userDetailsResponse)
}

func prepareGetUserByEntityRequest(req *authmodels.GetUserDetailsByEntityRequest) *auth.GetUserDetailsByEntityRequest {
	authEntityType := AuthEntityTypeMapper[req.AuthEntityType]

	switch authEntityType {

	case auth.AuthEntityType_AUTH_ENTITY_TYPE_USER_ID:
		if req.UserID <= 0 {
			return nil
		}
		return &auth.GetUserDetailsByEntityRequest{
			AuthEntityType: authEntityType,
			UserId:         req.UserID,
		}

	case auth.AuthEntityType_AUTH_ENTITY_TYPE_EMAIL:
		if utils.IsBlank(req.Email) {
			return nil
		}
		return &auth.GetUserDetailsByEntityRequest{
			AuthEntityType: authEntityType,
			Email:          req.Email,
		}

	case auth.AuthEntityType_AUTH_ENTITY_TYPE_PHONE:
		if utils.IsBlank(req.Phone) || req.IsdCode <= 0 {
			return nil
		}
		return &auth.GetUserDetailsByEntityRequest{
			AuthEntityType: authEntityType,
			Mobile: &auth.Mobile{
				Phone:   req.Phone,
				IsdCode: req.IsdCode,
			},
		}
	default:
		return nil
	}
}

func prepareGetUserDetailsByEntityResponse(response *auth.GetUserDetailsByEntityResponse) *authmodels.GetUserDetailsByEntityResponse {
	return &authmodels.GetUserDetailsByEntityResponse{
		Status: response.Status,
		User: &authmodels.User{
			UserID:             response.User.UserId,
			Name:               response.User.Name,
			Email:              response.User.Email,
			Mobile:             response.User.Mobile,
			SignupMethod:       response.User.SignupMethod,
			ZomatoMoneyBalance: response.User.ZomatoMoneyBalance,
		},
	}
}
