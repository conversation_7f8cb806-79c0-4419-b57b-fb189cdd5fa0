package auth

import (
	"net/http"

	authsvc "github.com/Zomato/auth-service-client-golang/pkg/auth"
	"github.com/Zomato/go/config"
	"github.com/Zomato/go/grpc/metadata"
	"github.com/Zomato/go/i18n"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	models "github.com/Zomato/zomato-api-gateway/models"
	authmodels "github.com/Zomato/zomato-api-gateway/models/auth"
	"github.com/Zomato/zomato-api-gateway/models/sushi"
	"github.com/gin-gonic/gin"
)

const (
	successStatus = "success"
)

var initiateDeleteUserProfileErrorCodeMapping = map[authsvc.InitiateDeleteUserProfileErrorCode]string{
	authsvc.InitiateDeleteUserProfileErrorCode_INITIATE_DELETE_USER_PROFILE_ERROR_CODE_INVALID:                "something-went-wrong",
	authsvc.InitiateDeleteUserProfileErrorCode_INITIATE_DELETE_USER_PROFILE_ERROR_CODE_ATTEMPTS_EXHAUSTED:     "send-otp-limit-reached",
	authsvc.InitiateDeleteUserProfileErrorCode_INITIATE_DELETE_USER_PROFILE_ERROR_CODE_ZOMAN_ACCOUNT_DELETION: "zoman-account-deletion-not-allowed",
}

var verifyDeleteUserProfileErrorCodeMapping = map[authsvc.VerifyDeleteUserProfileErrorCode]string{
	authsvc.VerifyDeleteUserProfileErrorCode_VERIFY_DELETE_USER_PROFILE_ERROR_CODE_INVALID:                      "something-went-wrong",
	authsvc.VerifyDeleteUserProfileErrorCode_VERIFY_DELETE_USER_PROFILE_ERROR_CODE_NOT_AUTHORIZED:               "not-authorized-to-complete-action",
	authsvc.VerifyDeleteUserProfileErrorCode_VERIFY_DELETE_USER_PROFILE_ERROR_CODE_WRONG_OTP_ATTEMPTS_EXHAUSTED: "verify-otp-limit-reached",
	authsvc.VerifyDeleteUserProfileErrorCode_VERIFY_DELETE_USER_PROFILE_ERROR_CODE_INVALID_OTP:                  "wrong-otp-message",
}

func InitiateDeleteUserProfileHandler(c *gin.Context) {
	if !config.GetBool(c, "auth_service.delete_user_profile.enabled") {
		c.AbortWithStatusJSON(
			http.StatusNotFound,
			models.StatusFailed(http.StatusText(http.StatusNotFound)),
		)
		return
	}

	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()

	log := logger.FromContext(c).WithField("user_id", userID)
	env := env.FromContext(c)

	var req authmodels.InitiateDeleteUserProfileRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.WithError(err).Error("[InitiateDeleteUserProfileHandler] error while binding request json")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(i18n.Translate(c, "something-went-wrong")),
		)
		return
	}

	var conn = env.AuthServiceConn()
	client := authsvc.NewAuthServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	response, err := client.InitiateDeleteUserProfile(ctx, &authsvc.InitiateDeleteUserProfileRequest{
		UserId:      userID,
		MessageUuid: req.MessageUUID,
		ClientDetails: &authsvc.ClientDetails{
			AppType:   getAppType(c),
			IpAddress: c.GetHeader(trueClientIPHeader),
		},
	})
	if err != nil {
		log.WithError(err).Error("[InitiateDeleteUserProfileHandler] error calling initiateDeleteUserProfile RPC")
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(c, "something-went-wrong")),
		)
		return
	}

	if response == nil {
		message := "something-went-wrong"
		if errorCode, ok := initiateDeleteUserProfileErrorCodeMapping[response.GetErrorCode()]; ok {
			message = errorCode
		}
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(c, message)),
		)
		return
	}

	if !response.GetStatus() {
		message := "something-went-wrong"
		if errorCode, ok := initiateDeleteUserProfileErrorCodeMapping[response.GetErrorCode()]; ok {
			message = errorCode
		}
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed(i18n.Translate(c, message)),
		)
		return
	}

	if response.GetAuthEntityType() == authsvc.AuthEntityType_AUTH_ENTITY_TYPE_INVALID || isBlank(response.GetAuthEntityValue()) {
		log.WithError(err).Error("invalid auth details from SendOTP RPC")
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(c, "something-went-wrong")),
		)
		return
	}

	apiResponse := prepareInitiateDeleteUserProfileResponse(c, response)
	c.JSON(http.StatusOK, apiResponse)
}

func prepareInitiateDeleteUserProfileResponse(c *gin.Context, response *authsvc.InitiateDeleteUserProfileResponse) *authmodels.InitiateDeleteUserProfileResponse {
	subtitle := &sushi.Label{
		Text: i18n.Translate(c, "enter-otp-text", response.GetAuthEntityValue()),
	}
	if response.GetOtpDeliveryMethod() == authsvc.OtpDeliveryMethod_OTP_DELIVERY_METHOD_WHATSAPP {
		subtitle = &sushi.Label{
			Text: i18n.Translate(c, "enter-otp-whatsapp-text", response.GetAuthEntityValue()),
		}
	}
	return &authmodels.InitiateDeleteUserProfileResponse{
		Status:  successStatus,
		Message: i18n.Translate(c, "otp-sent-successfully"),
		Data: &authmodels.InitiateDeleteUserProfileResponseData{
			MessageUUID:        response.GetMessageUuid(),
			Hash:               response.GetHash(),
			AuthenticationType: authEntityTypeMapping[response.GetAuthEntityType()],
		},
		Header: &sushi.Header{
			Title: &sushi.Label{
				Text: i18n.Translate(c, "verify-account-header"),
			},
		},
		Title: &sushi.Label{
			Text: i18n.Translate(c, "verify-account-title"),
		},
		Subtitle: subtitle,
		Button: &sushi.Button{
			Text: i18n.Translate(c, "submit-button-text"),
		},
	}
}

func VerifyDeleteUserProfileHandler(c *gin.Context) {
	if !config.GetBool(c, "auth_service.delete_user_profile.enabled") {
		c.AbortWithStatusJSON(
			http.StatusNotFound,
			models.StatusFailed(http.StatusText(http.StatusNotFound)),
		)
		return
	}

	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()

	log := logger.FromContext(c).WithField("user_id", userID)
	env := env.FromContext(c)

	var req authmodels.VerifyDeleteUserProfileRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.WithError(err).Error("[VerifyDeleteUserProfileHandler] error while binding request json")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(i18n.Translate(c, "something-went-wrong")),
		)
		return
	}

	var conn = env.AuthServiceConn()
	client := authsvc.NewAuthServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	response, err := client.VerifyDeleteUserProfile(ctx, &authsvc.VerifyDeleteUserProfileRequest{
		UserId: userID,
		Hash:   req.Hash,
		Otp:    req.OTP,
		Reason: req.Reason,
		ClientDetails: &authsvc.ClientDetails{
			AppType:   getAppType(c),
			IpAddress: c.GetHeader(trueClientIPHeader),
		},
	})
	if err != nil {
		log.WithError(err).Error("[VerifyDeleteUserProfileHandler] error calling verifyDeleteUserProfile RPC")
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(c, "something-went-wrong")),
		)
		return
	}

	if response == nil {
		message := "something-went-wrong"
		if errorCode, ok := verifyDeleteUserProfileErrorCodeMapping[response.GetErrorCode()]; ok {
			message = errorCode
		}
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(c, message)),
		)
		return
	}

	if !response.GetStatus() {
		message := "something-went-wrong"
		if errorCode, ok := verifyDeleteUserProfileErrorCodeMapping[response.GetErrorCode()]; ok {
			message = errorCode
		}
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed(i18n.Translate(c, message)),
		)
		return
	}

	c.JSON(http.StatusOK, models.StatusSuccess(i18n.Translate(c, "delete-profile-promise")))
}
