package auth

import (
	"net/http"

	authsvc "github.com/Zomato/auth-service-client-golang/pkg/auth"
	"github.com/Zomato/go/config"
	"github.com/Zomato/go/i18n"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	userInternal "github.com/Zomato/zomato-api-gateway/internal/user"
	models "github.com/Zomato/zomato-api-gateway/models"
	authmodels "github.com/Zomato/zomato-api-gateway/models/auth"
	"github.com/gin-gonic/gin"
)

func UpdateWhitelistedIpsHandler(ctx *gin.Context) {
	log := logger.FromContext(ctx)
	env := env.FromContext(ctx)

	var req authmodels.UpdateWhitelistedIpsRequest
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		log.WithError(err).Error("error while binding request json")
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(i18n.Translate(ctx, "something-went-wrong")),
		)
		return
	}

	var conn = env.AuthServiceConn()
	client := authsvc.NewAuthServiceClient(conn)

	zomatoClient := api.GetClientFromContext(ctx)
	userID := zomatoClient.UserID()

	hasPermissionForUpdatingIps := isUserAuthorized(ctx)

	if !hasPermissionForUpdatingIps {
		log.Errorf("Unauthorized users cannot update whitelisted IPs")
		ctx.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusFailed(i18n.Translate(ctx, "unauthorized-user-for-whitelistedips-operation")))
		return
	}

	var isOperationOnAPIKeyRestricted bool
	restrictedAPIKeys := config.GetStringSlice(ctx, "auth_service.whitelisted_ips_operation.restricted_api_keys")
	for _, restrictedAPIKey := range restrictedAPIKeys {
		if req.APIKey == restrictedAPIKey {
			isOperationOnAPIKeyRestricted = true
		}
	}
	if isOperationOnAPIKeyRestricted {
		log.Errorf("whitelisted IPs cannot be updated for restricted IPs")
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(i18n.Translate(ctx, "restricted-api-key-for-whitelistedips-updation")))
		return
	}

	updateObj := &authsvc.UpdateApiAuthDetailsRequest{
		UpdatedBy:      uint64(userID),
		ApiKey:         req.APIKey,
		WhiteListedIPS: req.WhitelistedIps,
	}

	_, err = client.UpdateAPIAuthDetails(ctx, updateObj)

	if err != nil {
		log.WithError(err).Error("error in calling UpdateApiAuthDetails RPC")
		ctx.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(ctx, "something-went-wrong")),
		)
		return
	}

	ctx.JSON(http.StatusOK, models.StatusSuccess("Whitelisted IPs updated successfully"))
}

func GetWhitelistedIpsHandler(ctx *gin.Context) {
	log := logger.FromContext(ctx)
	env := env.FromContext(ctx)

	apiKey, apiKeyFound := ctx.GetQuery("api_key")
	if !apiKeyFound {
		ctx.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailed("Api key not found"))
		return
	}

	var conn = env.AuthServiceConn()
	client := authsvc.NewAuthServiceClient(conn)

	hasPermissionToGetIps := isUserAuthorized(ctx)

	if !hasPermissionToGetIps {
		log.Errorf("Unauthorized users cannot get whitelisted IPs")
		ctx.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusFailed(i18n.Translate(ctx, "unauthorized-user-for-whitelistedips-operation")))
		return
	}

	getObj := &authsvc.GetApiAuthDetailsRequest{
		ApiKey: apiKey,
	}

	response, err := client.GetAPIAuthDetailsByAPIKey(ctx, getObj)
	if err != nil {
		log.WithError(err).Error("error in calling GetApiAuthDetailsByApiKey RPC")
		ctx.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(ctx, "something-went-wrong")),
		)
		return
	}

	authDetails := response.GetApiAuthDetails()
	if authDetails == nil {
		log.Errorf("No auth details found for API key: %s", apiKey)
		ctx.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(ctx, "something-went-wrong")),
		)
		return
	}

	finalData := &authmodels.GetWhitelistedIpsResponse{
		APIKey:         authDetails.GetApiKey(),
		WhitelistedIps: authDetails.GetWhiteListedIPS(),
		UserID:         authDetails.GetCreatedBy(),
		AuthID:         authDetails.GetId(),
	}
	ctx.JSON(http.StatusOK, finalData)
}

func isUserAuthorized(ctx *gin.Context) bool {
	log := logger.FromContext(ctx)
	zomatoClient := api.GetClientFromContext(ctx)
	userID := zomatoClient.UserID()

	userProfile, err := userInternal.FetchUserProfileByUserID(ctx, userID)
	if err != nil {
		log.WithError(err).Errorf("unable to fetch user profile for user id: %d", userID)
		return false
	}
	isZoman := userProfile.IsZoman

	if !isZoman {
		return false
	}

	var canGetOrUpdateWhitelistedIPs bool
	allowedUserIDs := config.GetIntSlice(ctx, "auth_service.whitelisted_ips_operation.allowed_user_ids")
	for _, allowedUserID := range allowedUserIDs {
		if userID == int64(allowedUserID) {
			canGetOrUpdateWhitelistedIPs = true
		}
	}
	return canGetOrUpdateWhitelistedIPs
}
