package auth

import (
	"encoding/csv"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"

	"github.com/Zomato/go/config"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/delight_service/pkg/utils"
	"github.com/Zomato/zomato-api-gateway/internal/user"
	"github.com/Zomato/zomato-api-gateway/models"

	authsvc "github.com/Zomato/auth-service-client-golang/pkg/userroles"
	"github.com/Zomato/go/grpc/metadata"
	"github.com/Zomato/go/i18n"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	authmodels "github.com/Zomato/zomato-api-gateway/models/auth"
	"github.com/gin-gonic/gin"
)

var (
	// EmailRegex is a regex to validate email
	emailRegex = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	// FileNameRegex is a regex to validate file name , only alphanumeric, underscores, hyphens, and dots are allowed
	fileNameRegex = regexp.MustCompile(`^[a-zA-Z0-9_\-\.\(\) ]+$`)
)

const (
	operationCreate        = "create"
	operationDelete        = "delete"
	superAdminRoleName     = "auth.roles.superadmin"
	approvalStatusPending  = "pending"
	approvalStatusApproved = "approved"
	approvalStatusRejected = "rejected"
)

var (
	requestTypeMapper = map[authsvc.RequestType]string{
		authsvc.RequestType_REQUEST_TYPE_CREATE: operationCreate,
		authsvc.RequestType_REQUEST_TYPE_DELETE: operationDelete,
	}

	requestTypeToProtoMapper = map[string]authsvc.RequestType{
		operationCreate: authsvc.RequestType_REQUEST_TYPE_CREATE,
		operationDelete: authsvc.RequestType_REQUEST_TYPE_DELETE,
	}

	statusFromProtoMapper = map[authsvc.ApprovalStatus]string{
		authsvc.ApprovalStatus_APPROVAL_STATUS_PENDING:  approvalStatusPending,
		authsvc.ApprovalStatus_APPROVAL_STATUS_APPROVED: approvalStatusApproved,
		authsvc.ApprovalStatus_APPROVAL_STATUS_REJECTED: approvalStatusRejected,
	}

	roleMappingOperationTypeMapper = map[string]authsvc.UserRoleMappingOperationType{
		operationCreate: authsvc.UserRoleMappingOperationType_USER_ROLE_MAPPING_OPERATION_TYPE_CREATE,
		operationDelete: authsvc.UserRoleMappingOperationType_USER_ROLE_MAPPING_OPERATION_TYPE_DELETE,
	}
)

func ListUserRolesHandler(c *gin.Context) {
	listUserRolesResponse := &authmodels.ListUserRolesResponse{
		Status:  false,
		Message: "Something went wrong. Please try again later!",
	}

	log := logger.FromContext(c)
	env := env.FromContext(c)

	var req authmodels.ListUserRolesRequest

	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Errorf("could not bind request,error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			listUserRolesResponse,
		)
		return
	}

	authServiceConn := env.AuthServiceConn()
	authServiceClient := authsvc.NewUserRolesServiceClient(authServiceConn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	response, err := authServiceClient.ListUserRoles(ctx, &authsvc.ListUserRolesRequest{
		Email: req.Email,
	})

	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"email": req.Email,
		}).Error("error while fetching user roles")
		prepareErrorResponse(c, err)
		return
	}

	if !response.GetStatus() {
		if response.GetErrorCode() == authsvc.ErrorCode_ERROR_CODE_INVALID_EMAIL {
			listUserRolesResponse.Message = i18n.Translate(c, "invalid-email")
			c.AbortWithStatusJSON(
				http.StatusBadRequest,
				listUserRolesResponse,
			)
			return
		}
		if response.GetErrorCode() == authsvc.ErrorCode_ERROR_CODE_USER_NOT_EXIST {
			listUserRolesResponse.Message = i18n.Translate(c, "user-not-exist-with-email")
			c.AbortWithStatusJSON(
				http.StatusBadRequest,
				listUserRolesResponse,
			)
			return
		}
	}

	listUserRolesResponse = prepareListUserRolesResponse(response)
	c.JSON(http.StatusOK, listUserRolesResponse)
}

func prepareListUserRolesResponse(
	response *authsvc.ListUserRolesResponse,
) *authmodels.ListUserRolesResponse {
	var roles []*authmodels.Role
	for _, role := range response.Roles {
		roles = append(roles, &authmodels.Role{
			Name: role.GetName(),
		})
	}
	return &authmodels.ListUserRolesResponse{
		Status:  true,
		Message: "success",
		UserID:  response.GetUser().GetUserId(),
		Name:    response.GetUser().GetName(),
		Roles:   roles,
	}
}

func UpdateUserRoleMappingHandler(c *gin.Context) {
	userRoleMappingResponse := &authmodels.UpdateUserRoleMappingResponse{
		Status:  false,
		Message: "Something went wrong. Please try again later!",
	}

	log := logger.FromContext(c)
	env := env.FromContext(c)

	var req authmodels.UpdateUserRoleMappingRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Errorf("could not bind request, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			userRoleMappingResponse,
		)
		return
	}

	if req.Operation != operationCreate && req.Operation != operationDelete {
		log.WithFields(map[string]interface{}{
			"user_id":   req.UserID,
			"role_name": req.RoleName,
			"operation": req.Operation,
		}).Error("invalid operation for update user role mapping")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			userRoleMappingResponse,
		)
		return
	}

	conn := env.AuthServiceConn()
	client := authsvc.NewUserRolesServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	if req.Operation == operationCreate {
		_, err = client.CreateUserRoleMapping(ctx, &authsvc.CreateUserRoleMappingRequest{
			UserId:   req.UserID,
			RoleName: req.RoleName,
		})
	} else {
		_, err = client.DeleteUserRoleMapping(ctx, &authsvc.DeleteUserRoleMappingRequest{
			UserId:   req.UserID,
			RoleName: req.RoleName,
		})
	}

	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"role_name": req.RoleName,
			"user_id":   req.UserID,
			"operation": req.Operation,
		}).Error("error while updating user role mapping")
		prepareErrorResponse(c, err)
		return
	}

	userRoleMappingResponse.Status = true
	userRoleMappingResponse.Message = "success"
	c.JSON(http.StatusOK, userRoleMappingResponse)
}

func UpdateUserRoleMappingHandlerV2(c *gin.Context) {
	userRoleMappingResponse := &authmodels.UpdateUserRoleMappingResponse{
		Status:  false,
		Message: "Something went wrong. Please try again later!",
	}

	if !config.GetBool(c, "auth_service.dashboard.update_user_role_mapping_v2.enabled") {
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			userRoleMappingResponse,
		)
		return
	}

	log := logger.FromContext(c)
	env := env.FromContext(c)

	var req authmodels.UpdateUserRoleMappingRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Errorf("could not bind request, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			userRoleMappingResponse,
		)
		return
	}

	if req.Operation != operationCreate && req.Operation != operationDelete {
		log.WithFields(map[string]interface{}{
			"user_id":   req.UserID,
			"role_name": req.RoleName,
			"operation": req.Operation,
		}).Error("invalid operation for update user role mapping")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			userRoleMappingResponse,
		)
		return
	}

	conn := env.AuthServiceConn()
	client := authsvc.NewUserRolesServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	if req.Operation == operationCreate {
		_, err = client.ApproveUserRoleMapping(ctx, &authsvc.ApproveUserRoleMappingRequest{
			UserId:        req.UserID,
			RoleName:      req.RoleName,
			OperationType: authsvc.UserRoleMappingOperationType_USER_ROLE_MAPPING_OPERATION_TYPE_CREATE,
			RequestId:     req.RequestId,
		})
	} else {
		_, err = client.ApproveUserRoleMapping(ctx, &authsvc.ApproveUserRoleMappingRequest{
			UserId:        req.UserID,
			RoleName:      req.RoleName,
			OperationType: authsvc.UserRoleMappingOperationType_USER_ROLE_MAPPING_OPERATION_TYPE_DELETE,
			RequestId:     req.RequestId,
		})
	}

	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"role_name":  req.RoleName,
			"user_id":    req.UserID,
			"operation":  req.Operation,
			"request_id": req.RequestId,
		}).Error("error while updating user role mapping")
		prepareErrorResponseWithErrorMessage(c, err)
		return
	}

	userRoleMappingResponse.Status = true
	userRoleMappingResponse.Message = "success"
	c.JSON(http.StatusOK, userRoleMappingResponse)
}

func BulkUpdateUserRoleMappingHandler(c *gin.Context) {
	defaultResponse := &authmodels.BulkUpdateUserRoleMappingResponse{
		Status:  false,
		Message: "Something went wrong. Please check if you're authorized!",
	}
	log := logger.FromContext(c)
	env := env.FromContext(c)
	conn := env.AuthServiceConn()
	client := authsvc.NewUserRolesServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	file, fileHeader, err := c.Request.FormFile("file")
	if err != nil {
		log.WithError(err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("File upload failed"),
		)
		return
	}
	defer file.Close()

	// Validate file name
	if !fileNameRegex.MatchString(fileHeader.Filename) {
		log.Error("uploaded file name contains invalid characters")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Uploaded file name contains invalid characters"),
		)
		return
	}

	// Check if the file is a CSV
	if fileHeader != nil && !strings.HasSuffix(strings.ToLower(fileHeader.Filename), ".csv") {
		log.Error("uploaded file is not a CSV")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Uploaded file must be a CSV"),
		)
		return
	}

	roles, err := parseCSVToUserRoles(c, file)
	if err != nil {
		log.WithError(err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(err.Error()),
		)
		return
	}

	if len(roles) == 0 {
		log.Error("no roles found in csv")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("no roles found in csv"),
		)
		return
	}

	log.WithField("roles", roles).Debug("roles to be updated")
	mappedRequest := authmodels.BulkUpdateUserRoleMappingRequest{
		Data: roles,
	}

	request, isRequestValid := prepareBulkUpdateUserRoleMappingRequest(mappedRequest)
	if !isRequestValid {
		log.Error("invalid CSV uploaded, please check again!")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("invalid CSV uploaded, please check again!"),
		)
		return
	}
	_, err = client.BulkUpdateUserRoleMappings(ctx, request)
	if err != nil {
		log.WithError(err).Error("error while bulk updating user role mapping")
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			defaultResponse,
		)
		return
	}
	c.JSON(http.StatusOK, &authmodels.BulkUpdateUserRoleMappingResponse{
		Status:  true,
		Message: "The roles should be updated in a few minutes",
	})
}

func prepareBulkUpdateUserRoleMappingRequest(
	req authmodels.BulkUpdateUserRoleMappingRequest,
) (*authsvc.BulkUpdateUserRoleMappingsRequest, bool) {
	userRoleList := []*authsvc.UserRoleInfo{}
	roleGroupsMap := map[string]bool{}

	for _, data := range req.Data {
		roleName := strings.ToLower(data.RoleName)
		email := data.Email
		roleGroupsMap[getRoleGroup(roleName)] = true
		operation := strings.ToLower(data.Operation)
		userRoleList = append(userRoleList, &authsvc.UserRoleInfo{
			RoleName:      roleName,
			Email:         email,
			OperationType: getOperationDTO(operation),
		})
	}

	roleGroups := make([]string, 0, len(roleGroupsMap))
	for roleGroup := range roleGroupsMap {
		roleGroups = append(roleGroups, roleGroup)
	}

	return &authsvc.BulkUpdateUserRoleMappingsRequest{
		UserRoleList: userRoleList,
		RoleGroups:   roleGroups,
	}, true
}

func parseCSVToUserRoles(c *gin.Context, file io.Reader) ([]*authmodels.UserRoleUpdationData, error) {
	log := logger.FromContext(c)
	reader := csv.NewReader(file)
	var roles []*authmodels.UserRoleUpdationData

	// Read all rows from the CSV
	rows, err := reader.ReadAll()
	if err == io.EOF {
		log.Error("empty csv file")
		return roles, fmt.Errorf("empty csv file")
	}

	if err != nil {
		log.Error("error while reading csv file: %v", err)
		return nil, fmt.Errorf("failed to read csv file")
	}
	// Check if the CSV is empty
	if len(rows) == 0 {
		log.Error("empty csv file")
		return roles, fmt.Errorf("empty csv file")
	}

	if len(rows) > 1000 {
		log.Error("csv file has more than allowed rows")
		return roles, fmt.Errorf("csv file has more than allowed rows")
	}

	rows = rows[1:] // Exclude the header row

	rowsLimit := config.GetInt(c, "auth_service.dashboard.bulk_upload.row_limit")

	for _, row := range rows {
		// Check if all three columns are empty
		if len(row) >= 3 && utils.IsBlank(row[0]) && utils.IsBlank(row[1]) && utils.IsBlank(row[2]) {
			continue // Skip this row as it is empty
		}
		// Check if the number of rows exceeds the limit
		if len(roles) > rowsLimit {
			return roles, fmt.Errorf("csv file has more than allowed rows")
		}
		if len(row) != 3 {
			return nil, fmt.Errorf("invalid data in csv file") // return error if invalid rows
		}

		email := strings.ToLower(strings.Trim(row[1], " "))

		if !isValidEmail(email) {
			log.Error("invalid email in csv file")
			return roles, fmt.Errorf("invalid email in csv file") // return error if invalid email
		}

		roleName := strings.ToLower(strings.Trim(row[0], " "))
		if !isValidRoleName(roleName) {
			log.Error("invalid role name in csv file")
			return roles, fmt.Errorf("invalid role name in csv file") // return error if invalid role name
		}

		if roleName == superAdminRoleName {
			log.Error("invalid role name SuperAdmin cannot be assigned")
			return roles, fmt.Errorf("invalid role name SuperAdmin cannot be assigned") // return error if invalid role name
		}
		operation := strings.ToLower(strings.Trim(row[2], " "))
		if getOperationDTO(operation) == authsvc.OperationType_OPERATION_TYPE_INVALID {
			log.Error("invalid operation in csv file")
			return roles, fmt.Errorf("invalid operation in csv file") // return error if invalid operation
		}

		data := &authmodels.UserRoleUpdationData{
			RoleName:  roleName,
			Email:     email,
			Operation: operation,
		}

		roles = append(roles, data)
	}

	return roles, nil
}

func getOperationDTO(operation string) authsvc.OperationType {
	switch operation {
	case operationCreate:
		return authsvc.OperationType_OPERATION_TYPE_CREATE
	case operationDelete:
		return authsvc.OperationType_OPERATION_TYPE_DELETE
	default:
		return authsvc.OperationType_OPERATION_TYPE_INVALID
	}
}

func isValidRoleName(roleName string) bool {
	if utils.IsBlank(roleName) || (len(strings.Split(roleName, ".")) != 3) {
		return false
	}
	return true
}

func getRoleGroup(roleName string) string {
	roleNameParts := strings.Split(roleName, ".")
	if len(roleNameParts) < 2 {
		return ""
	}
	return strings.Join(roleNameParts[:2], ".")
}

func UpdateUserRolesForDev(c *gin.Context, roleName string, userID int64, operation string) error {
	log := logger.FromContext(c)
	env := env.FromContext(c)

	conn := env.AuthServiceConn()
	client := authsvc.NewUserRolesServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	_, err := client.CreateUserRoleMapping(ctx, &authsvc.CreateUserRoleMappingRequest{
		UserId:   userID,
		RoleName: roleName,
	})

	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"role_name": roleName,
			"user_id":   userID,
			"operation": "create",
		}).Error("error while updating user role mapping")
		return err
	}

	return nil
}

func IsZoman(ctx *gin.Context) {
	if gin.Mode() == gin.TestMode {
		return
	}

	log := logger.FromContext(ctx)
	zomatoClient := api.GetClientFromContext(ctx)
	userID := zomatoClient.UserID()

	userProfile, err := user.FetchUserProfileByUserID(ctx, userID)
	if err != nil {
		log.WithError(err).Errorf("unable to fetch user profile for user id: %d", userID)
		ctx.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusFailed("Unable to get user profile by user id"),
		)
		return
	}
	isZoman := userProfile.IsZoman

	if !isZoman {
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("User is not a zoman"),
		)
		return
	}
}

func IsEternal(ctx *gin.Context) {
	if gin.Mode() == gin.TestMode {
		return
	}
	log := logger.FromContext(ctx)
	zomatoClient := api.GetClientFromContext(ctx)
	userID := zomatoClient.UserID()

	userProfile, err := user.FetchUserProfileByUserID(ctx, userID)
	if err != nil {
		log.WithError(err).Errorf("unable to fetch user profile for user id: %d", userID)
		ctx.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed("Unable to get user profile by user id"),
		)
		return
	}
	isEternal := userProfile.IsEternal

	if !isEternal {
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("Please login with eternal email address"),
		)
		return
	}
}

func IsOppoBackend(c *gin.Context) {
	client := api.GetClientFromContext(c)
	if !client.IsOppoBackend() {
		c.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusFailed("Unauthorized Request"),
		)
		return
	}
}

func isValidEmail(email string) bool {
	if email == "" {
		return false
	}
	email = strings.TrimSpace(email)

	return emailRegex.MatchString(email)
}

func ListUsersByRoleHandler(c *gin.Context) {
	listUserRolesResponse := &authmodels.ListUsersByRoleResponse{
		Status:  false,
		Message: "Something went wrong. Please try again later!",
	}
	if !config.GetBool(c, "auth_service.dashboard.list_role_users.enabled") {
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			listUserRolesResponse,
		)
		return
	}

	log := logger.FromContext(c)
	env := env.FromContext(c)

	var req authmodels.ListUsersByRoleRequest

	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Errorf("could not bind request, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			listUserRolesResponse,
		)
		return
	}
	if utils.IsBlank(req.RoleName) {
		log.Errorf("role name is empty")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			listUserRolesResponse,
		)
		return
	}

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)
	conn := env.AuthServiceConn()
	client := authsvc.NewUserRolesServiceClient(conn)

	response, err := client.ListRoleUsers(ctx, &authsvc.ListRoleUsersRequest{
		RoleName: req.RoleName,
	})
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"role_name": req.RoleName,
		}).Error("error while fetching user roles")
		prepareErrorResponse(c, err)
		return
	}

	if response == nil || !response.GetStatus() {
		log.Errorf("user not found, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusOK,
			listUserRolesResponse,
		)
		return
	}

	finalResponse := prepareListRoleUsersResponse(response)
	c.JSON(http.StatusOK, finalResponse)
}

func RequestRoleApprovalHandler(c *gin.Context) {
	log := logger.FromContext(c)
	env := env.FromContext(c)
	req := authmodels.RoleApprovalRequest{}
	res := &authmodels.RoleApprovalResponse{
		Status:  false,
		Message: "Something went wrong. Please try again later!",
	}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Errorf("could not bind request, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			&authmodels.RoleApprovalResponse{
				Status:  false,
				Message: "Invalid input",
			},
		)
		return
	}

	isValid := isValidRoleApprovalRequest(req)
	if !isValid {
		log.Errorf("could not validate request")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			&authmodels.RoleApprovalResponse{
				Status:  false,
				Message: "Invalid input",
			},
		)
		return
	}

	conn := env.AuthServiceConn()
	client := authsvc.NewUserRolesServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	response, err := client.RequestRoleApproval(ctx, &authsvc.RoleApprovalRequest{
		RoleName:    req.RoleName,
		Reason:      req.Reason,
		RequestType: requestTypeToProtoMapper[req.RequestType],
	})
	if err != nil {
		log.Errorf("error while getting role by name, error: %v", err)
		prepareErrorResponseWithErrorMessage(c, err)
		return
	}

	if !response.GetStatus() {
		log.Errorf("failed to raise approval request")
		if !utils.IsBlank(response.GetMessage()) {
			res.Message = response.GetMessage()
		}
		c.AbortWithStatusJSON(
			http.StatusOK,
			res,
		)
		return
	}

	res.Status = true
	res.Message = response.GetMessage()
	c.JSON(http.StatusOK, res)

}

func GetRoleRequestsByRequestorHandler(c *gin.Context) {
	log := logger.FromContext(c)
	env := env.FromContext(c)
	req := authmodels.GetRoleRequestsByRequestorRequest{}
	res := &authmodels.GetRoleRequestsByRequestorResponse{
		Status:  false,
		Message: "Something went wrong. Please try again later!",
	}
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Errorf("could not bind request, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			&authmodels.GetRoleRequestsByRequestorResponse{
				Status:  false,
				Message: "Invalid input",
			},
		)
		return
	}

	conn := env.AuthServiceConn()
	client := authsvc.NewUserRolesServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	response, err := client.GetRoleRequestsByRequestor(ctx, &authsvc.GetRoleRequestsByRequestorRequest{})
	if err != nil {
		log.Errorf("error while getting role by name, error: %v", err)
		prepareErrorResponseWithErrorMessage(c, err)
		return
	}

	if !response.GetStatus() {
		log.Errorf("failed to raise approval request")
		if !utils.IsBlank(response.GetMessage()) {
			res.Message = response.GetMessage()
		}
		c.AbortWithStatusJSON(
			http.StatusOK,
			res,
		)
	}

	roleApprovalRequestdata, err := prepareRoleApprovalRequestDataResponse(response.RoleApprovalRequestData)
	if err != nil {
		log.Errorf("error while preparing response, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			&authmodels.GetRoleRequestsForApproverResponse{
				Status:  false,
				Message: "Something went wrong. Please try again later!",
			},
		)
		return
	}
	res.RoleApprovalRequestData = roleApprovalRequestdata
	res.Status = true
	res.Message = response.GetMessage()
	c.JSON(http.StatusOK, res)

}

func GetRoleRequestsForApproverHandler(c *gin.Context) {
	log := logger.FromContext(c)
	env := env.FromContext(c)
	req := authmodels.GetRoleRequestsForApproverRequest{}
	res := &authmodels.GetRoleRequestsForApproverResponse{
		Status:  false,
		Message: "Something went wrong. Please try again later!",
	}
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Errorf("could not bind request, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			&authmodels.GetRoleRequestsForApproverResponse{
				Status:  false,
				Message: "Invalid input",
			},
		)
		return
	}

	conn := env.AuthServiceConn()
	client := authsvc.NewUserRolesServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	response, err := client.GetRoleRequestsForApprover(ctx, &authsvc.GetRoleRequestsForApproverRequest{})
	if err != nil {
		log.Errorf("error while getting role by name, error: %v", err)
		prepareErrorResponseWithErrorMessage(c, err)
		return
	}

	if !response.GetStatus() {
		log.Errorf("failed to raise approval request")
		if !utils.IsBlank(response.GetMessage()) {
			res.Message = response.GetMessage()
		}
		c.AbortWithStatusJSON(
			http.StatusOK,
			res,
		)
	}

	roleApprovalRequestdata, err := prepareRoleApprovalRequestDataResponse(response.RoleApprovalRequestData)
	if err != nil {
		log.Errorf("error while preparing response, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			&authmodels.GetRoleRequestsForApproverResponse{
				Status:  false,
				Message: "Something went wrong. Please try again later!",
			},
		)
		return
	}
	res.RoleApprovalRequestData = roleApprovalRequestdata
	res.Status = true
	res.Message = response.GetMessage()
	c.JSON(http.StatusOK, res)

}

func RejectApprovalRequestHandler(c *gin.Context) {
	log := logger.FromContext(c)
	env := env.FromContext(c)

	req := authmodels.RejectApprovalRequest{}
	updateRoleResponse := &authmodels.RejectApprovalResponse{
		Status:  false,
		Message: "Something went wrong. Please try again later!",
	}

	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Errorf("could not bind request, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			updateRoleResponse,
		)
		return
	}

	if utils.IsBlank(req.RequestID) {
		log.Error("request id is required")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			updateRoleResponse,
		)
		return
	}

	conn := env.AuthServiceConn()
	client := authsvc.NewUserRolesServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	if _, exists := roleMappingOperationTypeMapper[req.RequestType]; !exists {
		log.Errorf("invalid operation type: %s", req.RequestType)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			updateRoleResponse,
		)
		return
	}

	response, err := client.RejectUserRoleMapping(ctx, &authsvc.RejectUserRoleMappingRequest{
		RequestId:     req.RequestID,
		Reason:        req.Reason,
		RoleName:      req.RoleName,
		UserId:        req.RequestorUserID,
		OperationType: roleMappingOperationTypeMapper[req.RequestType],
	})
	if err != nil {
		log.Errorf("error while rejecting approval request, error: %v", err)
		prepareErrorResponseWithErrorMessage(c, err)
		return
	}

	if !response.GetStatus() {
		log.Error("failed to reject approval request")
		if !utils.IsBlank(response.GetMessage()) {
			updateRoleResponse.Message = response.GetMessage()
		}
		c.AbortWithStatusJSON(
			http.StatusOK,
			updateRoleResponse,
		)
		return
	}

	updateRoleResponse.Message = response.GetMessage()
	updateRoleResponse.Status = true
	c.JSON(http.StatusOK, updateRoleResponse)
}

func prepareRoleApprovalRequestDataResponse(
	roleApprovalRequestData []*authsvc.RoleApprovalRequestData,
) ([]*authmodels.RoleApprovalRequestData, error) {
	res := make([]*authmodels.RoleApprovalRequestData, 0)
	for _, roleApprovalRequestData := range roleApprovalRequestData {
		if _, exists := requestTypeMapper[roleApprovalRequestData.GetRequestType()]; !exists {
			return nil, fmt.Errorf("invalid request type")
		}
		if _, exists := statusFromProtoMapper[roleApprovalRequestData.GetStatus()]; !exists {
			return nil, fmt.Errorf("invalid status")
		}
		res = append(res, &authmodels.RoleApprovalRequestData{
			RoleName:              roleApprovalRequestData.GetRoleName(),
			Status:                statusFromProtoMapper[roleApprovalRequestData.GetStatus()],
			RequestID:             roleApprovalRequestData.GetRequestId(),
			RequestedAt:           roleApprovalRequestData.GetRequestedAt(),
			Reason:                roleApprovalRequestData.GetReason(),
			ApprovedAt:            roleApprovalRequestData.GetApprovedAt(),
			RequestApproverUserId: roleApprovalRequestData.GetRequestApproverUserId(),
			RequestorUserId:       roleApprovalRequestData.GetRequestorUserId(),
			ApprovedBy:            roleApprovalRequestData.GetApprovedBy(),
			RequestType:           requestTypeMapper[roleApprovalRequestData.GetRequestType()],
			RequestorEmail:        roleApprovalRequestData.GetRequestorEmail(),
		})
	}
	return res, nil
}

func prepareListRoleUsersResponse(
	response *authsvc.ListRoleUsersResponse,
) *authmodels.ListUsersByRoleResponse {
	var users []*authmodels.UserRoles
	for _, user := range response.Users {
		users = append(users, &authmodels.UserRoles{
			UserID:        user.GetUserId(),
			Email:         user.GetEmail(),
			RoleUpdatedAt: user.GetRoleUpdatedAt(),
		})
	}
	return &authmodels.ListUsersByRoleResponse{
		Status:  true,
		Message: "success",
		Users:   users,
	}
}

func isValidRoleApprovalRequest(req authmodels.RoleApprovalRequest) bool {
	if utils.IsBlank(req.RoleName) {
		return false
	}
	if utils.IsBlank(req.Reason) {
		return false
	}
	if _, exists := requestTypeToProtoMapper[req.RequestType]; !exists {
		return false
	}
	return true
}
