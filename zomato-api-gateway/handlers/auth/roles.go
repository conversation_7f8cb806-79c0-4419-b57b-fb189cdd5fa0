package auth

import (
	"net/http"

	"context"

	authsvc "github.com/Zomato/auth-service-client-golang/pkg/roles"
	"github.com/Zomato/go/config"
	"github.com/Zomato/go/grpc/metadata"
	"github.com/Zomato/go/i18n"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	authmodels "github.com/Zomato/zomato-api-gateway/models/auth"
	"github.com/Zomato/zomato-api-gateway/pkg/utils"
	"github.com/gin-gonic/gin"
)

func ListRolesHandler(c *gin.Context) {
	log := logger.FromContext(c)
	env := env.FromContext(c)

	conn := env.AuthServiceConn()
	client := authsvc.NewRolesServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	response, err := client.ListRoles(ctx, &authsvc.ListRolesRequest{})
	if err != nil {
		log.Errorf("error while listing roles, error: %v", err)
		prepareErrorResponse(c, err)
		return
	}

	listRolesResponse := prepareListRolesResponse(response)
	c.JSON(http.StatusOK, listRolesResponse)
}

func prepareListRolesResponse(response *authsvc.ListRolesResponse) *authmodels.ListRolesResponse {
	var roles []*authmodels.Role
	for _, role := range response.Roles {
		roles = append(roles, &authmodels.Role{
			RoleID:               role.GetRoleId(),
			Name:                 role.GetName(),
			Description:          role.GetDescription(),
			IsInactive:           role.GetIsInactive(),
			AssociatedDashboards: role.GetAssociatedDashboards(),
			Owner:                role.GetOwner(),
			ApproverRoles:        role.GetApproverRoles(),
			ApproverEmails:       role.GetApproverEmails(),
		})
	}

	return &authmodels.ListRolesResponse{
		Status:  true,
		Message: "Success",
		Roles:   roles,
	}
}

func CreateRoleHandler(c *gin.Context) {
	createRoleResponse := &authmodels.CreateRoleResponse{
		Status:  false,
		Message: "Something went wrong. Please try again later!",
	}

	log := logger.FromContext(c)
	env := env.FromContext(c)

	var req authmodels.CreateRoleRequest

	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Errorf("could not bind request, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			createRoleResponse,
		)
		return
	}

	conn := env.AuthServiceConn()
	client := authsvc.NewRolesServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	_, err = client.CreateRole(ctx, &authsvc.CreateRoleRequest{
		RoleName:    req.Name,
		Description: req.Description,
		Role: &authsvc.Role{
			RoleName:             req.Name,
			Description:          req.Description,
			AssociatedDashboards: req.AssociatedDashboards,
			Owner:                req.Owner,
			ApproverRoles:        req.ApproverRoles,
			ApproverEmails:       req.ApproverEmails,
		},
	})

	if err != nil {
		log.Errorf("error while creating role, error: %v", err)
		prepareErrorResponse(c, err)
		return
	}

	createRoleResponse.Status = true
	createRoleResponse.Message = "Success"
	c.JSON(http.StatusOK, createRoleResponse)
}

func UpdateRoleStatusHandler(c *gin.Context) {
	log := logger.FromContext(c)
	env := env.FromContext(c)

	updateRoleStatusResponse := &authmodels.UpdateRoleStatusResponse{
		Status:  false,
		Message: i18n.Translate(c, "something-went-wrong"),
	}

	var req authmodels.UpdateRoleStatusRequest

	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Errorf("could not bind request, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			updateRoleStatusResponse,
		)
		return
	}

	conn := env.AuthServiceConn()
	client := authsvc.NewRolesServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	_, err = client.UpdateRoleActiveStatus(ctx, &authsvc.UpdateRoleActiveStatusRequest{
		RoleName:   req.RoleName,
		IsInactive: req.IsInactive,
	})
	if err != nil {
		log.Errorf("error while updating role status, error: %v", err)
		prepareErrorResponse(c, err)
		return
	}

	successMessage := i18n.Translate(c, "role_activated")
	if req.IsInactive {
		successMessage = i18n.Translate(c, "role_inactivated")
	}
	updateRoleStatusResponse = &authmodels.UpdateRoleStatusResponse{
		Status:  true,
		Message: successMessage,
	}
	c.JSON(http.StatusOK, updateRoleStatusResponse)
}

func UpdateRole(c *gin.Context) {
	updateRoleResponse := &authmodels.UpdateRoleResponse{
		Status:  false,
		Message: i18n.Translate(c, "something-went-wrong"),
	}

	if !config.GetBool(c, "auth_service.dashboard.update_role.enabled") {
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			updateRoleResponse,
		)
		return
	}
	UserID := api.GetClientFromContext(c).UserID()
	log := logger.FromContext(c).WithFields(map[string]interface{}{
		"user_id": UserID,
	})
	env := env.FromContext(c)

	var req authmodels.UpdateRoleRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Errorf("could not bind request, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			updateRoleResponse,
		)
		return
	}

	if !isValidRoleUpdateRequest(req) {
		log.Error("invalid role update request")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			&authmodels.UpdateRoleResponse{
				Status:  false,
				Message: "invalid role update request",
			},
		)
		return
	}

	conn := env.AuthServiceConn()
	client := authsvc.NewRolesServiceClient(conn)
	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	ctxWithTimeout, cancel := context.WithTimeout(ctx, config.GetDuration(c, "auth_service.dashboard.update_role.timeout"))
	defer cancel()

	_, err = client.UpdateRole(ctxWithTimeout, &authsvc.UpdateRoleRequest{
		Role: &authsvc.Role{
			RoleName:             req.RoleName,
			Description:          req.Description,
			AssociatedDashboards: req.AssociatedDashboards,
			ApproverRoles:        req.ApproverRoles,
			Owner:                req.Owner,
			ApproverEmails:       req.ApproverEmails,
		},
	})
	if err != nil {
		log.Errorf("error while updating role, error: %v", err)
		prepareErrorResponseWithErrorMessage(c, err)
		return
	}
	updateRoleResponse.Status = true
	updateRoleResponse.Message = "Role updated successfully"
	c.JSON(http.StatusOK, updateRoleResponse)

}

func GetRoleByNameHandler(c *gin.Context) {
	log := logger.FromContext(c)
	env := env.FromContext(c)

	req := authmodels.GetRoleByNameRequest{}
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Errorf("could not bind query parameters, error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			&authmodels.GetRoleByNameResponse{
				Status:  false,
				Message: "Invalid input",
			},
		)
		return
	}

	if utils.IsBlank(req.RoleName) {
		log.Error("role name is required")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			&authmodels.GetRoleByNameResponse{
				Status:  false,
				Message: "role name is required",
			},
		)
		return
	}

	conn := env.AuthServiceConn()
	client := authsvc.NewRolesServiceClient(conn)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	response, err := client.GetRoleByName(ctx, &authsvc.GetRoleByNameRequest{RoleName: req.RoleName})
	if err != nil {
		log.Errorf("error while getting role by name, error: %v", err)
		prepareErrorResponse(c, err)
		return
	}

	if response == nil || response.GetRole() == nil {
		log.Error("role details not found")
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			&authmodels.GetRoleByNameResponse{
				Status:  false,
				Message: "role details not found",
			},
		)
		return
	}

	role := &authmodels.Role{
		Name:                 response.GetRole().GetName(),
		Description:          response.GetRole().GetDescription(),
		AssociatedDashboards: response.GetRole().GetAssociatedDashboards(),
		Owner:                response.GetRole().GetOwner(),
		ApproverRoles:        response.GetRole().GetApproverRoles(),
		IsInactive:           response.GetRole().GetIsInactive(),
		ApproverEmails:       response.Role.GetApproverEmails(),
	}

	c.JSON(http.StatusOK, &authmodels.GetRoleByNameResponse{
		Status:  true,
		Message: "Success",
		Role:    role,
	})
}

func isValidRoleUpdateRequest(req authmodels.UpdateRoleRequest) bool {
	if utils.IsBlank(req.RoleName) {
		return false
	}
	if !utils.IsBlank(req.Description) || !utils.IsBlank(req.Owner) || len(req.AssociatedDashboards) > 0 || len(req.ApproverRoles) > 0 || len(req.ApproverEmails) > 0 {
		return true
	}
	return false
}
