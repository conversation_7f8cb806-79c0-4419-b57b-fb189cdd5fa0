package auth

import (
	"net/http"

	authsvc "github.com/Zomato/auth-service-client-golang/pkg/client"
	"github.com/Zomato/go/grpc/codes"
	"github.com/Zomato/go/grpc/metadata"
	"github.com/Zomato/go/grpc/status"
	"github.com/Zomato/go/i18n"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	authmodels "github.com/Zomato/zomato-api-gateway/models/auth"
	"github.com/Zomato/zomato-api-gateway/pkg/utils"
	"github.com/gin-gonic/gin"
)

const (
	clientLimit = 30
)

func createAuthConnectionClient(ctx *gin.Context) (authsvc.ClientServiceClient, error) {
	env := env.FromContext(ctx)

	conn := env.AuthServiceConn()

	client := authsvc.NewClientServiceClient(conn)
	return client, nil
}

func CreateAuthClient(c *gin.Context) {

	clientResponse := authmodels.CreateClientResponse{
		Message: "Something went wrong. Please try again later!",
		Status:  false,
	}

	log := logger.FromContext(c)

	var req authmodels.Client

	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Errorf("could not bind request: %s", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			clientResponse)
		return
	}

	client, err := createAuthConnectionClient(c)
	if err != nil {
		log.Errorf("could not create client connection: %s", err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			clientResponse)
		return
	}

	authClientConfig := &authsvc.Oauth2Client{
		Name:                    req.Name,
		RedirectUris:            req.RedirectURIs,
		GrantTypes:              req.GrantTypes,
		ResponseTypes:           req.ResponseTypes,
		Scopes:                  req.Scopes,
		TokenEndpointAuthMethod: req.TokenEndpointAuthMethod,
	}

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	_, err = client.CreateClient(ctx, &authsvc.CreateClientRequest{Client: authClientConfig})
	if err != nil {
		log.Errorf("could not create client: %s", err)
		prepareErrorResponse(c, err)
		return
	}

	clientResponse.Message = "Auth Client is created"
	clientResponse.Status = true

	c.JSON(http.StatusOK, clientResponse)
}

func UpdateAuthClient(c *gin.Context) {

	clientResponse := authmodels.UpdateClientResponse{
		Message: "Something went wrong. Please try again later!",
		Status:  false,
	}

	log := logger.FromContext(c)

	var req authmodels.Client

	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Errorf("could not bind request: %s", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			clientResponse)
		return
	}

	client, err := createAuthConnectionClient(c)
	if err != nil {
		log.Errorf("could not create client connection: %s", err)
		prepareErrorResponse(c, err)
		return
	}

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	_, err = client.UpdateClient(ctx, &authsvc.UpdateClientRequest{
		ClientId: req.ClientID,
		Name:     req.Name,
	})

	if err != nil {
		log.Errorf("could not update client: %s", err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			clientResponse)
		return
	}

	clientResponse.Message = "Auth Client is updated"
	clientResponse.Status = true

	c.JSON(http.StatusOK, clientResponse)
}

func ListAuthClients(c *gin.Context) {

	clientResponse := authmodels.ListClientsResponse{
		Message: "Something went wrong. Please try again later!",
		Status:  false,
	}

	log := logger.FromContext(c)

	client, err := createAuthConnectionClient(c)
	if err != nil {
		log.Errorf("could not create client connection: %s", err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			clientResponse)
		return
	}

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	response, err := client.ListClients(ctx, &authsvc.ListClientRequest{Limit: clientLimit, Offset: 0})
	if err != nil {
		log.Errorf("could not list clients: %s", err)
		prepareErrorResponse(c, err)
		return
	}
	authClients := response.GetClient()

	clientList := []authmodels.Client{}
	for _, val := range authClients {
		each := authmodels.Client{
			ClientID:                val.ClientId,
			Name:                    val.Name,
			RedirectURIs:            val.RedirectUris,
			TokenEndpointAuthMethod: val.TokenEndpointAuthMethod,
			GrantTypes:              val.GrantTypes,
			ResponseTypes:           val.ResponseTypes,
			Scopes:                  val.Scopes,
		}
		clientList = append(clientList, each)
	}
	clientResponse.Clients = clientList
	clientResponse.Status = true
	clientResponse.Message = "success"

	c.JSON(http.StatusOK, clientResponse)
}

func prepareErrorResponse(c *gin.Context, err error) {
	response := struct {
		Status  bool   `json:"status"`
		Message string `json:"message"`
	}{}
	response.Status = false
	if e, ok := status.FromError(err); ok {
		if e.Code() == codes.PermissionDenied {
			response.Message = i18n.Translate(c, "not-authorized-to-perform-operation")
			c.AbortWithStatusJSON(
				http.StatusForbidden,
				response,
			)
			return
		}
	}
	response.Message = "Something went wrong. Please try again later!"
	c.AbortWithStatusJSON(
		http.StatusInternalServerError,
		response,
	)
}

func prepareErrorResponseWithErrorMessage(c *gin.Context, err error) {
	response := struct {
		Status  bool   `json:"status"`
		Message string `json:"message"`
	}{}
	response.Status = false
	if e, ok := status.FromError(err); ok {
		if e.Code() == codes.PermissionDenied {
			response.Message = i18n.Translate(c, "not-authorized-to-perform-operation")
			c.AbortWithStatusJSON(
				http.StatusForbidden,
				response,
			)
			return
		}
	}
	e, _ := status.FromError(err)
	if utils.IsBlank(e.Message()) {
		response.Message = "Something went wrong. Please try again later!"
	} else {
		response.Message = e.Message()
	}
	c.AbortWithStatusJSON(
		http.StatusInternalServerError,
		response,
	)
}
