package auth

import (
	"net/http"
	"strings"
	"time"

	authsvc "github.com/Zomato/auth-service-client-golang/pkg/auth"
	"github.com/Zomato/go/grpc/metadata"
	"github.com/Zomato/go/i18n"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	models "github.com/Zomato/zomato-api-gateway/models"
	authmodels "github.com/Zomato/zomato-api-gateway/models/auth"
	"github.com/Zomato/zomato-api-gateway/models/sushi"
	"github.com/gin-gonic/gin"
)

const (
	trueClientIPHeader = "True-Client-IP"
)

var initiateLogoutAllDevicesErrorCodeMapping = map[authsvc.InitiateLogoutAllDevicesErrorCode]string{
	authsvc.InitiateLogoutAllDevicesErrorCode_INITIATE_LOGOUT_ALL_DEVICES_ERROR_CODE_INVALID:     "something-went-wrong",
	authsvc.InitiateLogoutAllDevicesErrorCode_INITIATE_LOGOUT_ALL_DEVICES_OTP_ATTEMPTS_EXHAUSTED: "send-otp-limit-reached",
}

var verifyLogoutAllDevicesErrorCodeMapping = map[authsvc.VerifyLogoutAllDevicesErrorCode]string{
	authsvc.VerifyLogoutAllDevicesErrorCode_VERIFY_LOGOUT_ALL_DEVICES_ERROR_CODE_INVALID:                      "something-went-wrong",
	authsvc.VerifyLogoutAllDevicesErrorCode_VERIFY_LOGOUT_ALL_DEVICES_ERROR_CODE_NOT_AUTHORIZED:               "not-authorized-to-complete-action",
	authsvc.VerifyLogoutAllDevicesErrorCode_VERIFY_LOGOUT_ALL_DEVICES_ERROR_CODE_WRONG_OTP_ATTEMPTS_EXHAUSTED: "verify-otp-limit-reached",
	authsvc.VerifyLogoutAllDevicesErrorCode_VERIFY_LOGOUT_ALL_DEVICES_ERROR_CODE_INVALID_OTP:                  "wrong-otp-message",
}

var authEntityTypeMapping = map[authsvc.AuthEntityType]string{
	authsvc.AuthEntityType_AUTH_ENTITY_TYPE_INVALID: "invalid",
	authsvc.AuthEntityType_AUTH_ENTITY_TYPE_EMAIL:   "email",
	authsvc.AuthEntityType_AUTH_ENTITY_TYPE_PHONE:   "phone",
}

// LogoutHandler removes the zat cookie from browser
func LogoutHandler(c *gin.Context) {
	http.SetCookie(c.Writer, &http.Cookie{
		Name:     "zat",
		Value:    "",
		Path:     "/",
		Expires:  time.Unix(0, 0),
		Secure:   true,
		HttpOnly: true,
	})

	c.JSON(
		http.StatusOK,
		models.StatusSuccess("logout successful"),
	)
}

func LogoutFromAllDevices(c *gin.Context) {
	log := logger.FromContext(c)
	env := env.FromContext(c)

	var conn = env.AuthServiceConn()
	client := authsvc.NewAuthServiceClient(conn)

	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()

	log = log.WithField("user_id", userID)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	_, err := client.InvalidateAllAccessTokens(ctx, &authsvc.InvalidateAllAccessTokensRequest{
		UserId: userID,
		Client: authsvc.Client_CLIENT_CONSUMER,
	})

	if err != nil {
		log.WithError(err).Error("error in calling logoutAllDevices RPC")
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(c, "something-went-wrong")),
		)
		return
	}

	c.JSON(http.StatusOK, models.StatusSuccess(i18n.Translate(c, "logout-all-devices-success")))
}

func InitiateLogoutFromAllDevicesHandler(c *gin.Context) {
	log := logger.FromContext(c)
	env := env.FromContext(c)

	var req authmodels.InitiateLogoutAllDevicesRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.WithError(err).Error("error while binding request json")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(i18n.Translate(c, "something-went-wrong")),
		)
		return
	}

	var conn = env.AuthServiceConn()
	client := authsvc.NewAuthServiceClient(conn)

	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()

	log = log.WithField("user_id", userID)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	response, err := client.InitiateLogoutAllDevices(ctx, &authsvc.InitiateLogoutAllDevicesRequest{
		UserId:      userID,
		MessageUuid: req.MessageUUID,
		ClientDetails: &authsvc.ClientDetails{
			AppType:   getAppType(c),
			IpAddress: c.GetHeader(trueClientIPHeader),
		},
	})
	if err != nil {
		log.WithError(err).Error("error in calling initiateLogoutAllDevices RPC")
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(c, "something-went-wrong")),
		)
		return
	}

	if !response.GetStatus() {
		message := "something-went-wrong"
		if errorCode, ok := initiateLogoutAllDevicesErrorCodeMapping[response.GetErrorCode()]; ok {
			message = errorCode
		}
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed(i18n.Translate(c, message)),
		)
		return
	}

	if response.GetAuthDetails() == nil {
		log.WithError(err).Error("invalid auth details from initiateLogoutAllDevices RPC")
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(c, "something-went-wrong")),
		)
		return
	}

	if !response.GetAuthDetails().GetIsAuthEnabled() {
		c.JSON(http.StatusOK, models.StatusSuccess(i18n.Translate(c, "logout-all-devices-success")))
		return
	}

	apiResponse := prepareInitiateLogoutAllDevicesResponse(c, response)
	c.JSON(http.StatusOK, apiResponse)
}

func prepareInitiateLogoutAllDevicesResponse(c *gin.Context, response *authsvc.InitiateLogoutAllDevicesResponse) *authmodels.InitiateLogoutAllDevicesResponse {
	return &authmodels.InitiateLogoutAllDevicesResponse{
		Status:  "success",
		Message: i18n.Translate(c, "otp-sent-successfully"),
		Header: &sushi.Header{
			Title: &sushi.Label{
				Text: i18n.Translate(c, "enter-otp-text", response.GetAuthDetails().GetAuthEntityValue()),
			},
			Button: &sushi.Button{
				Text: i18n.Translate(c, "initiate-logout-all-devices-button-text"),
			},
		},
		Data: &authmodels.InitiateLogoutAllDevicesData{
			Hash:                 response.GetHash(),
			AuthVerificationType: authEntityTypeMapping[response.GetAuthDetails().GetAuthEntityType()],
			SuccessAPI: &sushi.APICallOnTap{
				URL: "gw/auth/v2/logout/all/verify",
			},
			MessageUUID: response.GetMessageUuid(),
		},
	}
}

func VerifyLogoutFromAllDevicesHandler(c *gin.Context) {
	log := logger.FromContext(c)
	env := env.FromContext(c)

	var req authmodels.VerifyLogoutAllDevicesRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.WithError(err).Error("error while binding request json")
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(i18n.Translate(c, "something-went-wrong")),
		)
		return
	}

	if isBlank(req.Hash) {
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(i18n.Translate(c, "something-went-wrong")),
		)
		return
	}

	if isBlank(req.Otp) {
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed(i18n.Translate(c, "wrong-otp-message")),
		)
		return
	}

	var conn = env.AuthServiceConn()
	client := authsvc.NewAuthServiceClient(conn)

	zomatoClient := api.GetClientFromContext(c)
	userID := zomatoClient.UserID()

	log = log.WithField("user_id", userID)

	md := prepareHeaders(c)
	ctx := metadata.NewOutgoingContext(c, md)

	response, err := client.VerifyLogoutAllDevices(ctx, &authsvc.VerifyLogoutAllDevicesRequest{
		UserId: userID,
		Otp:    req.Otp,
		Hash:   req.Hash,
		ClientDetails: &authsvc.ClientDetails{
			AppType:   getAppType(c),
			IpAddress: c.GetHeader(trueClientIPHeader),
		},
	})
	if err != nil {
		log.WithError(err).Error("error in calling verify logoutAllDevices RPC")
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailed(i18n.Translate(c, "something-went-wrong")),
		)
		return
	}

	if !response.GetStatus() {
		message := "something-went-wrong"
		if errorCode, ok := verifyLogoutAllDevicesErrorCodeMapping[response.GetErrorCode()]; ok {
			message = errorCode
		}
		c.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed(i18n.Translate(c, message)),
		)
		return
	}

	c.JSON(http.StatusOK, models.StatusSuccess(i18n.Translate(c, "logout-all-devices-success")))
}

func getAppType(c *gin.Context) authsvc.AppType {
	zomatoClient := api.GetClientFromContext(c)
	if zomatoClient.IsAndroidConsumer() {
		return authsvc.AppType_APP_TYPE_CONSUMER_ANDROID
	}
	if zomatoClient.IsIOSConsumer() {
		return authsvc.AppType_APP_TYPE_CONSUMER_IOS
	}
	if zomatoClient.IsAndroidServicesV3App() {
		return authsvc.AppType_APP_TYPE_MERCHANT_ANDROID
	}
	if zomatoClient.IsIOSMerchant() {
		return authsvc.AppType_APP_TYPE_MERCHANT_IOS
	}
	return authsvc.AppType_APP_TYPE_MERCHANT_WEB
}

func isBlank(s string) bool {
	return len(strings.TrimSpace(s)) == 0
}
