package converter

import (
	aiInsightsCommon "github.com/Zomato/ai-insights-service-client-golang/common"
	"github.com/Zomato/zomato-api-gateway/models/ai-insights-service/common"
	"net/http"
)

var StatusCodeMap = map[aiInsightsCommon.ResponseStatusCode]int{
	aiInsightsCommon.ResponseStatusCode_STATUS_CODE_OK:                http.StatusOK,
	aiInsightsCommon.ResponseStatusCode_STATUS_CODE_INVALID_ARGUMENTS: http.StatusBadRequest,
	aiInsightsCommon.ResponseStatusCode_STATUS_CODE_BAD_REQUEST:       http.StatusBadRequest,
	aiInsightsCommon.ResponseStatusCode_STATUS_CODE_INTERNAL_ERROR:    http.StatusInternalServerError,
	aiInsightsCommon.ResponseStatusCode_STATUS_CODE_DUPLICATE_REQUEST: http.StatusConflict,
	aiInsightsCommon.ResponseStatusCode_STATUS_CODE_UNSPECIFIED:       http.StatusServiceUnavailable,
}

var VisibilityStatusModelToProtoMap = map[string]aiInsightsCommon.VisibilityStatus{
	common.CX: aiInsightsCommon.VisibilityStatus_CX,
	common.MX: aiInsightsCommon.VisibilityStatus_MX,
	common.RX: aiInsightsCommon.VisibilityStatus_RX,
}

var VerificationStatusModelToProtoMap = map[string]aiInsightsCommon.VerificationStatus{
	common.AutoModerated:   aiInsightsCommon.VerificationStatus_AUTO_MODERATED,
	common.AutoRejected:    aiInsightsCommon.VerificationStatus_AUTO_REJECTED,
	common.ManualModerated: aiInsightsCommon.VerificationStatus_MANUAL_MODERATED,
	common.ManualRejected:  aiInsightsCommon.VerificationStatus_MANUAL_REJECTED,
}
