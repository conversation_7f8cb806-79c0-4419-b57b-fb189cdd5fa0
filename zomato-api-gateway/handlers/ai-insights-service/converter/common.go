package converter

import (
	aiInsightsCommon "github.com/Zomato/ai-insights-service-client-golang/common"
	"github.com/Zomato/ai-insights-service-client-golang/entity_registry"
	aiInsights "github.com/Zomato/ai-insights-service-client-golang/insights"
	usersvc "github.com/Zomato/user-service-client-golang/user"
	"github.com/Zomato/zomato-api-gateway/models/ai-insights-service/common"
	"github.com/Zomato/zomato-api-gateway/models/ai-insights-service/insights"
	"net/http"
)

func MapStatusCode(protoCode aiInsightsCommon.ResponseStatusCode) int {
	if code, ok := StatusCodeMap[protoCode]; ok {
		return code
	}
	return http.StatusServiceUnavailable
}

func ConvertEntityToProto(entity insights.Entity) *aiInsightsCommon.Entity {
	return &aiInsightsCommon.Entity{Type: entity.Type, Id: entity.ID}
}

func ConvertEntitiesToProto(entities []insights.Entity) []*aiInsightsCommon.Entity {
	var protoEntities []*aiInsightsCommon.Entity
	for _, entity := range entities {
		protoEntities = append(protoEntities, ConvertEntityToProto(entity))
	}
	return protoEntities
}

func ConvertSubEntityToProto(subEntity insights.SubEntity) *aiInsights.SubEntity {
	return &aiInsights.SubEntity{Type: subEntity.Type, Id: subEntity.ID}
}

func ConvertSubEntitiesToProto(subEntities []insights.SubEntity) []*aiInsights.SubEntity {
	var result []*aiInsights.SubEntity
	for _, subEntity := range subEntities {
		result = append(result, ConvertSubEntityToProto(subEntity))
	}
	return result
}

func ConvertVisibilityStatusToProto(status string) aiInsightsCommon.VisibilityStatus {
	if val, exists := VisibilityStatusModelToProtoMap[status]; exists {
		return val
	}
	return aiInsightsCommon.VisibilityStatus_VISIBILITY_STATUS_UNDEFINED
}

func ConvertVisibilityStatusesToProto(statuses []string) []aiInsightsCommon.VisibilityStatus {
	var result []aiInsightsCommon.VisibilityStatus
	for _, status := range statuses {
		result = append(result, ConvertVisibilityStatusToProto(status))
	}
	return result
}

func ConvertVerificationStatusToProto(status string) aiInsightsCommon.VerificationStatus {
	if val, exists := VerificationStatusModelToProtoMap[status]; exists {
		return val
	}
	return aiInsightsCommon.VerificationStatus_VERIFICATION_STATUS_UNDEFINED
}

func GetVerificationDetailsByUserProfile(userProfile *usersvc.User) *aiInsightsCommon.Verification {
	return &aiInsightsCommon.Verification{
		Name:   userProfile.GetName(),
		Email:  userProfile.GetEmail(),
		Status: ConvertVerificationStatusToProto(common.ManualModerated),
	}
}

func ConvertProtoInsightToInsight(protoInsight *aiInsights.Insight) *insights.Insight {
	return &insights.Insight{
		ID:                         protoInsight.Id,
		Tag:                        protoInsight.Tag,
		Type:                       protoInsight.Type,
		Rank:                       protoInsight.Rank,
		Version:                    protoInsight.Version,
		VisibilityStatus:           ConvertVisibilityStatusProtoToString(protoInsight.VisibilityStatus),
		KnowledgeBaseCitationCount: convertKnowledgeBaseCitationCountProtoListToModel(protoInsight.KnowledgeBaseCitationCount),
	}
}

func convertKnowledgeBaseCitationCountProtoListToModel(protoKnowledgeBaseCitationCount []*aiInsights.KnowledgeBaseCitationCount) []*insights.KnowledgeBaseCitationCount {
	var result []*insights.KnowledgeBaseCitationCount
	for _, protoCount := range protoKnowledgeBaseCitationCount {
		result = append(result, convertKnowledgeBaseCitationCountProtoToModel(protoCount))
	}
	return result
}
func convertKnowledgeBaseCitationCountProtoToModel(protoKnowledgeBaseCitationCount *aiInsights.KnowledgeBaseCitationCount) *insights.KnowledgeBaseCitationCount {
	return &insights.KnowledgeBaseCitationCount{
		KnowledgeBaseType: protoKnowledgeBaseCitationCount.KnowledgeBaseType,
		CitationCount:     protoKnowledgeBaseCitationCount.CitationCount,
	}
}

func ConvertVisibilityStatusProtoToString(status []aiInsightsCommon.VisibilityStatus) []string {
	var result []string
	for _, s := range status {
		result = append(result, s.String())
	}
	return result
}

func ConvertInsightsToProto(protoInsights []*aiInsights.Insight) []*insights.Insight {
	var convertedInsights []*insights.Insight
	for _, protoInsight := range protoInsights {
		converted := ConvertProtoInsightToInsight(protoInsight)
		convertedInsights = append(convertedInsights, converted)
	}
	return convertedInsights
}

func ConvertEntityInsightsMapToProto(entityInsights []*aiInsights.EntityInsight) []*insights.EntityInsight {
	var convertedEntityInsights []*insights.EntityInsight
	for _, entityInsight := range entityInsights {
		convertedEntityInsights = append(convertedEntityInsights, &insights.EntityInsight{
			Entity:   ConvertProtoEntityToEntity(entityInsight.Entity),
			Insights: ConvertInsightsToProto(entityInsight.Insights),
		})
	}
	return convertedEntityInsights
}

func ConvertCommonProtoResponseToHTTP(protoResp *aiInsightsCommon.Response) *common.HTTPResponse {
	return &common.HTTPResponse{
		StatusCode: MapStatusCode(protoResp.Status.Code),
		Message:    protoResp.Status.Message,
		Data:       protoResp.OutputBody,
	}
}

func ConvertPropertyKnowledgeBaseMapToModel(mapping map[string][]string) []*entity_registry.PropertyKnowledgeBaseMapping {
	var model []*entity_registry.PropertyKnowledgeBaseMapping
	for property, knowledgeBaseList := range mapping {
		model = append(model, &entity_registry.PropertyKnowledgeBaseMapping{
			Property:          property,
			KnowledgeBaseList: knowledgeBaseList,
		})
	}
	return model
}

func ConvertProtoEntityToEntity(entity *aiInsightsCommon.Entity) insights.Entity {
	return insights.Entity{
		Type: entity.Type,
		ID:   entity.Id,
	}
}

func ConvertToCommonResponse(objects []string) []*common.Response {
	var responses []*common.Response
	for _, object := range objects {
		responses = append(responses, &common.Response{
			Label: object,
			Value: object,
		})
	}
	return responses
}
