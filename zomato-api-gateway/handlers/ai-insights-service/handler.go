package ai_insights_service

import (
	"net/http"

	"github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/handlers/ai-insights-service/converter"
	"github.com/Zomato/zomato-api-gateway/handlers/ai-insights-service/service"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/Zomato/zomato-api-gateway/models/ai-insights-service/common"
	"github.com/Zomato/zomato-api-gateway/models/ai-insights-service/insights"
	"github.com/Zomato/zomato-api-gateway/models/ai-insights-service/registry"
	"github.com/gin-gonic/gin"
)

func CreateRegistryHandler(ctx *gin.Context) {
	log := logger.FromContext(ctx)

	var jsonReq *registry.CreateRegistryRequest
	if err := ctx.ShouldBindJSON(&jsonReq); err != nil {
		log.Errorf("[CreateReg<PERSON>ry<PERSON>and<PERSON>] could not bind request: %v", err.Error())
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("something went wrong, please try after some time"))
		return
	}

	response, err := service.CreateRegistry(ctx, jsonReq)
	if err != nil {
		log.WithError(err).Error("[CreateRegistryHandler] CreateRegistry call failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailed("failed to create registry"))
		return
	}
	ctx.JSON(http.StatusOK, converter.ConvertCommonProtoResponseToHTTP(response))
}

func ListEntitiesHandler(ctx *gin.Context) {
	log := logger.FromContext(ctx)

	response, err := service.ListEntities(ctx)
	if err != nil {
		log.WithError(err).Error("[ListEntitiesHandler] ListEntities call failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailed("failed to fetch entities"))
		return
	}

	ctx.JSON(http.StatusOK, &registry.ListEntitiesResponse{
		StatusCode: converter.MapStatusCode(response.Status.Code),
		Message:    response.Status.Message,
		Entities:   converter.ConvertToCommonResponse(response.Entities),
	})

}

func ListPropertiesHandler(ctx *gin.Context) {
	log := logger.FromContext(ctx)
	entityType := ctx.Param(common.EntityType)
	jsonReq := &registry.ListPropertiesRequest{
		EntityType: entityType,
	}

	response, err := service.ListProperties(ctx, jsonReq)
	if err != nil {
		log.WithError(err).Error("[ListPropertiesHandler] ListProperties call failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailed("failed to fetch properties"))
		return
	}
	ctx.JSON(http.StatusOK, &registry.ListPropertiesResponse{
		StatusCode: converter.MapStatusCode(response.Status.Code),
		Message:    response.Status.Message,
		Properties: converter.ConvertToCommonResponse(response.Properties),
	})
}

func ListInsightsHandler(ctx *gin.Context) {
	log := logger.FromContext(ctx)

	var jsonReq *insights.ListInsightsRequest
	if err := ctx.ShouldBindJSON(&jsonReq); err != nil {
		logger.Errorf("[ListInsightsHandler] could not bind request: %v", err)
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("something went wrong, please try after some time"))
		return
	}

	response, err := service.ListInsights(ctx, jsonReq)
	if err != nil {
		log.WithError(err).Error("[ListInsightsHandler] ListInsights call failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailed("failed to fetch insights"))
		return
	}
	ctx.JSON(http.StatusOK, &insights.ListInsightsResponse{
		StatusCode:     converter.MapStatusCode(response.Status.Code),
		Message:        response.Status.Message,
		EntityInsights: converter.ConvertEntityInsightsMapToProto(response.EntityInsights),
	})

}

func ListVersionsHandler(ctx *gin.Context) {
	log := logger.FromContext(ctx)

	var jsonReq *insights.ListVersionsRequest
	if err := ctx.ShouldBindJSON(&jsonReq); err != nil {
		logger.Errorf("[ListVersionsHandler] could not bind request: %v", err)
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("something went wrong, please try after some time"))
		return
	}

	response, err := service.ListVersions(ctx, jsonReq)
	if err != nil {
		log.WithError(err).Error("[ListVersionsHandler] ListVersions call failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailed("failed to fetch versions"))
		return
	}
	ctx.JSON(http.StatusOK, insights.ListVersionsResponse{
		StatusCode: converter.MapStatusCode(response.Status.Code),
		Message:    response.Status.Message,
		Versions:   converter.ConvertToCommonResponse(response.Versions),
	})
}

func UpdateActiveVersionHandler(ctx *gin.Context) {
	log := logger.FromContext(ctx)

	var jsonReq *insights.UpdateActiveVersionRequest
	if err := ctx.ShouldBindJSON(&jsonReq); err != nil {
		logger.Errorf("[UpdateActiveVersionHandler] could not bind request: %v", err)
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("something went wrong, please try after some time"))
		return
	}

	response, err := service.UpdateActiveVersion(ctx, jsonReq)
	if err != nil {
		log.WithError(err).Error("[UpdateActiveVersionHandler] UpdateActiveVersion call failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailed("failed to update version"))
		return
	}
	ctx.JSON(http.StatusOK, converter.ConvertCommonProtoResponseToHTTP(response))
}

func UpdateVisibilityStatusHandler(ctx *gin.Context) {
	log := logger.FromContext(ctx)

	var jsonReq *insights.UpdateVisibilityStatusRequest
	if err := ctx.ShouldBindJSON(&jsonReq); err != nil {
		logger.Errorf("[UpdateVisibilityStatusHandler] could not bind request: %v", err)
		ctx.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailed("something went wrong, please try after some time"))
		return
	}

	response, err := service.UpdateVisibilityStatus(ctx, jsonReq)
	if err != nil {
		log.WithError(err).Error("[UpdateVisibilityStatusHandler] UpdateVisibilityStatus call failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailed("failed to update visibility status"))
		return
	}
	ctx.JSON(http.StatusOK, converter.ConvertCommonProtoResponseToHTTP(response))
}
