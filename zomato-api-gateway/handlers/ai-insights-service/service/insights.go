package service

import (
	"context"
	"fmt"
	"net/http"
	"time"

	aiInsightsCommon "github.com/Zomato/ai-insights-service-client-golang/common"
	aiInsights "github.com/Zomato/ai-insights-service-client-golang/insights"
	zconfig "github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/handlers/ai-insights-service/converter"
	"github.com/Zomato/zomato-api-gateway/internal/api"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/internal/user"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/Zomato/zomato-api-gateway/models/ai-insights-service/insights"
	"github.com/gin-gonic/gin"
)

func ListInsights(ctx *gin.Context, jsonReq *insights.ListInsightsRequest) (*aiInsights.ListInsightsResponse, error) {

	killSwitch := zconfig.GetBool(ctx, "ai-insights-service.list_insights_rpc.is_killed")
	if killSwitch {
		ctx.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return nil, fmt.Errorf("kill switch enabled, service unavailable")
	}

	protoReq := &aiInsights.ListInsightsRequest{
		Tenant:           jsonReq.Tenant,
		Property:         jsonReq.Property,
		Version:          jsonReq.Version,
		Entities:         converter.ConvertEntitiesToProto(jsonReq.Entities),
		SubEntity:        converter.ConvertSubEntityToProto(jsonReq.SubEntity),
		VisibilityStatus: converter.ConvertVisibilityStatusToProto(jsonReq.VisibilityStatus),
	}

	_, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	environment := env.FromContext(ctx)
	conn := environment.AiInsightsServiceConn()
	client := aiInsights.NewInsightsServiceClient(conn)
	response, err := client.ListInsights(ctx, protoReq)
	if err != nil {
		log.WithError(err).Error("[ListInsightsService] ListInsights call failed")
		return nil, err
	}
	return response, nil
}

func ListVersions(ctx *gin.Context, jsonReq *insights.ListVersionsRequest) (*aiInsights.ListVersionsByEntityAndPropertyResponse, error) {
	killSwitch := zconfig.GetBool(ctx, "ai-insights-service.list_versions_rpc.is_killed")
	if killSwitch {
		ctx.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return nil, fmt.Errorf("kill switch enabled, service unavailable")
	}

	protoReq := &aiInsights.ListVersionsByEntityAndPropertyRequest{
		Entity:   converter.ConvertEntityToProto(jsonReq.Entity),
		Property: jsonReq.Property,
		Tenant:   jsonReq.Tenant,
	}

	_, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	environment := env.FromContext(ctx)
	conn := environment.AiInsightsServiceConn()
	client := aiInsights.NewInsightsServiceClient(conn)
	response, err := client.ListVersionsByEntityAndProperty(ctx, protoReq)
	if err != nil {
		log.WithError(err).Error("[ListVersionsService] ListVersions call failed")
		return nil, err
	}
	return response, nil
}

func UpdateActiveVersion(ctx *gin.Context, jsonReq *insights.UpdateActiveVersionRequest) (*aiInsightsCommon.Response, error) {

	killSwitch := zconfig.GetBool(ctx, "ai-insights-service.update_active_version_rpc.is_killed")
	if killSwitch {
		ctx.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return nil, fmt.Errorf("kill switch enabled, service unavailable")
	}

	protoReq := &aiInsights.UpdateActiveVersionRequest{
		Version:  jsonReq.Version,
		Tenant:   jsonReq.Tenant,
		Entity:   converter.ConvertEntityToProto(jsonReq.Entity),
		Property: jsonReq.Property,
	}

	_, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	environment := env.FromContext(ctx)
	conn := environment.AiInsightsServiceConn()
	client := aiInsights.NewInsightsServiceClient(conn)

	response, err := client.UpdateActiveVersion(ctx, protoReq)
	if err != nil {
		log.WithError(err).Error("[UpdateActiveVersionService] UpdateActiveVersion call failed")
		return nil, err
	}
	return response, nil
}

func UpdateVisibilityStatus(ctx *gin.Context, jsonReq *insights.UpdateVisibilityStatusRequest) (*aiInsightsCommon.Response, error) {

	killSwitch := zconfig.GetBool(ctx, "ai-insights-service.update_visibility_status_rpc.is_killed")
	if killSwitch {
		ctx.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return nil, fmt.Errorf("kill switch enabled, service unavailable")
	}
	zomatoClient := api.GetClientFromContext(ctx)
	userID := zomatoClient.UserID()
	userProfile, err := user.FetchUserProfileByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("user profile does not exists for user id: %v", userID)
	}

	protoReq := &aiInsights.UpdateVisibilityStatusRequest{
		Entity:           converter.ConvertEntityToProto(jsonReq.Entity),
		Property:         jsonReq.Property,
		Tenant:           jsonReq.Tenant,
		Version:          jsonReq.Version,
		InsightId:        jsonReq.InsightId,
		VisibilityStatus: converter.ConvertVisibilityStatusesToProto(jsonReq.VisibilityStatus),
		Verification:     converter.GetVerificationDetailsByUserProfile(userProfile),
	}

	_, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	environment := env.FromContext(ctx)
	conn := environment.AiInsightsServiceConn()
	client := aiInsights.NewInsightsServiceClient(conn)

	response, err := client.UpdateVisibilityStatus(ctx, protoReq)
	if err != nil {
		log.WithError(err).Error("[UpdateVisibilityStatusService] UpdateVisibilityStatus call failed")
		return nil, err
	}
	return response, nil
}
