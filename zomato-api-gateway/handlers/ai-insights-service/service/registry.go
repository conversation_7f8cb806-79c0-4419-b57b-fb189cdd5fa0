package service

import (
	"context"
	"fmt"
	"net/http"
	"time"

	aiInsightsCommon "github.com/Zomato/ai-insights-service-client-golang/common"
	aiInsightsRegistry "github.com/Zomato/ai-insights-service-client-golang/entity_registry"
	zconfig "github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/zomato-api-gateway/handlers/ai-insights-service/converter"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/models"
	"github.com/Zomato/zomato-api-gateway/models/ai-insights-service/registry"
	"github.com/gin-gonic/gin"
	"google.golang.org/protobuf/types/known/emptypb"
)

func CreateRegistry(ctx *gin.Context, jsonReq *registry.CreateRegistryRequest) (*aiInsightsCommon.Response, error) {

	killSwitch := zconfig.GetBool(ctx, "ai-insights-service.create_registry_rpc.is_killed")
	if killSwitch {
		ctx.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return nil, fmt.Errorf("kill switch enabled, service unavailable")
	}

	protoReq := &aiInsightsRegistry.CreateRegistryRequest{
		EntityType:                   jsonReq.EntityType,
		PropertyKnowledgeBaseMapping: converter.ConvertPropertyKnowledgeBaseMapToModel(jsonReq.PropertyKnowledgeBaseMap),
	}

	_, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	environment := env.FromContext(ctx)
	conn := environment.AiInsightsServiceConn()
	client := aiInsightsRegistry.NewRegistryServiceClient(conn)

	response, err := client.CreateRegistry(ctx, protoReq)
	if err != nil {
		log.WithError(err).Error("[CreateRegistryService] CreateRegistry call failed")
		return nil, err
	}

	return response, nil
}

func ListEntities(ctx *gin.Context) (*aiInsightsRegistry.ListEntitiesResponse, error) {

	killSwitch := zconfig.GetBool(ctx, "ai-insights-service.list_entities_rpc.is_killed")
	if killSwitch {
		ctx.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return nil, fmt.Errorf("kill switch enabled, service unavailable")
	}

	_, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	environment := env.FromContext(ctx)
	conn := environment.AiInsightsServiceConn()
	client := aiInsightsRegistry.NewRegistryServiceClient(conn)

	response, err := client.ListAllEntities(ctx, &emptypb.Empty{})
	if err != nil {
		log.WithError(err).Error("[ListEntitiesService] ListEntities call failed")
		return nil, err
	}
	return response, nil
}

func ListProperties(ctx *gin.Context, jsonReq *registry.ListPropertiesRequest) (*aiInsightsRegistry.ListPropertiesByEntityTypeResponse, error) {

	killSwitch := zconfig.GetBool(ctx, "ai-insights-service.list_entity_properties_rpc.is_killed")
	if killSwitch {
		ctx.AbortWithStatusJSON(
			http.StatusOK,
			models.StatusFailed("Kill Switch Enabled"),
		)
		return nil, fmt.Errorf("kill switch enabled, service unavailable")
	}

	protoReq := &aiInsightsRegistry.ListPropertiesByEntityTypeRequest{
		EntityType: jsonReq.EntityType,
	}

	_, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	environment := env.FromContext(ctx)
	conn := environment.AiInsightsServiceConn()
	client := aiInsightsRegistry.NewRegistryServiceClient(conn)

	response, err := client.ListPropertiesByEntityType(ctx, protoReq)
	if err != nil {
		log.WithError(err).Error("[ListPropertiesService] ListProperties call failed")
		return nil, err
	}
	return response, nil

}
