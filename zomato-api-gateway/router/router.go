package router

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/Zomato/zomato-api-gateway/handlers/accessibility"
	efsClient "github.com/Zomato/zomato-api-gateway/handlers/eternal_form_service/efs_client"
	efsMiddleware "github.com/Zomato/zomato-api-gateway/handlers/eternal_form_service/middlewares"
	"github.com/Zomato/zomato-api-gateway/handlers/menu/fow"
	"github.com/Zomato/zomato-api-gateway/handlers/push/live_activity"
	"github.com/Zomato/zomato-api-gateway/handlers/push/user_preferences"
	"github.com/Zomato/zomato-api-gateway/handlers/reward/fetch_assets"
	"github.com/Zomato/zomato-api-gateway/handlers/reward/fetch_contacts"

	"github.com/Zomato/go/benchmarking"
	"github.com/Zomato/go/runtime"
	"github.com/Zomato/go/tracer/cost"
	irctcCart "github.com/Zomato/zomato-api-gateway/handlers/cart/irctc"
	mmtCart "github.com/Zomato/zomato-api-gateway/handlers/cart/mmt"
	"github.com/Zomato/zomato-api-gateway/handlers/cinema"
	consumerapp "github.com/Zomato/zomato-api-gateway/handlers/consumer_app"
	enterprisedashboard "github.com/Zomato/zomato-api-gateway/handlers/corporate_funds/enterpriseDashboard"
	"github.com/Zomato/zomato-api-gateway/handlers/cultapp"
	"github.com/Zomato/zomato-api-gateway/handlers/delight_service/celeb_video_card"
	"github.com/Zomato/zomato-api-gateway/handlers/dev"
	"github.com/Zomato/zomato-api-gateway/handlers/dining/billpayment/crystal"
	"github.com/Zomato/zomato-api-gateway/handlers/dining/buffets"
	"github.com/Zomato/zomato-api-gateway/handlers/dining/consumer/acd"
	"github.com/Zomato/zomato-api-gateway/handlers/dining/consumer/ar"
	"github.com/Zomato/zomato-api-gateway/handlers/dining/consumer/order/tablereservation"
	"github.com/Zomato/zomato-api-gateway/handlers/dining/consumer/order/unlock"
	"github.com/Zomato/zomato-api-gateway/handlers/dining/nudge_mx"
	"github.com/Zomato/zomato-api-gateway/handlers/dining/offerwall"
	"github.com/Zomato/zomato-api-gateway/handlers/dining/rwg"
	"github.com/Zomato/zomato-api-gateway/handlers/ev_vendor_dashboard"
	"github.com/Zomato/zomato-api-gateway/handlers/faq"
	"github.com/Zomato/zomato-api-gateway/handlers/firefly/aibot"
	location2 "github.com/Zomato/zomato-api-gateway/handlers/go_out/location"
	respage "github.com/Zomato/zomato-api-gateway/handlers/go_out/res_page"
	sidemenu "github.com/Zomato/zomato-api-gateway/handlers/go_out/side_menu"
	"github.com/Zomato/zomato-api-gateway/handlers/go_out/tabs"
	"github.com/Zomato/zomato-api-gateway/handlers/irctc"
	irctcMiddlewares "github.com/Zomato/zomato-api-gateway/handlers/irctc/middleware"
	"github.com/Zomato/zomato-api-gateway/handlers/kitchen_management/consumer/chef"
	"github.com/Zomato/zomato-api-gateway/handlers/kitchen_management/consumer/scheduling"
	"github.com/Zomato/zomato-api-gateway/handlers/kitchen_management/s3"
	"github.com/Zomato/zomato-api-gateway/handlers/legendsservice"
	"github.com/Zomato/zomato-api-gateway/handlers/menu/csao"
	"github.com/Zomato/zomato-api-gateway/handlers/menu/interstitial"
	mmt "github.com/Zomato/zomato-api-gateway/handlers/mmt/train_ordering"
	mmtMiddlewares "github.com/Zomato/zomato-api-gateway/handlers/mmt/train_ordering/middleware"
	paymentPartner "github.com/Zomato/zomato-api-gateway/handlers/payments/payment_partner"
	paymentsAPIGatewayAuth "github.com/Zomato/zomato-api-gateway/handlers/payments_api_gw/auth"
	resvisibility "github.com/Zomato/zomato-api-gateway/handlers/search/resVisibility"
	sa "github.com/Zomato/zomato-api-gateway/handlers/serviceability_aggregator"
	subcommon "github.com/Zomato/zomato-api-gateway/handlers/subscription/common"
	"github.com/Zomato/zomato-api-gateway/handlers/subscription/teams"
	tabbedlocation "github.com/Zomato/zomato-api-gateway/handlers/tabbed_location"
	train_journey "github.com/Zomato/zomato-api-gateway/handlers/trainfulfillment/journey"
	tfsOutlet "github.com/Zomato/zomato-api-gateway/handlers/trainfulfillment/outlet"
	"github.com/Zomato/zomato-api-gateway/handlers/userpreferenceservice"
	"github.com/Zomato/zomato-api-gateway/handlers/userpreferenceservice/collection"
	reco "github.com/Zomato/zomato-api-gateway/handlers/userpreferenceservice/recommendation"
	manageReco "github.com/Zomato/zomato-api-gateway/handlers/userpreferenceservice/recommendation/manage_recommendations"
	tinder "github.com/Zomato/zomato-api-gateway/handlers/userpreferenceservice/tinder"
	"github.com/Zomato/zomato-api-gateway/handlers/weather"
	xtremeInsights "github.com/Zomato/zomato-api-gateway/handlers/xtreme_dashboard/insights"
	xtremeOrder "github.com/Zomato/zomato-api-gateway/handlers/xtreme_dashboard/order"
	xtremeUser "github.com/Zomato/zomato-api-gateway/handlers/xtreme_dashboard/user"
	"github.com/Zomato/zomato-api-gateway/pkg/datadog"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/ext"

	cartmiddlewares "github.com/Zomato/zomato-api-gateway/handlers/cart/middlewares"
	diningmiddlewares "github.com/Zomato/zomato-api-gateway/internal/middlewares/dining"

	diningOrder "github.com/Zomato/zomato-api-gateway/handlers/dining/consumer/order/order"
	"github.com/Zomato/zomato-api-gateway/handlers/dining/tablereservation/consumer/slots"
	recording "github.com/Zomato/zomato-api-gateway/handlers/telecom/call_recording"

	"github.com/Zomato/go/config"
	gg "github.com/Zomato/go/grpc/middlewares"
	"github.com/Zomato/go/health"
	"github.com/gin-gonic/gin"

	"github.com/Zomato/zomato-api-gateway/handlers/ads"
	"github.com/Zomato/zomato-api-gateway/handlers/aerobar"
	"github.com/Zomato/zomato-api-gateway/handlers/auth"
	"github.com/Zomato/zomato-api-gateway/handlers/auth/onetap"
	"github.com/Zomato/zomato-api-gateway/handlers/benefits/applymanualpromo"
	"github.com/Zomato/zomato-api-gateway/handlers/benefits/cartoffers"
	"github.com/Zomato/zomato-api-gateway/handlers/benefits/multicartoffers"
	"github.com/Zomato/zomato-api-gateway/handlers/benefits/offersavings"
	"github.com/Zomato/zomato-api-gateway/handlers/benefits/paymentinfo"
	blinkitAuth "github.com/Zomato/zomato-api-gateway/handlers/blinkit/auth"
	blinkitPayments "github.com/Zomato/zomato-api-gateway/handlers/blinkit/payments"
	blinkitUser "github.com/Zomato/zomato-api-gateway/handlers/blinkit/user"
	batsGoogle "github.com/Zomato/zomato-api-gateway/handlers/brand-ads-target-service/google-ads"
	"github.com/Zomato/zomato-api-gateway/handlers/carbontech"
	"github.com/Zomato/zomato-api-gateway/handlers/cart"
	cr "github.com/Zomato/zomato-api-gateway/handlers/cart/recommendation"
	pms "github.com/Zomato/zomato-api-gateway/handlers/cbilling/pms"
	"github.com/Zomato/zomato-api-gateway/handlers/clevertap"
	configHandlers "github.com/Zomato/zomato-api-gateway/handlers/config"
	"github.com/Zomato/zomato-api-gateway/handlers/consumer_menu"
	corporateFunds "github.com/Zomato/zomato-api-gateway/handlers/corporate_funds"
	"github.com/Zomato/zomato-api-gateway/handlers/delivery_leads_partner_dashboard"
	diningCart "github.com/Zomato/zomato-api-gateway/handlers/dining/consumer/order/cart"
	"github.com/Zomato/zomato-api-gateway/handlers/edition"
	editionDashboard "github.com/Zomato/zomato-api-gateway/handlers/edition/dashboard"
	editionTSP "github.com/Zomato/zomato-api-gateway/handlers/edition/tsp"
	editionWebhook "github.com/Zomato/zomato-api-gateway/handlers/edition/webhook"
	"github.com/Zomato/zomato-api-gateway/handlers/email/apollo"
	"github.com/Zomato/zomato-api-gateway/handlers/email/tracking"
	emailwebhook "github.com/Zomato/zomato-api-gateway/handlers/email/webhook"
	"github.com/Zomato/zomato-api-gateway/handlers/ephemeral_uploads"
	"github.com/Zomato/zomato-api-gateway/handlers/feedback"
	"github.com/Zomato/zomato-api-gateway/handlers/feedback/reviews"
	"github.com/Zomato/zomato-api-gateway/handlers/feedback/submit"
	"github.com/Zomato/zomato-api-gateway/handlers/gamification/awards"
	"github.com/Zomato/zomato-api-gateway/handlers/gamification/badges"
	"github.com/Zomato/zomato-api-gateway/handlers/gamification/badges/lookback"
	"github.com/Zomato/zomato-api-gateway/handlers/gamification/cointoss"
	crazydrops "github.com/Zomato/zomato-api-gateway/handlers/gamification/crazydrops"
	"github.com/Zomato/zomato-api-gateway/handlers/gamification/diwalimap"
	"github.com/Zomato/zomato-api-gateway/handlers/gamification/foodrescue"
	hand_cricket "github.com/Zomato/zomato-api-gateway/handlers/gamification/handcricket"
	gamification_server_status "github.com/Zomato/zomato-api-gateway/handlers/gamification/serverstatus"
	"github.com/Zomato/zomato-api-gateway/handlers/gamification/trivia"
	tc "github.com/Zomato/zomato-api-gateway/handlers/gamification/trivia/cart"
	"github.com/Zomato/zomato-api-gateway/handlers/gamification/trivia/coupons"
	trivia_faq "github.com/Zomato/zomato-api-gateway/handlers/gamification/trivia/faq"
	trivia_lobby "github.com/Zomato/zomato-api-gateway/handlers/gamification/trivia/lobby"
	"github.com/Zomato/zomato-api-gateway/handlers/gamification/trivia/metadata"
	"github.com/Zomato/zomato-api-gateway/handlers/gamification/zpl"
	zpl_history "github.com/Zomato/zomato-api-gateway/handlers/gamification/zpl/history"
	zpl_lobby "github.com/Zomato/zomato-api-gateway/handlers/gamification/zpl/lobby"
	"github.com/Zomato/zomato-api-gateway/handlers/gamification/zpl/matchscreen"
	zpl_metadata "github.com/Zomato/zomato-api-gateway/handlers/gamification/zpl/metadata"
	zpl_notify "github.com/Zomato/zomato-api-gateway/handlers/gamification/zpl/notifyme"
	zpl_submission "github.com/Zomato/zomato-api-gateway/handlers/gamification/zpl/submissions"
	giftCardsClaim "github.com/Zomato/zomato-api-gateway/handlers/gift_card/claim"
	giftCardsConsumer "github.com/Zomato/zomato-api-gateway/handlers/gift_card/consumer"
	giftCardsFAQ "github.com/Zomato/zomato-api-gateway/handlers/gift_card/faq"
	giftCardsLanding "github.com/Zomato/zomato-api-gateway/handlers/gift_card/landing_page"
	giftCardsOrder "github.com/Zomato/zomato-api-gateway/handlers/gift_card/order"
	"github.com/Zomato/zomato-api-gateway/handlers/gift_card/video"
	"github.com/Zomato/zomato-api-gateway/handlers/go_out/events/back_office"
	"github.com/Zomato/zomato-api-gateway/handlers/go_out/events/consumer/event"
	search2 "github.com/Zomato/zomato-api-gateway/handlers/go_out/events/consumer/search"
	groupOrdering "github.com/Zomato/zomato-api-gateway/handlers/group_ordering"
	"github.com/Zomato/zomato-api-gateway/handlers/highway"
	"github.com/Zomato/zomato-api-gateway/handlers/instant_kitchen/brands"
	dishCatalog "github.com/Zomato/zomato-api-gateway/handlers/instant_kitchen/dish_catalog"
	kitchen "github.com/Zomato/zomato-api-gateway/handlers/instant_kitchen/kitchens"
	instantOrder "github.com/Zomato/zomato-api-gateway/handlers/instant_kitchen/order"
	resCatalogue "github.com/Zomato/zomato-api-gateway/handlers/instant_kitchen/res_catalogue"
	"github.com/Zomato/zomato-api-gateway/handlers/inventory"
	"github.com/Zomato/zomato-api-gateway/handlers/karma"
	"github.com/Zomato/zomato-api-gateway/handlers/karma/crossapp"
	kitchenConsumer "github.com/Zomato/zomato-api-gateway/handlers/kitchen_management/consumer"
	kitchenInventory "github.com/Zomato/zomato-api-gateway/handlers/kitchen_management/inventory"
	kitchenManagement "github.com/Zomato/zomato-api-gateway/handlers/kitchen_management/kitchen"
	mealTime "github.com/Zomato/zomato-api-gateway/handlers/kitchen_management/meal_time"
	kitchenOrder "github.com/Zomato/zomato-api-gateway/handlers/kitchen_management/order"
	"github.com/Zomato/zomato-api-gateway/handlers/kitchen_management/scanneraction"
	vendor "github.com/Zomato/zomato-api-gateway/handlers/kitchen_management/vendor_handlers"
	"github.com/Zomato/zomato-api-gateway/handlers/location"
	locationAddress "github.com/Zomato/zomato-api-gateway/handlers/location/address"
	location_detail "github.com/Zomato/zomato-api-gateway/handlers/location/location_details"
	location_search "github.com/Zomato/zomato-api-gateway/handlers/location/location_search"
	"github.com/Zomato/zomato-api-gateway/handlers/location/location_selection"
	"github.com/Zomato/zomato-api-gateway/handlers/loginwithzomato"
	logswebhook "github.com/Zomato/zomato-api-gateway/handlers/logistics/webhook"
	"github.com/Zomato/zomato-api-gateway/handlers/logistics_order"
	tplHandler "github.com/Zomato/zomato-api-gateway/handlers/logistics_order/admin/third-party-dlos-handler"
	"github.com/Zomato/zomato-api-gateway/handlers/menu"
	"github.com/Zomato/zomato-api-gateway/handlers/menu/footer"
	notificationpreferences "github.com/Zomato/zomato-api-gateway/handlers/notification_preferences"
	"github.com/Zomato/zomato-api-gateway/handlers/nps"
	"github.com/Zomato/zomato-api-gateway/handlers/onesupport"
	"github.com/Zomato/zomato-api-gateway/handlers/onesupport/delight"
	oneSupportHotline "github.com/Zomato/zomato-api-gateway/handlers/onesupport/hotline"
	"github.com/Zomato/zomato-api-gateway/handlers/order"
	"github.com/Zomato/zomato-api-gateway/handlers/order/live_order"
	"github.com/Zomato/zomato-api-gateway/handlers/order/order_action"
	"github.com/Zomato/zomato-api-gateway/handlers/order/order_summary"
	op "github.com/Zomato/zomato-api-gateway/handlers/order_planner/service"
	payments "github.com/Zomato/zomato-api-gateway/handlers/payments"
	paymentsAuth "github.com/Zomato/zomato-api-gateway/handlers/payments/auth"
	paymentsDashboard "github.com/Zomato/zomato-api-gateway/handlers/payments/dashboard"
	paymentsEdition "github.com/Zomato/zomato-api-gateway/handlers/payments/edition"
	giftOffer "github.com/Zomato/zomato-api-gateway/handlers/payments/gift_offer"
	moneyTab "github.com/Zomato/zomato-api-gateway/handlers/payments/money_tab"
	userservice "github.com/Zomato/zomato-api-gateway/handlers/payments/user_service"
	zwallet "github.com/Zomato/zomato-api-gateway/handlers/payments/wallet_service"
	walletAddMoney "github.com/Zomato/zomato-api-gateway/handlers/payments/wallet_service/add_money"
	walletExternal "github.com/Zomato/zomato-api-gateway/handlers/payments/wallet_service/external"
	walletKyc "github.com/Zomato/zomato-api-gateway/handlers/payments/wallet_service/kyc"
	zomatoMoney "github.com/Zomato/zomato-api-gateway/handlers/payments/zomato_money"
	"github.com/Zomato/zomato-api-gateway/handlers/payments/zomato_money_v2"
	zmpl "github.com/Zomato/zomato-api-gateway/handlers/payments/zomato_paylater"
	adminHandler "github.com/Zomato/zomato-api-gateway/handlers/payments/zomato_paylater/admin"
	"github.com/Zomato/zomato-api-gateway/handlers/push"
	updategamestatus "github.com/Zomato/zomato-api-gateway/handlers/reward/casino/updategamestatus"
	rewardfaqcontroller "github.com/Zomato/zomato-api-gateway/handlers/reward/faq"
	playgamev1controller "github.com/Zomato/zomato-api-gateway/handlers/reward/playgamev1/controller"
	rewardpagecontroller "github.com/Zomato/zomato-api-gateway/handlers/reward/reward_page/controller"
	"github.com/Zomato/zomato-api-gateway/handlers/search"
	searchExternal "github.com/Zomato/zomato-api-gateway/handlers/search/external"
	sideMenu "github.com/Zomato/zomato-api-gateway/handlers/side_menu"
	"github.com/Zomato/zomato-api-gateway/handlers/sms"
	"github.com/Zomato/zomato-api-gateway/handlers/social/recommendation"
	"github.com/Zomato/zomato-api-gateway/handlers/social/sharing"
	"github.com/Zomato/zomato-api-gateway/handlers/stories/community"
	storiesConsumer "github.com/Zomato/zomato-api-gateway/handlers/stories/consumer"
	"github.com/Zomato/zomato-api-gateway/handlers/stories/merchant"
	"github.com/Zomato/zomato-api-gateway/handlers/stories/social"
	"github.com/Zomato/zomato-api-gateway/handlers/subscription"
	tabbedHome "github.com/Zomato/zomato-api-gateway/handlers/tabbed_home"
	"github.com/Zomato/zomato-api-gateway/handlers/telecom/dualleg"
	"github.com/Zomato/zomato-api-gateway/handlers/telecom/hotline"
	"github.com/Zomato/zomato-api-gateway/handlers/telecom/ivr"
	"github.com/Zomato/zomato-api-gateway/handlers/telecom/nummasking"
	"github.com/Zomato/zomato-api-gateway/handlers/telecom/voip"
	"github.com/Zomato/zomato-api-gateway/handlers/user"
	"github.com/Zomato/zomato-api-gateway/handlers/user/address"
	useraddress "github.com/Zomato/zomato-api-gateway/handlers/user/address"
	userprofile "github.com/Zomato/zomato-api-gateway/handlers/user/profile"
	"github.com/Zomato/zomato-api-gateway/handlers/userpreferenceservice/generic_preferences"
	"github.com/Zomato/zomato-api-gateway/handlers/vernacular"
	weatherPrediction "github.com/Zomato/zomato-api-gateway/handlers/weather/prediction"
	weatherUser "github.com/Zomato/zomato-api-gateway/handlers/weather/user"
	weatherStation "github.com/Zomato/zomato-api-gateway/handlers/weather/weather_station"
	"github.com/Zomato/zomato-api-gateway/handlers/whatsappservice"
	zomalandConsumer "github.com/Zomato/zomato-api-gateway/handlers/zomaland/consumer"
	"github.com/Zomato/zomato-api-gateway/handlers/zpush"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/internal/middlewares"
	"github.com/nanmu42/gzip"
	"github.com/newrelic/go-agent/v3/integrations/nrgin"
	"github.com/newrelic/go-agent/v3/newrelic"
	cors "github.com/rs/cors/wrapper/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

func Initialize(ctx context.Context, ev *env.Env, nrApp *newrelic.Application) *gin.Engine {
	r := gin.New()
	r.ContextWithFallback = true

	// Recovery middleware recovers from any panics and writes a 500 if there was one.
	if config.GetBool(ctx, "newrelic.enabled") {
		r.Use(gg.GinPanicRecovery())
	}
	if config.GetBool(ctx, "datadog.enabled") {
		r.Use(datadog.GinPanicRecovery())

		// store the handler name in the request context
		r.Use(func(c *gin.Context) {
			handlerName := c.HandlerName()
			if handlerName == "" {
				handlerName = c.FullPath()
			}
			c.Request = c.Request.WithContext(context.WithValue(c.Request.Context(), "handlerName", handlerName))
			c.Next()
		})

		r.Use(otelgin.Middleware(config.GetString(ctx, "datadog.service_name"),
			otelgin.WithGinFilter(func(c *gin.Context) bool {
				return !strings.HasPrefix(c.Request.URL.Path, "/health")
			}),
			otelgin.WithSpanNameFormatter(func(r *http.Request) string {
				if handlerName, ok := r.Context().Value("handlerName").(string); ok {
					return handlerName
				}

				// if the handler name is not found in the context,
				// then return empty string to log error in datadog
				return ""
			}),
		))

		r.Use(func(c *gin.Context) {
			// add cost data in the context if the request is sampled
			ctx, isTraceSampledForCost := cost.SetTraceSamplingFlagInContext(c.Request.Context())
			if isTraceSampledForCost {
				ctx = cost.WithIncomingCostData(ctx)
				ctx = cost.AddToRequestPath(ctx, cost.PathComponentTypeHTTPEndpoint, c.FullPath())
				c.Request = c.Request.WithContext(ctx)
			}
			c.Next()
		})
		r.Use(func(c *gin.Context) {
			c.Next() // Process request
			if statusCode := c.Writer.Status(); statusCode >= 400 && statusCode < 500 && statusCode != 404 {
				err := errors.New(fmt.Sprintf("error with client request %v", statusCode))
				span := trace.SpanFromContext(c.Request.Context())
				span.SetAttributes(attribute.String(ext.Error, err.Error()))
				defer span.End()
				if span != nil {
					span.SetAttributes(attribute.Int("http.status_code", statusCode))
				}
			}
		})

		// add panic recovery here to report root span errors on DD in case of panic
		r.Use(datadog.GinPanicRecovery())
	}

	// adds configuration to context
	r.Use(config.GinMiddleware())

	// Generate Request ID if not passed by the client
	r.Use(middlewares.RequestID())

	// Add Amazon Trace ID in response headers
	r.Use(middlewares.AmazonTraceID())

	// Store routing_context in context keys
	r.Use(middlewares.RoutingContext())

	// Add ContextServerHeader in context keys
	r.Use(middlewares.ContextServerHeader(config.GetString(ctx, "service.kuma_service_name")))

	// Write access log
	r.Use(middlewares.Logger())

	// Default timeout
	// r.Use(middlewares.DefaultTimeout())

	// compress responses using gzip
	r.Use(gzip.DefaultHandler().Gin)

	// record metrics for each transaction
	r.Use(middlewares.RequestMetrics())

	// Newrelic middleware for monitoring
	r.Use(nrgin.MiddlewareHandlerTxnNames(nrApp))

	r.Use(benchmarking.HttpInterceptor(ctx, "benchmarking"))

	// add env to the context
	r.Use(env.MiddleWare(ev))

	// add client information based on the provided headers / cookies in the request
	r.Use(middlewares.ClientMiddleware())

	// z trends doesnt require authentication
	zTrendsGroup := r.Group("/gw/z-trends")
	setupZTrendsRoutes(zTrendsGroup)

	corsMiddleware := cors.New(middlewares.ZomatoWebCORSWhitelist())

	// weather service public routes doesnt require authentication
	setupWeatherServiceRoutes(r.Group("/gw/weather/service", corsMiddleware))
	// payment wallet cbs public routes have custom auth middlewares and hence doesnt require authentication by auth service
	setupMerchantBusinessDashboardRoutes(r.Group("/gw/online_payments", corsMiddleware))

	// authenticate the request usinf provided access token in headers / cookies
	r.Use(middlewares.Authentication())

	// read request headers and add langugage metadata in the context
	r.Use(middlewares.Localization())

	// set location token in the context
	r.Use(middlewares.SetLocationToken())

	middlewares.InitializeCSRF(ctx)
	// health check route
	r.GET("/health", health.HealthcheckHandler())

	// /internal routes are for internal api calls made from within zomato VPC. These are
	// not exposed to the outside world
	v2internal := r.Group("/gw/internal")
	setupInternalRoutes(ctx, v2internal)

	v2 := r.Group("/v2")
	// Temp adding a rate limit of 100rps on add_user_device call for NYE
	v2.Use(middlewares.Ratelimit(config.GetFloat64(ctx, "rate_limits.add_user_device")))
	setupV2Routes(v2)

	// /gateway routes earlier used to go to Front Envoy -> Search Service.
	// These now pass through this service -> Search Service
	searchGateway := r.Group("/gateway")
	setupSearchGatewayRoutes(searchGateway)

	gateway := r.Group("/gw")
	{
		setupGatewayRoutes(gateway.Group("/"))
		setupAuthRoutes(gateway.Group("/auth"), corsMiddleware)
		setupPaymentsRoutes(gateway.Group("/payments/dashboard", corsMiddleware))
		setupEditionInternalRoutes(ctx, gateway.Group("/edition"), corsMiddleware)
		setupWebRoutes(gateway.Group("/web", corsMiddleware))
		setupOrderRoutes(gateway.Group("/order"))
		setupWalletServiceDashboardRoutes(gateway.Group("/payments/zomato_money/dashboard", corsMiddleware))
		setupMenuRoutes(gateway.Group("/menu"))
		setupEvDashboardDriverServiceRoutes(ctx, gateway.Group("/ev-vendor", corsMiddleware))
		setupDeliveryLeadsPartnerLMSRoutes(ctx, gateway.Group("/delivery-leads-partner", corsMiddleware))
		setupSubscriptionRoutes(gateway.Group("/subscription"))
		setupLegendsRoutes(gateway.Group("/legends", corsMiddleware))
		setupInventoryRoutes(gateway.Group("/inventory"))
		setupCartRoutes(gateway.Group("/cart"))
		setupGroupOrderingRoutes(gateway.Group("/group_ordering"))
		setupDelightServiceRoutes(ctx, gateway.Group("/delight"))
		setupFileUploadsRoutes(gateway.Group("/uploads", corsMiddleware))
		setupMerchantRoutes(gateway.Group("/merchant"))
		setupSocialRoutes(gateway.Group("/social"))
		setupFeedbackRoutes(gateway.Group("/feedback"))
		setupBlinkitRoutes(gateway.Group("/blinkit"))
		setupPushRoutes(gateway.Group("/push"))
		setupSMSRoutes(gateway.Group("/sms"))
		setupEmailRoutes(gateway.Group("/email"))
		setupPaymentServiceRoutes(ctx, gateway.Group("/payments"))
		setupKarmaRoutes(gateway.Group("/karma"))
		setupGamificationRoutes(gateway.Group("/gamification"))
		setupTrainFulfillmentServiceRoutes(gateway.Group("/train"))
		setupAIBotRoutes(gateway.Group("/aibot"))
		setupLoginWithZomatoRoutes(gateway.Group("/login-with-zomato"))
		setupAkamaiCallbackRoutes(gateway.Group("/akamai"))
		setupAdsRoutes(gateway.Group("/ads"), ctx)
		setUpCultAppRoutes(gateway.Group("/cult"))
		setupCinemaRouters(gateway.Group("/cinema"))
		setupOneSupportRoutes(gateway.Group("/onesupport"))
		setupRewardsRoutes(gateway.Group("/reward"))
		setupZomalandRoutes(gateway.Group("/zomaland", corsMiddleware))
		setupBenefitServiceRoutes(gateway.Group("/benefit"))
		setupDiningServiceRoutes(ctx, gateway.Group("/dining"))
		setUpGoOutRoutes(gateway.Group("/goout", corsMiddleware))
		setUpZliveRoutes(gateway.Group("/zlive", corsMiddleware))
		setupSchedulingRoutes(gateway.Group("/scheduling"))
		setupRestaurantRoutes(gateway.Group("/restaurant"))
		setupWeatherExternalRoutes(gateway.Group("/weather/external"))
		setupXtremeDashboardRoutes(gateway.Group("/xtreme", corsMiddleware))
		setupUserPreferenceServiceRoutes(gateway.Group("/user-preference"))
		setupHighwayRoutes(gateway.Group("/highway"))
		setupAddressFlowRoutes(gateway.Group("/user/address"))
		setupDevRoutes(gateway.Group("/dev"))
		setupOrderPlannerRoutes(gateway.Group("/order-planner"))
		setupFlywheelSocialAdsRoutes(gateway.Group("/social-ads", corsMiddleware))
		setupBrandAdsTargetServiceRoutes(gateway.Group("/bats"))
		setupPaymentGatewayRoutes(gateway.Group("/internal/payments-api-gateway"))
		setupUserServiceRoutes(gateway.Group("/user"))
		setupAerobarServiceRoutes(gateway.Group("/aerobar"))
		setupConfigRoutes(gateway.Group("/config"))
		setupEternalFormServiceRoutes(ctx, gateway.Group("/efs"))
		setupUserLocationRoutes(gateway.Group("/user/location"))
	}

	// deprecated
	instantGroup := r.Group("/gw/instant")
	instantGroup.Use(cors.New(middlewares.ZomatoWebCORSWhitelist()))
	setupInstantFoodRoutes(instantGroup)

	kitchenGroup := r.Group("/gw/kitchen")
	kitchenGroup.Use(cors.New(middlewares.ZomatoWebCORSWhitelist()))
	setupKitchenRoutes(kitchenGroup)

	storiesGroup := r.Group("/gw/stories")
	storiesGroup.Use(cors.New(middlewares.ZomatoWebCORSWhitelist()))
	setupStoriesRoutes(storiesGroup)

	corporateDashboard := r.Group("/gw/corporate_ordering")
	corporateDashboard.Use(cors.New(middlewares.ZomatoWebCORSWhitelist()))
	corporateDashboard.Use(middlewares.CheckCSRF())
	setupCorporateDashboardRoutes(corporateDashboard)

	// routes under this group will not use any middleware authentication
	// any auth if required should be handled by underlying micro service
	externalGateway := r.Group("/gw/ext")
	{
		setupExternalRoutes(ctx, externalGateway)
		setupLogisticsRouter(externalGateway)
		setupClevertapRoutes(externalGateway)
		setupZomalandExternalRoutes(externalGateway)
		setupEditionExternalRoutes(ctx, externalGateway)
		setupEvDashboardDriverServiceExternalRoutes(ctx, externalGateway.Group("/ev-vendor", cors.New(middlewares.EvVendorDashboardExternalApiCorsWhitelist())))
		setupEvDashboardDriverServiceExternalRoutesV2(ctx, externalGateway.Group("/v2/ev-vendor", cors.New(middlewares.EvVendorDashboardExternalApiCorsWhitelist())))
		setupExotelHotlineRoutes(
			externalGateway.Group("/exotel",
				middlewares.CheckIPWhitelist(config.GetStringSlice(ctx, "telecom_service.exotel.whitelisted_ips")),
			))
		setupIRCTCRoutes(ctx, externalGateway)
		setupMMTRoutes(ctx, externalGateway)
	}

	for _, route := range r.Routes() {
		fmt.Printf("%s %s -> %s\n", route.Method, route.Path, route.Handler)
	}

	return r
}

func setupIRCTCRoutes(ctx context.Context, r *gin.RouterGroup) {
	r = r.Group("/irctc",
		middlewares.CheckIPWhitelist(config.GetStringSlice(ctx, "irctc.whitelisted_ips")),
		irctcMiddlewares.CheckIrctcBackendClient(),
	)

	r.POST("/list_restaurants", irctc.ListRestaurants)
	r.POST("/list_restaurant_menu", menu.GetIrctcMenu)
	r.POST("/get_cart", irctcCart.GetCart)
	r.POST("/place_order", irctcCart.CheckoutCart)
	r.POST("/outlet/update/status", tfsOutlet.UpdateOutletStatusHandler)
}

func setupMMTRoutes(ctx context.Context, r *gin.RouterGroup) {
	r = r.Group("/mmt/train_ordering",
		middlewares.CheckIPWhitelist(config.GetStringSlice(ctx, "mmt.whitelisted_ips")),
		mmtMiddlewares.CheckMMTBackendClient(),
	)
	r.POST("/list_stations", mmt.ListStations)
	r.POST("/list_restaurants", mmt.ListRestaurants)
	r.POST("/list_restaurant_menu", menu.GetMmtMenu)
	r.POST("/validate_cart", mmtCart.ValidateCart)
	r.POST("/place_order", mmtCart.CheckoutCart)
	r.POST("/update_seat", mmt.UpdateSeatHandler)
	r.POST("/order_info", mmt.OrderInfoHandler)
	r.POST("/cancel_order", mmt.CancelOrderHandler)
	r.POST("/walker_info", mmt.WalkerInfoHandler)
	r.POST("/refund_transaction", mmt.RefundTransactionHandler)
}

func setupClevertapRoutes(r *gin.RouterGroup) {
	r.GET("/clevertap/res_recommendation", clevertap.GetResRecommendationHandler)
}

func setupV2Routes(r *gin.RouterGroup) {
	r.POST("/add_user_device.json", push.AddUserDeviceHandler)
}

func setupZTrendsRoutes(r *gin.RouterGroup) {
}

func setupSearchGatewayRoutes(r *gin.RouterGroup) {
	r.POST("/search/v1/recent_search", search.RegisterRecentSearchHandler)
	r.POST("/search/v1/clear_recent_search", search.ClearRecentSearchHandler)
	r.POST("/search/v1/get_recent_search", search.GetRecentSearchHandler)
	r.POST("/search/v1/get_blank_state", search.BlankStateHandler)
	r.POST("/search/v1/get_autocomplete", search.AutocompleteHandler)
}

func setupMerchantRoutes(r *gin.RouterGroup) {
	r.POST("/auth/verification/init", auth.IsUserLoggedIn, userprofile.InitiateAuthVerificationHandler)
	r.POST("/auth/verification/verify", auth.IsUserLoggedIn, userprofile.VerifyAuthenticationHandler)
	r.POST("/email/update/init", auth.IsUserLoggedIn, userprofile.InitiateEmailChangeHandler)
	r.POST("/email/update/verify", auth.IsUserLoggedIn, userprofile.UpdateEmailHandler)

	r.POST("/mobile/update/init", auth.IsUserLoggedIn, userprofile.InitiateMobileChangeHandler)
	r.POST("/mobile/update/verify", auth.IsUserLoggedIn, userprofile.UpdateMobileHandler)

	r.POST("/push/token", push.AddUserDeviceHandler)
}

func setupGatewayRoutes(r *gin.RouterGroup) {
	r.POST("/gateway/search/v1/get_blank_state", search.BlankStateHandler)

	r.POST("/nps/v1/survey", nps.SurveyHandler)
	r.POST("/nps/v1/rating", nps.RatingHandler)
	r.POST("/nps/v1/serve", nps.ServeHandler)

	r.POST("/menu/v1/item_details", menu.CatalogueHandler)

	r.POST("/user/preference/options", user.ListPreferenceOptionsHandler)
	r.POST("/user/preferences", auth.IsUserLoggedIn, user.SavePreferencesHandler)
	r.GET("/user/preference/v2/options", user.ListPreferenceOptionsV2Handler)

	r.POST("/user/auth/verification/init", auth.IsUserLoggedIn, userprofile.InitiateAuthVerificationHandler)
	r.POST("/user/auth/verification/verify", auth.IsUserLoggedIn, userprofile.VerifyAuthenticationHandler)
	r.POST("/user/email/update/init", auth.IsUserLoggedIn, userprofile.InitiateEmailChangeHandler)
	r.POST("/user/email/update/verify", auth.IsUserLoggedIn, userprofile.UpdateEmailHandler)

	r.POST("/user/mobile/update/init", auth.IsUserLoggedIn, userprofile.InitiateMobileChangeHandler)
	r.POST("/user/mobile/update/verify", auth.IsUserLoggedIn, userprofile.UpdateMobileHandler)
	r.POST("/user/facebook/mobile/link", auth.IsUserLoggedIn, userprofile.LinkMobileForFacebookUserHandler)

	r.POST("/user/address-sharing/generate-link", auth.IsUserLoggedIn, useraddress.GenerateAddressSharingLinkHandler)
	r.POST("/user/address-sharing/get-info", auth.IsUserLoggedIn, useraddress.GetSharedAddressInfoHandler)
	r.POST("/user/address-sharing/create-address", auth.IsUserLoggedIn, useraddress.CreateSharedAddressHandler)
	r.POST("/user/info", auth.IsUserLoggedIn, user.SaveProfileHandler)
	r.GET("/user/info", auth.IsUserLoggedIn, user.GetUserInfoHandler)
	r.GET("/user/v2/info", auth.IsUserLoggedIn, userprofile.GetUserInfoV2Handler)
	r.POST("/user/v2/info", auth.IsUserLoggedIn, userprofile.UpdateProfile)
	r.POST("/user/business-profile/verify-otp", auth.IsUserLoggedIn, userprofile.VerifyBusinessEmailHandler)
	r.POST("/user/business-profile/send-otp", auth.IsUserLoggedIn, userprofile.InitiateBusinessEmailVerificationHandler)
	r.POST("/user/business-profile/resend-otp", auth.IsUserLoggedIn, userprofile.InitiateBusinessEmailResendVerificationHandler)

	r.POST("/user/address/delete", useraddress.DeleteUserAddress)

	// user notification_preferences APIs (app)
	r.GET("/user/notification_preferences", notificationpreferences.GetAppNotificationPreferences)
	r.POST("/user/notification_preferences", notificationpreferences.UpdateAppNotificationPreferences)

	setupFinTechRoutes(r)

	setupInternalCFSRoutes(r)

	r.GET("/vernacular/messages/static", vernacular.GetStaticMessages)

	// Telecom Service
	r.POST("/telecom/v1/twilio/token", auth.IsUserLoggedIn, voip.AccessTokenHandler)
	r.POST("/telecom/v1/masking/get-masked-number", auth.IsUserLoggedIn, nummasking.AllocateNumberHandler)

	r.POST("/side-menu", sideMenu.GetSideMenuHandler)

	r.GET("/user/addresses", useraddress.GetUserAddresses)
	r.POST("/user/address/update", useraddress.UpdateUserAddress)
	r.POST("/user/address/create", useraddress.CreateUserAddress)
	r.POST("/user/address/contact-details", auth.IsUserLoggedIn, useraddress.UpdateAddressContactDetailsHandler)

	r.GET("/tabbed-home", tabbedHome.GetTabbedHomeHandler)
	r.POST("/tabbed-location", tabbedlocation.GetTabbedLocationHandler)

	r.GET("/countries/:country_id", location.GetCountryDetailsHandler)
	r.GET("/countries", location.GetCountriesHandler)

	r.POST("/user/add-address-page", auth.IsUserLoggedIn, useraddress.GetAddAddressPage)
	r.POST("/user/add-address", auth.IsUserLoggedIn, useraddress.AddUserAddress)
	r.POST("/user/address/update-pinned-state", auth.IsUserLoggedIn, useraddress.UpdateAddressPinnedState)

	// generic faq page
	r.GET("/faq-page", auth.IsUserLoggedIn, faq.PageHandler)

	// app aesthetics
	r.GET("/aesthetics", consumerapp.GetAesthetics)
}

func setupCorporateDashboardRoutes(r *gin.RouterGroup) {
	// Company apis
	r.OPTIONS("/company/profile")
	r.GET("/company/profile", auth.IsUserLoggedIn, corporateFunds.GetCompanyHandler)

	r.OPTIONS("/company/poi/all")
	r.GET("/company/poi/all", auth.IsUserLoggedIn, enterprisedashboard.GetAllPOIHandler)

	r.OPTIONS("/company/verify")
	r.POST("/company/verify", auth.IsUserLoggedIn, corporateFunds.VerifyCompanyHandler)

	// Commenting to prevent docker from restarting due to gin error
	// r.GET("/admin/company/:company_id/subscriptions", auth.IsUserLoggedIn, corporateFunds.GetAllSubscriptionHandler)

	/********* Authorization ********/
	r.OPTIONS("/auth")
	r.GET("/auth", auth.IsUserLoggedIn, corporateFunds.ValidateSPOCHandler)

	/********* Landing Page ********/
	r.OPTIONS("/landing_page")
	r.GET("/landing_page", auth.IsUserLoggedIn, enterprisedashboard.GetLandingPageInfoHandler)

	r.OPTIONS("/events/upcoming")
	r.GET("/events/upcoming", auth.IsUserLoggedIn, enterprisedashboard.GetAllUpcomingEventsHandler)

	r.OPTIONS("/orders/all")
	r.GET("/orders/all", auth.IsUserLoggedIn, enterprisedashboard.GetAllOrdersForCompanyHandler)

	r.OPTIONS("/company/offices")
	r.GET("/company/offices", auth.IsUserLoggedIn, enterprisedashboard.GetCompanyOfficesHandler)

	r.OPTIONS("company/order")
	r.GET("company/order", auth.IsUserLoggedIn, enterprisedashboard.GetOrderDetailsHandler)

	r.OPTIONS("/events/previous")
	r.GET("/events/previous", auth.IsUserLoggedIn, enterprisedashboard.GetAllPreviousEventsHandler)

	r.OPTIONS("/events/credit-transactions")
	r.GET("/events/credit-transactions", auth.IsUserLoggedIn, enterprisedashboard.GetCreditTransactionsForEventHandler)

	/********* Employees ************/
	r.OPTIONS("/employee/section")
	r.GET("/employee/section", auth.IsUserLoggedIn, enterprisedashboard.GetEmployeeSectionHandler)

	r.OPTIONS("/group/employees")
	r.GET("/group/employees", auth.IsUserLoggedIn, enterprisedashboard.GetEmployeesByGroupIdHandler)

	r.OPTIONS("/employee/details")
	r.GET("/employee/details", auth.IsUserLoggedIn, enterprisedashboard.GetEmployeeDetailsHandler)

	r.OPTIONS("/employee/credit_transactions")
	r.GET("/employee/credit_transactions", auth.IsUserLoggedIn, enterprisedashboard.GetCreditTransactionsForEmployeeHandler)

	r.OPTIONS("/employee/orders")
	r.GET("/employee/orders", auth.IsUserLoggedIn, enterprisedashboard.GetAllOrdersForEmployeeHandler)

	r.OPTIONS("/employee/all")
	r.GET("/employee/all", auth.IsUserLoggedIn, enterprisedashboard.GetAllEmployeesHandler)

	r.OPTIONS("/employee/get/:employee_id")
	r.GET("/employee/get/:employee_id", auth.IsUserLoggedIn, corporateFunds.GetEmployeeByIdHandler)

	r.OPTIONS("/employee/remove")
	r.POST("/employee/remove", auth.IsUserLoggedIn, enterprisedashboard.RemoveEmployeeFromCompanyHandler)

	r.OPTIONS("/employee/activate/:employee_id")
	r.POST("/employee/activate/:employee_id", auth.IsUserLoggedIn, corporateFunds.UpdateEmployeeStatusHandler)

	r.OPTIONS("/employee/upsert")
	r.POST("/employee/upsert", auth.IsUserLoggedIn, corporateFunds.UpsertEmployeeHandler)

	r.OPTIONS("/employee/upload/:process_id")
	r.GET("/employee/upload/:process_id", auth.IsUserLoggedIn, corporateFunds.GetUploadEmployeesProcessStatusHandler)

	r.OPTIONS("/employee/upload")
	r.POST("/employee/upload", auth.IsUserLoggedIn, corporateFunds.UploadEmployeesCSVHandler)

	r.OPTIONS("/employee/uploadv2")
	r.POST("/employee/uploadv2", auth.IsUserLoggedIn, corporateFunds.UploadV2EmployeesCSVHandler)

	/********* Groups **************/
	r.OPTIONS("/group/all")
	r.GET("/group/all", auth.IsUserLoggedIn, enterprisedashboard.GetAllGroupsHandler)

	r.OPTIONS("/group/details")
	r.GET("/group/details", auth.IsUserLoggedIn, enterprisedashboard.GetGroupDetailsHandler)

	r.OPTIONS("/group/create")
	r.POST("/group/create", auth.IsUserLoggedIn, enterprisedashboard.UploadEmployeesCSVHandler)

	r.OPTIONS("/group/upload/:process_id")
	r.GET("/group/upload/:process_id", auth.IsUserLoggedIn, enterprisedashboard.GetUploadGroupEmployeesProcessStatusHandler)

	r.OPTIONS("/group/employee_mapping/update")
	r.POST("/group/employee_mapping/update", auth.IsUserLoggedIn, corporateFunds.UpdateGroupEmployeeMappingHandler)

	r.OPTIONS("/program/add_group/add")
	r.POST("/program/add_group/add", auth.IsUserLoggedIn, enterprisedashboard.AddGroupToProgramHandler)

	/********* Programs ************/
	r.OPTIONS("/program/review")
	r.POST("/program/review", auth.IsUserLoggedIn, corporateFunds.ReviewProgramHandler)

	r.OPTIONS("/program/all")
	r.GET("/program/all", auth.IsUserLoggedIn, enterprisedashboard.GetAllProgramsHandler)

	r.OPTIONS("/program/save_draft")
	r.POST("/program/save_draft", auth.IsUserLoggedIn, enterprisedashboard.SaveProgramDraftHandler)

	r.OPTIONS("/program/create")
	r.POST("/program/create", auth.IsUserLoggedIn, enterprisedashboard.CreateProgramHandler)

	r.OPTIONS("/program/confirm_creation")
	r.POST("/program/confirm_creation", auth.IsUserLoggedIn, enterprisedashboard.ConfirmProgramCreationHandler)

	r.OPTIONS("/program/info")
	r.GET("/program/info", auth.IsUserLoggedIn, enterprisedashboard.GetProgramDetailsHandler)

	r.OPTIONS("/program/add_group/review")
	r.POST("/program/add_group/review", auth.IsUserLoggedIn, enterprisedashboard.ReviewProgramInfoHandler)

	r.OPTIONS("/program/add_employee/review")
	r.POST("/program/add_employee/review", auth.IsUserLoggedIn, enterprisedashboard.ReviewProgramForEmployeeToGroupMapping)

	r.OPTIONS("/program/add_employee/topup_cycle")
	r.POST("/program/add_employee/topup_cycle", auth.IsUserLoggedIn, enterprisedashboard.SelectTopupCycleForEmployeeAdditionHandler)

	r.OPTIONS("/program/payment_modes/update")
	r.POST("/program/payment_modes/update", auth.IsUserLoggedIn, enterprisedashboard.UpdatePaymentModesForProgramHandler)

	r.OPTIONS("/program/top")
	r.GET("/program/top", auth.IsUserLoggedIn, corporateFunds.GetAllProgramsHandler)

	r.OPTIONS("/program/info/:program_id")
	r.GET("/program/info/:program_id", auth.IsUserLoggedIn, corporateFunds.GetProgramDetailsHandler)

	r.OPTIONS("/program/payments")
	r.GET("/program/payments", auth.IsUserLoggedIn, enterprisedashboard.GetAllPaymentsForProgramHandler)

	r.OPTIONS("/program/emloyees")
	r.GET("/program/employees", auth.IsUserLoggedIn, enterprisedashboard.GetAllEmployeesForProgram)

	r.OPTIONS("/program/credit_transactions")
	r.GET("/program/credit_transactions", auth.IsUserLoggedIn, enterprisedashboard.GetCreditTransactionsForProgramHandler)

	r.OPTIONS("/program/activate/:program_id")
	r.POST("/program/activate/:program_id", auth.IsUserLoggedIn, corporateFunds.UpdateProgramStatusHandler)

	r.OPTIONS("/program/employee_mapping/review")
	r.POST("/program/employee_mapping/review", corporateFunds.ReviewProgramEmployeeMappingHandler)

	/********* Payments ************/
	r.OPTIONS("/payment/all")
	r.GET("/payment/all", auth.IsUserLoggedIn, enterprisedashboard.GetAllPaymentsForSPOCHandler)

	r.OPTIONS("/payment/create")
	r.POST("/payment/create", auth.IsUserLoggedIn, enterprisedashboard.CreatePaymentForSPOCHandler)

	r.OPTIONS("/payment/zpaykitCode")
	r.GET("/payment/zpaykitCode", enterprisedashboard.GetPaymentAccessCodeHandler)

	r.OPTIONS("/payment/request")
	r.GET("/payment/request", enterprisedashboard.GetPaymentRequestLogHandler)

	r.OPTIONS(("/payment/info"))
	r.GET("/payment/info", auth.IsUserLoggedIn, enterprisedashboard.GetPaymentInfoHandler)

	r.OPTIONS("/events/fundsblocked")
	r.GET("/events/fundsblocked", auth.IsUserLoggedIn, enterprisedashboard.GetAllFundsBlockedEventsHandler)

	// spoc invite
	r.OPTIONS("/invitation/accept")
	r.POST("/invitation/accept", corporateFunds.AcceptInviteHandler)

	r.OPTIONS("/invitation/validate")
	r.POST("/invitation/validate", corporateFunds.ValidateInviteHandler)

	r.OPTIONS("/online-payment/info")
	r.GET("/online-payment/info", corporateFunds.OnlinePaymentInfoHandler)

	r.OPTIONS("/online-payment/checkout_details")
	r.POST("/online-payment/checkout_details", corporateFunds.OnlinePaymentCheckoutDetailsHandler)

	r.OPTIONS("/invoice/all")
	r.GET("/invoice/all", enterprisedashboard.GetAllInvoicesForCompanyHandler)

	r.OPTIONS("/invoice/details")
	r.GET("/invoice/details", enterprisedashboard.GetInvoiceDetailsForCompanyHandler)

	r.OPTIONS("/invoice/files")
	r.GET("/invoice/files", enterprisedashboard.GetInvoiceAndReportFilesForCompanyHandler)

	r.OPTIONS("/invoice/payments")
	r.GET("/invoice/payments", enterprisedashboard.GetPaymentDetailsForInvoiceHandler)

	/*******************************Questions*********************************/
	r.OPTIONS("/question/all")
	r.GET("/question/all", auth.IsUserLoggedIn, corporateFunds.GetAllQuestionsForCompanyHandler)

	/*******************************Insights*********************************/
	r.OPTIONS("insights/orders")
	r.GET("insights/orders", auth.IsUserLoggedIn, enterprisedashboard.GetOrderInsightsForCompanyHandler)

	/*******************************Reports*********************************/
	r.OPTIONS("/report/all")
	r.GET("/report/all", auth.IsUserLoggedIn, enterprisedashboard.GetAllReportsHandler)

	r.OPTIONS("/report/details")
	r.GET("/report/details", auth.IsUserLoggedIn, enterprisedashboard.GetReportDetailsHandler)

	r.OPTIONS("/report/generate")
	r.POST("/report/generate", auth.IsUserLoggedIn, enterprisedashboard.GenerateReportHandler)

	/*******************************GiftCards*********************************/
	r.OPTIONS("/giftcard/all")
	r.POST("/giftcard/all", auth.IsUserLoggedIn, enterprisedashboard.GetAllGiftCardsForEnterpriseHandler)

	/*******************************Orders*********************************/
	r.OPTIONS("/order_expense/action")
	r.POST("/order_expense/action", enterprisedashboard.OrderExpenseActionHandler)

	/******************************LeadGen**********************************/
	r.OPTIONS("/lead_generation/submit")
	r.POST("/lead_generation/submit", corporateFunds.SubmitLeadGenHandler)

	/******************************Expense**********************************/
	r.OPTIONS("/expense/action")
	r.POST("/expense/action", enterprisedashboard.ExpenseActionHandler)
}

func setupExternalRoutes(ctx context.Context, r *gin.RouterGroup) {
	// logistics admin paths
	r.GET("/logistics/admin/tpl/:tpl/config", logistics_order.GatewayHandler)
	r.POST("/logistics/admin/tpl/:tpl/config", logistics_order.GatewayHandler)
	r.POST("/logistics/admin/db", logistics_order.GatewayHandler)
	r.GET("/logistics/admin/log", logistics_order.GatewayHandler)
	r.POST("/logistics/admin/log", logistics_order.GatewayHandler)
	r.GET("/logistics/admin/config/:category", logistics_order.GatewayHandler)
	r.POST("/logistics/admin/config/:category", logistics_order.GatewayHandler)

	// logistics dashboard paths
	r.POST("/logistics/dashboard/auth", logistics_order.GatewayHandler)
	r.GET("/logistics/dashboard/home", logistics_order.GatewayHandler)
	r.GET("/logistics/dashboard/feature", logistics_order.GatewayHandler)
	r.GET("/logistics/dashboard/order", logistics_order.GatewayHandler)
	r.GET("/logistics/dashboard/tpl", logistics_order.GatewayHandler)

	// logistics third party logistics paths
	// old routes kept the same, later on will make one route
	r.POST("/logistics/tpl/:tplId/:orderId/:action", middlewares.GetTplCallbackHandler(ctx))
	r.GET("/logistics/tpl/:tplId/:orderId/:action", logistics_order.GatewayHandler)
	r.POST("/logistics/tracking/tpl/:tplId/:action", logistics_order.GatewayHandler)
	// new routes
	r.POST("/logistics/v2/tpl/:tplId/:orderId/:action", tplHandler.TplActionHandler)

	r.GET("/email/redirect", tracking.TrackMailActions)
	r.GET("/email/unsubscribe", tracking.EmailUnsubscribeTrack)

	// sendgrid events webhook route
	r.POST("/email/webhook/sendgrid", emailwebhook.SendgridEventsHandler)

	fonadaSMSWhitelistedIps := config.GetStringSlice(ctx, "sms_service.fonada.whitelisted_ips")
	plivoSMSWhitelistedIps := config.GetStringSlice(ctx, "sms_service.plivo.whitelisted_ips")
	gupshupSMSWhitelistedIps := config.GetStringSlice(ctx, "sms_service.gupshup.whitelisted_ips")
	karixSMSWhitelistedIps := config.GetStringSlice(ctx, "sms_service.karix.whitelisted_ips")
	kaleyraSMSWhitelistedIPs := config.GetStringSlice(ctx, "sms_service.kaleyra.whitelisted_ips")
	r.POST("/sms/v1/callbacks/alisms", sms.AliSMSCallbackHandler)
	r.POST("/sms/v1/callbacks/airtel", sms.AirtelCallbackHandler)
	r.POST("/sms/v1/callbacks/exotel", sms.ExotelCallbackHandler)
	r.GET("/sms/v1/callbacks/mobishastra", sms.MobishastraCallbackHandler)
	r.POST("/sms/v1/callbacks/gupshup", middlewares.CheckIPWhitelist(gupshupSMSWhitelistedIps), sms.GupshupCallbackHandler)
	r.GET("/sms/v1/callbacks/fonada", middlewares.CheckIPWhitelist(fonadaSMSWhitelistedIps), sms.FonadaSMSCallbackHandler)
	r.POST("/sms/v1/callbacks/plivo", middlewares.CheckIPWhitelist(plivoSMSWhitelistedIps), sms.PlivoCallbackHandler)
	r.POST("/sms/v1/callbacks/karix", middlewares.CheckIPWhitelist(karixSMSWhitelistedIps), sms.KarixCallbackHandler)
	r.POST("/sms/v1/callbacks/kaleyra", middlewares.CheckIPWhitelist(kaleyraSMSWhitelistedIPs), sms.KaleyraCallbackHandler)

	gupshupWhitelistedIps := config.GetStringSlice(ctx, "onesupport.rm.whitelisted_ips")
	r.GET("/onesupport/whatsapp/rm-eligiblity", middlewares.CheckIPWhitelist(gupshupWhitelistedIps), delight.IsUserEligibleForRM)

	hotlineWhitelistedIps := config.GetStringSlice(ctx, "onesupport.hotline.exotel.whitelisted_ips")
	// support-service
	r.GET("/support-service/hotline/exotel/callback", middlewares.CheckIPWhitelist(hotlineWhitelistedIps), oneSupportHotline.HotLineExotelCallbackHandler)

	// Telecom
	airtelWhitelistedIps := config.GetStringSlice(ctx, "telecom_service.airtel.whitelisted_ips")
	fonadaWhitelistedIps := config.GetStringSlice(ctx, "telecom_service.fonada.whitelisted_ips")
	plivoWhitelistedIps := config.GetStringSlice(ctx, "telecom_service.plivo.whitelisted_ips")
	r.POST("/telecom/v1/twilio/call", voip.MakeCallHandler)
	r.POST("/telecom/v1/twilio/call/status", voip.CallStatusHandler)
	r.POST("/telecom/v1/twilio/dial/status", voip.DialStatusHandler)
	r.POST("/telecom/v1/twilio/recording/status", voip.RecordingStatusHandler)
	r.POST("/telecom/v1/exotel/callback", nummasking.ExotelWebhook)
	r.POST("/telecom/v1/exotel/health", nummasking.ExotelHealthCheck)
	r.POST("/telecom/v1/kaleyra/callback", nummasking.KaleyraWebhook)
	r.POST("/telecom/v1/kaleyra/health", nummasking.KaleyraHealthCheck)
	r.POST("/telecom/v1/airtel/nummasking/callback",
		middlewares.CheckIPWhitelist(airtelWhitelistedIps),
		nummasking.AirtelCallbackHandler)
	r.POST("/telecom/v1/fonada/nummasking/callback",
		middlewares.CheckIPWhitelist(fonadaWhitelistedIps),
		nummasking.FonadaCallbackHandler)
	r.POST("/telecom/v1/plivo/nummasking/callback",
		middlewares.CheckIPWhitelist(plivoWhitelistedIps),
		nummasking.PlivoNumberMaskingCallback)
	r.POST("/telecom/v1/exotel/dualleg/callback", dualleg.ExotelDualLegCallback)
	r.GET("/telecom/v1/solutions_infini/callback/:connection_id", dualleg.SolutionsInfiniCallback)
	r.POST("/telecom/v1/plivo/dualleg/callback",
		middlewares.CheckIPWhitelist(plivoWhitelistedIps),
		dualleg.PlivoDualLegCallback)
	r.GET("/telecom/v1/kaleyra/ivr/callback", middlewares.CheckIPWhitelist(config.GetStringSlice(ctx, "telecom_service.kaleyra.whitelisted_ips")), ivr.KaleyraIvrCallback)
	r.POST("/telecom/v1/exotel/ivr/callback", middlewares.CheckIPWhitelist(config.GetStringSlice(ctx, "telecom_service.exotel.whitelisted_ips")), ivr.ExotelIvrCallback)
	r.GET("/telecom/v1/recording", recording.FetchCallRecording)
	r.POST("/telecom/v1/plivo/ivr/callback", middlewares.CheckIPWhitelist(plivoWhitelistedIps), ivr.PlivoIvrCallback)

	// one support media redirect url
	r.GET("/one-support/media", onesupport.MediaRedirect)

	// wallet external routes
	r.POST("/payments/wallet/pinelabs/kyc/notify",
		middlewares.CheckIPWhitelist(config.GetStringSlice(ctx, "wallet_service.pineperks.callback.whitelisted_ips")),
		walletExternal.PinelabsKycNotifyCallback,
	)

	// whatsapp service webhooks
	whatsappWhitelist := config.GetStringSlice(ctx, "whatsapp_service.gupshup_ips")
	whatsappEventRegistryTopic := config.GetString(ctx, "whatsapp_service.inbound_messages_received_topic")
	whatsappHMACValidationKey := config.GetString(ctx, "whatsapp_service.gupshup_hmac_private_key")
	whatsappHMACValidationHeader := config.GetString(ctx, "whatsapp_service.gupshup_hmac_header_name")
	whatsappGr := r.Group("/whatsapp/webhook",
		middlewares.CheckIPWhitelist(whatsappWhitelist),
		middlewares.POSTRequestHMACValidation(whatsappHMACValidationKey, whatsappHMACValidationHeader, "whatsappService"),
	)
	whatsappGr.POST("/reply", whatsappservice.InboundMessageHandler(whatsappEventRegistryTopic))
	whatsappDLRStatusEventRegistryTopic := config.GetString(ctx, "whatsapp_service.dlr_status_topic")
	whatsappGr.POST("/deliverystatus", whatsappservice.DLRStatusCallback(whatsappDLRStatusEventRegistryTopic))
	// karix
	karixWhitelistedIPs := config.GetStringSlice(ctx, "whatsapp_service.karix.whitelisted_ips")
	karixUsername := config.GetString(ctx, "whatsapp_service.karix.username")
	karixPassword := config.GetString(ctx, "whatsapp_service.karix.password")
	karixGroup := r.Group("/whatsapp/webhook/karix",
		middlewares.CheckIPWhitelist(karixWhitelistedIPs),
		middlewares.BasicAuthMiddleware(karixUsername, karixPassword),
	)
	karixGroup.POST("/inbound_message", whatsappservice.KarixInboundMessageHandler)
	karixGroup.POST("/delivery_status", whatsappservice.KarixDeliveryStatusHandler)
	karixGroup.POST("/template_updates", whatsappservice.KarixTemplateUpdateHandler)

	// meta
	r.GET("/whatsapp/webhook/meta", whatsappservice.MetaWebhookVerificationHandler)
	r.POST("/whatsapp/webhook/meta", whatsappservice.MetaWebhookMessageHandler)

	// kobotoolbox webhook
	kobotoolboxBasicAuthUsername := config.GetString(ctx, "carbon_tech.kobotoolbox.auth_username")
	kobotoolboxBasicAuthPassword := config.GetString(ctx, "carbon_tech.kobotoolbox.auth_password")
	kobotoolboxAuthHeader := config.GetString(ctx, "carbon_tech.kobotoolbox.auth_header")
	kobotoolboxAuthHeaderValue := config.GetString(ctx, "carbon_tech.kobotoolbox.auth_header_value")
	r.POST("/carbontech/webhook/kobotoolbox",
		middlewares.PostRequestHeaderValidation(kobotoolboxAuthHeader, kobotoolboxAuthHeaderValue),
		gin.BasicAuth(gin.Accounts{
			kobotoolboxBasicAuthUsername: kobotoolboxBasicAuthPassword,
		}),
		carbontech.KobotoolboxWebhookHandler)

	// M2P
	r.GET("/m2p-wallet-transaction/sms/send",
		middlewares.CheckIPWhitelist(config.GetStringSlice(ctx, "m2p_wallet.m2p_whitelisted_ips")),
		gin.BasicAuth(gin.Accounts{
			config.GetString(ctx, "m2p_wallet.m2p_credentials.username"): config.GetString(ctx, "m2p_wallet.m2p_credentials.password"),
		}),
		walletExternal.M2PSmsHandler,
	)

	// Eatfit service webhooks
	eatfitWhitelistedIps := config.GetStringSlice(ctx, "eatfit.inventory_sync.ips")
	eatfitEventRegistryTopic := config.GetString(ctx, "legends_service.inventory_sync_messages_topic")
	eatfitHMACValidationKey := config.GetString(ctx, "eatfit.inventory_sync.hmac_private_key")
	eatfitHMACValidationHeader := config.GetString(ctx, "eatfit.inventory_sync.hmac_header_name")
	r.POST("/legends/inventory_sync/webhook",
		middlewares.CheckIPWhitelist(eatfitWhitelistedIps),
		middlewares.POSTRequestHMACValidation(eatfitHMACValidationKey, eatfitHMACValidationHeader, "eatfit"),
		legendsservice.InboundMessageHandler(eatfitEventRegistryTopic),
	)

	//Search External Hooks
	recommendedResultsWhitelistedIps := config.GetStringSlice(ctx, "search_service.get_recommended_results.ips")
	isRecommendedResultsEnabled := config.GetBool(ctx, "search_service.get_recommended_results.enabled")
	r.POST("/search/v1/get_recommended_results", middlewares.KillswitchMiddleware(ctx, !isRecommendedResultsEnabled), auth.IsOppoBackend, middlewares.CheckIPWhitelist(recommendedResultsWhitelistedIps), searchExternal.GetRecommendedResults)
}

func setupLogisticsRouter(r *gin.RouterGroup) {
	r.POST("/hook/northpass", logswebhook.NorthpassWebhookHandler)
}

func setupEditionCardWebsiteRoutes(ctx context.Context, r *gin.RouterGroup) {
	r = r.Group("/", middlewares.AllowOrigins(
		[]string{config.GetString(ctx, "listen.edition_domain")},
	))

	r.POST("/qde/submit", edition.SubmitQDEDataForEditionWebHandler)
	r.OPTIONS("/qde/submit")
}

func setupWebRoutes(r *gin.RouterGroup) {
	// user notification_preferences APIs (web)
	r.OPTIONS("/user/notification_preferences")
	r.GET("/user/notification_preferences", notificationpreferences.FetchNotificationPreferencesHandler)
	r.POST("/user/notification_preferences", notificationpreferences.UpdateNotificationPreferencesHandler)

	//user info update APIs (web)
	r.OPTIONS("/user/info")
	r.POST("/user/info", auth.IsUserLoggedIn, user.SaveProfileHandler)

	//Social sharing (web)
	r.OPTIONS("/social/sharing/generate-link")
	r.POST("/social/sharing/generate-link", sharing.GenerateSharingLinkHandler)

	// merchant web update email APIs
	r.OPTIONS("/merchant/auth/verification/init")
	r.POST("/merchant/auth/verification/init", auth.IsUserLoggedIn, userprofile.InitiateAuthVerificationHandler)

	r.OPTIONS("/merchant/auth/verification/verify")
	r.POST("/merchant/auth/verification/verify", auth.IsUserLoggedIn, userprofile.VerifyAuthenticationHandler)

	r.OPTIONS("/merchant/email/update/init")
	r.POST("/merchant/email/update/init", auth.IsUserLoggedIn, userprofile.InitiateEmailChangeHandler)

	r.OPTIONS("/merchant/email/update/verify")
	r.POST("/merchant/email/update/verify", auth.IsUserLoggedIn, userprofile.UpdateEmailHandler)

	r.OPTIONS("/merchant/mobile/update/init")
	r.POST("/merchant/mobile/update/init", auth.IsUserLoggedIn, userprofile.InitiateMobileChangeHandler)

	r.OPTIONS("/merchant/mobile/update/verify")
	r.POST("/merchant/mobile/update/verify", auth.IsUserLoggedIn, userprofile.UpdateMobileHandler)

	r.OPTIONS("/merchant/push/token")
	r.POST("/merchant/push/token", push.AddUserDeviceHandler)

	r.OPTIONS("/merchant/logout/all/init")
	r.POST("/merchant/logout/all/init", auth.IsUserLoggedIn, auth.InitiateLogoutFromAllDevicesHandler)

	r.OPTIONS("/merchant/logout/all/verify")
	r.POST("/merchant/logout/all/verify", auth.IsUserLoggedIn, auth.VerifyLogoutFromAllDevicesHandler)

}

func setupPaymentsRoutes(r *gin.RouterGroup) {
	r.OPTIONS("/*all")

	r.GET("/get_transaction_information", paymentsDashboard.GetTransactionInformation)
	r.POST("/get_transactions", paymentsDashboard.GetTransactions)
	r.POST("/refund_transaction", paymentsDashboard.RefundTransaction)
}

func setupInternalCFSRoutes(r *gin.RouterGroup) {
	r.POST("/internal/cfs/corporate/cart/business_detail/get_bottom_sheet", corporateFunds.DistrictMiddleware, corporateFunds.GetBusinessOrderDetailBottomSheetFormDataHandler)
	r.POST("/internal/cfs/corporate/cart/business_details/bottom_sheet/autosuggest", corporateFunds.DistrictMiddleware, corporateFunds.AutoSuggestBusinessOrderBottomSheetHandler)
	r.POST("/internal/cfs/corporate/cart/business_details/bottom_sheet/confirm", corporateFunds.DistrictMiddleware, corporateFunds.ConfirmBusinessOrderBottomSheetHandler)
	r.POST("/internal/cfs/corporate/cart/business_details/bottom_sheet/dismiss", corporateFunds.DistrictMiddleware, corporateFunds.DismissBusinessOrderBottomSheetHandler)
	r.POST("/internal/cfs/corporate/meal_plan/bottom_sheet", corporateFunds.DistrictMiddleware, corporateFunds.GetMealPlanBottomSheetHandler)
	r.POST("/internal/cfs/corporate/cart/business_details/bottom_sheet/upload", corporateFunds.DistrictMiddleware, corporateFunds.ImageUploadBusinessOrderBottomSheetHandler)
}

func setupFinTechRoutes(r *gin.RouterGroup) {

	r = r.Group("/", payments.SetAppTheme(), payments.SetUserSpecificFlags(), payments.PopulateCountryIdInContext())

	r.POST("/corporate/get_landing_page", auth.IsUserLoggedIn, corporateFunds.GetLandingPageHandler)
	r.POST("/corporate/meal_plans/opt_in", auth.IsUserLoggedIn, corporateFunds.OptInForEnterpriseMealsHandler)
	r.POST("/corporate/meal_plans/exit_company", auth.IsUserLoggedIn, corporateFunds.ExitCompanyForEnterpriseMealsHandler)
	r.POST("/corporate/get_verification_page", auth.IsUserLoggedIn, corporateFunds.GetEmailVerificationPageHandler)
	r.POST("/corporate/verify_email", auth.IsUserLoggedIn, corporateFunds.EmailVerificationHandler)
	r.POST("/corporate/resend_otp", auth.IsUserLoggedIn, corporateFunds.EmailVerificationResendHandler)
	r.POST("/corporate/verify_otp", auth.IsUserLoggedIn, corporateFunds.OTPVerificationHandler)
	r.POST("/corporate/business_profile_faqs", auth.IsUserLoggedIn, corporateFunds.BusinessProfileFAQsHandler)
	r.POST("/corporate/meal_plans/faq", auth.IsUserLoggedIn, corporateFunds.CorporateMealsFAQsHandler)
	r.POST("/corporate/reimbursements/get_page", auth.IsUserLoggedIn, corporateFunds.GetReimbursementsPageHandler)
	r.POST("/corporate/reimbursements/get_download_url", auth.IsUserLoggedIn, corporateFunds.GetReimbursementsReportURLHandler)
	r.POST("/corporate/meal_plans/dashboard", auth.IsUserLoggedIn, corporateFunds.MainDashboardScreenHandler)
	r.POST("/corporate/meal_plan/dashboard/update", auth.IsUserLoggedIn, corporateFunds.GetDashboardUpdateHandler)
	r.GET("/corporate/meal_plans/transactions", auth.IsUserLoggedIn, corporateFunds.MealPlansTransactionHandler)
	r.POST("/corporate/lead_generation/form", auth.IsUserLoggedIn, corporateFunds.LeadGenerationFormHandler)
	r.POST("/corporate/lead_generation/submit", auth.IsUserLoggedIn, corporateFunds.LeadGenerationFormSubmitHandlerForApp)
	r.POST("/corporate/cart/business_detail/get_bottom_sheet", auth.IsUserLoggedIn, corporateFunds.GetBusinessOrderDetailBottomSheetFormDataHandler)
	r.POST("/corporate/meal_plan/dashboard/bottom_sheet/confirm", auth.IsUserLoggedIn, corporateFunds.ConfirmBusinessOrder)
	r.POST("/corporate/meal_plan/dashboard/bottom_sheet", auth.IsUserLoggedIn, corporateFunds.GetBudgetRequestBottomSheetHandler)
	r.POST("/corporate/cart/business_details/bottom_sheet/autosuggest", auth.IsUserLoggedIn, corporateFunds.AutoSuggestBusinessOrderBottomSheetHandler)
	r.POST("/corporate/cart/business_details/bottom_sheet/confirm", auth.IsUserLoggedIn, corporateFunds.ConfirmBusinessOrderBottomSheetHandler)
	r.POST("/corporate/cart/business_details/bottom_sheet/dismiss", auth.IsUserLoggedIn, corporateFunds.DismissBusinessOrderBottomSheetHandler)
	r.POST("/corporate/business-email/bottom_sheet", auth.IsUserLoggedIn, corporateFunds.GetBusinessEmailSheetHandler)
	r.POST("/corporate/business-email/validate_email_eligibility", auth.IsUserLoggedIn, corporateFunds.ValidateBusinessEmailEligibilityHandler)
	r.POST("/corporate/meal_plan/bottom_sheet", auth.IsUserLoggedIn, corporateFunds.GetMealPlanBottomSheetHandler)
	r.POST("/corporate/cart/business_details/bottom_sheet/upload", auth.IsUserLoggedIn, corporateFunds.ImageUploadBusinessOrderBottomSheetHandler)
	r.POST("/corporate/phone/eligibility", auth.IsUserLoggedIn, corporateFunds.ValidatePhoneEligibilityHandler)
	r.POST("/corporate/otp/bottom_sheet", auth.IsUserLoggedIn, corporateFunds.PhoneOTPVerificationHandler)
	r.POST("/corporate/otp/success", auth.IsUserLoggedIn, corporateFunds.ValidatePhoneSuccessHandler)
	r.POST("/corporate/otp/verify", auth.IsUserLoggedIn, corporateFunds.ValidatePhoneOTPHandler)
	r.POST("/corporate/otp/resend", auth.IsUserLoggedIn, corporateFunds.ResendPhoneOTPHandler)
	r.POST("/corporate/bottom_sheet/result", auth.IsUserLoggedIn, corporateFunds.BottomSheetResultsPageHandler)

	// gift card
	r.POST("/gift-cards/claim", auth.IsUserLoggedIn, giftCardsClaim.ClaimHandler)
	r.POST("/gift-cards/faq", auth.IsUserLoggedIn, giftCardsFAQ.FaqPageHandler)
	r.POST("/gift-cards/landing-page", auth.IsUserLoggedIn, giftCardsLanding.GetBalanceLandingPageHandler)
	r.POST("/gift-cards/transaction-details", auth.IsUserLoggedIn, giftCardsConsumer.GetTransactionDetailsHandler)
	r.POST("/gift-cards/order/landing-page", auth.IsUserLoggedIn, giftCardsLanding.GetPurchaseGiftCardLandingPageHandler)
	r.POST("/gift-cards/claim-page", auth.IsUserLoggedIn, giftCardsLanding.ClaimPageHandler)
	r.POST("/gift-cards/order/customization-page", auth.IsUserLoggedIn, giftCardsConsumer.GetCustomizationPageHandler)
	r.POST("/gift-cards/order/video-page", auth.IsUserLoggedIn, giftCardsConsumer.GetVideoSuggestionsHandler)
	r.POST("/gift-cards/order/cart", auth.IsUserLoggedIn, giftCardsConsumer.GetCartHandler)
	r.POST("/gift-cards/order/details", auth.IsUserLoggedIn, giftCardsOrder.GetOrderDetailsHandler)
	r.POST("/gift-cards/order/place", auth.IsUserLoggedIn, giftCardsConsumer.PlaceOrderHandler)
	r.POST("/gift-cards/order/payment-status", auth.IsUserLoggedIn, giftCardsConsumer.PlaceOrderStatusHandler)
	r.POST("/gift-cards/order/history", auth.IsUserLoggedIn, giftCardsOrder.GetOrderHistoryHandler)
	r.POST("/gift-cards/video/celeb/template/validate", auth.IsUserLoggedIn, video.CelebVideoTemplateValidationHandler)

	// zomato money tab / page
	r.POST("/payments/zomato_money_tab", auth.IsUserLoggedIn, moneyTab.MoneyPageHandler)
	r.POST("/payments/in/zomato_money/landing", auth.IsUserLoggedIn, moneyTab.MoneyPageHandlerV2)
	r.GET("/payments/in/wallet/transactions", auth.IsUserLoggedIn, moneyTab.GetWalletTransactions)
	r.POST("/payments/in/wallet_sdk/configure", auth.IsUserLoggedIn, zomato_money_v2.SDKConfigureHandler)

	// zomato money linking
	r.POST("/payments/in/zomato_money/settings", auth.IsUserLoggedIn, moneyTab.MoneySettingsHandler)
	r.POST("/payments/in/zomato_money/refresh_bottom_sheet", auth.IsUserLoggedIn, moneyTab.BottomSheetRefreshHandler)
	r.POST("/payments/in/zomato_money/faq", auth.IsUserLoggedIn, moneyTab.MoneyFaqHandler)

	r.POST("/payments/in/zomato_money/link", auth.IsUserLoggedIn, moneyTab.ZomatoMoneyLinkHandler)
	r.POST("/payments/in/zomato_money/verify_otp", auth.IsUserLoggedIn, moneyTab.ZomatoMoneyVerifyOtpHandler)

	// zomato money selection bottomsheet
	r.POST("/payments/in/zomato_money_v2/bottom_sheet", auth.IsUserLoggedIn, zomato_money_v2.ZomatoMoneyBottomsheetHandler)
	r.POST("/payments/in/zomato_money_v2/bottom_sheet/validate", auth.IsUserLoggedIn, zomato_money_v2.ZomatoMoneyBottomsheetValidationHandler)
	r.POST("/payments/in/zomato_money_v2/intro", auth.IsUserLoggedIn, zomato_money_v2.ZomatoMoneyIntroHandler)

	r.POST("/payments/zomato_money/home", auth.IsUserLoggedIn, zomatoMoney.GetHomePageHandler)
	r.POST("/payments/zomato_money/transaction", zomatoMoney.OpenTransactionHandler)
	r.GET("/payments/zomato_money/prepaid_wallet/signup_form", zomatoMoney.GetWalletActivationPageHandler)
	r.POST("/payments/zomato_money/prepaid_wallet/add", zomatoMoney.ActivatePrepaidWalletHandler)
	r.POST("/payments/zomato_money/prepaid_wallet/link", zomatoMoney.VerifyPrepaidWalletOtpHandler)
	r.POST("/payments/zomato_money/prepaid_wallet/recharge", zomatoMoney.GetAddMoneyPageHandler)
	r.GET("/payments/zomato_money/paylater_wallet/landing_page", zomatoMoney.GetBNPLLandingPageHandler)
	r.GET("/payments/zomato_money/paylater_wallet/signup_form", zomatoMoney.GetPayLaterActivationPageHandler)
	r.POST("/payments/zomato_money/order", zomatoMoney.MakeOrderHandler)
	r.GET("/payments/zomato_money/order/status", zomatoMoney.GetPaymentStatusForOrderHandler)
	r.POST("/payments/zomato_money/prepaid_wallet/resend_otp", zomatoMoney.PrepaidWalletResendOtpHandler)
	r.POST("/payments/zomato_money/balance_tag", zomatoMoney.GetBalanceTag)
	r.POST("/payments/zomato_money/prepaid_wallet/polling", zomatoMoney.ProcessStepHandler)
	r.POST("/payments/zomato_money/prepaid_wallet/autoadd/mandate/cancel", zomatoMoney.CancelAutoAddMandateHandler)
	r.POST("/payments/zomato_money/prepaid_wallet/autoadd/predebit/cancel", zomatoMoney.CancelAutoAddTxnHandler)
	r.POST("/payments/zomato_money/prepaid_wallet/autoadd/details", zomatoMoney.AutoAddDetailsHandler)
	r.POST("/payments/zomato_money/prepaid_wallet/autoadd/remove_banner", zomatoMoney.CancelAutoAddBannerHandler)
	r.GET("/payments/in/get-sdk-token", paymentsAuth.GetPaymentSDKToken)

	r.GET("/payments/wallet/kyc_form_page", auth.IsUserLoggedIn, walletKyc.GetKycFormHandler)
	r.POST("/payments/wallet/submit_kyc", auth.IsUserLoggedIn, walletKyc.SubmitKycFormHandler)
	r.POST("/payments/wallet/update/kyc_details", auth.IsUserLoggedIn, walletKyc.GetUpdateKycDetailsHandler)
	r.POST("/payments/wallet/verify_kyc_otp", auth.IsUserLoggedIn, walletKyc.VerifyKycOtpHandler)
	r.POST("/payments/wallet/resend_kyc_otp", auth.IsUserLoggedIn, walletKyc.ResendKycOtpHandler)
	r.POST("/payments/wallet/kyc/result", auth.IsUserLoggedIn, walletKyc.GetKycResultPageHandler)

	r.POST("/payments/zomato_wallet/dashboard", auth.IsUserLoggedIn, zomatoMoney.GetWalletDashboard)
	r.POST("/payments/zomato_wallet/add_money", auth.IsUserLoggedIn, walletAddMoney.GetAddMoneyPageHandler)
	r.POST("/payments/zomato_wallet/auto_add_mandate", auth.IsUserLoggedIn, walletAddMoney.GetAutoMandatePageHandler)
	r.POST("/payments/zomato_wallet/auto_add_mandate/cancel", auth.IsUserLoggedIn, walletAddMoney.CancelAutoAddMandateHandler)
	r.POST("/payments/zomato_wallet/order/make", auth.IsUserLoggedIn, walletAddMoney.MakeOrderHandler)
	r.GET("/payments/zomato_wallet/transactions", auth.IsUserLoggedIn, zwallet.GetWalletTransactionsHandler)
	r.GET("/payments/zomato_wallet/get_payment_status", auth.IsUserLoggedIn, walletAddMoney.GetPaymentStatusHandler)

	// paylater apis
	r.POST("/payments/paylater/home", auth.IsUserLoggedIn, zmpl.GetActivationPageHandler)
	r.GET("/payments/paylater/form", auth.IsUserLoggedIn, zmpl.GetFormPageHandler)
	r.POST("/payments/paylater/verify-ovd", auth.IsUserLoggedIn, zmpl.VerifyOVDPageHandler)
	r.POST("/payments/paylater/polling", auth.IsUserLoggedIn, zmpl.PollingPageHandler)
	r.POST("/payments/paylater/tnc", auth.IsUserLoggedIn, zmpl.SuccessPageHandler)
	r.POST("/payments/paylater/activate", auth.IsUserLoggedIn, zmpl.ActivateCLHandler)
	r.POST("/payments/paylater/pay-bill", auth.IsUserLoggedIn, zmpl.PayBillHandler)
	r.POST("/payments/paylater/order", auth.IsUserLoggedIn, zmpl.CreateOrderHandler)
	r.GET("/payments/paylater/order/status", auth.IsUserLoggedIn, zmpl.GetBillPaymentStatusHandler)

	// paylater admin apis
	r.POST("/payments/paylater/admin/create-process", auth.IsUserLoggedIn, adminHandler.CreateProcessHandler)
	r.POST("/payments/paylater/admin/process-activation", auth.IsUserLoggedIn, adminHandler.ProcessActivationHandler)

	// payments partners lead gen
	r.POST("/payments/partner/lead_generation/bottomsheet", auth.IsUserLoggedIn, paymentPartner.LeadGenerationBottomSheetHandler)
	r.POST("/payments/partner/lead_generation/submit_lead", auth.IsUserLoggedIn, paymentPartner.SubmitLeadHandler)

	// gift offer apis
	r.POST("/gift_offer/claim", auth.IsUserLoggedIn, giftOffer.GiftOfferClaimHandler)
}

func setupEditionInternalRoutes(ctx context.Context, r *gin.RouterGroup, corsMiddleware gin.HandlerFunc) {
	r = r.Group("/", edition.ServcieEnabled())

	setupEditionCardRoutes(r)
	setupEditionCardWebsiteRoutes(ctx, r.Group("/web", corsMiddleware))
	setupEditionDashboardRoutes(r.Group("/dashboard", corsMiddleware))
	setupEditionTSPRoutes(r.Group("/tsp"))
}

func setupEditionCardRoutes(r *gin.RouterGroup) {
	r.POST("/v1/invite/page", edition.InvitePageHandler)
	r.POST("/v1/invite/faq", edition.InviteFAQPageHandler)
	r.POST("/v1/invite/accept", edition.AcceptInviteHandler)
	r.POST("/v1/cvp/dismiss/banner", edition.SaveCvpBannerView)
	r.POST("/v1/address/page", edition.GetAddressPageHandler)
	r.POST("/v1/address/submit", edition.SubmitAddressHandler)
	r.POST("/v1/address/pincode", auth.IsUserLoggedIn, edition.GetPincodeSheetHandler)
	r.GET("/v1/qde/page", edition.QDEPageHandler)
	r.POST("/v1/qde/submit", edition.SubmitQDEDataHandler)
	r.POST("/v1/qde/otp/resend", edition.QDEResendOTPHandler)
	r.POST("/v1/qde/otp/submit", edition.QDESubmitOTPHandler)
	r.POST("/v1/qde/check", edition.CheckCreditLimitStatus)
	r.POST("/v1/qde/pan/fetchdob", auth.IsUserLoggedIn, edition.GetDOBFromPAN)
	r.GET("/v1/ckyc/page", edition.CKYCPageHandler)
	r.POST("/v1/ckyc/submit", edition.SubmitCKYCDataHandler)
	r.POST("/v1/ckyc/otp/resend", edition.CKYCResendOTPHandler)
	r.POST("/v1/ckyc/otp/submit", edition.CKYCSubmitOTPHandler)
	r.POST("/v1/appointment/page", edition.AppointmentPageHandler)
	r.POST("/v1/appointment/submit", edition.SubmitAppointmentDataHandler)
	r.POST("/v1/appointment/calendar/save", edition.SaveCalendarEvent)
	r.POST("/v1/kyc/page", edition.GetKYCOptionsPageHandler)
	r.POST("/v1/kyc/instant", edition.GetInstantKYCPageHandler)
	r.POST("/v1/kyc/submit", edition.SubmitKYCOptionHandler)
	r.POST("/v1/kyc/result", edition.KYCResultHandler)
	r.POST("/v1/validate/dob/mismatch", auth.IsUserLoggedIn, edition.ValidateDobMismatchHandler)
	r.POST("/v1/accept/revise/credit", auth.IsUserLoggedIn, edition.AcceptRevisedCreditLimitHandler)
	r.POST("/v1/kyc/cardissued", edition.CardIssuedPageHandler)
	r.GET("/v1/kyc/skip", auth.IsUserLoggedIn, edition.SkipKycPageHandler)
	r.POST("/v1/okyc/update", auth.IsUserLoggedIn, edition.UpdateOKYCStatusHandler)
	r.POST("/v1/okyc/address/switch", auth.IsUserLoggedIn, edition.SwitchAddressSheetHandler)
	r.POST("/v1/okyc/address/confirm", auth.IsUserLoggedIn, edition.GetAddressConfirmationPageHandler)
	r.POST("/v1/kyc/switch/biometric", auth.IsUserLoggedIn, edition.SwitchVideoToBiometric)
	r.GET("/v1/reminder/page", auth.IsUserLoggedIn, edition.GetReminderPageHandler)
	r.POST("/v1/reminder/submit", auth.IsUserLoggedIn, edition.SubmitReminderHandler)

	r.POST("/v1/lifecycle/page", edition.DashboardPageHandler)
	r.POST("/v1/lifecycle/shimmer", edition.DashboardShimmerHandler)
	r.POST("/v1/lifecycle/transaction", auth.IsUserLoggedIn, edition.TransactionDetailsHandler)
	r.POST("/v1/transaction/page", auth.IsUserLoggedIn, edition.TransactionFullPageHandler)
	r.POST("/v1/rewards/page", auth.IsUserLoggedIn, edition.RewardsPageHandler)
	r.POST("/v1/rewards/offer", auth.IsUserLoggedIn, edition.RewardsOfferSheetHandler)
	r.POST("/v1/rewards/history", auth.IsUserLoggedIn, edition.CashbackHistoryHandler)
	r.POST("/v1/rewards/redeem/page", auth.IsUserLoggedIn, edition.RedeemSheetHandler)
	r.POST("/v1/redeem/page", auth.IsUserLoggedIn, edition.RedeemFullPageHandler)
	r.POST("/v1/rewards/redeem/submit", auth.IsUserLoggedIn, edition.RedeemEditionCashHandler)
	r.POST("/v1/waitlist/page", auth.IsUserLoggedIn, edition.WaitlistStatusPageHandler)
	r.POST("/v1/cardtracking/page", auth.IsUserLoggedIn, edition.GetEditionCardTracking)
	r.POST("/v1/card/delivered", auth.IsUserLoggedIn, edition.MarkCardDelivered)
	r.POST("/v1/dismiss/banner", auth.IsUserLoggedIn, edition.SaveDashboardBannerView)
	r.POST("/v1/card/upgrade/page", auth.IsUserLoggedIn, edition.UpgradeJourneyPageHandler)
	r.POST("/v1/card/upgrade/otp/generate", auth.IsUserLoggedIn, edition.GetUpgradeOTPPageHandler)
	r.POST("/v1/card/upgrade/otp/validate", auth.IsUserLoggedIn, edition.ValidteUpgradeOTPHandler)
	r.POST("/v1/failure/page", auth.IsUserLoggedIn, edition.CardBlockFailurePageHandler)

	r.POST("/v1/carddetails/triggerpage", auth.IsUserLoggedIn, edition.GetCardDetailsTriggerPageHandler)
	r.POST("/v1/carddetails/otp/generate", auth.IsUserLoggedIn, edition.GetOTPToShowCardDetailsHandler)
	r.POST("/v1/carddetails/otp/validate", auth.IsUserLoggedIn, edition.ValidateOTPToShowCardDetailsHandler)
	r.POST("/v1/cardcontrols/otp/generate", auth.IsUserLoggedIn, edition.GetOTPToUpdateCardControlsHandler)
	r.POST("/v1/cardcontrols/otp/validate", auth.IsUserLoggedIn, edition.ValidateAndSaveCardControls)
	r.POST("/v1/notification/preference/save", auth.IsUserLoggedIn, edition.SaveNotificationPreference)
	r.POST("/v1/faq/page", paymentsEdition.GetFaq)

	// "/sdk" routes for all the API calls from client (App) to edition
	r.POST("/v1/sdk/init", auth.IsUserLoggedIn, editionTSP.GenerateTSPAccessToken)

	r.POST("/admin/file/tranasctions", auth.IsUserLoggedIn, edition.ProcessRBLCardTransactionsFile)
	r.POST("/admin/tranasctions/update", auth.IsUserLoggedIn, edition.CorrectTransactionData)
	r.POST("/admin/user/uuid/update", auth.IsUserLoggedIn, edition.UpdateUserUUidHandler)
	r.POST("/admin/user/application/whitelist", auth.IsUserLoggedIn, edition.WhitelistUserForCard)
	r.POST("/admin/user/blacklist/clear", auth.IsUserLoggedIn, edition.ClearBlacklist)
	r.POST("/admin/user/zaccount/transfer", auth.IsUserLoggedIn, edition.TransferZomatoAccount)
	r.POST("/admin/merchantdata/update", auth.IsUserLoggedIn, edition.UpdateTxnMerchantData)
	r.POST("/admin/offer/create", auth.IsUserLoggedIn, edition.CreateNewOffer)
	r.POST("/admin/offer/update/eligibility", auth.IsUserLoggedIn, edition.UpdateOfferEligibility)
	r.POST("/admin/user_offer/create", auth.IsUserLoggedIn, edition.CreateNewUserOffer)
	r.POST("/admin/user_offer/update/eligibility", auth.IsUserLoggedIn, edition.UpdateUserOfferEligibility)
	r.POST("/admin/service_request/update", auth.IsUserLoggedIn, edition.UpdateServiceRequest)
	r.POST("/admin/comm/create", auth.IsUserLoggedIn, edition.CreateNewCommunication)
	r.POST("/admin/comm/update/eligibility", auth.IsUserLoggedIn, edition.UpdateCommunicationEligibility)
	r.POST("/admin/comm/update/config", auth.IsUserLoggedIn, edition.UpdateCommunicationConfig)
	r.POST("/admin/jobs/trigger", auth.IsUserLoggedIn, edition.TriggerJob)
	r.POST("/admin/rbl_serviceability/update", auth.IsUserLoggedIn, edition.UpdateRBLServiceability)
	r.POST("/admin/kyc_option/update", auth.IsUserLoggedIn, edition.UpdateKycOptionData)
	r.POST("/admin/cache/clear", auth.IsUserLoggedIn, edition.ClearCache)
	r.POST("/admin/cashback_redemption/update/eligibility", auth.IsUserLoggedIn, edition.UpdateCashbackRedemptionEligibility)
	r.POST("/admin/edition_card/update", auth.IsUserLoggedIn, edition.UpdateEditionCardInfo)
	r.POST("/admin/kycdata/update", auth.IsUserLoggedIn, edition.UpdateKycInfo)
	r.POST("/admin/application/update", auth.IsUserLoggedIn, edition.UpdateCardApplication)
	r.POST("/admin/appointment/capacity", auth.IsUserLoggedIn, edition.ChangeAppointmentCapacity)
	r.POST("/admin/cardtracking/update", auth.IsUserLoggedIn, edition.UpdateCardTracking)
	r.POST("/admin/cardtracking/create", auth.IsUserLoggedIn, edition.CreateCardTracking)
	r.POST("/admin/onboard/non_journey/users", auth.IsUserLoggedIn, edition.OnboardNonJourneyUsers)

	r.POST("/v1/billpay/pending/payment", auth.IsUserLoggedIn, edition.GetPendingBillPaymentHandler)
	r.POST("/v1/billpay/order", auth.IsUserLoggedIn, edition.MakeOrderHandler)
	r.POST("/v1/billpay/status", auth.IsUserLoggedIn, edition.GetBillPaymentStatusHandler)
	r.POST("/v1/billpay/poll/status", auth.IsUserLoggedIn, edition.BillPayPollingPageHandler)

	r.POST("/v1/rbl/sdk/card/activate", auth.IsUserLoggedIn, edition.SDKCardActivationHandler)
	r.POST("/v1/rbl/sdk/poll/bind_device", auth.IsUserLoggedIn, edition.GetSDKDeviceBindingPoller)
	r.POST("/v1/rbl/sdk/biometric/page", auth.IsUserLoggedIn, edition.GetSDKBiometricSelectionPage)
	r.POST("/v1/rbl/sdk/pin/page", auth.IsUserLoggedIn, edition.GetSDKPinPage)
	r.POST("/v1/rbl/sdk/cardcontrols", auth.IsUserLoggedIn, edition.GetSDKCardControls)
	r.POST("/v1/rbl/sdk/device_change/page", auth.IsUserLoggedIn, edition.GetSDKDeviceChangePage)
}

func setupEditionDashboardRoutes(r *gin.RouterGroup) {
	r.OPTIONS("/user/details")
	r.POST("/user/details", editionDashboard.GetUserDetailsAndStatus)

	r.OPTIONS("/name/search")
	r.POST("/name/search", editionDashboard.GetUsersByNameSearch)

	r.OPTIONS("/transactions")
	r.POST("/transactions", editionDashboard.GetTransactions)

	r.OPTIONS("/correct/cashback/sheet")
	r.POST("/correct/cashback/sheet", editionDashboard.GetCashbackCorrectionSheet)

	r.OPTIONS("/correct/cashback/submit")
	r.POST("/correct/cashback/submit", editionDashboard.SubmitCashbackCorrection)

	r.OPTIONS("/reset/sheet")
	r.POST("/reset/sheet", editionDashboard.GetResetSheet)

	r.OPTIONS("/reset/submit")
	r.POST("/reset/submit", editionDashboard.SubmitReset)

	r.OPTIONS("/appointment/sheet")
	r.POST("/appointment/sheet", editionDashboard.GetAppointmentSheet)

	r.OPTIONS("/appointment/submit")
	r.POST("/appointment/submit", editionDashboard.SubmitAppointment)
}

func setupEditionTSPRoutes(r *gin.RouterGroup) {
	// "/rbl/internal" routes for all the Edition calls from Edition TSP to edition service
	rblInternal := r.Group("/rbl/internal")
	{
		rblInternal.POST("/user_eligibility/clear", editionTSP.ClearUserEligibilityHandler)
		rblInternal.POST("/card_application/fetch", editionTSP.GetCardApplication)
		rblInternal.POST("/card_application/update", editionTSP.UpdateCardApplication)
		rblInternal.POST("/edition_user/fetch", editionTSP.GetEditionUser)
		rblInternal.POST("/onboarding/complete", editionTSP.CompleteCustomerOnboarding)
		rblInternal.POST("/edition_card/update", editionTSP.UpdateEditionCard)
	}
}

func setupEditionExternalRoutes(ctx context.Context, r *gin.RouterGroup) {
	r = r.Group("/", edition.ServcieEnabled())

	r.POST("/edition/runner/kyc_order_status", editionWebhook.RunnrCallbackHandler)
	r.POST("/edition/skygge/kyc_update", editionWebhook.SkyggeCallbackHandler)
	r.POST("/edition/rbl/kyc/okyc", editionWebhook.RblOKycHandler)
	r.POST("/edition/rbl/kyc/khosla", gin.BasicAuth(gin.Accounts{
		config.GetString(ctx, "edition_service.khosla_username"): config.GetString(ctx, "edition_service.khosla_password"),
	}), editionWebhook.VkycKhoslaCallback)
	r.POST("/edition/rbl/kyc/khosla/webview", gin.BasicAuth(gin.Accounts{
		config.GetString(ctx, "edition_service.khosla_username"): config.GetString(ctx, "edition_service.khosla_password"),
	}), editionWebhook.VKYCClientCallback)
	r.GET("/edition/rbl/kyc/otp", editionWebhook.RblOtpKycHandler)
	r.POST("/edition/rbl/card/transactions", editionWebhook.RblTransactionsHandler)
	r.GET("/edition/v1/webview/open", edition.GetKYCWebviewHandler)
	r.POST("/edition/v1/billpay/pas/status", editionWebhook.TransactionCallbackHandler)
	r.POST("/edition/aftership/callback", editionWebhook.CardDeliveryStatusCallback)
	r.POST("/edition/rbl/billpay/transactiondata", edition.GetBillPayDataForRBL)

	// Redirection to listen url needs to support both GET and POST
	r.GET("/edition/v1/webview/listen", editionWebhook.WebviewListenerHandler)
	r.POST("/edition/v1/webview/listen", editionWebhook.WebviewListenerHandler)
}

func setupWalletServiceDashboardRoutes(r *gin.RouterGroup) {

	r.OPTIONS("/details")
	r.POST("/details", auth.IsZoman, zwallet.GetWalletDetails)

	r.OPTIONS("/transactions")
	r.POST("/transactions", auth.IsZoman, zwallet.GetTransactions)

	r.OPTIONS("/redact")
	r.POST("/redact", auth.IsZoman, zomatoMoney.CloseWalletHandler)
}

func setupAuthRoutes(r *gin.RouterGroup, corsMiddleware gin.HandlerFunc) {
	r.POST("/login/apple", auth.LoginWithAppleHandler)
	r.POST("/apple/disconnect", auth.DisconnectAppleHandler)
	r.OPTIONS("/validate", corsMiddleware)
	r.POST("/validate", corsMiddleware, middlewares.SetCSRFCookie(), auth.ValidateHandler)
	r.OPTIONS("/callback", corsMiddleware)
	r.POST("/callback", corsMiddleware, middlewares.CheckCSRF(), auth.CallbackHandler)
	r.OPTIONS("/logout", corsMiddleware)
	r.POST("/logout", corsMiddleware, auth.LogoutHandler)
	r.POST("/logout/all", auth.IsUserLoggedIn, auth.LogoutFromAllDevices)
	r.POST("/v2/logout/all/init", auth.IsUserLoggedIn, auth.InitiateLogoutFromAllDevicesHandler)
	r.POST("/v2/logout/all/verify", auth.IsUserLoggedIn, auth.VerifyLogoutFromAllDevicesHandler)
	r.POST("/user/delete/initiate", auth.IsUserLoggedIn, auth.InitiateDeleteUserProfileHandler)
	r.POST("/user/delete/verify", auth.IsUserLoggedIn, auth.VerifyDeleteUserProfileHandler)
	r.GET("/onetap/trusted-users", onetap.GetTrustedOnetapUsersHandler)
	r.POST("/onetap/remove-trusted-user", onetap.RemoveTrustedOnetapUserHandler)
}

func setupOrderRoutes(r *gin.RouterGroup) {
	r.GET("/rider_numbers", order.GetRiderNumbers)
	r.POST("/history/online_order", order.FetchOrderHistoryHandler)
	r.POST("/history/delete_order", order.DeleteOrderHandler)
	r.POST("/reorder/past_orders", order.FetchPastOrdersHandler)
	r.GET("/order_summary", order_summary.FetchOrderSummaryHandler)
	r.POST("/order-action", order_action.OrderActionHandler)
	r.POST("/update-contact", auth.IsUserLoggedIn, live_order.UpdateContactInfo)
	r.POST("/crystal-view", auth.IsUserLoggedIn, live_order.GetCrystalView)
	r.POST("/address/validate", auth.IsUserLoggedIn, live_order.ValidateAddressUpdate)
	r.POST("/address/update", auth.IsUserLoggedIn, live_order.UpdateOrderAddress)
	r.POST("/crystal-polling", auth.IsUserLoggedIn, live_order.GetCrystalPolling)
	r.POST("/tip/cart", auth.IsUserLoggedIn, live_order.GetTipCart)
	r.POST("/tip/make", auth.IsUserLoggedIn, live_order.MakeTipPayment)
	r.POST("/payment/status", auth.IsUserLoggedIn, live_order.GetPaymentStatus)
}

func setupSocialRoutes(r *gin.RouterGroup) {
	r.POST("/sharing/generate-link", sharing.GenerateSharingLinkHandler)
	r.POST("/sharing/dismiss-view", sharing.DismissSharedViewHandler)
	r.POST("/sharing/archive-view", sharing.ArchiveSharedViewHandler)
	r.POST("/sharing/resolve-link", sharing.ResolveLinkHandler)
	r.POST("/recommendation/create-reference", auth.IsUserLoggedIn, recommendation.CreateReferenceHandler)
}

func setupFeedbackRoutes(r *gin.RouterGroup) {
	r.POST("/add-feedback-page", auth.IsUserLoggedIn, feedback.PostOrderAddFeedbackPageHandler)
	r.POST("/save-rating", auth.IsUserLoggedIn, submit.SaveRatingHandler)
	r.POST("/update-review-metadata", auth.IsUserLoggedIn, reviews.UpdateReviewMetadata)
	r.POST("/call-feedback", auth.IsUserLoggedIn, feedback.CallFeedbackHandler)

	// v3 routes
	r.POST("/add-detailed-feedback", auth.IsUserLoggedIn, feedback.AddDetailedFeedbackBottomSheetHandler)
}

func setupBrandAdsTargetServiceRoutes(r *gin.RouterGroup) {
	r.POST("/google-ads/store", auth.IsUserLoggedIn, batsGoogle.StoreGoogleAds)
}

func setupMenuRoutes(r *gin.RouterGroup) {
	r.POST("/:res_id", consumer_menu.GetConsumerMenu)
	r.GET("/:res_id", consumer_menu.GetConsumerMenu)
	r.GET("/item_recommendations", menu.GetRecommendedItemsOnMenu)
	r.GET("/search_config", menu.GetMenuSearchConfig)
	r.GET("/config", menu.GetMenuConfig)
	r.POST("/footer/:res_id", footer.GetFooterData)
	r.POST("/res_info/:res_id", menu.GetResInfoData)
	r.POST("/recommendation/csao", csao.GetCsaoData)
	r.POST("/recommendation/fow", fow.GetFowData)
	r.POST("/recommendation/offer_items", menu.GetOfferItemsRecommendation)
	r.POST("/interstitial", interstitial.GetInterstitialData)
	r.POST("/start/:res_id", consumer_menu.GetMenuStartData)
	r.POST("/res-visibility/update", menu.UpdateResVisibility)
	r.POST("/feature/event", menu.ProcessFeatureEvent)
	r.POST("/item_availability", consumer_menu.GetItemAvailability)
	r.POST("/serviceability_reminder", menu.ProcessServiceabilityReminder)
	r.POST("/image_instruction/upload", menu.ImageInstructionUploadHandler)
	r.POST("/image_instruction/retrieve", menu.ImageInstructionRetrieveHandler)
	r.POST("/price_parity", menu.GetPriceParityInfo)
	r.POST("/raise_ticket", menu.RaisePriceParityTicket)
	r.POST("/feedback", menu.FeedbackHandler)
	r.POST("/match_score_feedback", menu.MatchScoreConsumerFeedbackHandler)
	r.POST("/curated_pack", menu.GetCuratedPack)
	r.POST("/review_insights", menu.GetReviewInsights)
	r.POST("/friends_recommendation", auth.IsUserLoggedIn, menu.GetFriendsRecommendations)
}

func setupSubscriptionRoutes(r *gin.RouterGroup) {
	r.POST("/membership", auth.IsUserLoggedIn, subcommon.SetIsZomanToContextMiddleware(), subscription.GetMembershipDetails)
	r.POST("/intro", subcommon.SetIsZomanToContextMiddleware(), subscription.GetPlanIntroDetails)
	r.POST("/config", auth.IsUserLoggedIn, subscription.GetPlanConfig)
	r.POST("/verify", auth.IsUserLoggedIn, subscription.ValidateUserSubscription)
	r.POST("/status", auth.IsUserLoggedIn, subscription.GetUserVerificationStatus)
	r.POST("/purchase", auth.IsUserLoggedIn, subscription.PlaceSubscriptionOrder)
	r.POST("/order-status", auth.IsUserLoggedIn, subscription.GetOrderStatus)
	r.POST("/cart", auth.IsUserLoggedIn, subcommon.SetIsZomanToContextMiddleware(), subscription.GetSubscriptionCart)
	r.POST("/faq", auth.IsUserLoggedIn, subcommon.SetIsZomanToContextMiddleware(), subscription.GetSubscriptionFaq)
	r.POST("/share-details", auth.IsUserLoggedIn, subscription.SubscriptionShareDetails)
	r.POST("/extend", auth.IsUserLoggedIn, subscription.CreateSubscriptionExtension)
	r.POST("/user-properties/update", auth.IsUserLoggedIn, subscription.UpdateUserProperties)
	r.POST("/validate-coupon", auth.IsUserLoggedIn, subscription.ValidateCouponCode)
	r.POST("/auto-renew-status", auth.IsUserLoggedIn, subscription.UpdateRenewStatus)
	r.POST("/get-coupon", auth.IsUserLoggedIn, subcommon.SetIsZomanToContextMiddleware(), subscription.GetGamificationCouponInfo)
	r.POST("/delights/list", auth.IsUserLoggedIn, subscription.GetDelightsList)
	r.POST("/delights/claim", auth.IsUserLoggedIn, subscription.ClaimDelight)
	r.POST("/loyalty/intro", auth.IsUserLoggedIn, subcommon.SetIsZomanToContextMiddleware(), subscription.GetLoyaltyIntroBottomsheet)
	r.POST("/loyalty/initiate-verification", auth.IsUserLoggedIn, subcommon.SetIsZomanToContextMiddleware(), subscription.InitiateVerification)
	r.POST("/loyalty/otp-bottomsheet", auth.IsUserLoggedIn, subcommon.SetIsZomanToContextMiddleware(), subscription.RenderOTPBottomsheet)
	r.POST("/loyalty/verify-otp", auth.IsUserLoggedIn, subcommon.SetIsZomanToContextMiddleware(), subscription.VerifyAndCreateSubscription)
	r.POST("/loyalty/landing", auth.IsUserLoggedIn, subcommon.SetIsZomanToContextMiddleware(), subscription.RenderLandingPage)
	r.POST("/loyalty/ledger", auth.IsUserLoggedIn, subcommon.SetIsZomanToContextMiddleware(), subscription.GetLoyaltyLedger)
	r.POST("/loyalty/settings", auth.IsUserLoggedIn, subcommon.SetIsZomanToContextMiddleware(), subscription.GetLoyaltySettings)
	r.POST("/loyalty/faq", auth.IsUserLoggedIn, subcommon.SetIsZomanToContextMiddleware(), subscription.GetLoyaltyFaq)
	r.POST("/teams/create", auth.IsUserLoggedIn, teams.CreateTeam)
	r.POST("/teams/join", auth.IsUserLoggedIn, teams.JoinTeam)
	r.POST("/teams/leave", auth.IsUserLoggedIn, teams.LeaveTeam)
	r.POST("/teams/remove-member", auth.IsUserLoggedIn, teams.RemoveMember)
	r.POST("/teams/update-team-status", auth.IsUserLoggedIn, teams.UpdateTeamStatus)
	r.GET("/teams/intro", auth.IsUserLoggedIn, teams.GetIntroPageResponse)
	r.GET("/teams/create-team-page", auth.IsUserLoggedIn, teams.GetCreateTeamPage)
	r.GET("/teams/home-page", auth.IsUserLoggedIn, teams.GetHomePageResponse)
	r.POST("/cancel-membership", auth.IsUserLoggedIn, subscription.CancelMembership)
}

func setupLegendsRoutes(r *gin.RouterGroup) {
	r.POST("/faq", legendsservice.GetLegendsFAQ)

	// get user launch buzz details
	r.OPTIONS("/launch-buzz-details")
	r.GET("/launch-buzz-details", auth.IsUserLoggedIn, legendsservice.GetLaunchBuzzDetails)

	// mark launch buzz gift claimed
	r.OPTIONS("/launch-buzz-gift")
	r.POST("/launch-buzz-gift", auth.IsUserLoggedIn, legendsservice.MarkLaunchBuzzClaimed)
}

func setupInventoryRoutes(r *gin.RouterGroup) {
	r.POST("/validation", inventory.GetInventoryDetails)
}

func setupCartRoutes(r *gin.RouterGroup) {
	r.POST("/build", cart.BuildCart)
	r.POST("/recommend/similar-items", cr.RecommendSimilarCatalogItemsHandler)
	r.Use(cors.New(middlewares.ZomatoWebCORSWhitelist()))
	r.Use(cartmiddlewares.ContextModifierMiddleware())
	r.POST("/share", cart.BuildSharedCart)
	r.OPTIONS("/share")
	r.POST("/create", cart.CreateCart)
	r.POST("/checkout", cart.CheckoutCart)
	r.POST("/cart_to_menu", cart.BackPress)
	r.GET("/pay_after_order/bottomsheet", cart.GetPayAfterOrderBottomsheetHandler)
	r.POST("/cart_impression_events", cart.CartImpressionEventsHandler)
}

func setupGroupOrderingRoutes(r *gin.RouterGroup) {
	r.Use(groupOrdering.ContextModifierMiddleware())
	r.Use(groupOrdering.GroupOrderRolloutMiddleware())
	r.POST("/get-group-ordering-configs", groupOrdering.GetGroupOrderingConfigs)
	r.POST("/get-group-preview", groupOrdering.GetGroupPreview)
	r.POST("/get-archival-time-slots", groupOrdering.GetArchivalTimeSlots)
	r.POST("/create-group", groupOrdering.CreateGroup)
	r.POST("/fetch-group", groupOrdering.FetchGroup)
	r.POST("/add-member", groupOrdering.AddMember)
	r.POST("/update-member", groupOrdering.UpdateMember)
	r.POST("/update-group-state", groupOrdering.UpdateGroupState)
	r.POST("/mqtt-event-callback", groupOrdering.GetMqttEventCallback)
	r.POST("/lock-group", groupOrdering.LockGroup)
	r.POST("/get-post-order-summary", groupOrdering.GetPostOrderSummary)
	r.POST("/get-updated-group-catalog", groupOrdering.GetUpdatedGroupCatalog)
}

func setupDelightServiceRoutes(ctx context.Context, r *gin.RouterGroup) {
	trueFanWhitelistedIPs := config.GetStringSlice(ctx, "delight_service.third_party.true_fan.whitelisted_ips")
	r.POST("/celeb-video-card/callback/video_status", middlewares.CheckIPWhitelist(trueFanWhitelistedIPs), celeb_video_card.VideoStatusCallback)
	r.POST("/celeb-video-card/validate_input_template_args", celeb_video_card.ValidateInputTemplateArgs)
	r.POST("/celeb-video-card/validate_post_order_video_request", celeb_video_card.ValidatePostOrderVideoRequest)
}

func setupFileUploadsRoutes(r *gin.RouterGroup) {
	r.POST("/presigned-url", ephemeral_uploads.GeneratePreSignedUrl)
	r.OPTIONS("/presigned-url")
}

func setupBlinkitRoutes(r *gin.RouterGroup) {
	r.POST("/auth/get-access-token", auth.IsUserLoggedIn, blinkitAuth.GetBlinkitAccessTokenHandler)

	// below routes called from blinkit backend
	r.POST("/payments/get-access-token", blinkitAuth.IsBlinkitBackendClient, blinkitPayments.GetAccessTokenHandler)
	r.POST("/payments/get-hash", blinkitAuth.IsBlinkitBackendClient, blinkitPayments.GetHashHandler)
	r.POST("/payments/get-request-id", blinkitAuth.IsBlinkitBackendClient, blinkitPayments.GetRequestIDHandler)
	r.POST("/payments/get-refund", blinkitAuth.IsBlinkitBackendClient, blinkitPayments.GetRefundHandler)
	r.POST("/user/address/get-details", blinkitAuth.IsBlinkitBackendClient, blinkitUser.GetAddressDetailsHandler)
	r.POST("/user/addresses/get-details", blinkitAuth.IsBlinkitBackendClient, blinkitUser.GetAddressesDetailsHandler)
}

func setupPaymentGatewayRoutes(r *gin.RouterGroup) {
	r.Use(paymentsAPIGatewayAuth.IsPaymentAPIGatewayClient, paymentsAPIGatewayAuth.ValidateRequestChecksum, paymentsAPIGatewayAuth.AddUserInfoToClient, payments.PopulateCountryIdInContext())

	r.POST("/payments/in/zomato_money_v2/bottom_sheet", zomato_money_v2.ZomatoMoneyBottomsheetHandler)
	r.POST("/payments/in/zomato_money_v2/bottom_sheet/validate", zomato_money_v2.ZomatoMoneyBottomsheetValidationHandler)

	r.POST("/gift-cards/claim", giftCardsClaim.ClaimHandler)
	r.POST("/gift-cards/claim-page", giftCardsLanding.ClaimPageHandler)
	r.POST("/gift-cards/order/customization-page", giftCardsConsumer.GetCustomizationPageHandler)
	r.POST("/gift-cards/order/history", giftCardsOrder.GetOrderHistoryHandler)

	r.POST("/payments/zomato_wallet/add_money", walletAddMoney.GetAddMoneyPageHandler)
	r.POST("/payments/zomato_wallet/add_money_refresh", walletAddMoney.GetAddMoneyPageRefreshHandler)
	r.POST("/payments/zomato_wallet/order/make", walletAddMoney.MakeOrderHandler)
	r.GET("/payments/zomato_wallet/get_payment_status", walletAddMoney.GetPaymentStatusHandler)

	r.POST("/payments/in/zomato_money/settings", moneyTab.MoneySettingsHandler)
	r.POST("/payments/in/zomato_money/faq", moneyTab.MoneyFaqHandler)

	r.POST("/payments/in/zomato_money/link", auth.IsUserLoggedIn, moneyTab.ZomatoMoneyLinkHandler)
	r.POST("/payments/in/zomato_money/link_bottom_sheet", auth.IsUserLoggedIn, moneyTab.GetLinkingBottomSheetHandler)
	r.POST("/payments/in/zomato_money/verify_otp", auth.IsUserLoggedIn, moneyTab.ZomatoMoneyVerifyOtpHandler)
	r.POST("/payments/in/zomato_money/refresh_bottom_sheet", auth.IsUserLoggedIn, moneyTab.BottomSheetRefreshHandler)

	r.POST("/payments/in/wallet_sdk/configure", auth.IsUserLoggedIn, zomato_money_v2.SDKConfigureHandler)
}

func setupLoginWithZomatoRoutes(r *gin.RouterGroup) {
	r.POST("/get-consent", auth.IsUserLoggedIn, loginwithzomato.GetConsentHandler)
	r.POST("/save-consent", auth.IsUserLoggedIn, loginwithzomato.SaveConsentHandler)
	r.POST("/get-token", auth.IsUserLoggedIn, loginwithzomato.GetTokenHandler)
	r.POST("/get-user-details", loginwithzomato.GetUserDetailsHandler)
}

func setupOneSupportRoutes(r *gin.RouterGroup) {
	r.GET("/auth/get-access-token", onesupport.GetOneSupportAccessTokenHandler)
	r.GET("/auth/get-payload-token", onesupport.GetOneSupportPayloadTokenHandler)
	r.GET("/auth/get-tokens", onesupport.GetOneSupportTokensHandler)
}

func setupAkamaiCallbackRoutes(r *gin.RouterGroup) {
	r.GET("/callback", auth.AkamaiCallbackHandler)
}

func setupBenefitServiceRoutes(r *gin.RouterGroup) {
	r.POST("/cart-offers", cartoffers.GetCartOffersForUser)
	r.POST("/apply-manual-promo", cartoffers.ApplyManualPromo)
	r.POST("/promo-payment-info", paymentinfo.GetPromoPaymentInfo)
	r.POST("/offer-savings-info", offersavings.GetOfferSavingsInfo)
	r.POST("/multi-cart-offers", multicartoffers.GetMultiCartOffers)
	r.POST("/apply-manual-promo-v2", applymanualpromo.ApplyManualPromo)
}

func setupKarmaRoutes(r *gin.RouterGroup) {
	r.POST("/device-info", karma.DeviceInfoHandler)
	r.POST("/play-integrity/config", auth.IsUserLoggedIn, karma.GetPlayIntegrityConfig)
	r.POST("/play-integrity/token", auth.IsUserLoggedIn, karma.SetPlayIntegrityToken)
	r.GET("/cross-app", auth.IsUserLoggedIn, crossapp.GetEntityUniqueEncryptedValue)
	r.POST("/cross-app", auth.IsUserLoggedIn, crossapp.SendCrossAppEventToKarma)
}

func setupGamificationTriviaRoutes(r *gin.RouterGroup) {
	r.POST("/lobby", auth.IsUserLoggedIn, trivia_lobby.PlanHandler)
	r.POST("/submit", auth.IsUserLoggedIn, trivia.SubmitHandler)
	r.POST("/get-result", auth.IsUserLoggedIn, trivia.CountHandler)
	r.GET("/faqs", auth.IsUserLoggedIn, trivia_faq.PageHandler)
	r.POST("/metadata", auth.IsUserLoggedIn, metadata.LobbyMetadataHandler)
	r.GET("/metadata-get", auth.IsUserLoggedIn, metadata.LobbyMetadataHandler)
	r.POST("/live-people-count", auth.IsUserLoggedIn, trivia.PlayersHandler)
	r.POST("/claim", auth.IsUserLoggedIn, trivia.ClaimHandler)
	r.POST("/cart", auth.IsUserLoggedIn, tc.RewardAmplifyCartHandler)
	r.POST("/place-order", auth.IsUserLoggedIn, tc.PlaceRewardAmplifyOrder)
	r.POST("/order-status", auth.IsUserLoggedIn, tc.AmplifyOrderStatusHandler)
	r.POST("/server-status", auth.IsUserLoggedIn, gamification_server_status.StatusHandler)
	r.POST("/coupons/page-details", auth.IsUserLoggedIn, coupons.AllCouponsHandler)
}

func setupUserServiceRoutes(r *gin.RouterGroup) {
	r.POST("/get-tenant-addresses", auth.IsUserLoggedIn, address.GetAddressesFromTenantHandler)
	r.POST("/import-tenant-addresses", auth.IsUserLoggedIn, address.ImportAddressesFromTenantHandler)
	r.POST("/manage-veg-schedule", auth.IsUserLoggedIn, userprofile.GetUserVegSchedule)
	r.POST("/update-veg-schedule", auth.IsUserLoggedIn, userprofile.UpdateUserVegSchedule)
}

func setupAccessibilityRoutes(r *gin.RouterGroup) {
	// GET endpoint for fetching accessibility preferences
	r.GET("/get-accessibility-info", accessibility.GetAccessibilityHandler)

	// POST endpoint for updating accessibility preferences
	r.POST("/update-accessibility-info", accessibility.UpdateAccessibilityHandler)
}

func setupAerobarServiceRoutes(r *gin.RouterGroup) {
	r.GET("/get-aerobars", aerobar.GetAerobars)
}

func setupGamificationZplRoutes(r *gin.RouterGroup) {
	r.POST("/lobby", auth.IsUserLoggedIn, zpl_lobby.PlanHandler)
	r.POST("/home", auth.IsUserLoggedIn, matchscreen.Handler)
	r.POST("/match-status", auth.IsUserLoggedIn, matchscreen.PollingHandler)
	r.POST("/history", auth.IsUserLoggedIn, zpl_history.PlanHandler)

	r.POST("/submit", auth.IsUserLoggedIn, zpl_submission.SubmitHandler)
	r.POST("/live-people-count", auth.IsUserLoggedIn, zpl.PlayersHandler)
	r.POST("/metadata", auth.IsUserLoggedIn, zpl_metadata.LobbyMetadataHandler)
	r.POST("/server-status", auth.IsUserLoggedIn, gamification_server_status.StatusHandler)
	r.POST("/claim-coupon", auth.IsUserLoggedIn, zpl.ClaimHandler)
	r.POST("/notify-me", auth.IsUserLoggedIn, zpl_notify.NotifyHandler)
}

func setupGamificationFoodRescueRoutes(r *gin.RouterGroup) {
	r.POST("/create-cart", auth.IsUserLoggedIn, foodrescue.CreateCartHandler)
}

func setupGamificationCrazyDropsRoutes(r *gin.RouterGroup) {
	r.POST("/drop-soon", auth.IsUserLoggedIn, crazydrops.DropSoonHandler)
	r.POST("/create-cart", auth.IsUserLoggedIn, foodrescue.CreateCartForCrazyDropsHandler)
}

func setupGamificationDiwaliMapRoutes(r *gin.RouterGroup) {
	r.Use(cors.New(middlewares.ZomatoWebCORSWhitelist()))
	r.OPTIONS("/facts")
	r.GET("/facts", auth.IsUserLoggedIn, diwalimap.GetFactsHandler)
}

func setupGamificationHandCricketRoutes(r *gin.RouterGroup) {
	r.GET("/lobby", auth.IsUserLoggedIn, hand_cricket.GetHandCricketLobby)
	r.GET("/all-rewards", auth.IsUserLoggedIn, hand_cricket.GetAllRewardsHandler)
	r.GET("/achievements", auth.IsUserLoggedIn, hand_cricket.GetAchievementsHandler)
	r.GET("/faqs", auth.IsUserLoggedIn, hand_cricket.GetFaqsHandler)
	r.GET("/start-game", auth.IsUserLoggedIn, hand_cricket.StartGameHandler)
	r.POST("/update-game-status", auth.IsUserLoggedIn, hand_cricket.UpdateGameStatusHandler)
	r.GET("/view-post-match-rewards", auth.IsUserLoggedIn, hand_cricket.ViewRewardsHandler)
	r.POST("/add-user-consent-data", auth.IsUserLoggedIn, hand_cricket.AddUserConsentDataHandler)
	r.GET("/view-leaderboard", auth.IsUserLoggedIn, hand_cricket.ViewLeaderboardHandler)
	r.POST("/create-join-room", auth.IsUserLoggedIn, hand_cricket.CreateOrJoinGameRoom)
	r.GET("/room", auth.IsUserLoggedIn, hand_cricket.GetGameRoom)
	r.POST("/trigger-game-start", auth.IsUserLoggedIn, hand_cricket.TriggerGameStart)
	r.GET("/get-dual-player-game-state", auth.IsUserLoggedIn, hand_cricket.GetDualPlayerGameStateHandler)
	r.POST("/update-dual-player-game-state", auth.IsUserLoggedIn, hand_cricket.UpdateDualPlayerGameStateHandler)
	r.POST("/select-team", auth.IsUserLoggedIn, hand_cricket.SelectTeamHandler)
	r.POST("/rematch-dual-player-game", auth.IsUserLoggedIn, hand_cricket.TriggerRematch)
}

func setupTrainFulfillmentServiceRoutes(r *gin.RouterGroup) {
	r.GET("/landing-page", auth.IsUserLoggedIn, train_journey.GetTrainOrderingLandingPageHandler)
	r.POST("/validate-pnr", auth.IsUserLoggedIn, train_journey.ValidatePNRHandler)
	r.GET("/journey-details", auth.IsUserLoggedIn, train_journey.GetJourneyDetailsFromPNR)
	r.GET("/journey-stations", auth.IsUserLoggedIn, train_journey.GetJourneyStationsFromAddressID)
	r.POST("/update-address", auth.IsUserLoggedIn, train_journey.UpdateAddressHandler)
	r.GET("/update-seat-page", auth.IsUserLoggedIn, train_journey.GetSeatUpdationBottomSheetHandler)
	r.POST("/update-seat", auth.IsUserLoggedIn, train_journey.UpdateSeatHandler)
	r.POST("/update-address-v2", auth.IsUserLoggedIn, train_journey.UpdateAddressV2Handler)
	r.GET("/add-pnr-bottom-sheet", auth.IsUserLoggedIn, train_journey.GetAddPNRBottomSheet)
	r.POST("/search", auth.IsUserLoggedIn, train_journey.GetSearchResultsHandler)
}

func setupHighwayRoutes(r *gin.RouterGroup) {
	r.POST("/landing-page", auth.IsUserLoggedIn, highway.GetLandingPageHandler)
}

func setupAddressFlowRoutes(r *gin.RouterGroup) {
	r.POST("/add-page", locationAddress.AddAddressPageHandler)
	r.POST("/save", locationAddress.SaveAddressHandler)
}

func setupDevRoutes(r *gin.RouterGroup) {
	if runtime.KumaMeshName() != "dev" {
		return
	}

	r.GET("/map-permissions", auth.IsZoman, dev.MapPermissionsHandler)
	r.GET("/complete-order", auth.IsZoman, dev.CompleteOrder)
}

func setupUserPreferenceServiceRoutes(r *gin.RouterGroup) {
	collectionRoutes := r.Group("/collection")
	recommendationRoutes := r.Group("/recommendation")
	tinderFlowRoutes := r.Group("/tinder")
	r.POST("/dismiss-action", auth.IsUserLoggedIn, userpreferenceservice.DismissActionHandler)
	r.POST("/update-preferences", auth.IsUserLoggedIn, generic_preferences.UpdateGenericPreferenceHandler)
	setupCollectionRoutes(collectionRoutes)
	setupRecommendationRoutes(recommendationRoutes)
	setupTinderFlowRoutes(tinderFlowRoutes)
	setupAccessibilityRoutes(r.Group("/accessibility"))
}

func setupCollectionRoutes(r *gin.RouterGroup) {
	r.POST("/manage", auth.IsUserLoggedIn, collection.ManageCollectionHandler)
	r.POST("/create", auth.IsUserLoggedIn, collection.CreateCollectionHandler)
	r.POST("/save", auth.IsUserLoggedIn, collection.SaveCollectionHandler)
	r.POST("/unsave", auth.IsUserLoggedIn, collection.UnsaveCollectionHandler)
	r.POST("/delete", auth.IsUserLoggedIn, collection.DeleteCollectionHandler)
	r.POST("/update", auth.IsUserLoggedIn, collection.UpdateCollectionHandler)
}

func setupRecommendationRoutes(r *gin.RouterGroup) {
	r.POST("/sync-contacts", auth.IsUserLoggedIn, reco.SyncContactsHandler)
	r.POST("/unsync-contacts", auth.IsUserLoggedIn, reco.UnsyncContactsHandler)
	r.POST("/get-contacts", auth.IsUserLoggedIn, reco.GetContactsHandler)
	r.POST("/recommended-by-info", auth.IsUserLoggedIn, reco.RecommendedByInfoBottomSheetHandler)
	r.POST("/update-subscribed-recommendations", auth.IsUserLoggedIn, reco.UpdateSubscribedRecommendationsHandler)
	r.POST("/notify-contacts-to-recommend", auth.IsUserLoggedIn, reco.NotifyContactsToRecommend)
	r.POST("/update-recommendations-collection", auth.IsUserLoggedIn, reco.UpdateRecommendationsCollectionHandler)
	r.POST("/manage-recommendations", auth.IsUserLoggedIn, manageReco.ManageRecommendationsHandler)
}

func setupTinderFlowRoutes(r *gin.RouterGroup) {
	r.POST("/dish_preferences", auth.IsUserLoggedIn, tinder.GetUserTinderDishPreferences)
}

func setupCinemaRouters(r *gin.RouterGroup) {
	r.Use(cartmiddlewares.ContextModifierMiddleware())
	r.GET("/update-seat-details", auth.IsUserLoggedIn, cinema.GetSeatDetailsBottomSheetHandler)
}

func setupGamificationRoutes(r *gin.RouterGroup) {
	r.POST("/awards/page-details", auth.IsUserLoggedIn, awards.ProcessAwardsPageDetailsPlans)
	r.POST("/awards/update-vote", auth.IsUserLoggedIn, awards.ProcessAwardsVoteUpdationPlan)
	r.POST("/awards/winner-page-details", auth.IsUserLoggedIn, awards.ResAwardsWinningPage)
	r.POST("/awards/winner-stories", auth.IsUserLoggedIn, awards.WinnerStoriesHandler)

	// TODO: Remove endpoint once all usages of this endpoint are changed
	r.POST("/process-game-plans", auth.IsUserLoggedIn, cointoss.ProcessCoinTossPlans)
	r.POST("/process-coin-toss-plans", auth.IsUserLoggedIn, cointoss.ProcessCoinTossPlans)
	r.POST("/badges/page-details", auth.IsUserLoggedIn, badges.ProcessBadgesPlans)
	r.POST("/badges/update-last-viewed", auth.IsUserLoggedIn, badges.UpdateLastViewedBadge)
	r.POST("/lookback/page-details", auth.IsUserLoggedIn, lookback.GetPageDetails)

	r.POST("/res-awards/landing-page", auth.IsUserLoggedIn, awards.ResAwardsLandingPage)
	r.POST("/res-awards/vote-page", auth.IsUserLoggedIn, awards.VotePageHandler)
	r.POST("/res-awards/vote", auth.IsUserLoggedIn, awards.VoteResHandler)
	r.POST("/res-awards/leaderboard", auth.IsUserLoggedIn, awards.ResAwardsLeaderboard)
	r.POST("/res-awards/res-details", auth.IsUserLoggedIn, awards.ResDetailsHandler)
	r.POST("/res-awards/res-details-bottom-sheet", auth.IsUserLoggedIn, awards.ResDetailsBottomSheetHandler)
	r.POST("/res-awards/winner-page-details", auth.IsUserLoggedIn, awards.ResAwardsWinningPage)

	setupGamificationTriviaRoutes(r.Group("/trivia"))
	setupGamificationZplRoutes(r.Group("/zpl"))
	setupGamificationHandCricketRoutes(r.Group("/hand-cricket"))
	setupGamificationFoodRescueRoutes(r.Group("/food-rescue"))
	setupGamificationDiwaliMapRoutes(r.Group("/diwali-map"))
	setupGamificationCrazyDropsRoutes(r.Group("/crazy-drops"))
}

func setupAIBotRoutes(r *gin.RouterGroup) {
	r.GET("/intro", auth.IsUserLoggedIn, aibot.IntroHandler)
}

func setupStoriesRoutes(r *gin.RouterGroup) {
	merchantRoutes := r.Group("/merchant")
	setupStoriesMerchantRoutes(merchantRoutes)

	creatorRoutes := r.Group("/community")
	setUpStoryCreatorRoutes(creatorRoutes)

	socialGroup := r.Group("/social")
	setupStoriesSocialGroup(socialGroup)

	storyGroup := r.Group("/story")
	setupStoryRoutes(storyGroup)

	r.POST("/feed", storiesConsumer.GetFeed)

	r.POST("/collection", storiesConsumer.GetStoriesOfACollection)
	// since deeplink of stories is routed to old story flow, sre has added a converter to convert this,
	// but original method post is being converted to get. Temp fix will resolve this later
	r.GET("/collection", storiesConsumer.GetStoriesOfACollection)
}

func setUpStoryCreatorRoutes(r *gin.RouterGroup) {
	r.OPTIONS("/get")
	r.POST("/get", community.GetStories)

	r.OPTIONS("/add")
	r.POST("/add", community.AddStory)

	r.OPTIONS("/is-authorised")
	r.POST("/is-authorised", community.GetAuthorisation)
}

func setupZomalandRoutes(r *gin.RouterGroup) {
	r.POST("/cart", auth.IsUserLoggedIn, zomalandConsumer.GetZomalandCart)
	r.POST("/payment-status", auth.IsUserLoggedIn, zomalandConsumer.GetPaymentStatus)
	r.POST("/update-ticket-delivery-address", auth.IsUserLoggedIn, zomalandConsumer.UpdateTicketDeliveryAddress)
	r.POST("/event-schedule", zomalandConsumer.GetEventSchedule)
	r.POST("/pre-register", auth.IsUserLoggedIn, zomalandConsumer.PreRegister)
	r.POST("/home", zomalandConsumer.GetZomalandHome)
	r.POST("/purchase", auth.IsUserLoggedIn, zomalandConsumer.PurchaseTicket)
	r.POST("/tickets-details", auth.IsUserLoggedIn, zomalandConsumer.GetTicketsDetails)
	r.POST("/gallery", zomalandConsumer.GetGallery)
	r.POST("/res-listing", zomalandConsumer.ResListingPage)
	r.POST("/get-tickets-history", auth.IsUserLoggedIn, zomalandConsumer.GetTicketsHistoryResponse)
	r.POST("/faqs", zomalandConsumer.GetFaqs)
	r.POST("/simpl-register", auth.IsUserLoggedIn, zomalandConsumer.SimplRegister)
	r.POST("/refund", auth.IsUserLoggedIn, zomalandConsumer.Refund)
	r.POST("/cancel", auth.IsUserLoggedIn, zomalandConsumer.CancelOrder)
	r.POST("/get-resale-tickets", auth.IsUserLoggedIn, zomalandConsumer.GetZLiveResaleTicketsForEvent)
	r.POST("/cancel-tickets", auth.IsUserLoggedIn, zomalandConsumer.GetCancellableTicketsForOrder)
	r.POST("/top-picks", zomalandConsumer.GetTopPicks)
}

func setupZomalandExternalRoutes(r *gin.RouterGroup) {
	r.POST("/zomaland/insider/sell-notification", zomalandConsumer.AddInsiderSell)
	r.POST("/zomaland/insider/change-phase", zomalandConsumer.ChangePhase)
	r.POST("/zomaland/bms/issue-qrs", zomalandConsumer.IssueQRs)
	r.POST("/events/order/redemption", zomalandConsumer.RedeemTicketsForOrder)
}

func setupStoriesSocialGroup(r *gin.RouterGroup) {
	r.POST("/linked-account", social.GetLoginInfo)
	r.OPTIONS("/linked-account")

	r.POST("/link", social.Login)
	r.OPTIONS("/link")

	r.POST("/content", social.GetContent)
	r.OPTIONS("/content")

	r.POST("/unlink", social.UnLink)
	r.OPTIONS("/unlink")

	r.POST("/callbacks/fb/deauthorize", social.FacebookDeAuthorizeCallbackHandler)
	r.GET("/callbacks/fb/deauthorize/status", social.FacebookDeAuthStatusHandler)
}

func setupStoriesMerchantRoutes(r *gin.RouterGroup) {
	r.OPTIONS("/get")
	r.POST("/get", merchant.GetStoriesForMerchant)

	r.OPTIONS("/add")
	r.POST("/add", merchant.AddStory)

	r.OPTIONS("/delete")
	r.POST("/delete", merchant.DeleteStoryByMerchant)

	r.OPTIONS("/get-insights")
	r.GET("/get-insights", merchant.GetAllInsights)

	r.OPTIONS("/subject/insights")
	r.POST("/subject/insights", merchant.GetSubjectInsights)
}

func setupStoryRoutes(r *gin.RouterGroup) {
	r.OPTIONS("/seen")
	r.POST("/seen", storiesConsumer.AddImpression)
	r.OPTIONS("/report")
	r.POST("/report", auth.IsUserLoggedIn, storiesConsumer.ReportInappropriateStory)
}

func setUpGoOutRoutes(r *gin.RouterGroup) {
	setupGoOutEventsRoutes(r.Group("/events"))
	//Set ResID in request for tracking
	r.Use(diningmiddlewares.SetResIDFromRequest())
	//Log event to jumbo
	r.Use(diningmiddlewares.APIRequestTrackingMiddleware())
	r.POST("/side-menu", sidemenu.GetSideMenuHandler)
	r.POST("/location", location2.GetLocation)
	r.GET("/tabs", tabs.GetTabs)
	r.POST("/restaurant/info", respage.GetDiningResPage)
}

func setupZLiveBackOfficeRoutes(r *gin.RouterGroup) {
	r.Use(middlewares.ZLiveBackOfficeAuthMiddleware())
	r.GET("/homepage", back_office.GetHomepageResponse)
	r.POST("/ticket-invite", back_office.GetTicketInvite)
}

func setUpZliveRoutes(r *gin.RouterGroup) {
	setUpZliveEventsRoutes(r.Group("/events"))
	setupZLiveBackOfficeRoutes(r.Group("/back-office"))
}

func setUpZliveEventsRoutes(r *gin.RouterGroup) {
	r.POST("/details", event.GetZLiveEvent)
	r.POST("/calendar-view", event.GetZLiveEventCalendarView)
}

func setupGoOutEventsRoutes(r *gin.RouterGroup) {
	r.POST("/search", search2.GetSearchResults)
	r.POST("/details", event.GetEvent)
	r.POST("/search_v1", search2.GetHomePageForOldVersion)
}

func setupPushRoutes(r *gin.RouterGroup) {
	// push notification trigger
	r.POST("/notification/send", paymentsAuth.IsAllowedZPushClient, zpush.GeneratePayloadAndSendPush)
	// send live activity app-events to push notification service
	r.POST("/live-activity", auth.IsUserLoggedIn, live_activity.SendLiveActivityDetails)
	// send updated user-preference to push notification service
	r.POST("/user-preference/update", auth.IsUserLoggedIn, user_preferences.UpdateUserPreferences)
}

func setupSMSRoutes(r *gin.RouterGroup) {
	// send & resend SMS message
	r.POST("/send", paymentsAuth.IsAllowedZSMSClient, sms.GeneratePayloadAndSendSMS)
	r.POST("/resend", paymentsAuth.IsAllowedZSMSClient, sms.GeneratePayloadAndResendSMS)
}

func setupEmailRoutes(r *gin.RouterGroup) {
	// email notification trigger
	r.POST("/send", paymentsAuth.IsAllowedZEmailClient, apollo.GeneratePayloadAndSendEmail)
}

func setupPaymentServiceRoutes(ctx context.Context, r *gin.RouterGroup) {
	r.GET("/user/profile", paymentsAuth.IsAllowedZUserServiceClient, userservice.FetchProfileFromUserService)
	r.POST("/user/payment-serviceability-flag", paymentsAuth.IsAllowedZUserServiceClient, userservice.UpdateUserPaymentServiceability)

	r.POST("/user/create", gin.BasicAuth(gin.Accounts{
		config.GetString(ctx, "payments.zppl_username"): config.GetString(ctx, "payments.zppl_password"),
	}), paymentsAuth.IsAllowedZUserServiceClient, userservice.CreateUserProfileByMobile)
}

func setupRewardsRoutes(r *gin.RouterGroup) {
	r.POST("/playgame", playgamev1controller.Playgame)
	r.POST("/reward_page", rewardpagecontroller.GetRewards)
	r.GET("/faq", rewardfaqcontroller.GetRewardsFaq)
	r.POST("/update-game-status", updategamestatus.UpdateGameStatus)
	r.POST("/fetch-contacts", fetch_contacts.FetchPremiumReferralContacts)
	r.POST("/fetch-assets", fetch_assets.FetchAssets)
}

// deprecated
func setupInstantFoodRoutes(r *gin.RouterGroup) {
	merchantDashboardRoutes := r.Group("/dashboard")
	setupInstantFoodMerchantDashboardRoutes(merchantDashboardRoutes)

	orderRoutes := r.Group("/order")
	setupInstantFoodOrderRoutes(orderRoutes)
}

func setupInstantFoodMerchantDashboardRoutes(r *gin.RouterGroup) {
	r.OPTIONS("/kitchens")
	r.GET("/kitchens", auth.IsUserLoggedIn, kitchen.GetKitchens)
	r.OPTIONS("/restaurants/validity")
	r.POST("/restaurants/validity", auth.IsUserLoggedIn, kitchen.GetResNameValidation)
	r.POST("/kitchens", auth.IsUserLoggedIn, kitchen.CreateKitchen)
	r.OPTIONS("/warehouses/validity")
	r.POST("/warehouses/validity", auth.IsUserLoggedIn, kitchen.GetWarehouseCodeValidation)
	r.OPTIONS("/brands")
	r.GET("/brands", auth.IsUserLoggedIn, brands.GetAllBrands)
	r.OPTIONS("/user/kitchens")
	r.GET("/user/kitchens", auth.IsUserLoggedIn, kitchen.GetAllowedUserKitchens)

	r.OPTIONS("/dishes/catalogue")
	r.GET("/dishes/catalogue", auth.IsUserLoggedIn, dishCatalog.GetDishesCatalogue)
	r.OPTIONS("/dishes/subdishes/catalogue")
	r.GET("/dishes/subdishes/catalogue", auth.IsUserLoggedIn, dishCatalog.GetSubDishesCatalogueForParentDish)
	r.OPTIONS("/dishes/catalogue/upsert")
	r.POST("/dishes/catalogue/upsert", auth.IsUserLoggedIn, dishCatalog.UpsertDish)
	r.OPTIONS("/dishes/catalogue/upsert-v2")
	r.POST("/dishes/catalogue/upsert-v2", auth.IsUserLoggedIn, dishCatalog.UpsertDishV2)
	r.OPTIONS("/dishes/subdishes/update")
	r.POST("/dishes/subdishes/update", auth.IsUserLoggedIn, dishCatalog.RemoveDishSubDishMapping)

	r.OPTIONS("/menu")
	r.GET("/menu", auth.IsUserLoggedIn, resCatalogue.GetMenuByResID)
	r.OPTIONS("/menu/dishtoggle")
	r.POST("/menu/dishtoggle", auth.IsUserLoggedIn, resCatalogue.UpdateKitchenDishState)

	r.OPTIONS("/dishes")
	r.GET("/dishes", auth.IsUserLoggedIn, dishCatalog.GetDishByID)
	r.OPTIONS("/dishes/subdishes")
	r.GET("/dishes/subdishes", auth.IsUserLoggedIn, dishCatalog.GetSubDishByID)

	r.OPTIONS("/brands/upsert")
	r.POST("/brands/upsert", auth.IsUserLoggedIn, brands.UpsertBrand)

	r.OPTIONS("/dishes/search")
	r.GET("/dishes/search", auth.IsUserLoggedIn, dishCatalog.DishSearch)
}

// deprecated
func setupInstantFoodOrderRoutes(r *gin.RouterGroup) {
	r.OPTIONS("/serviceable-menu")
	r.POST("/serviceable-menu", auth.IsUserLoggedIn, instantOrder.GetServiceableMenu)
}

func setupAdsRoutes(r *gin.RouterGroup, ctx context.Context) {
	r.POST("/menu/similar_res", auth.IsUserLoggedIn, ads.GetSimilarAdsRail)
	r.POST("/one-support/pause-ad", auth.IsOneSupportAutomationWorkerIP,
		gin.BasicAuth(gin.Accounts{
			config.GetString(ctx, "one_support.automation_credentials.username"): config.GetString(ctx, "one_support.automation_credentials.password"),
		}), ads.PauseAdFromOneSupport)
}

func setUpCultAppRoutes(r *gin.RouterGroup) {
	r.POST("/authorize", auth.IsUserLoggedIn, auth.IsEternal, cultapp.IsUserAuthorized)
	r.GET("/book/get_pre_signed_url", auth.IsUserLoggedIn, auth.IsEternal, cultapp.GeneratePresignedURL)
	r.GET("/policies", auth.IsUserLoggedIn, auth.IsEternal, cultapp.GetPolicies)
	r.GET("/shortcuts", auth.IsUserLoggedIn, auth.IsEternal, cultapp.GetShortcuts)
	r.GET("/get_user_details", auth.IsUserLoggedIn, auth.IsEternal, cultapp.GetUserProfile)
}

func setupKitchenRoutes(r *gin.RouterGroup) {
	r.OPTIONS("/getAllKitchens")
	r.GET("/getAllKitchens", auth.IsUserLoggedIn, kitchenManagement.GetKitchens)

	r.OPTIONS("/get-all-meal-timings")
	r.GET("/get-all-meal-timings", auth.IsUserLoggedIn, mealTime.GetAllMealTimings)

	// scanner action routes
	r.OPTIONS("scanner_actions/get")
	r.GET("scanner_actions/get", auth.IsUserLoggedIn, scanneraction.GetScannerActions)

	orderRoutes := r.Group("/order")
	setupKitchenOrderRoutes(orderRoutes)

	// kitchen inventory routes
	inventoryGroup := r.Group("/inventory")
	setupKitchenInventoryRoutes(inventoryGroup)

	kitchenConsumerGroup := r.Group("/consumer")
	setupKitchenConsumerRoutes(kitchenConsumerGroup)

	// routes for merchant dashboard desktop app
	mdDesktopAppGroup := r.Group("/md-desktop-app")
	setupKitchenMDDesktopAppRoutes(mdDesktopAppGroup)

	// vendor routes
	r.OPTIONS("vendor")
	r.GET("vendor", auth.IsUserLoggedIn, vendor.GetVendors)
}

func setupKitchenMDDesktopAppRoutes(r *gin.RouterGroup) {
	// route for downloading desktop app
	r.OPTIONS("/download")
	r.GET("/download", auth.IsUserLoggedIn, s3.GetMDDesktopApp)
}

func setupKitchenInventoryRoutes(r *gin.RouterGroup) {
	r.OPTIONS("/get-all-instock-dishes")
	r.GET("/get-all-instock-dishes", auth.IsUserLoggedIn, kitchenInventory.GetAllInStockDishes)

	r.OPTIONS("/get-inventory-details")
	r.GET("/get-inventory-details", auth.IsUserLoggedIn, kitchenInventory.GetInventoryDetails)

	r.OPTIONS("/inventory-reasons")
	r.GET("/inventory-reasons", auth.IsUserLoggedIn, kitchenInventory.GetInventoryReasons)

	r.OPTIONS("/update-inventory-details")
	r.POST("/update-inventory-details", auth.IsUserLoggedIn, kitchenInventory.UpdateInventoryDetails)
}

func setupKitchenOrderRoutes(r *gin.RouterGroup) {
	r.POST("/scan", auth.IsUserLoggedIn, kitchenOrder.Scan)
	r.OPTIONS("/print-status")
	r.POST("/print-status", auth.IsUserLoggedIn, kitchenOrder.UpdatePrintStatus)

	updateOrder := r.Group("/update")
	updateOrder.OPTIONS("/state")
	updateOrder.POST("/state", auth.IsUserLoggedIn, kitchenOrder.UpdateOrderState)
}

func setupKitchenConsumerRoutes(r *gin.RouterGroup) {
	r.OPTIONS("/roster")
	r.GET("/roster", auth.IsUserLoggedIn, kitchenConsumer.GetRosterEntities)
	r.OPTIONS("/preference/entity")
	r.POST("/preference/entity", auth.IsUserLoggedIn, kitchenConsumer.SetEntityPreference)
	r.OPTIONS("/chef")
	r.POST("/chef/stories", auth.IsUserLoggedIn, chef.GetChefStories)
	slotsRoutes := r.Group("/slots")
	slotsRoutes.POST("/get-all", auth.IsUserLoggedIn, scheduling.GetAll)
	slotsRoutes.GET("/edit", auth.IsUserLoggedIn, scheduling.EditSlotsOptions)
	slotsRoutes.POST("/edit", auth.IsUserLoggedIn, scheduling.EditSlots)
}

func setupDiningServiceRoutes(ctx context.Context, r *gin.RouterGroup) {
	r.Use(diningmiddlewares.AreDiningRoutesKilled)
	// validate if any unwanted headers are present in the request
	r.Use(diningmiddlewares.ValidateDiningHeaders())

	//Set ResID in request for tracking
	r.Use(diningmiddlewares.SetResIDFromRequest())
	//Log event to jumbo
	r.Use(diningmiddlewares.APIRequestTrackingMiddleware())

	r.POST("/tr/booking-history", auth.IsUserLoggedIn, tablereservation.GetHistory)
	r.POST("/cart", auth.IsUserLoggedIn, diningCart.GetCart)
	r.POST("/checkout", auth.IsUserLoggedIn, diningCart.Checkout)
	r.POST("/tr/slots", auth.IsUserLoggedIn, slots.GetTrSlots)
	r.POST("/order/details", auth.IsUserLoggedIn, diningOrder.GetOrderDetails)
	r.POST("/order/cancel", auth.IsUserLoggedIn, diningOrder.CancelOrder)
	r.POST("/cart/offers", offerwall.GetOfferWall)
	r.POST("/buffets/info", buffets.GetBuffets)
	r.POST("/get-res-phone-number-details", auth.IsUserLoggedIn, acd.GetResPhoneNumberDetails)

	restaurantGroup := r.Group("/restaurant")
	setUpDiningRestaurantRoutes(restaurantGroup)

	reserveWithGoogleGroup := r.Group("/rwg/v3", diningmiddlewares.IsAuthorisedForReserveWithGoogle)
	setupReserveWithGoogleRoutes(ctx, reserveWithGoogleGroup)

	nudgeMxGroup := r.Group("/nudge-mx")
	setNudgeMxGroup(nudgeMxGroup)

	unlockGroup := r.Group("/unlock")
	setUnlockGroup(unlockGroup)

	billPaymentGroup := r.Group("/bill-payment")
	setBillPaymentGroup(billPaymentGroup)
}

func setUnlockGroup(r *gin.RouterGroup) {
	r.POST("/order-history", auth.IsUserLoggedIn, unlock.GetHistory)
}

func setBillPaymentGroup(r *gin.RouterGroup) {
	r.POST("/order/details", auth.IsUserLoggedIn, crystal.OrderDetails)
}

func setNudgeMxGroup(r *gin.RouterGroup) {
	r.POST("/raise-gold-request", nudge_mx.RequestNonGoldRestaurantOnGold)
}

func setupRestaurantRoutes(r *gin.RouterGroup) {
	r.POST("/user-preference", resvisibility.ManageHide)
}

func setupSchedulingRoutes(r *gin.RouterGroup) {
	r.POST("/servicable_slots", sa.GetServiceableSlots)
}

// setupReserveWithGoogleRoutes APIs used by google in real time
func setupReserveWithGoogleRoutes(ctx context.Context, r *gin.RouterGroup) {
	r.GET("/HealthCheck", health.HealthcheckHandler())

	rateLimitingGroup := r.Group("")
	rateLimitingGroup.Use(diningmiddlewares.ReserveWithGoogleRateLimiter(ctx))
	rateLimitingGroup.POST("/BatchAvailabilityLookup", rwg.GetTRSlotsAvailability)
	rateLimitingGroup.POST("/CreateBooking", rwg.CreateBooking)
	rateLimitingGroup.POST("/UpdateBooking", rwg.UpdateBooking)
	rateLimitingGroup.POST("/SetMarketingPreference", rwg.SetMarketingPreference)
}

func setupWeatherServiceRoutes(r *gin.RouterGroup) {
	r.OPTIONS("/zws_city_locality_detail")
	r.GET("/zws_city_locality_detail", weatherStation.GetAllZWSDeviceCityToLocalityInfo)
	r.OPTIONS("/get_city_weather_data")
	r.GET("/get_city_weather_data", weatherStation.GetCityWeatherData) // for getting bulk device data from weather dashboard
	r.OPTIONS("/get_weather_trends_data")
	r.GET("/get_weather_trends_data", weatherStation.GetWeatherTrendsData) // api for getting weather trends data of locality and city with hourly basis
	r.OPTIONS("/get_location_data_from_geo_point")
	r.GET("/get_location_data_from_geo_point", weatherUser.GetLocationDataFromGeoPoint) // api for getting location data from geo points
	r.OPTIONS("/contact_us")
	r.POST("/contact_us", weatherUser.ContactUs) // api for contact us through email
	r.OPTIONS("/get_locality_weather_facts")
	r.GET("/get_locality_weather_facts", weatherStation.GetLocalityWeatherFactsData) // api for getting weather facts data
	r.OPTIONS("/get_weather_facts")
	r.GET("/get_weather_facts", weatherStation.GetWeatherFactsData) // api for getting weather facts data
}

func setupWeatherExternalRoutes(r *gin.RouterGroup) {

	// Public Weather API Routes
	allAllowedCORSRoutes := r.Group("/")
	allAllowedCORSRoutes.Use(cors.New(middlewares.WeatherExternalApiCorsWhitelist()))
	allAllowedCORSRoutes.OPTIONS("/v0/get_weather_data")
	allAllowedCORSRoutes.GET("/v0/get_weather_data", weatherUser.VerifyWeatherApiKey(), weatherStation.GetWeatherDataForAuthorizedUser) // API for calling weather data from pinpoint lat-long
	allAllowedCORSRoutes.OPTIONS("/v0/get_locality_weather_data")
	allAllowedCORSRoutes.GET("/v0/get_locality_weather_data", weatherUser.VerifyWeatherApiKey(), weatherStation.GetLocalityWeatherDataForAuthorizedUser) // API for calling locality weather data from city and locality id

	// WeatherUnion API Routes
	onlyZomatoOriginAllowedRoutes := r.Group("/")
	onlyZomatoOriginAllowedRoutes.Use(cors.New(middlewares.ZomatoWebCORSWhitelist()))
	onlyZomatoOriginAllowedRoutes.Use(middlewares.CheckCSRF())
	onlyZomatoOriginAllowedRoutes.Use(middlewares.VerifyReferrerMiddleware([]string{weather.WEATHER_UNION_ALLOWED_REFERER}))

	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/download_previous_data")
	onlyZomatoOriginAllowedRoutes.GET("/v0/download_previous_data", auth.IsUserLoggedIn, weatherUser.VerifyWeatherUser(), weatherStation.GetLocalityWeatherDownloadLinkForAuthorizedUser) // API for calling locality weather data from city and locality id
	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/user/is_registered_user_check")
	onlyZomatoOriginAllowedRoutes.GET("/v0/user/is_registered_user_check", auth.IsUserLoggedIn, weatherUser.GetIsRegisteredUser) // API for getting if user is registered weather user
	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/user/create_weather_profile")
	onlyZomatoOriginAllowedRoutes.POST("/v0/user/create_weather_profile", auth.IsUserLoggedIn, weatherUser.UpdateUserDetailsAndGenerateApiToken) // API for creating user profile and getting API token
	// onlyZomatoOriginAllowedRoutes.POST("/v0/user/delete_weather_profile", auth.IsZoman, weatherUser.DeleteWeatherUserProfile)               // API for deleting weather user profile
	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/user/get_weather_user_profile")
	onlyZomatoOriginAllowedRoutes.GET("/v0/user/get_weather_user_profile", auth.IsUserLoggedIn, weatherUser.GetUserProfile) // API for getting user profile
	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/user/request_beta_access")
	onlyZomatoOriginAllowedRoutes.POST("/v0/user/request_beta_access", auth.IsUserLoggedIn, weather.SetIsZomanToContextMiddleware, weatherUser.RequestBetaAccess) // API for requesting beta access
	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/user/create_push_config")
	onlyZomatoOriginAllowedRoutes.POST("/v0/user/create_push_config", auth.IsUserLoggedIn, weather.SetIsZomanToContextMiddleware, weatherUser.CreatePushConfig) // API for creating webhook
	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/user/update_push_config")
	onlyZomatoOriginAllowedRoutes.POST("/v0/user/update_push_config", auth.IsUserLoggedIn, weather.SetIsZomanToContextMiddleware, weatherUser.UpdatePushConfig) // API for updating webhook
	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/user/get_push_config")
	onlyZomatoOriginAllowedRoutes.GET("/v0/user/get_push_config", auth.IsUserLoggedIn, weather.SetIsZomanToContextMiddleware, weatherUser.GetPushConfig) // API for getting webhook

	// WeatherUnion Internal Dashboard API Routes
	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/dash/get_beta_users")
	onlyZomatoOriginAllowedRoutes.GET("/v0/dash/get_beta_users", auth.IsUserLoggedIn, auth.IsZoman, weatherUser.GetBetaUsersForStatus) // API for getting beta users
	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/dash/update_user_beta_status")
	onlyZomatoOriginAllowedRoutes.POST("/v0/dash/update_user_beta_status", auth.IsUserLoggedIn, auth.IsZoman, weatherUser.UpdateUserBetaStatus) // API for updating user beta status
	onlyZomatoOriginAllowedRoutes.OPTIONS(("v0/dash/update_user_push_config"))
	onlyZomatoOriginAllowedRoutes.POST("/v0/dash/update_user_push_config", auth.IsUserLoggedIn, auth.IsZoman, weatherUser.UpdateUserPushConfigAdmin) // API for updating user push config
	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/dash/update_user_type")
	onlyZomatoOriginAllowedRoutes.POST("/v0/dash/update_user_type", auth.IsUserLoggedIn, auth.IsZoman, weatherUser.UpdateUserTypeAdmin) // API for updating user type

	// prediction routes
	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/prediction/heatmap_metadata")
	onlyZomatoOriginAllowedRoutes.GET("/v0/prediction/heatmap_metadata", auth.IsUserLoggedIn, auth.IsZoman, weatherPrediction.GetPredictionHeatmapMetadata)

	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/prediction/bounding_box")
	onlyZomatoOriginAllowedRoutes.POST("/v0/prediction/bounding_box", auth.IsUserLoggedIn, auth.IsZoman, weatherPrediction.GetPredictionDataBoundingBox)

	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/prediction/all_cities")
	onlyZomatoOriginAllowedRoutes.GET("/v0/prediction/all_cities", auth.IsUserLoggedIn, auth.IsZoman, weatherPrediction.GetPredictionDataForAllCities)

	// vendor dashboard
	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/vendor/devices")
	onlyZomatoOriginAllowedRoutes.POST("/v0/vendor/devices", auth.IsUserLoggedIn, weather.SetIsZomanToContextMiddleware, weatherStation.GetAllWeatherDevices)
	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/vendor/pings")
	onlyZomatoOriginAllowedRoutes.POST("/v0/vendor/pings", auth.IsUserLoggedIn, weatherStation.GetPingsForDevice)
	onlyZomatoOriginAllowedRoutes.OPTIONS("/v0/vendor/ping_counts")
	onlyZomatoOriginAllowedRoutes.POST("/v0/vendor/ping_counts", auth.IsUserLoggedIn, weatherStation.GetPingCountForDevices)
}

func setUpDiningRestaurantRoutes(r *gin.RouterGroup) {
	r.POST("/catalogue/ar", auth.IsUserLoggedIn, ar.GetARCatalogue)
}

func setupEvDashboardDriverServiceRoutes(ctx context.Context, r *gin.RouterGroup) {

	generate_presign_url_for_image := "generate_presign_url_for_image"
	r.POST("/images/:category/upload-image", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, generate_presign_url_for_image), middlewares.FleetPartnerRateLimit(ctx, generate_presign_url_for_image), ev_vendor_dashboard.GeneratePreSignedUrlForImage)

	rider_doc_list := "rider_doc_list"
	r.GET("/rider-doc-list", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, rider_doc_list), middlewares.FleetPartnerRateLimit(ctx, rider_doc_list), ev_vendor_dashboard.GetRiderDocList)

	ev_battery_types := "ev_battery_types"
	r.GET("/vehicle/battery-types", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, ev_battery_types), middlewares.FleetPartnerRateLimit(ctx, ev_battery_types), ev_vendor_dashboard.GetBatterytypes)

	get_zones := "get_zones"
	r.GET("/zones", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_zones), middlewares.FleetPartnerRateLimit(ctx, get_zones), ev_vendor_dashboard.GetZones)

	get_cities := "get_cities"
	r.GET("/cities", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_cities), middlewares.FleetPartnerRateLimit(ctx, get_cities), ev_vendor_dashboard.GetAllCities)

	generate_otp := "generate_otp"
	r.POST("/otp/generate", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, generate_otp), middlewares.FleetPartnerRateLimit(ctx, generate_otp), ev_vendor_dashboard.GenerateOtp)

	get_tags := "get_tags"
	r.GET("/tags", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_tags), middlewares.FleetPartnerRateLimit(ctx, get_tags), ev_vendor_dashboard.GetTagsList)

	get_vehicle_carrier_types := "get_vehicle_carrier_types"
	r.GET("/vehicle/carrier-types", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_vehicle_carrier_types), middlewares.FleetPartnerRateLimit(ctx, get_vehicle_carrier_types), ev_vendor_dashboard.GetAllCarriers)

	// rider metrics for vendor
	generate_rider_metrics_report := "generate_rider_metrics_report"
	r.POST("/:vendor_id/rider-metrics/reports/generate", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, generate_rider_metrics_report), middlewares.FleetPartnerRateLimit(ctx, generate_rider_metrics_report), ev_vendor_dashboard.GenerateRiderMetricsReport)

	// rider metrics for vendor
	get_rider_metrics_report := "get_rider_metrics_report"
	r.POST("/:vendor_id/rider-metrics/reports/get", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_rider_metrics_report), middlewares.FleetPartnerRateLimit(ctx, get_rider_metrics_report), ev_vendor_dashboard.GetRiderMetricsReport)

	// vendor apis
	get_all_vendors := "get_all_vendors"
	r.GET("/list", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_all_vendors), middlewares.FleetPartnerRateLimit(ctx, get_all_vendors), ev_vendor_dashboard.FetchAllVendors)

	get_vendor_profile := "get_vendor_profile"
	r.GET("/:vendor_id/profile", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_vendor_profile), middlewares.FleetPartnerRateLimit(ctx, get_vendor_profile), ev_vendor_dashboard.GetVendorProfile)

	create_vendor := "create_vendor"
	r.POST("/vendors/create", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, create_vendor), middlewares.FleetPartnerRateLimit(ctx, create_vendor), ev_vendor_dashboard.CreateVendors)

	update_vendor := "update_vendor"
	r.POST("/vendors/update", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, update_vendor), middlewares.FleetPartnerRateLimit(ctx, update_vendor), ev_vendor_dashboard.UpdateVendor)

	// vendor user apis
	get_users_for_vendor := "get_users_for_vendor"
	r.GET("/:vendor_id/user", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_users_for_vendor), middlewares.FleetPartnerRateLimit(ctx, get_users_for_vendor), ev_vendor_dashboard.FetchAllUsersForVendor)

	create_vendor_user := "create_vendor_user"
	r.POST("/:vendor_id/user/create", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, create_vendor_user), middlewares.FleetPartnerRateLimit(ctx, create_vendor_user), ev_vendor_dashboard.CreateUserForVendor)

	update_vendor_user := "update_vendor_user"
	r.POST("/:vendor_id/user/update", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, update_vendor_user), middlewares.FleetPartnerRateLimit(ctx, update_vendor_user), ev_vendor_dashboard.UpdateUserForVendor)

	get_user_profile_by_id := "get_user_profile_by_id"
	r.GET("/user/profile", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_user_profile_by_id), middlewares.FleetPartnerRateLimit(ctx, get_user_profile_by_id), ev_vendor_dashboard.GetUserProfileById)

	// vehicles apis
	// TODO: Deprecate this
	get_all_vehicles := "get_all_vehicles"
	r.GET("/vehicle/list", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_all_vehicles), middlewares.FleetPartnerRateLimit(ctx, get_all_vehicles), ev_vendor_dashboard.FetchAllMakeModels)

	// TODO: Deprecate this
	get_vehicle_by_id := "get_vehicle_by_id"
	r.GET("/vehicle", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_vehicle_by_id), middlewares.FleetPartnerRateLimit(ctx, get_vehicle_by_id), ev_vendor_dashboard.GetMakeModelById)

	// TODO: Deprecate this
	create_vehicle := "create_vehicle"
	r.POST("/vehicle/create", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, create_vehicle), middlewares.FleetPartnerRateLimit(ctx, create_vehicle), ev_vendor_dashboard.CreateMakeModels)

	// TODO: Deprecate this
	update_vehicle := "update_vehicle"
	r.POST("/vehicle/update", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, update_vehicle), middlewares.FleetPartnerRateLimit(ctx, update_vehicle), ev_vendor_dashboard.UpdateMakeModel)

	// stores apis
	get_store_for_vendor := "get_store_for_vendor"
	r.GET("/:vendor_id/store", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_store_for_vendor), middlewares.FleetPartnerRateLimit(ctx, get_store_for_vendor), ev_vendor_dashboard.GetStoresForVendor)

	create_store := "create_store"
	r.POST("/:vendor_id/store/create", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, create_store), middlewares.FleetPartnerRateLimit(ctx, create_store), ev_vendor_dashboard.CreateStores)

	update_store := "update_store"
	r.POST("/:vendor_id/store/update", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, update_store), middlewares.FleetPartnerRateLimit(ctx, update_store), ev_vendor_dashboard.UpdateStore)

	// inventory apis
	get_inventory_for_vendor := "get_inventory_for_vendor"
	r.GET("/:vendor_id/inventory", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_inventory_for_vendor), middlewares.FleetPartnerRateLimit(ctx, get_inventory_for_vendor), ev_vendor_dashboard.GetInventoryForVendorWithFilters)

	create_inventory := "create_inventory"
	r.POST("/:vendor_id/inventory/create", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, create_inventory), middlewares.FleetPartnerRateLimit(ctx, create_inventory), ev_vendor_dashboard.CreateVendorInventory)

	update_inventory := "update_inventory"
	r.POST("/:vendor_id/inventory/update", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, update_inventory), middlewares.FleetPartnerRateLimit(ctx, update_inventory), ev_vendor_dashboard.UpdateVendorInventory)

	// leads api
	get_leads := "get_leads"
	r.GET("/:vendor_id/leads", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_leads), middlewares.FleetPartnerRateLimit(ctx, get_leads), ev_vendor_dashboard.GetEvLeadList)

	get_lead_status_map := "get_lead_status_map"
	r.GET("/leads/status-map", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_lead_status_map), middlewares.FleetPartnerRateLimit(ctx, get_lead_status_map), ev_vendor_dashboard.GetEvLeadStateMap)

	update_lead := "update_lead"
	r.POST("/:vendor_id/leads", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, update_lead), middlewares.FleetPartnerRateLimit(ctx, update_lead), ev_vendor_dashboard.UpdateEvLead)

	// rented vehicles apis
	get_rented_vehicles := "get_rented_vehicles"
	r.POST("/vendor/fetch-rented-ev-vehicles", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_rented_vehicles), middlewares.FleetPartnerRateLimit(ctx, get_rented_vehicles), ev_vendor_dashboard.GetRentedVehiclesForVendor)

	vehicle_handover_initiate := "vehicle_handover_initiate"
	r.POST("/vehicle/handover/initiate", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, vehicle_handover_initiate), middlewares.FleetPartnerRateLimit(ctx, vehicle_handover_initiate), ev_vendor_dashboard.InitiateRentedVehicleHandover)

	vehicle_handover_complete := "vehicle_handover_complete"
	r.POST("/vehicle/handover/complete", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, vehicle_handover_complete), middlewares.FleetPartnerRateLimit(ctx, vehicle_handover_complete), ev_vendor_dashboard.CompleteRentedVehicleHandover)

	vehicle_surrender_initiate := "vehicle_surrender_initiate"
	r.POST("/vehicle/surrender/initiate", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, vehicle_surrender_initiate), middlewares.FleetPartnerRateLimit(ctx, vehicle_surrender_initiate), ev_vendor_dashboard.InitiateRentedVehicleSurrender)

	vehicle_surrender_complete := "vehicle_surrender_complete"
	r.POST("/vehicle/surrender/complete", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, vehicle_surrender_complete), middlewares.FleetPartnerRateLimit(ctx, vehicle_surrender_complete), ev_vendor_dashboard.CompleteRentedVehicleSurrender)

	generate_csv_presign_url := "generate_csv_presign_url"
	r.POST("/rented-vehicle/generate/pre-signed-url", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, generate_csv_presign_url), middlewares.FleetPartnerRateLimit(ctx, generate_csv_presign_url), ev_vendor_dashboard.GeneratePreSignUrl)

	ack_rental_csv_upload := "ack_rental_csv_upload"
	r.POST("/rented-vehicle/acknowledge/rental-csv-upload", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, ack_rental_csv_upload), middlewares.FleetPartnerRateLimit(ctx, ack_rental_csv_upload), ev_vendor_dashboard.ProcessBulkRentalRentedVehicle)

	ack_zsp_csv_upload := "ack_zsp_csv_upload"
	r.POST("/rented-vehicle/acknowledge/zsp-csv-upload", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, ack_zsp_csv_upload), middlewares.FleetPartnerRateLimit(ctx, ack_zsp_csv_upload), ev_vendor_dashboard.ProcessBulkZspRentedVehicle)

	// Vehicle apis
	vehicle_csv_upload_ack := "vehicle_csv_upload_ack"
	r.OPTIONS("/vehicle/csv-upload/acknowledge")
	r.POST("/vehicle/csv-upload/acknowledge", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, vehicle_csv_upload_ack), middlewares.FleetPartnerRateLimit(ctx, vehicle_csv_upload_ack), ev_vendor_dashboard.ProcessVehicleCSVUpload)

	get_vehicle := "get_vehicle"
	r.OPTIONS("/:vendor_id/vehicle")
	r.GET("/:vendor_id/vehicle", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_vehicle), middlewares.FleetPartnerRateLimit(ctx, get_vehicle), ev_vendor_dashboard.GetVehiclesForVendorWithFilters)

	// Make Model apis
	get_all_make_models := "get_all_make_models"
	r.OPTIONS("/make_model/list")
	r.GET("/make_model/list", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_all_make_models), middlewares.FleetPartnerRateLimit(ctx, get_all_make_models), ev_vendor_dashboard.FetchAllMakeModels)

	get_make_model_by_id := "get_make_model_by_id"
	r.OPTIONS("/make_model")
	r.GET("/make_model", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_make_model_by_id), middlewares.FleetPartnerRateLimit(ctx, get_make_model_by_id), ev_vendor_dashboard.GetMakeModelById)

	create_make_model := "create_make_model"
	r.OPTIONS("/make_model/create")
	r.POST("/make_model/create", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, create_make_model), middlewares.FleetPartnerRateLimit(ctx, create_make_model), ev_vendor_dashboard.CreateMakeModels)

	update_make_model := "update_make_model"
	r.OPTIONS("/make_model/update")
	r.POST("/make_model/update", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, update_make_model), middlewares.FleetPartnerRateLimit(ctx, update_make_model), ev_vendor_dashboard.UpdateMakeModel)

	// get driver license number
	get_driver_license_number := "get_driver_license_number"
	r.OPTIONS("/driver/license-number")
	r.GET("/driver/license-number", auth.IsUserLoggedIn, middlewares.FleetPartnerKillSwitch(ctx, get_driver_license_number), middlewares.FleetPartnerRateLimit(ctx, get_driver_license_number), ev_vendor_dashboard.GetDriverLicenseNumber)
}

func setupDeliveryLeadsPartnerLMSRoutes(ctx context.Context, r *gin.RouterGroup) {
	// csrf check
	env := config.GetString(ctx, "service.env")
	if env != "dev" {
		r.Use(middlewares.CheckCSRF())
	}
	// user-management routes
	onboard_user := "onboard_user"
	r.POST("/users/onboard", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, onboard_user), delivery_leads_partner_dashboard.OnboardUser)

	fetch_user_by_phone_no := "fetch_user_by_phone_no"
	r.GET("/users/fetch_by_phone", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, fetch_user_by_phone_no), delivery_leads_partner_dashboard.FetchUserByPhone)

	fetch_user_profile := "fetch_user_profile"
	r.GET("/users/fetch_profile", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, fetch_user_profile), delivery_leads_partner_dashboard.FetchUserProfile)

	list_users := "list_users"
	r.GET("/users/list", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, list_users), delivery_leads_partner_dashboard.ListUsersUnderUserId)

	update_user := "update_user"
	r.POST("/users/update", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, update_user), delivery_leads_partner_dashboard.UpdateUser)

	get_required_docs := "get_required_docs"
	r.GET("/users/qc_docs/list", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, get_required_docs), delivery_leads_partner_dashboard.GetRequiredDocs)

	generate_qc_doc_pre_sign_url := "generate_qc_doc_pre_sign_url"
	r.POST("/users/qc_docs/generate/pre_signed_url", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, generate_qc_doc_pre_sign_url), delivery_leads_partner_dashboard.GenerateUploadUrls)

	submit_qc_docs := "submit_qc_docs"
	r.POST("/users/qc_docs/upload_ack", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, submit_qc_docs), delivery_leads_partner_dashboard.SubmitUploadedDocs)

	//lead-management-routes
	generate_bulk_leads_upload_csv_presign_url := "generate_bulk_leads_upload_csv_presign_url"
	r.POST("/bulk_upload_leads/generate/pre_signed_url", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, generate_bulk_leads_upload_csv_presign_url), delivery_leads_partner_dashboard.GeneratePreSignUrlForLeadsUpload)

	r.POST("/bulk_upload_leads/generate/bulkops_pre_signed_url", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, generate_bulk_leads_upload_csv_presign_url), delivery_leads_partner_dashboard.GeneratePreSignUrlForLeadsUploadViaBulkOps)

	bulk_leads_upload_csv_ack := "bulk_leads_upload_csv_ack"
	r.POST("/bulk_upload_leads/acknowledge", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, bulk_leads_upload_csv_ack), delivery_leads_partner_dashboard.ProcessBulkLeadsUpload)

	r.POST("/bulk_upload_leads/acknowledgeV2", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, bulk_leads_upload_csv_ack), delivery_leads_partner_dashboard.ProcessBulkLeadsUploadViaBulkOps)

	fetch_bulk_upload_history := "fetch_bulk_upload_history"
	r.POST("/bulk_upload_leads/history", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, fetch_bulk_upload_history), delivery_leads_partner_dashboard.GetBulkUploadHistory)

	fetch_error_file_via_bulkops := "fetch_error_file_via_bulkops"
	r.GET("/bulk_upload_leads/error_file", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, fetch_error_file_via_bulkops), delivery_leads_partner_dashboard.GetErrorFileFromBulkOps)

	single_lead_upload := "single_lead_upload"
	r.POST("/lead/upload", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, single_lead_upload), delivery_leads_partner_dashboard.SingleLeadUpload)

	generate_lead_report := "generate_lead_report"
	r.POST("/users/generate_lead_report", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, generate_lead_report), delivery_leads_partner_dashboard.GenerateLeadReport)

	get_user_lead_reports := "lead_reports"
	r.GET("/users/lead_reports", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, get_user_lead_reports), delivery_leads_partner_dashboard.ListUserLeadReports)

	get_report_types := "get_report_types"
	r.GET("/lead/get_report_types", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, get_report_types), delivery_leads_partner_dashboard.GetReportTypes)

	get_carriers := "get_carriers"
	r.GET("/lead/get_carriers", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, get_carriers), delivery_leads_partner_dashboard.GetCarriers)

	get_businesses := "get_businesses"
	r.GET("/lead/get_businesses", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, get_businesses), delivery_leads_partner_dashboard.GetBusinesses)

	get_cities := "get_cities"
	r.GET("/lead/get_cities", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, get_cities), delivery_leads_partner_dashboard.GetAllCities)

	get_tags := "get_tags"
	r.GET("/users/get_tags", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, get_tags), delivery_leads_partner_dashboard.GetTagsList)

	generate_b2b_credentials := "generate_b2b_credentials"
	r.POST("/users/generate_b2b_credentials", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, generate_b2b_credentials), delivery_leads_partner_dashboard.GenerateB2BCredentials)

	invalidate_b2b_credentials := "invalidate_b2b_credentials"
	r.POST("/users/invalidate_b2b_credentials", auth.IsUserLoggedIn, middlewares.DeliveryLeadsPartnerDashboardKillSwitch(ctx, invalidate_b2b_credentials), delivery_leads_partner_dashboard.InvalidateB2BCredentials)
}

func setupXtremeDashboardRoutes(r *gin.RouterGroup) {
	userController := xtremeUser.NewController()
	orderController := xtremeOrder.NewController()
	insightsController := xtremeInsights.NewController()

	r.OPTIONS("/user/is_registered")
	r.GET("/user/is_registered", auth.IsUserLoggedIn, userController.IsRegisteredUser)

	r.OPTIONS("/user/profile")
	r.GET("/user/profile", auth.IsUserLoggedIn, userController.GetUserProfile)

	r.OPTIONS("/user/create")
	r.POST("/user/create", auth.IsUserLoggedIn, userController.CreateUser)

	r.OPTIONS("/user/update")
	r.POST("/user/update", auth.IsUserLoggedIn, userController.UpdateUser)

	r.OPTIONS("/user/delete")
	r.POST("/user/delete", auth.IsUserLoggedIn, userController.DeleteUser)

	r.OPTIONS("/users")
	r.GET("/users", auth.IsUserLoggedIn, userController.FetchUsers)

	r.OPTIONS("/merchant/update")
	r.POST("/merchant/update", auth.IsUserLoggedIn, userController.UpdateMerchant)

	r.OPTIONS("/orders")
	r.GET("/orders", auth.IsUserLoggedIn, orderController.GetOrders)

	r.OPTIONS("/order/details")
	r.GET("/order/details", auth.IsUserLoggedIn, orderController.GetOrderDetails)

	r.OPTIONS("/order/call_masking")
	r.GET("/order/call_masking", auth.IsUserLoggedIn, orderController.GetOrderMaskedNumberDetails)

	r.OPTIONS("/create_ticket")
	r.POST("/create_ticket", auth.IsUserLoggedIn, orderController.CreateTicket)

	r.OPTIONS("/order_summary")
	r.POST("/order_summary", auth.IsUserLoggedIn, insightsController.GetOrderSummary)

	r.OPTIONS("/order_trend")
	r.POST("/order_trend", auth.IsUserLoggedIn, insightsController.GetOrderTrend)

	r.OPTIONS("/wow_comparison")
	r.POST("/wow_comparison", auth.IsUserLoggedIn, insightsController.GetWoWComparison)
}

func setupEvDashboardDriverServiceExternalRoutes(ctx context.Context, r *gin.RouterGroup) {

	vehicles_ev_models := "vehicles_ev_models"
	vehicles_handover_initiate := "vehicles_handover_initiate"
	vehicles_handover_complete := "vehicles_handover_complete"
	vehicles_surrender_initiate := "vehicles_surrender_initiate"
	vehicles_surrender_complete := "vehicles_surrender_complete"
	rider_validate := "rider_validate"
	lead_status_update := "lead_status_update"

	r.OPTIONS("/vehicles/ev/models")
	r.GET("/vehicles/ev/models", middlewares.FleetPartnerExternalKillSwitch(ctx, vehicles_ev_models), middlewares.FleetPartnerExternalRateLimit(ctx, vehicles_ev_models), ev_vendor_dashboard.VerifyEvVendorApiKey(), ev_vendor_dashboard.GetAllEvVehicles)

	r.OPTIONS("/vehicle/handover/initiate")
	r.POST("/vehicle/handover/initiate", middlewares.FleetPartnerExternalKillSwitch(ctx, vehicles_handover_initiate), middlewares.FleetPartnerExternalRateLimit(ctx, vehicles_handover_initiate), ev_vendor_dashboard.VerifyEvVendorApiKey(), ev_vendor_dashboard.InitiateExternalVehicleHandover)

	r.OPTIONS("/vehicle/handover/complete")
	r.POST("/vehicle/handover/complete", middlewares.FleetPartnerExternalKillSwitch(ctx, vehicles_handover_complete), middlewares.FleetPartnerExternalRateLimit(ctx, vehicles_handover_complete), ev_vendor_dashboard.VerifyEvVendorApiKey(), ev_vendor_dashboard.CreateExternalRentedVehicle)

	r.OPTIONS("/vehicle/surrender/initiate")
	r.POST("/vehicle/surrender/initiate", middlewares.FleetPartnerExternalKillSwitch(ctx, vehicles_surrender_initiate), middlewares.FleetPartnerExternalRateLimit(ctx, vehicles_surrender_initiate), ev_vendor_dashboard.VerifyEvVendorApiKey(), ev_vendor_dashboard.InitializeExternalVehicleSurrender)

	r.OPTIONS("/vehicle/surrender/complete")
	r.POST("/vehicle/surrender/complete", middlewares.FleetPartnerExternalKillSwitch(ctx, vehicles_surrender_complete), middlewares.FleetPartnerExternalRateLimit(ctx, vehicles_surrender_complete), ev_vendor_dashboard.VerifyEvVendorApiKey(), ev_vendor_dashboard.SurrenderExternalRentedVehicle)

	r.OPTIONS("/rider/validate")
	r.POST("/rider/validate", middlewares.FleetPartnerExternalKillSwitch(ctx, rider_validate), middlewares.FleetPartnerExternalRateLimit(ctx, rider_validate), ev_vendor_dashboard.VerifyEvVendorApiKey(), ev_vendor_dashboard.ValidateRider)

	r.OPTIONS("/leads/status-update")
	r.POST("/leads/status-update", middlewares.FleetPartnerExternalKillSwitch(ctx, lead_status_update), middlewares.FleetPartnerExternalRateLimit(ctx, lead_status_update), ev_vendor_dashboard.VerifyEvVendorApiKey(), ev_vendor_dashboard.UpdateSharedLeadStatus)
}

func setupEvDashboardDriverServiceExternalRoutesV2(ctx context.Context, r *gin.RouterGroup) {

	vehicles_ev_models_v2 := "vehicles_ev_models_v2"
	vehicles_handover_initiate_v2 := "vehicles_handover_initiate_v2"
	vehicles_handover_complete_v2 := "vehicles_handover_complete_v2"
	vehicles_surrender_initiate_v2 := "vehicles_surrender_initiate_v2"
	vehicles_surrender_complete_v2 := "vehicles_surrender_complete_v2"
	rider_validate_v2 := "rider_validate_v2"
	lead_status_update_v2 := "lead_status_update_v2"

	r.OPTIONS("/vehicles/ev/models")
	r.GET("/vehicles/ev/models", middlewares.FleetPartnerExternalKillSwitch(ctx, vehicles_ev_models_v2), middlewares.FleetPartnerExternalRateLimit(ctx, vehicles_ev_models_v2), ev_vendor_dashboard.VerifyEvVendorApiKey(), ev_vendor_dashboard.GetAllEvVehiclesV2)

	r.OPTIONS("/vehicle/handover/initiate")
	r.POST("/vehicle/handover/initiate", middlewares.FleetPartnerExternalKillSwitch(ctx, vehicles_handover_initiate_v2), middlewares.FleetPartnerExternalRateLimit(ctx, vehicles_handover_initiate_v2), ev_vendor_dashboard.VerifyEvVendorApiKey(), ev_vendor_dashboard.InitiateExternalVehicleHandoverV2)

	r.OPTIONS("/vehicle/handover/complete")
	r.POST("/vehicle/handover/complete", middlewares.FleetPartnerExternalKillSwitch(ctx, vehicles_handover_complete_v2), middlewares.FleetPartnerExternalRateLimit(ctx, vehicles_handover_complete_v2), ev_vendor_dashboard.VerifyEvVendorApiKey(), ev_vendor_dashboard.CreateExternalRentedVehicleV2)

	r.OPTIONS("/vehicle/surrender/initiate")
	r.POST("/vehicle/surrender/initiate", middlewares.FleetPartnerExternalKillSwitch(ctx, vehicles_surrender_initiate_v2), middlewares.FleetPartnerExternalRateLimit(ctx, vehicles_surrender_initiate_v2), ev_vendor_dashboard.VerifyEvVendorApiKey(), ev_vendor_dashboard.InitializeExternalVehicleSurrenderV2)

	r.OPTIONS("/vehicle/surrender/complete")
	r.POST("/vehicle/surrender/complete", middlewares.FleetPartnerExternalKillSwitch(ctx, vehicles_surrender_complete_v2), middlewares.FleetPartnerExternalRateLimit(ctx, vehicles_surrender_complete_v2), ev_vendor_dashboard.VerifyEvVendorApiKey(), ev_vendor_dashboard.SurrenderExternalRentedVehicleV2)

	r.OPTIONS("/rider/validate")
	r.POST("/rider/validate", middlewares.FleetPartnerExternalKillSwitch(ctx, rider_validate_v2), middlewares.FleetPartnerExternalRateLimit(ctx, rider_validate_v2), ev_vendor_dashboard.VerifyEvVendorApiKey(), ev_vendor_dashboard.ValidateRiderV2)

	r.OPTIONS("/leads/status-update")
	r.POST("/leads/status-update", middlewares.FleetPartnerExternalKillSwitch(ctx, lead_status_update_v2), middlewares.FleetPartnerExternalRateLimit(ctx, lead_status_update_v2), ev_vendor_dashboard.VerifyEvVendorApiKey(), ev_vendor_dashboard.UpdateSharedLeadStatusV2)
}

func setupExotelHotlineRoutes(r *gin.RouterGroup) {
	r.OPTIONS("/hotline/connect")
	r.GET("/hotline/connect", hotline.HotlineCallConnect)
}

func setupOrderPlannerRoutes(r *gin.RouterGroup) {
	r.POST("/recommendation-page", auth.IsUserLoggedIn, op.GetRecommendationPage)
	r.POST("/recent-searches", auth.IsUserLoggedIn, op.GetRecentSearches)
}

func setupMerchantBusinessDashboardRoutes(r *gin.RouterGroup) {
	r.OPTIONS("/payment_hash")
	r.POST("/payment_hash", pms.IsPaymentUserTokenValid, pms.GetPaymentHash)

	r.OPTIONS("/payment_access_token")
	r.POST("/payment_access_token", pms.IsPaymentUserTokenValid, pms.GetPaymentAccessToken)

	r.OPTIONS("/user_auth_token")
	r.POST("/user_auth_token", pms.IsPaymentTokenValid, pms.GeneratePaymentsUserAuthToken)

	r.OPTIONS("/payment_order_detail")
	r.POST("/payment_order_detail", pms.IsPaymentTokenValid, pms.GetPaymentOrderDetails)
}

func setupConfigRoutes(r *gin.RouterGroup) {
	r.GET("/priority_tracking_events", configHandlers.GetPriorityEventsConfig) // API for getting priority events config
}

func setupEternalFormServiceRoutes(ctx context.Context, r *gin.RouterGroup) {
	efsClientGroup := r.Use(auth.IsUserLoggedIn, efsMiddleware.IsUserWhitelisted)

	// NOTE: This route value is used in FormPreviewLink
	efsClientGroup.GET("/forms", efsMiddleware.KillSwitch(ctx, "client_list_user_forms"), efsMiddleware.RateLimit(ctx, "client_list_user_forms"), efsClient.ClientListUserForms)
	efsClientGroup.GET("/form/:form_id", efsMiddleware.KillSwitch(ctx, "get_client_form"), efsMiddleware.RateLimit(ctx, "get_client_form"), efsClient.GetClientForm)
	efsClientGroup.POST("/form/generate-upload-urls", efsMiddleware.KillSwitch(ctx, "generate_file_upload_urls"), efsMiddleware.RateLimit(ctx, "generate_file_upload_urls"), efsClient.GenerateFileUploadURLs)
	efsClientGroup.POST("/form/:form_id/submit", efsMiddleware.KillSwitch(ctx, "submit_form"), efsMiddleware.RateLimit(ctx, "submit_form"), efsClient.SubmitForm)
}

func setupUserLocationRoutes(r *gin.RouterGroup) {
	r.POST("/search", auth.IsUserLoggedIn, location_search.GetLocationSearchHandler)
	r.POST("/selection", auth.IsUserLoggedIn, location_selection.UserLocationSelectionHandler)
	r.POST("/details", auth.IsUserLoggedIn, location_detail.GetLocationDetailsHandler)
}
