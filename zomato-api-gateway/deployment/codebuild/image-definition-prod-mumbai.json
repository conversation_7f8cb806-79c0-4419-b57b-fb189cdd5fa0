{"services": [{"containers": [{"image": "<container-image>", "name": "prod-api-gateway-v2-container", "ssmParameterNamePrefix": "ZOMATO_API_GATEWAY_", "environmentVariables": {"DD_VERSION": "<version>", "DD_TRACE_REPORT_HOSTNAME": "true"}, "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}], "ssmParameterPathPrefix": "/zservices/api-gateway-v2/prod/", "ecsServiceName": "prod-api-gateway-v2", "ecsClusterName": "prodaps1"}], "serviceName": "zomato-api-gateway", "otelCollectorNamespace": "", "appconfigEnvironment": "prod", "disableConfigInjection": true, "enableGlobalSreAppconfig": true, "enableGlobalDPAppconfig": true}