version: 0.2

env:
  variables:
    GITHUB_REPO_NAME: "zomato-api-gateway"
    ECR_DEV_REPOSITORY_NAME: "912841491139.dkr.ecr.ap-south-1.amazonaws.com/services/zomato-api-gateway"
    ECR_STAG_REPOSITORY_NAME: "931301707529.dkr.ecr.ap-southeast-1.amazonaws.com/services/zomato-api-gateway"
    ECR_STAG_MUMBAI_REPOSITORY_NAME: "931301707529.dkr.ecr.ap-south-1.amazonaws.com/services/zomato-api-gateway"
    DOCKER_BUILDKIT: 1 

phases:
  install:
    runtime-versions:
      docker: 18
    commands:
      - echo "Starting"

  pre_build:
    commands:
      - | #find the release tag. DO NOT REMOVE the '|' in front of this line else the below line gets parsed as key: value because of colon_space
        export IMAGE_TAG=$(curl -s -H "Authorization: token $GITHUB_BOT_PAC" -H "Accept: application/vnd.github.v3+json" https://api.github.com/repos/Zomato/$GITHUB_REPO_NAME/commits/$CODEBUILD_RESOLVED_SOURCE_VERSION | jq -r '.commit.tree.sha' | cut -c 1-7)

  build:
    commands:
      # assume role
      - mkdir -p $HOME/.aws
      - aws sts assume-role --role-arn arn:aws:iam::912841491139:role/services/dev-zomato-api-gateway-v1/dev-zomato-api-gateway-v1-ecr-push-role --role-session-name "RoleSession1" | jq -r '. | "export AWS_ACCESS_KEY_ID=\(.Credentials.AccessKeyId); export AWS_SECRET_ACCESS_KEY=\(.Credentials.SecretAccessKey); export AWS_SESSION_TOKEN=\(.Credentials.SessionToken); export AWS_DEFAULT_REGION=ap-southeast-1"' > /tmp/_aws_creds.source
      - . /tmp/_aws_creds.source
      - aws ecr get-login --no-include-email --region ap-south-1 | sh
      - docker pull $ECR_DEV_REPOSITORY_NAME:latest-dev
      - docker tag $ECR_DEV_REPOSITORY_NAME:latest-dev $ECR_STAG_REPOSITORY_NAME:$IMAGE_TAG
      - docker tag $ECR_DEV_REPOSITORY_NAME:latest-dev $ECR_STAG_MUMBAI_REPOSITORY_NAME:$IMAGE_TAG

      - unset AWS_SESSION_TOKEN AWS_SECRET_ACCESS_KEY AWS_ACCESS_KEY_ID

      - $(aws ecr get-login --region $AWS_DEFAULT_REGION --no-include-email)
      - docker push $ECR_STAG_REPOSITORY_NAME:$IMAGE_TAG
      - $(aws ecr get-login --region ap-south-1 --no-include-email)
      - docker push $ECR_STAG_MUMBAI_REPOSITORY_NAME:$IMAGE_TAG

  post_build:
    commands:
      - sed -i "s#<container-image>#$REPOSITORY_URI:$IMAGE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-stag.json
      - sed -i "s#<container-image>#$REPOSITORY_URI_MUMBAI:$IMAGE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-stag-mumbai.json

artifacts:
  secondary-artifacts:
    ConfigArtifacts:
      discard-paths: yes
      files:
        - "configs/config.yaml"
    ImageDefinitionArtifacts:
      discard-paths: yes
      files:
        - "deployment/codebuild/image-definition-stag.json"
    ImageDefinitionArtifacts_1:
      discard-paths: yes
      files:
        - "deployment/codebuild/image-definition-stag-mumbai.json"