version: 0.2

env:
  variables:
    GITHUB_REPO_NAME: "zomato-api-gateway"
    ECR_REPOSITORY_URI_MUMBAI: "931301707529.dkr.ecr.ap-south-1.amazonaws.com/services/zomato-api-gateway"
    DOCKER_BUILDKIT: 1 

phases:
  install:
    runtime-versions:
      docker: 18
    commands:
      - echo "Starting"

  pre_build:
    commands:
    - | #find the release tag. DO NOT REMOVE the '|' in front of this line else the below line gets parsed as key: value because of colon_space
      export IMAGE_TAG=$(curl -s -H "Authorization: token $GITHUB_BOT_PAC" -H "Accept: application/vnd.github.v3+json" https://api.github.com/repos/Zomato/$GITHUB_REPO_NAME/commits/$CODEBUILD_RESOLVED_SOURCE_VERSION | jq -r '.commit.tree.sha' | cut -c 1-7)
      export RELEASE_TAG=$(curl -s -H "Authorization: token $GITHUB_BOT_PAC" https://api.github.com/repos/Zomato/$GITHUB_REPO_NAME/releases/latest | jq -r  '.tag_name')

  build:
    commands:
      - $(aws ecr get-login --region $AWS_DEFAULT_REGION --no-include-email)
      - MANIFEST=$(aws ecr batch-get-image --repository-name $ECR_REPOSITORY_NAME_DEFAULT --image-ids imageTag=$IMAGE_TAG --query 'images[].imageManifest' --output text)
      - aws ecr put-image --repository-name $ECR_REPOSITORY_NAME_DEFAULT --image-tag $RELEASE_TAG --image-manifest "$MANIFEST"
      
      - $(aws ecr get-login --region ap-south-1 --no-include-email)
      - MANIFEST=$(aws ecr batch-get-image --region ap-south-1 --repository-name $ECR_REPOSITORY_NAME_DEFAULT --image-ids imageTag=$IMAGE_TAG --query 'images[].imageManifest' --output text)
      - aws ecr put-image --region ap-south-1 --repository-name $ECR_REPOSITORY_NAME_DEFAULT --image-tag $RELEASE_TAG --image-manifest "$MANIFEST"


  post_build:
    commands:
      - echo "machine github.com login $GITHUB_BOT_NAME password $GITHUB_BOT_PAC" > .netrc
      - sed -i "s#<container-image>#$REPOSITORY_URI:$RELEASE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-prod.json
      - sed -i "s#<container-image>#$REPOSITORY_URI_MUMBAI:$RELEASE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-prod-mumbai.json


artifacts:
  secondary-artifacts:
    ConfigArtifacts:
      discard-paths: yes
      files:
        - "configs/config.yaml"
    ImageDefinitionArtifacts:
      discard-paths: yes
      files:
        - "deployment/codebuild/image-definition-prod.json"
    ImageDefinitionArtifacts_1:
      discard-paths: yes
      files:
        - "deployment/codebuild/image-definition-prod-mumbai.json"
