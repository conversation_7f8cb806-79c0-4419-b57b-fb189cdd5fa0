{"services": [{"containers": [{"image": "<container-image>", "name": "main", "ssmParameterNamePrefix": "ZOMATO_API_GATEWAY_"}], "ssmParameterPathPrefix": "/zservices/zomato-api-gateway/dev/", "ecsServiceName": "dev-zomato-api-gateway", "ecsClusterName": "dev-cluster"}], "appconfigEnvironment": "dev", "disableConfigInjection": true, "serviceName": "zomato-api-gateway", "otelCollectorNamespace": "", "enableGlobalSreAppconfig": true, "enableGlobalDPAppconfig": true}