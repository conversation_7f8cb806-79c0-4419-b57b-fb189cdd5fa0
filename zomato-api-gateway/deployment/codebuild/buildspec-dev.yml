version: 0.2

env:
  variables:
    REPO_URI: "github.com/Zomato/zomato-api-gateway"
    DOCKER_BUILDKIT: 1

phases:
  install:
    runtime-versions:
      docker: 18
    commands:
      - echo "Starting"

  pre_build:
    commands:
      - echo "machine github.com login $GITHUB_BOT_NAME password $GITHUB_BOT_PAC" > .netrc
      - echo "Logging in to Amazon ECR..."
      - aws --version
      - $(aws ecr get-login --region $AWS_DEFAULT_REGION --no-include-email)

  build:
    commands:
      - echo $CODEBUILD_RESOLVED_SOURCE_VERSION
      - IMAGE_TAG=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - make build_dev
      - docker push $ECR_REPOSITORY_URI_DEFAULT:latest-dev
      - docker push $ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG

  post_build:
    commands:
      - sed -i "s#<container-image>#$ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-dev.json

artifacts:
  secondary-artifacts:
    ConfigArtifacts:
      discard-paths: yes
      files:
        - "configs/config.yaml"
    ImageDefinitionArtifacts:
      discard-paths: yes
      files:
        - "deployment/codebuild/image-definition-dev.json"
