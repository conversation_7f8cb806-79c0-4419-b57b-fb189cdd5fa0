{"account": "************", "region": "ap-southeast-1", "appconfigName": "preprod-zomato-api-gateway", "otelCollectorNamespace": "", "deploymentGroups": [{"deployStage": {"stage": "preprod"}, "services": [{"ecsClusterName": "stagingcluster", "ecsServiceName": "preprod-api-gateway-v2", "ssmParameterPathPrefix": "/zservices/api-gateway-v2/preprod/", "containers": [{"image": "<container-image>", "name": "preprod-api-gateway-v2-container", "ssmParameterNamePrefix": "ZOMATO_API_GATEWAY_", "environmentVariables": {"DD_VERSION": "<version>", "DD_TRACE_REPORT_HOSTNAME": "true"}, "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"account": "************", "region": "ap-south-1", "ecsClusterName": "stagaps1", "ecsServiceName": "preprod-api-gateway-v2", "ssmParameterPathPrefix": "/zservices/api-gateway-v2/preprod/", "containers": [{"image": "<mumbai-container-image>", "name": "preprod-api-gateway-v2-container", "ssmParameterNamePrefix": "ZOMATO_API_GATEWAY_", "environmentVariables": {"DD_VERSION": "<version>", "DD_TRACE_REPORT_HOSTNAME": "true"}, "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}]}]}