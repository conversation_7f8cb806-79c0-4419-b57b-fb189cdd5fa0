env:
  variables:
    AWS_DEFAULT_ACCOUNT_ID: "************"
    AWS_DEFAULT_REGION: "ap-southeast-1"
    GITHUB_REPO_NAME: "zomato-api-gateway"
    ECR_DEV_REPOSITORY_NAME: "************.dkr.ecr.ap-south-1.amazonaws.com/services/zomato-api-gateway"
    ECR_STAG_REPOSITORY_NAME: "************.dkr.ecr.ap-southeast-1.amazonaws.com/services/zomato-api-gateway"
    ECR_STAG_MUMBAI_REPOSITORY_NAME: "************.dkr.ecr.ap-south-1.amazonaws.com/services/zomato-api-gateway"
    DOCKER_BUILDKIT: 1 

commands:
  - echo "Starting"
  - echo "machine github.com login $GITHUB_BOT_NAME password $GITHUB_BOT_PAC" > .netrc
  - echo Logging in to Amazon ECR...

  # login to preprod ecr
  - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_DEFAULT_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com

  # login to preprod mumbai ecr
  - aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin $AWS_DEFAULT_ACCOUNT_ID.dkr.ecr.ap-south-1.amazonaws.com

  # login to dev ecr
  - aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ************.dkr.ecr.ap-south-1.amazonaws.com

  - |
    export IMAGE_TAG=$(curl -s -H "Authorization: token $GITHUB_BOT_PAC" -H "Accept: application/vnd.github.v3+json" https://api.github.com/repos/Zomato/$GITHUB_REPO_NAME/commits/$COMMIT_TAG | jq -r '.commit.tree.sha' | cut -c 1-7)
    
  # pull the image from dev ECR repo
  - docker pull $ECR_DEV_REPOSITORY_NAME:$IMAGE_TAG
  - docker tag $ECR_DEV_REPOSITORY_NAME:$IMAGE_TAG $ECR_STAG_REPOSITORY_NAME:$IMAGE_TAG
  - docker tag $ECR_DEV_REPOSITORY_NAME:$IMAGE_TAG $ECR_STAG_MUMBAI_REPOSITORY_NAME:$IMAGE_TAG

  # push image tag to stag ECR repo
  - docker push $ECR_STAG_REPOSITORY_NAME:$IMAGE_TAG
  - docker push $ECR_STAG_MUMBAI_REPOSITORY_NAME:$IMAGE_TAG
  - echo "pushed image to ECR [done]"
  
  - sed -i "s#<container-image>#$ECR_STAG_REPOSITORY_NAME:$IMAGE_TAG#" deployment/preprod/image-definition.json
  - sed -i "s#<mumbai-container-image>#$ECR_STAG_MUMBAI_REPOSITORY_NAME:$IMAGE_TAG#" deployment/preprod/image-definition.json

artifacts:
  ConfigArtifacts: 
    files:
      - configs/config.yaml
  ImageDefinitionArtifact: 
    file: deployment/preprod/image-definition.json