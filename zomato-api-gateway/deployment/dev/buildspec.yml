env:
  variables:
    ECR_REPOSITORY_URI_DEFAULT: ************.dkr.ecr.ap-south-1.amazonaws.com/services/zomato-api-gateway
    AWS_DEFAULT_ACCOUNT_ID: "************" # Required
    AWS_DEFAULT_REGION: "ap-south-1" # Required

commands:
  - echo "Starting"
  - echo "machine github.com login $GITHUB_BOT_NAME password $GITHUB_BOT_PAC" > .netrc
  - echo "Logging in to Amazon ECR..."
  - aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ************.dkr.ecr.ap-south-1.amazonaws.com

  - |
    export IMAGE_TAG=$(curl -s -H "Authorization: token $GITHUB_BOT_PAC" -H "Accept: application/vnd.github.v3+json" https://api.github.com/repos/Zomato/$GITHUB_REPO_NAME/commits/$COMMIT_TAG | jq -r '.commit.tree.sha' | cut -c 1-7)
  - echo $IMAGE_TAG

  - make build_dev
  - docker push $ECR_REPOSITORY_URI_DEFAULT:latest-dev
  - docker push $ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG
  - sed -i "s#<container-image>#$ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG#" $GITHUB_WORKSPACE/deployment/dev/image-definition.json

artifacts:
  ConfigArtifacts:
    files:
      - configs/config.yaml

  ImageDefinitionArtifact:
    file: deployment/dev/image-definition.json
