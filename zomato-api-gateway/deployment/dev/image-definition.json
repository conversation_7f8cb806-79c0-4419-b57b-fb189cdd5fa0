{"deploymentGroups": [{"deployStage": {"stage": "dev"}, "manualApproval": false, "services": [{"containers": [{"image": "<container-image>", "name": "main", "ssmParameterNamePrefix": "ZOMATO_API_GATEWAY_", "environmentVariables": {"DD_SERVICE": "zomato-api-gateway"}}], "ssmParameterPathPrefix": "/zservices/zomato-api-gateway/dev/", "ecsServiceName": "dev-zomato-api-gateway", "ecsClusterName": "dev-cluster"}]}], "account": "************", "region": "ap-south-1", "appconfigName": "dev-zomato-api-gateway", "otelCollectorNamespace": ""}