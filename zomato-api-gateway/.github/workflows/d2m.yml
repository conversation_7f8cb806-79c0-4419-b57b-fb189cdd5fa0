name: Raise PR to Master

on:
  push: 
    branches:
      - dev
 
permissions:
  id-token: write
  contents: read
  packages: read
  checks: write
  pull-requests: read

jobs:
  raise-pr:
    runs-on: [self-hosted,spot,2v,acc-infraprod,arch-x64]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::154774607397:role/github-action-auto-merge-role
          aws-region: ap-southeast-1

      - name: Get github token
        id: get-token
        run: |
          export GITHUB_TOKEN=$(aws ssm get-parameter --region ap-southeast-1 --name /sre/github/zghbot1/token --with-decryption | jq -r '.Parameter.Value')
          echo "GITHUB_TOKEN=$GITHUB_TOKEN" >> $GITHUB_ENV

      - name: Check if PR exists
        id: check-pr
        env:
          GITHUB_TOKEN: ${{ env.GITHUB_TOKEN }}
        run: |
          prs=$(gh pr list \
          --repo "$GITHUB_REPOSITORY" \
          --json baseRefName,headRefName \
          --jq '
              map(select(.baseRefName == "master" and .headRefName == "dev"))
              | length
          ')
          if [[ $prs -gt 0 ]]; then
             echo "skip=true" >> "$GITHUB_OUTPUT"
          fi
      - name: Create PR to Master
        if: steps.check-pr.outputs.skip != 'true'
        id: create-pr
        env:
          GITHUB_TOKEN: ${{ env.GITHUB_TOKEN }}
        run: |
          gh pr create --base master --head dev --title d2m --body d2m --label "skip-changelog";
