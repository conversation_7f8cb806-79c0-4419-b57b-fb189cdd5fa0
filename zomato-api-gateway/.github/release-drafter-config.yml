name-template: "v$NEXT_MINOR_VERSION"
tag-template: "v$NEXT_MINOR_VERSION"
categories:
  - title: "🚀 Features"
    labels:
      - "feature"
      - "enhancement"
  - title: "🐛 Bug Fixes"
    labels:
      - "fix"
      - "bugfix"
      - "bug"
  - title: "🧰 Maintenance"
    label: "chore"
  - title: "🔮 Crystal"
    labels:
      - "crystal"
  - title: "🛒 Cart"
    labels:
      - "cart"
  - title: "🍔 Menu"
    labels:
      - "menu"
  - title: "🌍 Location"
    labels:
      - "location"
  - title: "🎛️ User Preferences"
    labels:
      - "user-preferences"
change-template: "- $TITLE @$AUTHOR (#$NUMBER)"
change-title-escapes: '\<*_&'
version-resolver:
  major:
    labels:
      - "major"
  minor:
    labels:
      - "minor"
  patch:
    labels:
      - "patch"
  default: minor
exclude-labels:
  - "skip-changelog"
template: |
  ## New Release: v$NEXT_MINOR_VERSION

  $CHANGES

  ### Thanks to our contributors:
  $CONTRIBUTORS
