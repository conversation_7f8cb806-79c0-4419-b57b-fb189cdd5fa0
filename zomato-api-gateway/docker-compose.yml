version: "2.3"

services:
  zomato-api-gateway:
    hostname: zomato-api-gateway
    build:
      context: .
      dockerfile: Dockerfile
    container_name: zomato-api-gateway
    ports:
      - "8080:3000"
    volumes:
      - ./configs:/root/configs
    environment:
      CONFIG_SOURCE: local:default,global-config|sre,global-config|dataplatform
    depends_on:
      pubsub-kafka:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - zomato_local
      - jis-shared-net

  pubsub-kafka:
    hostname: pubsub-kafka
    image: confluentinc/cp-kafka:6.2.1
    container_name: pubsub-kafka
    depends_on:
      - zookeeper
    networks:
      - zomato_local
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nc", "-vz", "localhost", "9092"]
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 10s
    ports:
      - "29092:29092"
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://pubsub-kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost

  zookeeper:
    image: confluentinc/cp-zookeeper:6.2.1
    hostname: zookeeper
    container_name: zookeeper
    restart: unless-stopped
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - zomato_local

  statsd:
    image: "prom/statsd-exporter:v0.18.0"
    ports:
      - "9125:9125/udp"
      - "9102:9102"
    # volumes:
    #   - ./configs/statsd_mapping.yml:/tmp/statsd_mapping.yml
    # command: ["--statsd.mapping-config=/tmp/statsd_mapping.yml"]
    networks:
      - zomato_local
    container_name: zomato-api-gateway_statsd_1

  agent:
    image: datadog/agent:latest
    hostname: agent
    environment:
      - DD_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxx
      - DD_APM_ENABLED=true
      - DD_HOSTNAME=datadog-agent-host
      - DD_CONTAINER_COLLECTOR=true
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /proc/:/host/proc/:ro
      - /sys/fs/cgroup:/host/sys/fs/cgroup:ro
    expose:
      - "3000"  
    ports:
      - "8126:8126"                       # APM / Tracing port
      - "8125:8125/udp"                   # DogStatsD for custom metrics
    networks:
      - zomato_local

  # localstack:
  #   image: localstack/localstack:3.8.1
  #   hostname: localstack
  #   container_name: api-gateway-localstack
  #   ports:
  #     - 4566:4566
  #     - 8055:8055
  #   environment:
  #     - SERVICES=s3,sqs,ssm
  #     - HOSTNAME=localstack
  #     - DEFAULT_REGION=ap-south-1
  #     - DOCKER_HOST=unix:///run/podman/podman.sock
  #     - DEBUG=1
  #   restart: unless-stopped
  #   volumes:
  #     - "./docker-entrypoint-extend.sh:/etc/localstack/init/ready.d/extend.sh"
  #   healthcheck:
  #     test:
  #       - CMD
  #       - bash
  #       - -c
  #       - awslocal s3 ls
  #     interval: 10s
  #     timeout: 10s
  #     retries: 5
  #     start_period: 10s

  # otlp-collector:
  #   image: otel/opentelemetry-collector-contrib:0.109.0
  #   container_name: api-gateway-otlp-collector
  #   ports:
  #     - 4317:4317  # OTLP gRPC
  #     - 4318:4318  # OTLP HTTP
  #     - 9103:9103  # Prometheus metrics
  # clickhouse:
  #   hostname: clickhouse
  #   image: clickhouse/clickhouse-server:23.8-alpine
  #   container_name: api-gateway-clickhouse
  #   ports:
  #   - 8123:8123
  #   - 9000:9000
  #   - 9009:9009
  #   ulimits:
  #     nofile: 
  #       soft: 262144
  #       hard: 262144

networks:
  jis-shared-net:
    external: true
  zomato_local:
    name: "zomato_local_net"
