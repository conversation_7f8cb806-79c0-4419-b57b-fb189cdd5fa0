#!/bin/bash
set -eo pipefail

# Executing S3
echo 'Creating new s3 bucket'
awslocal s3 mb s3://dev-jumbo-insights-service/

# Executing SQS
echo 'Creating new sqs queues'
awslocal sqs create-queue --queue-name dev-jumbo-insights-service-trino-queue --region ap-south-1
awslocal sqs list-queues

# Executing SSM
echo 'Creating new SSM param'
awslocal ssm put-parameter \
  --name "/zservices/jumboinsightsservice/encryption/dev/AES_ZOMATO4_NUMBER_VERIFICATION" \
  --value "123" \
  --type "SecureString"  \
  --overwrite
