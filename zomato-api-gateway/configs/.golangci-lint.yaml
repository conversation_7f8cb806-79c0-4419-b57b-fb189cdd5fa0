run:
  # default concurrency is a available CPU number
  concurrency: 4

  # exit code when at least one issue was found, default is 1
  issues-exit-code: 1

  # include test files or not, default is true
  tests: true

  skip-dirs:
    - deployment
    - configs
    - test
    - (^|/)vendor($|/)
    - (^|/)third_party($|/)
    - (^|/)testdata($|/)
    - (^|/)examples($|/)
    - (^|/)Godeps($|/)
    - (^|/)builtin($|/)

  # default is true. Enables skipping of directories:
  #   vendor$, third_party$, testdata$, examples$, Godeps$, builtin$
  skip-dirs-use-default: true


linters-settings:
  # TODO: Check gci, gofumpt, gomodguard, stylecheck

  depguard:
    # https://github.com/OpenPeeDeeP/depguard
    list-type: blacklist
    include-go-root: false
    packages:
      - github.com/sirupsen/logrus
    packages-with-error-message:
      - github.com/sirupsen/logrus: "Please use github.com/Zomato/go/logger"

  dogsled:
    # checks assignments with too many blank identifiers; default is 2
    max-blank-identifiers: 2

  dupl:
    # tokens count to trigger issue
    threshold: 200

  funlen:
    lines: 300
    statements: 150

  goconst:
    # minimal length of string constant, 3 by default
    min-len: 5
    # minimal occurrences count to trigger, 3 by default
    min-occurrences: 5

  gosec:
    includes:
      - G101 # Look for hard coded credentials
      - G107 # Url provided to HTTP request as taint input
      - G108 # Profiling endpoint automatically exposed on /debug/pprof
      - G109 # Potential Integer overflow made by strconv.Atoi result conversion to int16/32
      - G110 # Potential DoS vulnerability via decompression bomb
      - G201 # SQL query construction using format string
      - G202 # SQL query construction using string concatenation 
      - G203 # Use of unescaped data in HTML templates
      - G305 # File traversal when extracting zip/tar archive
      - G402 # Look for bad TLS connection settings

    excludes:
      - G102
      - G103
      - G104
      - G106
      - G401 # Detect the usage of DES, RC4, MD5 or SHA1
      - G404 # Insecure random number source (rand)
      - G501
      - G502
      - G503
      - G504
      - G505
    exclude-generated: true

  gocritic:
    enabled-tags:
      - performance
    disabled-checks:
      - wrapperFunc
      - dupImport # https://github.com/go-critic/go-critic/issues/845

  gocyclo:
    min-complexity: 40

  golint:
    # minimal confidence for issues, default is 0.8
    min-confidence: 0.8

  gomnd:
    settings:
      mnd:
        # the list of enabled checks, see https://github.com/tommy-muehle/go-mnd/#checks for description.
        checks: argument,case,condition,operation,return,assign

  govet:
    # report about shadowed variables
    check-shadowing: true

  misspell:
    locale: UK
    ignore-words:
      - initialized
      - serialized
      - finalized
      - utilize
      - materialize
      - personalize

  lll:
    # max line length, lines longer will be reported. Default is 120.
    # '\t' is counted as 1 character by default, and can be changed with the tab-width option
    line-length: 180
    # tab width in spaces.
    tab-width: 4

  nolintlint:
    allow-leading-space: true # don't require machine-readable nolint directives (i.e. with no leading space)
    allow-unused: false # report any unused nolint directives
    require-explanation: false # don't require an explanation for nolint directives
    require-specific: false # don't require nolint directives to be specific about which linter is being skipped

  gomodguard:
    blocked:
      versions:
        - github.com/Zomato/go:
            version: "< 0.0.11"  
            reason: "github.com/Zomato/go versions below 0.0.11 are not allowed. Please update the version."
        - github.com/Zomato/go/health:
            version: "< 1.3.0"  
            reason: "github.com/Zomato/go/health versions below 1.3.0 are not allowed. Please update the version."
  
  errcheck:
    check-type-assertions: false
    check-blank: true
    exclude-functions:
      - io/ioutil.ReadFile
      - io.Copy(*bytes.Buffer)
      - io.Copy(os.Stdout)

linters:
  disable-all: true
  enable:
    - bodyclose
    - deadcode
    - depguard
    - dupl
    - errcheck
    - exhaustive
    - funlen
    - gochecknoinits
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - goimports
    - golint
    - gosec
    - gosimple
    - govet
    - ineffassign
    - lll
    - misspell
    - noctx
    - rowserrcheck
    - staticcheck
    - structcheck
    - typecheck
    - unparam
    - unused
    - varcheck
    - whitespace
    - gomodguard
    - sqlclosecheck

  # don't enable: (as suggested in golangci docs)
  # - gomnd
  # - asciicheck
  # - gochecknoglobals
  # - gocognit
  # - godot
  # - godox
  # - goerr113
  # - nestif
  # - prealloc
  # - testpackage
  # - wsl

  # disabled linters:
  # - durationcheck
  # - wastedassign
  # - nolintlint
  # - dogsled
  # - nakedret
  # - exportloopref
  # - gci
  # - gofumpt
  # - goheader
  # - gomodguard
  # - goprintffuncname
  # - nlreturn
  # - scopelint


issues:
  # # Show only new issues created after git revision `REV`
  # new-from-rev: "{{ .env.CI_BASE_COMMIT_ID }}"
  
  max-issues-per-linter: 2000
  max-same-issues: 100

  exclude-rules:
    # Exclude lll issues for long lines with go:generate
    # Exclude golint issues for "Rpc" to "RPC" conversion
    - linters:
        - lll
      source: "^//go:generate "
    - linters:
        - misspell
      text: ".*serialized.*misspelling"
    - linters:
        - golint
      text: ".*Rpc.* should be .*RPC"

    # Exclude some linters from running on tests files.
    - path: _test\.go
      linters:
        - gocyclo
        - errcheck
        - gosec
        - lll
        - funlen
        - exhaustive
        - gomnd
        - gocritic
        - goconst
        - lll
        - misspell
        - nakedret
        - dogsled
        - dupl

severity:
  # Default value is empty string.
  # Set the default severity for issues. If severity rules are defined and the issues
  # do not match or no severity is provided to the rule this will be the default
  # severity applied. Severities should match the supported severity names of the
  # selected out format.
  default-severity: warning

  # The default value is false.
  # If set to true severity-rules regular expressions become case sensitive.
  case-sensitive: false

  # When a list of severity rules are provided, severity information will be added to lint
  # issues. Severity rules have the same filtering capability as exclude rules except you
  # are allowed to specify one matcher per severity rule.
  # Only affects out formats that support setting severity information.
  # rules:
  #   - linters:
  #     - dupl
  #     severity: info
  rules:
    - linters:
      - errcheck
      - gomodguard
      - ineffassign
      severity: error
    - linters:
      - funlen
      - exhaustive
      - gomnd
      - goconst
      - gocyclo
      - lll
      - misspell
      - nakedret
      - dogsled
      - dupl
      severity: info
    - linters:
      - gocritic
      - deadcode
      - gochecknoinits
      - gofmt
      - goimports
      - gosimple
      - nolintlint
      - sqlclosecheck
      - staticcheck
      - unparam
      - unused
      - varcheck
      - whitespace
      - structcheck
      - golint
      - durationcheck
      - wastedassign
      severity: warning

service:
  golangci-lint-version: 1.30.0
  prepare: []