sre:
  global:
    kafka_clusters:
      - name: offline
        brokers:
          # use dev environemnt brokers here or your own brokers defined in docker-compose
          - zomato-kafka-1.dev.zmain.zomans.com:9092
          - zomato-kafka-2.dev.zmain.zomans.com:9092
          - zomato-kafka-3.dev.zmain.zomans.com:9092
        version: 2.0.0

      - name: online
        brokers:
          - pkc-d8owz.ap-southeast-1.aws.confluent.cloud:9092
        sasl:
          # make sure to not commit real username passwords here, security team will be furious
          username: xxxx
          password: xxxx
        version: 3.2.0

      # in dev environment we use same kafka cluster for online and o2 fullfillment topics
      - name: o2
        brokers:
          - pkc-d8owz.ap-southeast-1.aws.confluent.cloud:9092
        sasl:
          username: xxxx
          password: xxxx
        version: 3.2.0
athena:
  global:
    cost:
      enabled: true
      sampling_percentage: 100
      sampling_mod: 100
