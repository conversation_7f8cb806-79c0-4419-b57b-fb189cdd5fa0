---
kind: pipeline
name: default

steps:
  - name: mod-download
    image: &goimage "golang:1.13.4-buster"
    commands:
      - go mod download

  - name: lint
    image: *goimage
    environment:
      REVIEWDOG_GITHUB_API_TOKEN:
        from_secret: REVIEWDOG_GITHUB_API_TOKEN
    commands:
      - curl -sfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.21.0
      - curl -sfL https://raw.githubusercontent.com/reviewdog/reviewdog/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v0.9.13
      - LINT_OUTPUT="$(golangci-lint run --config=./configs/.golangci-lint.yaml --enable-all --deadline 10m  ./... || true)"
      - echo $LINT_OUTPUT | reviewdog -f=golangci-lint -diff="git diff origin/$DRONE_TARGET_BRANCH"
      - echo $LINT_OUTPUT | reviewdog -f=golangci-lint -diff="git diff origin/$DRONE_TARGET_BRANCH" -reporter=github-pr-review
    depends_on:
      - mod-download

services:
  - name: docker
    image: docker:dind
    privileged: true
    volumes:
      - name: dockersock
        path: /var/run

volumes:
  - name: dockersock
    temp: {}

trigger:
  event:
    - push
    - pull_request