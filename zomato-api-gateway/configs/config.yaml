service:
  name: dev-zomato-api-gateway
  mode: debug
  env: dev
  kuma_service_name: api-gateway
go_runtime:
  mem_limit:
    enabled: true
  metrics:
    enabled: true
  gogc: -1
listen:
  port: '8080'
  admin_domain: admin.eks.zdev.net zomato-api-gateway.eks.zdev.net zomato-api-gateway-eaa-app.eks.zdev.net accounts.eks.zdev.net zomato-api-gateway-eaa-access.eks.zdev.net api-gateway.mesh api.eks.zdev.net
  edition_domain: admin.eks.zdev.net,https://admin.eks.zdev.net,api-gateway.mesh,http://api-gateway.mesh,https://api-gateway.mesh,api.eks.zdev.net,http://api.eks.zdev.net,https://api.eks.zdev.net
cors:
  allowed_domains: https://localhost:3000 https://admin.zomans.com https://www.zomato.com https://admin.eks.zdev.net https://admin-preprod.eks.zdev.net https://admin-preprod.zomans.com https://accounts.eks.zdev.net https://service-control-dashboard.eks.zdev.net http://api-gateway.mesh https://api-gateway.mesh api-gateway.mesh http://api.eks.zdev.net https://api.eks.zdev.net api.eks.zdev.net https://fleet-partner.eks.zdev.net
csrf:
  encryption_key: q7x1j%y8PI93e$8e%dUtB6c*wkhXk&Vb
newrelic:
  key: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  enabled: 'true'
metrics:
  statsd_addr: localhost:9125
auth_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: auth-service
  skip_api_auth_call:
    allowed_api_key_values: android_api_key@zomato_android_v2 ios_api_key@zomato_ios_v2
  whitelisted_ips_operation:
    allowed_user_ids: [**********]
    restricted_api_keys: [""]
  oauth_401_flow_enabled: true
  oauth_401_logging_enabled: true
  oauth_401_flow_enabled_for_android: true
  oauth_401_flow_enabled_for_ios: true
  is_third_party_limiting_enabled: true
  extra_metadata_info_percent: 100
  delete_user_profile:
    enabled: true
  dashboard:
    bulk_upload:
      row_limit: 50
    user_details:
      enabled: true
    list_role_users:
      enabled: true
    update_user_role_mapping_v2:
      enabled: true
    update_role:
      enabled: false
      timeout: 5000ms
  onetap:
    enable_for_ios: true
    enable_for_android: true
    enable_tracking: true
    get_trusted_users:
      enabled: true
      timeout: 5000ms
    remove_trusted_user:
      enabled: true
      timeout: 5000ms
log:
  format: json
  level: debug
kafka:
  online_pubsub:
    version: 2.0.0
  offline_pubsub:
    brokers: zomato-kafka-1.dev.zmain.zomans.com:9092 zomato-kafka-2.dev.zmain.zomans.com:9092 zomato-kafka-3.dev.zmain.zomans.com:9092
    version: 2.0.0
  offline_msk:
    # offline msk is same as offline pubsub in dev env
    brokers: zomato-kafka-1.dev.zmain.zomans.com:9092 zomato-kafka-2.dev.zmain.zomans.com:9092 zomato-kafka-3.dev.zmain.zomans.com:9092
    sasl_username: ""
    sasl_password: ""
    sasl_mechanism: ""
    version: 2.0.0
  online_confluent:
    sasl_username: ""
    sasl_password: ""
    brokers: zomato-kafka-1.dev.zmain.zomans.com:9092 zomato-kafka-2.dev.zmain.zomans.com:9092 zomato-kafka-3.dev.zmain.zomans.com:9092
    version: 3.2.0
  o2_placement_fulfillment_confluent:
    sasl_username: ""
    sasl_password: ""
    brokers: zomato-kafka-1.dev.zmain.zomans.com:9092 zomato-kafka-2.dev.zmain.zomans.com:9092 zomato-kafka-3.dev.zmain.zomans.com:9092
    version: 2.0.0
  enqueue:
    enabled: true
  publishers:
    isKilled: false
    cartUIReconciliationEventsPublisher:
      clientId: cart-service
      isKilled: false
      topic: "zomato.cart-service.cart-ui-reconciliation-events"
      cluster: "offline-msk"
      publishTimeout: 1s
      numberOfRetries: 1
      ack: 2

sqs:
  publishers:
    is_killed: false
    cartImpressionEventsPublisher:
      is_killed: false
      queue_url: "https://sqs.ap-south-1.amazonaws.com/xxxxx/cart-service-cart-impression-events"

jumbo:
  brokers: zomato-kafka-1.dev.zmain.zomans.com:9092,zomato-kafka-2.dev.zmain.zomans.com:9092,zomato-kafka-3.dev.zmain.zomans.com:9092
  topic: zomato.jumbo-pipeline.offline-events-v2
  realtime_topic: logistics.jumbo-pipeline.realtime-events-v2
ephemeral_uploads:
  s3:
    bucket: dev-api-gateway-v2-ephemeral-uploads
    multipart_presign_ttl: 900
  aws:
    region: ap-south-1
s3:
  endpoint_enabled: true
  endpoint_url: http://127.0.0.1:4566
  region: ap-south-1

menu_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: menu-service
  web_menu_timeout: '5000'
  web_api_key: 3566c60f970936218b2d49202d6d17c8
  timeout: 20000
menu_mx_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: menu-service
  bulk_create_dishes:
    timeout_ms: 10000
  bulk_fetch_dishes:
    timeout_ms: 10000
  bulk_update_dishes:
    timeout_ms: 10000
  bulk_delete_dishes:
    timeout_ms: 10000
  bulk_restore_deleted_dishes:
    timeout_ms: 10000
firefly_service:
  host: "kuma-gateway.eks.zdev.net"
  port: 80
  authority_header: "ml-firefly-service"
  aibots:
    is_killed: false
  dashboard:
    whitelisted_users: [321005436, 1447354986, 262475129, 335343561, 1447354909, 1447354997]
  execute_api_plan:
    is_killed: false
    timeout_ms: 3000
moderation_tool_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: "ml-data-moderation-tool"
ai-insights-service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: "ai-insights-service"
  list_insights_rpc:
    timeout: 1000
    is_killed: false
  create_registry_rpc:
    timeout: 1000
    is_killed: false
  list_entities_rpc:
    timeout: 1000
    is_killed: false
  list_entity_properties_rpc:
    timeout: 1000
    is_killed: false
  list_versions_rpc:
    timeout: 1000
    is_killed: false
  update_active_version_rpc:
    timeout: 1000
    is_killed: false
  update_visibility_status_rpc:
    timeout: 1000
    is_killed: false
  whitelisted_users:
    - 334588177
    - 290945001
    - 56997170
kitchen_service:
  enabled: false
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: kitchen-service
profile_store_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: profilestore-service
  monitor_flag: 'false'
  get_profile:
    timeout_ms: 50000
    timeout: 5000
  get_multiple_profiles:
    timeout_ms: 50000
    timeout: 5000
  set_profile:
    timeout_ms: 50000
    timeout: 5000
  blinkit:
    host: kuma-gateway.eks.zdev.net
    port: '80'
    authority_header: blinkit-profilestore-service
bulk_operation_service:
  enabled: true
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: "bulk-operation-service"
  max_receive_msg_length_mb: 4
  timeout: 5000
promo_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: promo-service
  offer_wall_v18_enabled: true
  get_voucher_info:
    timeout: 2000
  update_campaign_priority:
    timeout_ms: 8000
  get_campaign_info:
    timeout: 15s
  santa_dashboard:
    whitelisted_users: [1, 2, 3, 1447354897, 56638517, 219763245, 1447354839, 1447354901, 1447354794, 1447354988, 1447354737, 1447354364, 38270700]
  tata_neu_campaign:
    enabled: true
    whitelisted_users: [21973245]
    banner_image_url: "https://b.zmtcdn.com/promo_service/tata_neu_campaign/64e78825b513b1ac5362ea04eb5849c81744024577.png"
    banner_image_aspect_ratio: 1.0
merchant_discounting_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: merchant-discounting-service-main-svc
  timeout: 5000
promo_ext_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: ext-promo-service
location_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: location-service-v2
  get_location_info:
    timeout: 10000
  get_feature_flags:
    timeout: 10000
  get_city_details:
    timeout: 10000
  get_cell_id_using_s2:
    enabled: 'true'
  get_serviceable_source_entities_for_user:
    timeout: 10000
  get_all_o2_active_cities:
    timeout: 2000
  highway:
    get_eligible_nodes_timeout: 1000ms
  delivery_area_rules:
    timeout: 1000ms
    kill_switch: false
  get_tabbed_location_for_address:
    timeout: 1000ms
    kill_switch: false
  get_tabbed_user_addresses_with_location_info:
    timeout: 1000ms
    kill_switch: true
  get_blackzones_for_entity:
    timeout: 2000ms
  token:
    keys: ['abcd1234@MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAo1TSb/UOISwmZf40Lb8bre73n6ioaMRnwhqNcRkrabJ48yoWnO/k0gepXxQAIJay8MH3asEF7+DL0oNmZBUwT4QnqsWbwpBAvxXZ+u+da+BRO/tRO0Q4XDbSKRci9GGwY0Yb65jQb1jxTX+SD8bfsdsOUfo8mXaqcGr/TbwlwhmSQlz17akBY7+YlmCzmRuJ3McExSBUSlikW7tUtsybr806oHq2Wv1B4lI9cke/qJ3ZDCKF4RGYjAY3sD5Ud33xgslLl9EWWjlTi1r2AdDerWxJlSmBVJ0kCR5ivfVBjAPB+eU0iJQy1cLLyl+gB4cglDGSkm6rG7awuFv6WVLuFQIDAQAB']
    issuer: location-service
    get_city_details:
      mismatch_enabled: true
      read_enabled: true
      logging_enabled: true
  search_locations:
    kill_switch: false
    timeout: 1000ms
  get_location_details:
    timeout: 1000ms
    kill_switch: false
dote_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: dote-service
train_fulfillment_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: "train-fulfillment-service"
  train_ordering:
    enabled: true
    enabled_for_zomans: true
    enabled_user_ids: [266349740]
    enabled_for_all: true
    ui_revamp_enabled: true
    disable_platform_ordering: true
    new_dark_mode:
      enabled: true
      enabled_for_all: false
      enabled_user_ids: [*********]
    res_serviceability:
      enabled: true
      enabled_for_zomans: true
      whitelisted_users: []
      rollout_percentage: 0
      check_enabled: true
      max_station_check:
        enabled: true
        count: 5
      min_serviceable_res_count: 2
    accessibility_info:
      enabled: true
      enabled_for_android: true
      enabled_for_android_beta: true
      enabled_for_ios: true
    custom_invalid_pnr_message_enabled: true
    journey_to_landing_redirection_enabled: true
    address:
      build_home_action:
        enabled: false
        enabled_for_android: true
        enabled_for_android_beta: true
        enabled_for_ios: true
    draft_pnr:
        enabled: true
        enabled_user_ids: [1447355246,287672203, 1447355101,1447355354]
        enabled_percentage: 0
        test_pnrs_enabled: true
        test_pnrs: ["1111111111", "2232054533","8130989095","2812835331","6349360203"]
    outlet:
      logging_enabled: true
    search_v2:
      enabled: true
      enabled_user_ids: []
      rollout_percentage: 100
      enabled_for_zomans: true
      is_refresh_tabs_disabled: false

flywheel_ads_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: "flywheel-ads-service"
  web:
    enabled: true
    timeout: 10s
  batch_upload_assets:
    max_concurrency: 5
    validate_images: true
    validate_images_test_users: [365903405, 204891965]

brand_ads_target_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: brand-ads-target-service
  store_google_ads:
    timeout: 2000ms
  get_credit_line:
    timeout: 2000ms
  hold_credit_line:
    timeout: 2000ms
  capture_credit_line:
    timeout: 2000ms
  refund_credit_line:
    timeout: 2000ms

gamification_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: gamification-service
  dashboard:
    enabled: false
  crazy_drops:
    enabled: true
    use_crazy_drops_endpoint: true
    drop_soon:
      enabled: true
      enabled_user_ids: []
      enabled_for_all: true
      killed_poi_ids: [13234]
      enable_open_minicart_flow: true
      drop_soon_info_image_url: "https://b.zmtcdn.com/data/o2_assets/f07863c47ebad6663967f118185135a01745947171.png"
      drop_soon_info_image_dark_mode_url: "https://b.zmtcdn.com/data/o2_assets/7a7a93e0661df1b8a1940be9c47c64ee1745947185.png"
      bottom_sheet_image:
        light_mode_aspect_ratio: 0.672043
        dark_mode_aspect_ratio: 0.718390822
        city_urls_19: "{\"1\":{\"light_mode_image_url\":\"link1\",\"dark_mode_image_url\":\"link2\"},\"2\":{\"light_mode_image_url\":\"link3\",\"dark_mode_image_url\":\"link4\"}}"
        city_urls_9: "{\"3\":{\"light_mode_image_url\":\"link1\",\"dark_mode_image_url\":\"link2\"},\"4\":{\"light_mode_image_url\":\"link3\",\"dark_mode_image_url\":\"link4\"}}"
        fallback_light_image: ""
        fallback_dark_image: ""
      get_drop_soon_details_timeout: 1000ms
      get_drop_soon_details_enabled: true
      visibility_duration: 300000
      min_visibility_duration: 300000
      top_image_bottom_padding: 0
      image_bottom_padding: 0
      lottie_url: https://b.zmtcdn.com/data/file_assets/4292128c7589054a07f8f16cbce129591724147187.json
    lottie_configs:
      animation_url: https://b.zmtcdn.com/data/o2_assets/861843e6d11df137bb28d424077437bf1745483428.lottie
      top_image_animation_url: https://b.zmtcdn.com/data/o2_assets/861843e6d11df137bb28d424077437bf1745483428.lottie
      animation_aspect_ratio: 1.32
      animation_height: 1.32
      top_image_animation_height: 1.32
      visibility_duration: 300
      min_visibility_duration: 30
      ios:
        animation_url: https://b.zmtcdn.com/data/o2_assets/861843e6d11df137bb28d424077437bf1745483428.lottie
        top_image_animation_url: https://b.zmtcdn.com/data/o2_assets/861843e6d11df137bb28d424077437bf1745483428.lottie
        animation_aspect_ratio: 1.32
        animation_height: 1.32
        top_image_animation_height: 1.32
        visibility_duration: 300
        min_visibility_duration: 30
    bottom_sheet:
      enabled: true
      enabled_for_android: true
      enabled_for_ios: false
    enabled_for_all: true
    test_user_ids: [35306965]
    recommendations_test:
      enabled_user_ids: []
      entity_ids: []
    android:
      dismiss_actions_enabled: true
    handlers:
      write_poi_info:
        slot_mappings: "{\"1\":{\"start_time\":\"20:00\",\"end_time\":\"20:15\"},\"2\":{\"start_time\":\"20:23\",\"end_time\":\"20:38\"},\"3\":{\"start_time\":\"20:50\",\"end_time\":\"21:05\"},\"4\":{\"start_time\":\"21:20\",\"end_time\":\"21:35\"},\"5\":{\"start_time\":\"21:45\",\"end_time\":\"22:00\"},\"full_day\":{\"start_time\":\"00:06\",\"end_time\":\"23:59\"}}"
        timeout: 1000ms
        whitelisted_user_ids: []
  food_rescue:
    enabled: true
    enabled_for_android: true
    enabled_for_android_beta: true
    enabled_for_ios: true
    enabled_for_ios_beta: true
    bottom_sheet_enabled: true
    bottom_sheet:
      enabled_for_android: true
      enabled_for_ios: true
      enabled_for_android_beta: true
    notifications:
      enabled: true
      mqtt_consume_delay: 2
    disabled_for_restricted_locations: true
    disabled_for_blackzones: true
    bottom_sheet_enabled_percentage: 10
    test_user_ids: [35306965]
    lottie_visibility_duration: 30 # In Seconds
    lottie_min_visibility_duration: 30 # In Seconds
    lottie_v2:
      percent_enabled: 100
    rpcs_timeouts:
      list_user_eligible_orders: 1s
    is_enabled_for_zomans: false
    poi_check_enabled: true
    restricted_address_types_enabled: true
  diwali_map:
    enabled: true
    get_facts_timeout: 1000ms
  awards_v2:
    enabled: true
    refresh_to_leaderboard_enabled: false
    fab_tab_disabled: true
    show_vote_page_header_subtitle: false
    winners_page_enabled: true
    get_city_nominations_timeout: 500ms
    get_leaderboard_details_timeout: 500ms
    get_category_nominations_timeout: 1000ms
    vote_nominated_res_timeout: 1000ms
    get_res_details_timeout: 500ms
    is_vote_counter_enabled: true
    voting_counter_animation_delay: 2000
    vote_counter_limit_for_android_v2: 999999
    is_vote_counter_disabled_for_android_v2: true
    show_bookmark: true
    is_pure_veg_mode_enabled: true
    bookmark_timeout: 200ms
    delivery_other_categories_ar: 1.6
    dining_other_categories_ar: 1
    main_categories_ar: 1
    voted_categories_ar: 1
    billboard_lottie: "https://b.zmtcdn.com/data/file_assets/f7f1e1b6fa3d673c9b67ca3252cf65c01744963199.json"
    billboard_lottie_ar: 1.2
    empty_state_image: "https://b.zmtcdn.com/data/o2_assets/36d46736b5b4f7e4f1d233e68d96d8751718637390.png"
    empty_state_dark_image: "https://b.zmtcdn.com/data/o2_assets/7ca3afc69c1c3ffd643311053d66277b1744797373.png"
    about_res_awards_image: "https://b.zmtcdn.com/data/o2_assets/72c99d14e65d38085711198109881c451744797161.png"
    about_res_awards_dark_image: "https://b.zmtcdn.com/data/o2_assets/9f01f275674937e446208981d1187be91744797183.png"
    vote_page_header_lottie: "https://b.zmtcdn.com/data/file_assets/9929f25a54475fecc539bc60b2e9c0601717616332.json"
    vote_page_header_image_url: "https://b.zmtcdn.com/data/o2_assets/732e461155f0e7050139296715a56dde1744955867.png"
    vote_page_header_image_ar: 2.453
    voted_animation_thank_you_image:
      url: "https://b.zmtcdn.com/data/o2_assets/e084683738e5bc221f800a4021b28b541717681484.png"
      aspect_ratio: 6.266
    is_vote_counter_disabled_for_android: true
    vote_counter_limit_for_android: 999999
    billboard_vote_counter_lottie_for_android: "https://b.zmtcdn.com/data/file_assets/313aa80051b06a1c6e88c5e0a8b5da1c1717244641.json"
    billboard_vote_counter_lottie_ar_for_android: 0.35
    voting_counter_animation_delay_for_android: 200
    new_user_date: "2019-01-08 11:32:33"
    is_new_user_voting_disabled: true
    is_delivery_nomination_tag_disabled: true
    is_dining_nomination_tag_disabled: false
    refresh_search_for_android_disabled: true
    refresh_tabbed_home_for_android_enabled: true
    winners_page_header_image: https://b.zmtcdn.com/data/o2_assets/6e53f919312a682c8767a52d79129daf1720435820.png
    winners_page_header_image_ar: 2.14
    winners_one_link: https://link.zomato.com/xqzv/poim460k
    winners_voted_animation_lottie: https://b.zmtcdn.com/data/o2_assets/b7a088ca41deebfbe7e92b8773d3290c1720418665.lottie
    winners_voted_animation_lottie_ar: 1.195
    winners_voted_animation_lottie_height: 68
    winners_voted_animation_lottie_width: 68
    results_processing_state_enabled: false
    results_announced_state_enabled: true
    result_state_image: https://b.zmtcdn.com/data/o2_assets/3ff6f768c5d3238d0ee691eff5d2fe691721027250.png
    result_state_dark_image: https://b.zmtcdn.com/data/o2_assets/3ff6f768c5d3238d0ee691eff5d2fe691721027250.png
    result_state_image_ar: 1.481
    results_total_vote_count: 10000000
    bottom_sheet_image_url: https://b.zmtcdn.com/data/o2_assets/d50e19839ff761e92ce49367e35fbf741718265955.png
    winner_page_postback_params_enabled: true
    leaderboard_page:
      enabled: false
      show_header_subtitle: false
      header_top_image:
        url: "https://b.zmtcdn.com/data/o2_assets/6e53f919312a682c8767a52d79129daf1720435820.png"
        ar: 2.14
      show_voted_res: false
    share:
      one_link: "https://link.zomato.com/xqzv/poim460k"
      image_url: "https://b.zmtcdn.com/data/o2_assets/85bc75b3210eb2491603fd6c602ba4731718801041.png"
  badges:
    assets:
      infographic_image: "data/o2_assets/43b0266dc4701819fdd2cb15c70578641701931624.png"
      infographic_image_aspect_ratio: 0.10907
      no_badges_billboard_image: "data/o2_assets/cdececca91f64e9d70b89a31a458a03d1702199809.png"
      no_badges_billboard_image_aspect_ratio: 1.2975
      no_badges_banner_image: "data/o2_assets/7033c3889df215cac1e7a5c20910f0db1702925190.png"
      no_badges_banner_image_aspect_ratio: 3.25
    stories:
      maximum_badges: 4
      action_type_share_enabled: true
      parallax_effect_disabled: 'false'
      user_stories_disabled: false
      animation_effect_disabled_device:
        ios: true
        android: true
      parallax_effect_disabled_device:
        ios: true
        android: true
      enable_index_tracking: 'true'
      enable_video_media: 'true'
      video_media_enabled:
        ios: 'true'
        android: 'true'
    lookback:
      zoman_enabled: true
      eternal:
        fetch_enabled: true
        is_enabled: true
        is_linked_enabled: true
      metricsEnabled: true
      pan_india_enabled: true
      redirectionURL:
        enabled: true
        webView:
          deeplink: "zomato://zpl?url=https%3A%2F%2Fwww.zomato.com%2Fwebview%2Fgeneric%3Fpage_type%3Dzomato_2024_lookback_update%20app&should_disbale_bounce_on_webview=true&navigation_bar_type=transparent"
      experiment:
        enabled: true
      app_version:
        android: "3000"
        beta: "3000"
        ios: "30.0.0"
      profile_store:
        set_profile:
          force_sync: false
        get_profile:
          timeout_in_ms: 500
        multiget_profiles:
          timeout_in_ms: 500
      indexTracking:
        enabled: true
      getPageDetails:
        killswitch: false
      landingPage:
        watchStory:
          togglePageEnabled: true
          sweetModeTogglePage:
            bgAnimationURL: https://b.zmtcdn.com/data/file_assets/6fc4a9ca486ebeb56910116206ff5cee1733755098.json
            bgFallbackImageURL: https://b.zmtcdn.com/data/o2_assets/32630d7d424f89d87acc323c0496bc111734371836.png
          roastModeTogglePage:
            bgAnimationURL: https://b.zmtcdn.com/data/file_assets/d398e2284ef59e37214a65f07406d1301733760147.json
            bgFallbackImageURL: https://b.zmtcdn.com/data/o2_assets/8b3860bc621029548edbb15766f5890f1734371691.png
        enabled: true
        billboardImageURL: https://b.zmtcdn.com/data/o2_assets/e7a4809f76cb14b835de662909dd92201733924623.png
        roastModeWarning:
          imageURL: https://b.zmtcdn.com/data/o2_assets/9c64c2129b1b29b821f4aa78532147d91733581658.png
        personalities:
          enabled: true
          stateOnOffSwapEnabled: false
          defaultPersonalityID: p18
          bgImage:
            aspectRatio: 1.05633
            stateOn: https://b.zmtcdn.com/data/o2_assets/2ebcf0074b019c45164b40195f3b9dc51733924879.png
            stateOff: https://b.zmtcdn.com/data/o2_assets/0e0daa8d6f204d9ae5d0cf313da77f7a1733924888.png
            stateOffRoastModeDisabled:
              url: https://b.zmtcdn.com/data/o2_assets/e2458de2b502d5e86059f3401aff0b831734090006.png
              aspectRatio: 1.2931
          topImage:
            aspectRatio: 15.5
            height: 30
            stateOn: https://b.zmtcdn.com/data/o2_assets/cbcc80a66abd4e3151382af2b530a6281733938503.png
            stateOff: https://b.zmtcdn.com/data/o2_assets/c6701a87af3c041661ea7d596a29e8101733938494.png
          switchData:
            animation:
              url: https://b.zmtcdn.com/data/file_assets/f305b5cd611db35e0d9366d68bfa2acf1733932177.json
            roast:
              animation:
                url: https://b.zmtcdn.com/data/file_assets/f305b5cd611db35e0d9366d68bfa2acf1733932177.json
            sweet:
              animation:
                url: https://b.zmtcdn.com/data/file_assets/f305b5cd611db35e0d9366d68bfa2acf1733932177.json
          toggleAnimation:
            url: https://b.zmtcdn.com/data/file_assets/f305b5cd611db35e0d9366d68bfa2acf1733932177.json
          cardView:
            enabled: true
        header:
          image:
            url: https://b.zmtcdn.com/data/o2_assets/1066f2cec5d3e1bf11ab8d7ae26a490f1733650692.png
          themeImage:
            url: https://b.zmtcdn.com/data/o2_assets/bda03aef9e29a8b4ffc88814f91125281733734947.png
        countryStats:
          delivery:
            aspectRatios: 0.2144 0.2585
            image:
              urls:
                - https://b.zmtcdn.com/data/o2_assets/17605b1c160518448b2b3d243741e63c1734534290.jpg
                - https://b.zmtcdn.com/data/o2_assets/5363ef1994a564acbb095504d64263741734534284.jpg
            darkThemeImage:
              urls:
                - https://b.zmtcdn.com/data/o2_assets/635b3e34c2598ccb3f89ff7d9fec4a1d1734534286.jpg
                - https://b.zmtcdn.com/data/o2_assets/d4a42714623f944a6b6720af639ec6231734534287.jpg
          dining:
            aspectRatios: 0.2886 0.2585
            image:
              urls:
                - https://b.zmtcdn.com/data/o2_assets/80248f71c094b9e7541484d797b8f4be1734534286.jpg
                - https://b.zmtcdn.com/data/o2_assets/607b796f34f6897348edf093207c002a1734534289.jpg
            darkThemeImage:
              urls:
                - https://b.zmtcdn.com/data/o2_assets/c04aab982b42ffff497abdb50af3a2e71734534285.jpg
                - https://b.zmtcdn.com/data/o2_assets/de077f5f3bef119691d65c957632bde91734534288.jpg
      stories:
        enabled: true
        tracking:
          enabled: true
        stats:
          max_limit: 5
          exclusion_ids : ' '
          bucket_limit: 25
        personalities:
          max_limit: 3
          exclusion_ids: ' '
          bucket_limit: 60
        audio:
          should_play: true
          is_unmuted: true
  awards:
    vote_count_visibility_threshold: 1
    category_vote_count_visibility:
      enabled: 'true'
      min_threshold: 5
      display_multiplier: 1.25
    voting_disabled: true
    is_winning_phase: true
    uae:
      voting_disabled: false
      is_winning_phase: false
      category_vote_count_visibility:
        display_multiplier: 10
      coke_campaign:
        enabled: true
  trivia:
    double_reward_banner:
      enabled: 'true'
    akamai_caching_enabled: 'true'
    result_jitter_enabled: 'true'
  zpl:
    player_count_poll_interval: 5
    match_status_poll_interval: 5
    notify_me_enabled: 'false'
    final_match_ended: 'false'
    info_rail_enabled: 'false'
    scoreboard_enabled: 'false'
    end_state_enabled: 'false'
    coupons_enabled: 'true'
    game_button_loader_enabled: true
    game_button_loader:
      enabled: 'true'
      max_duration: 1
    prediction_history:
      enabled: 'true'
      ongoing_match_visibility: 'true'
    video:
      option_time_up_enabled: 'true'
      low_internet_custom_timeline_enabled: 'true'
      skip_enabled: 'true'
    spotlight_rail:
      enabled: 'true'
      offer_enabled: 'true'
      min_count: 2
      max_count: 10
      strict_serviceability_enabled: 'true'
      rating_enabled: true
      serviceability_bypass: false
      image_name_bypass: true
      rating_call_max_concurrency: 5
      min_ads_count: 1
      max_ads_count: 100
stories_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: stories-service
dining_service:
  host: "kuma-gateway.eks.zdev.net"
  port: 80
  authority_header: "dining-service"
  timeout_in_ms:
    get_masked_number: 1000
    get_entity_enrichment_data: 500
    get_business_details: 500
    get_business_page_entities: 500
    get_media: 500
    get_product_inventory: 500
dish_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: dish-service
cbilling_service:
  business_dashboard_enabled: false
  host: 127.0.0.1
  port: '80'
  grpc:
    host: kuma-gateway.eks.zdev.net
    port: 80
    authority_header: "cbilling-service-client"
  web:
    protocol: "https"
    host: "vipulkanojia.eks.zdev.net"
    stageserver: ""
    header_host: ""
    invoice_dashboard:
      api_key: "8ab2f8a34735ec8d648c5a9af8e8dc0012312"
  pms:
    public_key: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApjiYQzWh8kmVTbISmw79RgUykLE8zLK8I+5X84Mo/XP0YR+Vo0zmQz1Dq56yK5ldjroqAbkXQ+CVuSsOKNKJm1rkQXlLbtfcfB4tJLYdzuPNOhuLZtk0Gfn5tu3LlexZ9XMp76StMYdypXwRcey+peNowQxFy0N7c10z3FHBL/40/girsTGZOKw+A+mEKrmmQaT2iDG/8DEY6DIYzLG2TzY2qa4AarqePIghtduHpCMKc05Lozp5XPpArReyEleduO00SREd3aFgicybR9CZIL5OmTrGUfHxnJxeiy4KXHALlLp7el0GoyesaNNow6QEbF3BPJPEoUr2bX892fBObwIDAQAB"
merchant_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: merchant-service
template_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: template-service
ads_target_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: ads-target-service
exp_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: experimentation-service
  get_bucket:
    timeout_ms: 2000
  get:
    timeout_ms: '2000'
nps_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: nps-service
edition_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: edition-service
  skygge_salt: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  okyc_salt: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  rbl_txn_salt: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  aftership_salt: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  okyc_skip_key: 'false'
  khosla_username: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  khosla_password: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  check_user_serviceability: 'true'
  enabled: true
logistics_order_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: logisticsorder-service
logistics_support_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: logistics-support-service
reviews_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: reviews-service.dev.svc.istio.zeks.io
  timeout:
    post_feedback: 400
    update_feedback: 1000
    fetch_feedback_for_transaction: 50
    get_active_rating_for_user_res_combo: 50
    update_review_metadata: 200
    fetch_reference_for_id: 50
    fetch_reference: 1000
    get_search_results: 50
    get_review_details: 50
    fetch_popular_tags_for_subject: 50
    dismiss_view: 100
    search_references: 50
    add_reference_and_generate_link: 2000
    archive_view: 100
    fetch_restaurant_rating: 50
    get_user_feature_value: 200
    fetch_references_generated_by_source: 100
    fetch_feedback_by_id: 500
  sharing_service:
    recommendation_reference_creation_killed: true
    custom_links:
      enabled: true
api_internal:
  protocol: https
  host: api.eks.zdev.net
  api_key: 1130d2a2f53adaf33c098b4e48322ffc
  header_host: api.eks.zdev.net
  stageserver:
    irctc:
      forceserver: "aa115"
upload_service:
  enabled: true
  protocol: https
  host: api.eks.zdev.net
  endpoint: alicloud-upload
  timeout: 10s
  max_concurrent_uploads: 5
zomaland:
  host: kuma-gateway.eks.zdev.net
  api_protocol: http
  auth_username: *******
  auth_password: password
  dreamcast_secret: dnsdncdkjceweeww
  insider_secret: nvwrnvjwnnsvjnsnjvs
  bms_secret: nvwrnvjwnnsvjnsnjvs
  timeout:
    default: 5s
    homepage: 5s
    cart: 5s
    book_ticket: 5s
    payment: 5s
    refund: 5s
    event_details: 5s
    reselling: 5s
    event_details_v2: 5s
    event_tickets: 5s
    generate_private_ticket_link: 5s
  refund:
    active_users: [154321031, ********]
    enabled_for_all: true
    allowed_datetime: "2023-01-27 19:00:00"
  ecs_migration:
    is_enabled: true
    is_enabled_for_zomans: true
    host: kuma-gateway.eks.zdev.net
    whitelisted_users: []
    rollout_percentage: 0
  ecs_shadowing:
    is_enabled: true
    host: kuma-gateway.eks.zdev.net
    whitelisted_users: []
    rollout_percentage: 100
user_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: user-service
  use_payment_serviceability: 'true'
  fetch_address_by_id_rpc_timeout_ms: 100ms
  business_profile_rpc_kill_switch: false
  shared_address_id_rpc_timeout_ms: 100ms
  profile_v2:
    enabled_for_zomans: true
    enabled_for_all: false
    business_profile:
      enabled: true
      enabled_for_verified_users: true
      reimbursement:
        enabled: false
        banner_enabled: false
      reimbursement_banner:
        enabled: false
    updated_date_picker:
      enabled: true
  enterprise_state:
    enabled_for_all: true
  update_user_address_rpc_timeout: 100ms
  address:
    save_address_backend_tracking_enabled: true
    home_gps_or_precise_location_off:
      most_recent_date_sorting_enabled: true
    address_sharing_ui_changes_enabled_percent: 100
  user_profile:
    dob_updation_enabled: true
    anniversary_update_enabled: true
    mx_block_already_linked_enabled: true
    veg_festival_enabled: true
    set_in_ctx_enabled_percentage: 100
  user_info:
    veg_v2_kill_switch: false
  import_addresses_from_tenant:
    enabled: true
    testing:
      enabled: false
      user_ids: []
  veg_schedule:
    enabled: true
    veg_lottie_url: ""
    updated_pref_veg_lottie_url: ""
    updated_pref_veg_lottie_dark_url: ""
    veg_off_today_lottie_url: ""
    veg_off_today_lottie_dark_url: ""
    updated_pref_lottie_url: ""
    updated_pref_lottie_dark_url: ""
  rpc:
    get_addresses_from_tenant:
      timeout: 10s
    import_addresses_from_tenant:
      timeout: 10s
    insert_user_address:
      timeout: 10000ms
    update_user_address_v2:
      timeout: 10000ms
    update_user_veg_schedule:
      timeout: 10000ms
corporate_funds_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: corporate-funds-service
  timeout: 10s
  s3_bucket_name: "dev-corporate-funds-service-content"
gift_cards_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: gift-card-service
  timeout: 10s
jumbo_insights_service:
  host: localhost
  port: '7001'
  authority_header: jumbo-insights-service
sms_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: sms-service-v2
  sms_callback_confluent_enabled: true
  kill_switch:
    exotel_callbacks: false
    airtel_callbacks: false
    alisms_callbacks: false
    mobishastra_callbacks: false
    bulk_send_sms: false
    gupshup_callbacks: false
    fonada_callbacks: false
    plivo_callbacks: false
    karix_callbacks: false
    kaleyra_callbacks: false
  fonada:
    whitelisted_ips: ["***************", "************"]
  kaleyra:
    whitelisted_ips: []
  karix:
    whitelisted_ips: []
  plivo:
    whitelisted_ips: []
    authentication_enabled: true
  gupshup:
    whitelisted_ips: ["*************"]
    authentication_enabled: true
    authentication_key: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  bulk_sms:
    max_allowed_file_size: 1000
search_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: search-service-v2
  get_raw_search_candidates:
    enabled: true
    timeout_ms: 1000
  get_dishes_from_ids:
    enabled: true
    timeout_ms: 1000
  get_dynamic_slots:
    enabled: true
    timeout_ms: 1000
  get_recommended_results:
    enabled: true
    ips: []
  get_order_planner_query:
    enabled: true
    timeout_ms: 1000
  list_recent_order_planner_queries:
    enabled: true
    timeout_ms: 1000
  bulk_dish_entity_action:
    enabled: true
    timeout_ms: 1000
  dashboards:
    read_restriction_enabled: true
    write_restriction_enabled: true
    write_users: []
    read_users: []
search_worker_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: search-service-grpc-worker
reward_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority: reward-service
  is_play_game_enabled: 'true'
  should_show_referral_popup: true
  should_reverse_brand_rewards: true
  rewards_page_size: 20
  rewards_page_pagination_enabled: true
  rewards_page_earnings_snippet_disabled: true
  init_reward_test: [ ********* ]
  get_available_points:
    timeout_ms: 500
    enabled: true
  register_user_account:
    timeout_ms: 500
    enabled: true
  get_user_game_rewards:
    timeout_ms: 100
  check_account_eligibility:
    timeout_ms: 500
    enabled: true
graph_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority: graph-service
  dashboard:
    user_id_enabled: true
    view_linkages:
      kill_switch: false
    view_query_attributes:
      kill_switch: false
phone_verification_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority: phone-verification-service
  initiate_verification:
    timeout_ms: 100
    enabled: true
  validate_otp:
    timeout_ms: 100
    enabled: true
  get_verified_user_id:
    timeout_ms: 50
    enabled: true
  unverify_all_numbers_for_user_id:
    timeout_ms: 50
    enabled: true
composite_order_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: ordercomposite-service
  last_order_by_res_id_timeout_ms: 101ms
  rpc:
    update_payment:
      whitelisted_users: []
butterfly_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: butterfly-service-v3
time_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: time-service-v2
osrm_aggregator:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: "osrm-aggregator-service"
live_order_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: live-order-service
  telecom:
    allocate_number:
      dealloation_duration: 3600
      los_migration:
        enabled: true
        is_enabled_for_zomans: true
        enabled_for_test_users: true
        enabled_user_ids: []
        rollout_percentage: 100
  update_contact:
    timeout_ms: 500
  validate_order_address:
    timeout_ms: 500
  update_order_address:
    timeout_ms: 500
  get_order_masked_contact_entities:
    timeout_ms: 500
  get_crystal_view:
    timeout_ms: 5000
  execute_live_order_action:
    timeout_ms: 5000
  reschedule_order:
    timeout_ms: 5000
  get_tip_cart:
    timeout_ms: 500
  make_tip_payment:
    timeout_ms: 500
  get_payment_status:
    timeout_ms: 500
  get_order_tracking_details:
    timeout_ms: 500
  get_polling_data:
    timeout_ms: 5000
email_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: email-service
  whitelisted_domains: zdev.net eks.zdev.net editioncard.com feedingindia.org hyperpure.com zomans.com zomato.com zmtcdn.com zoma.to bnc.lt zomato.onelink.me atlassian.net rblbank.com rbi.org.in
  stripo:
    client_id: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
    client_secret: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  preview:
    promotional_email: *******
    transactional_email: *******
    zomato:
      promotional_email: *******
      transactional_email: *******
    blinkit:
      promotional_email: *******
      transactional_email: *******
    hyperpure:
      promotional_email: *******
      transactional_email: *******
    district:
      promotional_email: *******
      transactional_email: *******
  jumbo:
    table: email_tracking
  kill_switch:
    apollo_dashboard:
      show_email_tracking_section: true
    sendgrid_callbacks: false
  webhook:
    sendgrid:
      auth_key:
        transactional: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
        promotional: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
        onesupport: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
        district:
          transactional: ""
          promotional: ""
        blinkit:
          auth_service:
            transactional: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
      msk_kafka_producer_enabled: true
rate_limits:
  add_user_device: '0.5'
notification_preferences:
  profile:
    name: profile
    token: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  unsubscribe_url:
    name: unsubscribe_url
    token: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  dashboard_url: ' '
profiler:
  enabled: 'false'
  credentials: '{"type":"service_account","project_id":"service-profiler","private_key_id":"","private_key":"","client_email":"*******","client_id":"102917646637614496210","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/profiler-service-account%40service-profiler.iam.gserviceaccount.com"}'
  goroutine_profile:
    enabled: false
  block_profile:
    enabled: false
  mutex_profile:
    enabled: false
hermes_service:
  host: localhost
  port: '80'
  authority_header: hermes
payments:
  host: kuma-gateway.eks.zdev.net:80
  host_header: payments-service
  auth_username: client-zomato-consumer
  auth_password: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  check_zwallet_serviceability: 'true'
  token_log_enbled: 'true'
  zppl_username: 'S9DZcqM73jn3'
  zppl_password: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
  zppl_user_salt: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
  zomato_wallet:
    kill_switch: false
    add_money:
      enabled: true
  gift_card:
    kill_switch: false
  enterprise_meal:
    kill_switch: false
  pay_after_order:
    experiment_check:
      enabled: true
    profile_store_check:
      enabled: true
  zomato_upi:
    min_android_version: "2000"
    min_android_beta_version: "500"
    min_ios_version: "20.0.0"
  pas_access_token:
    rate_limit_enabled: true
    rate_limited_user_ids: []
    rate_limit_per_minute: 100
  cash_on_delivery:
    boost_amount_limit_tooltip_enabled: false
    boost_amount_limit_cart_tooltip_enabled: false

payments_api_gw:
  host: kuma-gateway.eks.zdev.net:80
  host_header: "payments-api-gw-http-main-svc"
  checksum_key: dummy
  whitelisted_ips: []
ads_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: ads-service
  gupshup_servers: ************* ************** ************
  platform_sections_rpc_timeout_ms: 500
  get_aggregated_ads_performance_rpc_timeout_ms: 500
  get_ads_performance_trend_chart_rpc_timeout_ms: 500
  platform_products_rpc_timeout_ms: 10000000
  platform_filters_rpc_timeout_ms: 100000
  get_ads_performance_pie_chart_rpc_timeout_ms: 500
  download_ads_performance_rpc_timeout_ms: 500
  get_ads_performance_mealtime_bar_chart_rpc_timeout_ms: 500
  platform_targeting_rpc_timeout_ms: 500
  platform_scheduling_rpc_timeout_ms: 500
  platform_budget_rpc_timeout_ms: 500
  platform_estimate_rpc_timeout_ms: 500
  platform_payment_rpc_timeout_ms: 500
  download_ads_performance_status_rpc_timeout_ms: 500
  platform_create_ad_rpc_timeout_ms: 500
  platform_save_draft_rpc_timeout_ms: 10000000
  platform_validate_budget_csv_rpc_timeout_ms: 500
  single_ad_group_rpc_timeout_ms: 2500
  multiple_ad_groups_rpc_timeout_ms: 2500
  platform_entity_details_rpc_timeout_ms: 1000
  get_featured_image_rpc_timeout_ms: 500
  campaigns_fetch_rpc_timeout_ms: 10000
  campaigns_summary_rpc_timeout_ms: 10000
  campaigns_action_rpc_timeout_ms: 10000
  validate_bulk_operation_rpc_timeout_ms: 10000
  start_bulk_operation_rpc_timeout_ms: 10000
  tracking_download_rpc_timeout_ms: 10000
  contracts_fetch_rpc_timeout_ms: 10000
  contracts_summary_rpc_timeout_ms: 10000
  get_bulk_operation_type_rpc_timeout_ms: 10000
zpush:
  host: push-service-v2.eks.zdev.net
  port: '80'
  host_header: push-service-v2.eks.zdev.net
  auth_username: 87F4B1EDF265277AA4D29CA52A6B1
  auth_password: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  add_device_confluent_kill_switch: true
push_notification_service:
  user_preference_events:
    kill_switch: false
  live_activity_events:
    kill_switch: false
credit_line_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: credit-line-service
wallet_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: wallet-service
  timeout: 10s
  test_users: '["206075730","31975064","63274230","168896721","36936484","186121388","49158533"]'
  add_money_enabled: 'true'
  default_add_money_amount: "1000" # in rupees
  onboarding_enabled: 'true'
  wallet_closure_enabled: 'false'
  cashback_offer_enabled: false
  cashback_offer_enabled_on_add_money: false
  pineperks:
    callback:
      username: "1234"
      whitelisted_ips: ["*************", "***************", "**************", "*************", "***************", "**************", "**************", "**************"]
    sdk:
      url: "1234"
      username: "1234"
  flat_discount:
    enabled: true
    default_money_banner: "https://b.zmtcdn.com/data/o2_assets/8b7cc19c702f61f330a1f5313532b99d1701959188.png"
    wallet_created_money_banner: "https://b.zmtcdn.com/data/o2_assets/2f8da05ff659d7272ec732bf9448fb741701955134.png"
    add_money_banner: "https://b.zmtcdn.com/data/o2_assets/8b7cc19c702f61f330a1f5313532b99d1701959188.png"
    min_recharge_wallet_balance_in_paise: 100000
    percentage: 10
  credit_limit_exceeded_enabled: true
  dark_mode:
    enabled: true
    rollout_percentage: 100
    is_enabled_for_zomans: true
    test_user_ids: [341363812]
  auto_add_money:
    android_version: "3000"
    android_beta_version: "3000"
    ios_version: "30.0.0"
    wallet_sdk_version:
      android: "1.0.0"
    enabled:
      section_enabled: true
      new_mandates: true
    rollout:
      blinkit:
        min: 0
        max: 100
    default_checkbox_enabled: false
    whitelisted_entity_types: ['ZOMATO_USER']
    default_auto_add_flow_enabled: true
    default_auto_add:
      blinkit: true
  zomato_money_cashback:
    wallet_sdk_version:
      android: "1.0.0"
    android_version: "3000"
    android_beta_version: "3000"
    ios_version: "30.0.0"
    enabled: true
    zoman_enabled: true
    eternal_enabled: true
    whitelisted_user_ids: []
    whitelisted_cities: []
    stub_user_ids: []
    min_cashback_amount_in_paisa: 10000
    cashback_tag_on_cart_bottomsheet_enabled: false
    cart_cashback_bottom_green_banner_enabled: true
    enabled_user_perc:
      min: 0
      max: 100
  add_money_v3:
    enabled: true
    zoman_enabled: false
    eternal_enabled: false
    whitelisted_user_ids: []
    whitelisted_entity_types: ['ZOMATO_USER']
    enabled_user_perc:
      min: 0
      max: 0
    whitelisted_cities: []
    ios_sdk_version: "1.4.0"
    android_sdk_version: "0.8.0"
    claim_gift_card_section:
      enabled: true
      whitelisted_user_ids: []
      eligible_sources: ["SMENU", "MoneyCartStrip"]
      is_new_tag_enabled: true
      rollout:
        min: 0
        max: 100
      ios_sdk_version: "1.5.0 "
      android_sdk_version: "0.9.0"
    show_attributed_text:
      ios_sdk_version: "1.8.0"
      android_sdk_version: "0.9.0"
      blinkit_user:
        ios_sdk_version: "1.5.0"
        android_sdk_version: "0.9.0"

vernacular_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: vernacular-service
  get_static_messages:
    timeout: '200'
    enabled: 'true'
  bulk_get_localization_data:
    timeout: '200'
    enabled: 'true'
  kill_vernac_for_new_apps:
    enabled: 'true'
telecom_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: telecom-service
  twilio_account_sid: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  twilio_auth_token: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  query_params_prefix: zom-
  callback_scheme: https
  callback_topic_confluent_enabled: false
  healthcheck_callback_topic_confluent_enabled: false
  fetch_call_recording:
  # The below key should be kept in-line with the key in the telecom-service config fetch_call_recording.hmac_private_kay
    hmac_private_kay: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    verify_lifeline_agent:
      enabled: true
      timeout_ms: 2000
      request_url: "http://es-apache-internal.mesh:80/api-internal/unified-support/is_valid_lifeline_user"
  airtel:
    authentication_enabled: false
    authentication_key: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    whitelisted_ips: ["**************", "************", "**************", "************"]
  fonada:
    authentication_enabled: false
    authentication_key: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    whitelisted_ips: ["**************", "**************", "*************"]
  exotel:
    whitelisted_ips: ["*************", "***********", "***********", "*************", "**************", "*************", "**************", "**************", "************"]
  kaleyra:
    whitelisted_ips: ["************", "*************", "*************", "**************", "**************", "************", "**************", "***********", "************", "************"]
  plivo:
    SAYJNJYZA2ZWMTMDVIZI:
      auth_token: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    SAN2FHNWRLZTYTNDBHMY:
      auth_token: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    SAZTC0MJCXMJUTMGE0YS:
      auth_token: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    dual_leg:
      auth_token: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    dual_leg_cnr:
      auth_token: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    number_masking:
      auth_token: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    authentication_enabled: true
    authentication_key: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    whitelisted_ips: ["************", "************", "**********"]
    dual_leg_auth_ids: ["SAYJNJYZA2ZWMTMDVIZI"]
    num_masking_auth_ids: ["SAZJNJYZA2ZWMTMDVIZI"]
  kill_switch:
    voip:
      twilio_callbacks: false
    num_masking:
      exotel_callbacks: false
      exotel_health_check: false
      kaleyra_callbacks: false
      kaleyra_health_check: false
    dual_leg:
      exotel_callbacks: false
      solutions_infini_callbacks: false
    hotline:
      hotline_with_usecase: false
whatsapp_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: whatsapp-service
  gupshup_ips: ************* *********** ************** ************* ************
  inbound_messages_received_topic: zomato.zomato-api-gateway.receive-whatsapp-inbound-messages
  incident_dashboard_topic: zomato.zomato-api-gateway.bulk-notification
  dlr_status_topic: zomato.zomato-api-gateway.whatsapp-receive-dlr-status
  gupshup_hmac_private_key: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  gupshup_hmac_header_name: X-Sd-Signature
  notification_preferences_enabled: 'true'
  inbound_messages_received_confluent_enabled: true
  incident_dashboard_confluent_enabled: true
  dlr_status_confluent_enabled: true
  default_topics:
    inbound_message: "zomato.zomato-api-gateway.whatsapp-inbound-message-callbacks-common"
    delivery_status: "zomato.zomato-api-gateway.whatsapp-delivery-status-callbacks-common"
    template_updates: "zomato.zomato-api-gateway.whatsapp-template-update-callback-default"
  karix:
    whitelisted_ips: ["127.0.0.1"]
    username: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    password: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  meta:
    verify_token: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    app_secret: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    media_url_prefix: "https://graph.facebook.com/v23.0/"
karma_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: karma-service.dev.svc.istio.zeks.io
  sdk_not_allowed_tenants: ' '
  play_integrity_allowed_tenants: zomato
  dashboard_rpc_kill_switch: false
  dashboard:
    user_id_enabled: false
    cod:
      get_rule_entities:
        kill_switch: false
      view_rule_configs:
        kill_switch: false
      alter_rule_configs:
        kill_switch: false
      get_rule_names:
        kill_switch: false
  events:
    menu_open:
      migrated_percentage: 100
  cross_app:
    kill_switch: false
    events:
      kill_switch: false
  device_info:
    kill_switch: false
  play_integrity:
    get:
      kill_switch: false
    set:
      kill_switch: false
dining_events:
  is_enabled: true
blinkit:
  checksum_key: c7f81962bdd53d4de20d000bb74a62aa1fd181ba6ac9ae864192568b7cc50eb4
benefits_service:
  web_api_key: 3c2e193c6c7da2178cafd43f22b0f1eb
  timeout: '5000'
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: benefits-service
  additive_offers:
    min_ios_version: "17.49.0"
    min_android_version: "2001"
    min_android_beta_version: "196"
  get_voucher_applicability:
    timeout_ms: 500
group_ordering:
  rollout:
    enabled: true
    zoman_enabled: true
    eternal_enabled: true
    whitelisted_user_ids: []
    enabled_for_all: true
    eternal_linked_property:
      enabled: true
  archival_slots: [30m, 1h, 2h, 3h, 6h, 12h, 24h]
  mqtt_credentials_override:
    enabled: true
    username: "zemqtt"
    password: "xxxxxxxx"
  archival_time:
    step_value: 1800s
    max_permissible_time: 6h
    minimum_duration_for_selection: 1800
  fetch_group_order:
    polling_time: 120000
  member_encoding:
    encryption_key: "q7x1j%y8PI93e$8e%dUtB6c*wkhXk&Vb"
    deterministic_nonce_key: "dbx1j%y89IP5e$9e%dUtB6c*wk%Xk&Vq"
  feature_rollout:
    faq:
      enabled: true
    admin_prelock_update:
      enabled: true
      zoman_enabled: true
      enabled_for_all: true
    unlock:
      enabled_for_all: true
      zoman_enabled: true
    validate_user_for_sharing:
      enabled_for_all: true
    dark_mode:
      enabled: true
    group_page_header_image:
      enabled_for_all: true
      whitelisted_users: []
  app_versions:
    android: 841
    beta: 500
    ios: 17.79.3
  sharing:
    image_url: "o2_assets/eee2be4ce8450073d1f68968f9f046d61724334929.png"
cart:
  food_rescue:
    zomato_money:
      enabled: true
      enabled_for_zomans: true
      test_user_ids: []
  vip_mode:
    enabled: true 
  edt_section:
    show_tooltip: true
    send_unspecified_shipping_speed: false
    send_shipping_speed_preferences: true
    priority_delivery_unavailable_popup_enabled: true
    priority_delivery_toast_enabled: true
    v2_snippet_enabled: true
    priority_delivery_floating_pill:
      is_enabled: true
      impressions_per_session: 1
      session_count: 3
  offer_section:
    offer_section_floating_pill:
      is_enabled: true
      impressions_per_session: 1
      session_count: 10
    new_user_floating_pill:
      is_enabled: true
      impressions_per_session: 1
      session_count: 10
  cancellation_policy_allow_rescheduling:
    enabled: true
    enabled_user_ids: []
    enabled_for_zomans: true
    enabled_percentage: 100
  exclusive_offers:
    enabled: true
    enabled_user_ids: []
    enabled_for_zomans: true
    enabled_percentage: 100
  scratch_card_detail:
    enabled: true
    enabled_user_ids: []
    enabled_for_zomans: true
    enabled_percentage: 100
  unsupported_flows:
    home_redirection:
      is_enabled: true
  crazy_drops:
    invalid_state_image: "/data/o2_assets/f99f6b959b8a116ac4776a6a833e82831747747749.png"
    should_avoid_menu_fetch: true
    v1_flow:
      is_enabled: true
      enabled_user_ids: []
      enabled_percentage: 100
  logging:
    stack_trace_whitlisted_error_code: []
    error_config:
      enabled: false
    promo_snippet:
      enabled: true
  eternal_linked_property_check:
    enabled: true
  build_cart:
    new_cart:
      timeout_in_ms: 100000
    menu:
      fetch_all_items:
        is_enabled: true
    response_v2:
      is_enabled: true
    default_cart_type_switch:
      is_enabled: true
  share_cart:
    handler:
      is_enabled: true
      enabled_for_zomans: true
      enabled_for_guests: true
      enabled_percentage: 100
  checkout_cart:
    features:
      order_counter:
        enabled: true
      address_format:
        enabled: true
      order_success_animation:
        lottie_url: "data/file_assets/9c023caea7d4cc87ba4ddcc9347715e11716274684.json"
  checkout_data:
    ignore_payments_checkout_data:
      enabled: true
  events:
    cart_back_press:
      enabled: true
      delay: 2000
      enabled_search_source: true
      enabled_page_identifier: true
      get_rail_enabled: true
      sync_call_percentage: 100
      worker_size: 2
      job_size: 1000
      get_similar_carts_timeout: 5000
  upstream:
    train_fulfilment_service:
      rpc:
        trigger_irctc_app_order_cancellation:
          is_killed: false
          retry_enabled: true
          timeout: 1s
  dynamic_addons:
    is_enabled: true
  express_food_order:
    is_enabled: true
    hide_inapplicability_below_threshold: true
    inapplicability_hiding_threshold: 15
    show_inapplicability_state_in_toast: true
    hide_inapplicability_for_menu_high_rush: true
    flow_shipment_speed_in_cart_request: true
    popup_enabled: true
    power_ui_assets_from_cart_service: true
    bypass_experiment_check_on_ui: true
    express_dc_discount_popup:
      is_enabled: false
  zomato_add_money_changes:
    is_enabled: false
    zoman_enabled: false
    whitelisted_users: []
    consider_config_for_state_handling: false
  multi_cart:
    post_order_payment:
      is_enabled: false
  something_went_wrong:
    max_display_count: 3
  non_logs_banner:
    is_enabled: true
  create_cart:
    enable_charge_animation: true
    outline_fi_button_enabled: 10
    outline_fi_button_eternal_enabled: true
  surge_fee:
    show_free: true
    should_update_edt_icon: true
  delivery_fee:
    show_free: true
  pet_cake:
    enabled: true
cart_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: "cart-service"
  create_cart:
    menu:
      max_goroutine_count: 5
  error_popup:
    benefits_updated:
      enabled: false
      zoman_enabled: false
      enabled_percentage: 0
  sdk_event_data:
    is_enabled: true
  item_recommendations:
    is_enabled: true
  profile_switcher:
    should_check_eligible_flows: true
  rpc:
    get_cart_by_cart_id:
      admin:
        whitelisted_users: [264385628, 0]

subscription_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: "subscription-service"
  unidays_client_id: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  unidays_scope: "openid member contact member-status:academic:student offline_access institution"
  verification_delay: "2000"
  verification_retries: 0
  cart_address_enabled: true
  otg_scrarch_card_enabled: true
  omit_success_popup_aspect_ratio: true
  dark_mode:
    enabled: true
    enabled_for_zomans: true
    enabled_for_all: true
    test_user_ids: []
    enabled_for_eternal_linked_users: true
  json_lottie_migration:
    enabled_for_zomans: true
    enabled_for_all: true
    user_id_start_mod: 0
    user_id_end_mod: 0
  dashboard:
    bypass_auth: true
    whitelisted_users: [1, 2, 3]
    admin_users: [1, 2, 3]
    restricted_timings:
      activate_campaign: []
      deactivate_campaign: ["1600_1700", "1300_1500"]
      update_campaign: ["0000_2359"]
    update_plan_group_criteria_with_campaign: false
  get_valid_subscriptions_for_entity:
    timeout_ms: 5000
    enabled: true
  get_campaigns:
    timeout_ms: 5000
    enabled: true
  get_active_subscriptions_for_entity:
    timeout_ms: 5000
    enabled: true
  get_subscription_details_by_id:
    timeout_ms: 5000
    enabled: true
  get_plan_entity_listing:
    timeout_ms: 5000
    enabled: true
  get_subscription_order_status:
    timeout_ms: 5000
    enabled: true
  initialise_subscription_order:
    timeout_ms: 5000
    enabled: true
  get_active_plan_configs:
    timeout_ms: 5000
    enabled: true
  get_plan_details:
    timeout_ms: 5000
    enabled: true
  get_user_properties:
    timeout_ms: 5000
    enabled: true
  get_plan_listing_eligiblity_info:
    timeout_ms: 5000
    enabled: true
  validate_subscription_for_entity:
    timeout_ms: 5000
    enabled: true
  get_verification_status:
    timeout_ms: 5000
    enabled: true
  create_subscription_extension:
    timeout_ms: 5000
    enabled: true
  get_res_listings:
    timeout_ms: 5000
    enabled: true
  get_cart_info:
    timeout_ms: 5000
    enabled: true
  create_discount:
    timeout_ms: 2000
    enabled: true
  update_discount_status:
    timeout_ms: 2000
    enabled: true
  process_approval:
    timeout_ms: 2000
    enabled: true
  claim_delight:
    timeout_ms: 1000
    enabled: true
  get_user_delights:
    timeout_ms: 1000
    enabled: true
  auto_renew_subscription:
    timeout_ms: 5000
    enabled: true
  create_partnership:
    timeout_ms: 5000
    enabled: true
  list_discounts:
    timeout_ms: 5000
    enabled: true
  cancel_subscription:
    timeout_ms: 5000
    enabled: true
  refund_subscription:
    timeout_ms: 5000
    enabled: true
  update_membership_state:
    timeout_ms: 5000
    enabled: true
  update_permission:
    enabled: true
    timeout_ms: 5000
  create_team:
    enabled: true
    timeout_ms: 5000
  join_team:
    enabled: true
    timeout_ms: 5000
  remove_member:
    enabled: true
    timeout_ms: 5000
  update_team_status:
    enabled: true
    timeout_ms: 5000
  get_applicable_delights:
    enabled: true
    timeout_ms: 5000
  refund_orders_for_entity:
    enabled: true
    timeout_ms: 5000
  unidays_callback_url: "https://zomato-api-gateway.eks.zdev.net/gw/ext/subscription/unidays/callback"
  gold_uae:
    enabled: true
    intro_enabled: true
    enabled_for_zomans: true
    whitelisted_user_ids: [209907968, *********, 200370979, ********, ********, 175191155, *********, 192432245]
    enabled_for_all: false
    enabled_city_ids: [51, 56, 57]
    intro_page_info_banner:
      enabled: true
      header_title: "HSBC Exclusive"
      non_member_banner_url: "subscription/1d3bf6f134e73ac47bbc6319ff02feba1732797589.png"
      non_member_dark_theme_banner_url: "subscription/343a02c6af0906d3fba5a70f9b413ec51732797582.png"
      non_member_banner_aspect_ratio: 2.74
      member_banner_url: "subscription/5bc08ec240d2ea310b9354a1501794441732797574.png"
      member_dark_theme_banner_url: "subscription/fa5316bb2ad341ff19dd27afc573c31f1732797562.png"
      member_banner_aspect_ratio: 1.44
  dc_packs:
    enabled: true
  promo_packs:
    enabled: true
    side_menu_enabled: true
    side_menu_enabled_for_all: true
    side_menu_enabled_for_zomans: true
    remodelling_enabled: true
    whitelisted_user_ids: ["*********", "********"]
  brand_loyalty:
    haldirams:
      bypass_linked_user_check: true
  gold:
    reduced_distance:
      bottom_container_title:
        enabled: true
    gold_friday_sale: true
    is_zomato_bday: true
    bday_sale_enabled: true
    intro_res_rail_enabled: true
    enabled: true
    enabled_for_zomans: true
    listing_rpc_enabled: true
    new_share_flow_enable: true
    otg_enabled: true
    vip_mode_enabled: true
    inapplicable_city_ui_enabled: true
    distance_extension_enabled: true
    app_icon_flow_enabled: true
    app_icon_flow_enabled_for_everyone: true
    app_icon_whitelisted_user_ids: [*********, ********]
    purchase_cart_v2_flow_user_ids: [16333309]
    purchase_cart_v2_flow_enabled_for_all: true
    purchase_cart_v2_enabled_for_gold_lite: true
    divine_banner_enabled: true
    divine_droid_fallback_enabled: true
    noon_one_integration_enabled: true
    device_limiting_ids: ["*********", "*********"]
    remove_non_logs_free_dc_enabled: true
    tier1_city_list: [3, 1, 4, 5, 6, 2, 11, 7, 10, 12, 14, 8, 33, 20, 9, 30, 32, 38, 29, 28, 13, 40, 11310, 22, 36, 23, 11300, 21, 11333, 34, 31, 26, 11054, 27, 11306, 25, 39, 35, 11336, 16, 11337]
    emerging_city_list: [11380, 11435, 11357, 11366, 11429, 11593, 11885, 11439, 11314, 11374, 11448, 11398, 11455, 11557, 11385, 11520, 11291, 11395, 11383, 11346, 11481, 11340, 11463, 11514, 11798, 11470, 11394, 11460, 11354, 11561, 11474, 11316, 11480, 11410, 11494, 11449, 11459, 11434, 11486, 11529, 11378, 11787, 11462, 11432, 11504, 11390, 11496, 11468, 11564, 11416, 11406, 11476, 11523, 11550, 11414, 11319, 11583, 11475, 11703, 11322, 11687, 11534, 11441, 11473, 11418, 11500, 11553, 11452, 11511, 11297, 11446, 12674, 11444, 11477, 11540, 11954, 11585, 12065, 11613, 11555, 11660, 11712, 11351, 11490, 11427, 11458, 11546, 11502, 11599, 11489, 11786, 11595, 11505, 11492, 11893, 11742, 11430, 11466, 11425]
    device_limiting_enabled_for_all: false
    enabled_country_ids: [1, 214]
    savings_promise_enabled: true
    platform_otg_expt_name: ""
    platform_otg_expt_tg_list: []
    exclude_payment_sdk_for_free_subscription: true
    tell_your_friends_flow_enabled: true
    free_dc_know_more_section_enabled: true
    free_dc_know_more_section_min_ios_version: "20.0.0"
    free_dc_know_more_section_min_android_version: "2000"
    free_dc_know_more_section_min_android_beta_version: "194"
    bottomsheet_top_image_support_min_android_version: "2000"
    bottomsheet_top_image_support_min_android_beta_version: "194"
    device_limiting_logout_flow_enabled: true
    device_limiting_click_action_logout_flow_enabled: true
    device_limiting_click_action_logout_flow_enabled_for_all: true
    device_limiting_logout_flow_whitelisted_ids: []
    device_limiting_logout_flow_user_id_start_mod: 0
    device_limiting_logout_flow_user_id_end_mod: 100
    device_limiting_logout_from_other_devices_domain: "https://accounts.eks.zdev.net"
    device_limiting_logout_flow_zoman_enabled: true
    fiesta: true
    voucher_v2_flow_enabled: true
    carnival_flow:
      enabled: true
      enabled_for_all: true
      whitelisted_user_ids: ["*********", "********", "*********", "*********"]
    delights_flow:
      enabled: true
      enabled_for_all: true
      enable_eligible_delights: false
      whitelisted_user_ids: ["*********", "*********", "*********", "********"]
    bot_builder_support:
      enabled: false
      whitelisted_user_ids: ["*********"]
      enabled_for_zomans: false
      ios_rollout_percentage: 0
      android_rollout_percentage: 0
      min_ios_version: "20.00.0"
      min_android_version: "2000"
      min_android_beta_version: "800"
    coupon_section:
      enabled_for_all: true
      enabled_for_zomans: true
      whitelisted_user_ids: ["*********", "********"]
      new_tracking_source_enabled: true
      zomaland_discount_price: 200
      zomaland_expiry_unix_time: ********00
      zomaland_section_enabled_city_ids: [1]
      zomaland_image_url: "data/o2_assets/53892d9d60b6e0161756dfc2f23c12be1702467030.png"
      zomaland_image_aspect_ratio: 2.823
      gold_lite_promos_enabled_for_all: true
      valid_promo_new_cart_ui_enabled: true
    show_member_till_title_text_for_ufr: true
    mov_reduction:
      banner:
        enabled: true
        whitelisted_users: [ 364240133 ]
        enabled_for_all: true
        banner_url: "data/o2_assets/8b7cc19c702f61f330a1f5313532b99d1701959188.png"
        banner_aspect_ratio: 2.507
    surge_fee:
      banner:
        enabled: true
        whitelisted_users: [364240133]
        enabled_for_all: true
        banner_url: "data/o2_assets/8b7cc19c702f61f330a1f5313532b99d1701959188.png"
        bottomsheet_url: "data/o2_assets/97424e1b010d02397edd8bd0b1337f051746620068.png"
        bottomsheet_dark_url: "data/o2_assets/c4ab4532142c7571a6b522532cdf4ae01746620087.png"
        banner_aspect_ratio: 2.507
        bottomsheet_aspect_ratio: 1.2
      surge_term:
        enabled: true
        whitelisted_users: [364240133]
        enabled_for_all: true
      cancellation_popup:
        enabled: true
        whitelisted_users: [364240133]
        enabled_for_all: true
    district_migration:
      enabled: true
      whitelisted_users: [ ]
      enabled_for_all: false
      banner:
        migration_pending_image_url: ""
        migration_pending_image_aspect_ratio: 0.0
        migration_done_image_url: ""
        migration_done_image_aspect_ratio: 0.0
        migration_webview_deeplink: ""
  get_serviceable_source_entities_for_user_rpc:
    kill_switch: true
  phone_verification_v2:
    enabled: true
  gold_teams:
    enabled: true
    whitelisted_user_ids: [206399975]
zpl:
  web_api_key: 7e61b7c3035bc2a2dedb7d46f6313763
aerobar_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: "aerobar-service"
  get_aerobar_details:
    timeout: 5000ms
    reviews_revamp_enabled: true
  template_operation:
    allowed_user_ids: []
  update_template:
    allowed_user_ids: []
  aerobar_event_enqueuer:
    enabled: true
  get_aerobars_from_web:
    enabled: false
    timeout: 10s
    web_endpoint: "/v2/aerobar.json"
    jumbo_logging:
      enabled: true
      enabled_for_eternals: true
      enabled_user_percentage: 100
      enabled_user_ids: []
      enabled_for_guest_user_ids: true
  get_aerobars_from_gateway:
    enabled: true
    enabled_for_zomans: true
    enabled_for_eternals: true
    enabled_for_eternal_linked: true
    enabled_user_ids: []
    enabled_user_percentage: 100
    enabled_user_mod_factor: 347
    enabled_for_guest_user_ids: true
  group_ordering:
    enabled: true
  aerobars:
    local_aerobars:
      config_enabled: true
      consumer_app:
        aerobar:
          enabled: true
          enabled_user_ids: []
      cart:
        mro:
          rollout:
            enabled: true
            test_user_ids: []
            enabled_percentage: 100
          introduction:
            global_impression_count: 2
            tooltip_global_impression_count: 2
    o2_aerobar:
      local_aerobar_menu_redirection:
        enabled: true
  reads_allowed_templates: ["table_reservation", "online_ordering", "intercity_online_ordering", "blinkit_online_ordering", "dining", "subscription", "calling", "online_order_sharing", "premium_referral"]
  mqtt_data:
      username: "zemqtt"
      password: "2Lw2JUq0QYA0zT"
      keepalive: 900
onesupport:
  host: chat-backend-v2.eks.zdev.net
  api_protocol: https
  auth_username: *******
  auth_password: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  access_token_timeout_ms: '1000'
  payload_token_timeout_ms: '1000'
  default_chat_tag: delivery
  peoplebot:
    auth_username: "*******"
    auth_password: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  business_password:
    zomato:
      online_ordering: ""
      z_butler: ""
      gold: ""
      blinkit_grocery: ""
      enterprise: ""
      tr: ""
    eternal:
      worker_team: ""
  rm:
    is_enabled: true
    allowed_user_ids: [917011860685]
    whitelisted_ips: ["*******"]
    profile_store:
      is_udpp_enabled: true
      is_udpp_sha1_encoding_enabled: true
  hotline:
    exotel:
      enabled: false
      whitelisted_ips: ["*************", "***********", "***********", "*************", "**************", "*************", "**************", "**************", "*************", "**************"]

incident_dashboard:
  allowed_emails: ******* ******* ******* ******* *******
  kafka_topic: zomato.zomato-api-gateway.bulk-notification
service_kill_switches_dashboard:
  allowed_emails: ******* ******* ******* ******* *******
consul:
  host: **************:8500
  token: ' '
  update_allowed_keys: web/application/web/akamai_cache/restaurant/test/is_enabled web/application/web/akamai_cache/restaurant/test/disabled_cities
  update_value_timeout_ms: '1000'
  get_value_timeout_ms: '1000'
  datacenter: dc1
  host_header: ' '
login_with_zomato:
  magicpin:
    app_signature: 3119d2517cfe089edf050c426ab69f0460b83da3517b950faa1abcd7158e1e0b
    package_id_android: com.magicpin.local
    package_id_ios: in.magicpin.ios
    checksum_key: 50c426ab69f046ab950f1e0ba1abcd7158e0b833119d2517cfe089edf0da3517
  blinkit:
    app_signature: 6db1523d9b8dd152f78beba1719370d40ff8619899b49b5d29677bedfa0016ba
    package_id_android: com.grofers.customerapp
    package_id_ios: com.grofers.consumer
    checksum_key: 3d9b8dd152f0ff8619899b449b5d29677be49b5d29677beb1523d9b8dd0d40ff3
    dob_scope_enabled: true
  blinkit_in_zomato:
    app_signature: 6db1523d9b8dd152f78beba1719370d40ff8619899b49b5d29677bedfa0016ba
    package_id_android: com.application.zomato
    package_id_ios: com.zomato.zomato
    checksum_key: 3d9b8dd152f0ff8619899b449b5d29677be49b5d29677beb1523d9b8dd0d40ff3
  goout:
    app_signature: "6db1523d9b8dd152f78beba1719370d40ff8619899b49b5d29649b5d29677be6ba"
    package_id_android: "com.application.zomato.opa"
    package_id_ios: "com.zomato.zomato"
  blinkit_lit:
    app_signature: "6db1523d9b8dd152f78beba1719370d40ff8619899b49b5d29649b5d29677be6ba"
    package_id_android: "com.grofers.customerapp.lit"
    package_id_ios: ""
  xtreme:
    app_signature: "6db1523d9b8dd152f78beba1719370d40ff8619899b49b5d29649b5d29677be6ba"
    app_signture: "6db1523d9b8dd152f78beba1719370d40ff8619899b49b5d29649b5d29677be6ba"
    package_id_android: "com.application.zomato.xtreme"
    package_id_ios: ""
    checksum_key: "3d9b8dd152f0ff8619899b449b5d29677be49b5d29677beb1523d9b8dd0d40ff3"
  bistro:
    app_signature: "6db1523d9b8dd152f78beba1719370d40ff8619899b49b5d29649b5d29677be6ba"
    package_id_android: "com.application.zomato.bistro"
    package_id_ios: ""
  district:
    app_signature: "6db1523d9b8dd152f78beba1719370d40ff8619899b49b5d29649b5d29677be6ba"
    package_id_android: "com.application.zomato.district"
    package_id_ios: "com.district.prod"
side_menu:
  partner_rewards:
    enabled: false
    enabled_for_zomans: false
    enabled_for_all: false
    enabled_percentage: 0
    whitelisted_user_ids: []
  coupon_section_v2:
    enabled: true
    enabled_user_ids: []
    enabled_for_zomans: true
    rollout_percentage: 0
  sections_subtitle_support:
    enabled: true
    enabled_user_ids: []
    get_rewards_data_for_user:
      enabled: true
      enabled_user_ids: []
      rollout_percentage: 0
  accessibility_section:
    enabled: true
  order_history_section:
    enabled: true
  user_activity:
    is_killed: true
  liked_v2:
    enabled: false
  hidden_res:
    enabled: true
    android_version: 2000
    ios_version: 22.0.0
    android_beta_version: 300
    enabled_for_zomans: false
    enabled_for_everyone: false
  restaurant_awards:
    enabled: true
    enabled_user_ids: [164193566]
    live_cities: ["1", "2", "4", "20", "3", "32", "11", "30", "12", "11290", "26", "14", "21", "7", "8", "33", "40", "11306", "9", "11302", "38", "11300", "29", "35", "10", "28", "13", "23", "6", "5", "34", "22", "11310", "31", "36", "27", "16", "24", "11307"]
    enabled_for_zomans: true
  review_service:
    customer_rating:
      enabled: 'true'
  free_dc_zomans:
    live_countries: 1 214
    live_cities: 1 5552 3828
    enabled: 'true'
  table_booking:
    allowed_cities: '1'
    enabled: 'true'
  table_booking_v2:
    allowed_cities: '1'
    enabled: 'true'
    chatbot_killed: 'false'
  grocery:
    enabled: 'true'
    serviceability_via_cell:
      enabled: 'true'
      city_android_config: '{"0":20,"5":100,"3":100,"4":100}'
      city_ios_config: '{"0":100}'
      enabled_dszs: '-1'
      remove_city_check_enabled: true
    grocery_order_section_enabled: 'true'
  dining:
    live_cities: '6'
    enabled: 'true'
    pay_without_deals_cities: ["51", "56", "57", "92"]
    events:
      whitelisted_users: ["-1"]
      live_cities: ["1", "2", "3", "4", "5", "6", "7", "8", "10", "11", "12", "13"]
      enabled: false
      chat_enabled: false
    settings:
      enabled: true
      user_buckets: [6]
  dining_unlock:
    allowed_cities: ["1"]
    enabled: false
    enabled_for_zomans: false
    enabled_user_ids: [********]
  zomaland:
    allowed_countries: [1]
    allowed_cities: [2, 4, 6]
    active_users: []
    enabled_percentage: 0
    enabled_for_zomans: false
    enabled_for_all: false
    chat_enabled: false
  fic:
    allowed_countries: [1]
    active_users: []
    enabled_cities: [1]
    enabled_percentage: 0
    enabled_for_zomans: false
    enabled_for_all: false
    side_menu_section_enabled: true
  feeding_india:
    enabled: false
  money:
    enabled: true
    money_section_item:
      enabled: true
    edition_card:
      enabled: true
    zomato_wallet:
      enabled: true
      enabled_for_zomans: true
      whitelisted_user_ids: []
      rollout_percentage: 0
    enterprise_meals:
      enabled: true
      lead_generation:
        enabled: false
    gift_card:
      enabled_gift_card_claim: false
      enabled_history_section: false
      enabled_balance_section: false
      enabled_support_section: false
    zcredits:
      enabled: false
    page:
      enabled: true
      show_balance: true
    payments:
      enabled: true
    zomato_upi:
      enabled: true
      enabled_for_zomans: true
      whitelisted_user_ids: []
      enabled_user_perc:
        min: 0
        max: 100
  zomato_report_card:
    enabled: true
    enabled_for_all: true
    enabled_user_ids: []
  gift_card:
    is_killed: 'false'
    allowed_countries: [1]
    enabled_for_all: 'false'
    enabled_for_zomans: 'false'
    test_user_ids: [16333309, 159328115, 186163479]
  birthday_top_container:
    enabled_percentage: 100
    enabled_for_zomans: true
    allowed_user_ids: [2001]
  purchase_gift_card:
    is_killed: true
    test_user_ids: []
    enabled_for_zomans: false
    enabled_percentage: 0
  app_rating:
    enabled_for_android: true
    enabled_for_ios: true
  referral:
    enabled: true
  subscription:
    enabled: true
    campus: true
    gold: true
    gold_selling_section_enabled: true
    gold_profile_section_enabled: true
    free_deliveries_section_enabled: true
    gold_coupon_section:
      enabled_for_all: true
      whitelisted_user_ids: ["*********", "********"]
  pro:
    enabled: true
    chat_enabled: false
    new_app_icon_flow_enabled_for_everyone: true
    new_app_icon_flow_whitelisted_user_ids: []
  upi:
    enabled: true
    enabled_for_zomans: false
    enabled_user_perc:
      min: 0
      max: 0
    test_user_ids: [252112359, 179100063, 122180089, ********, 67725074, 254941896]
  language_section:
    enabled: 'true'
  badges_2022:
    enabled: 'false'
    enabled_for_zomans: 'true'
  quiz_hour:
    enabled: 'false'
  games:
    enabled: true
    zpl:
      enabled_for_all: true
      enabled_for_zomans: true
    trivia:
      enabled_for_all: false
      enabled_for_zomans: false
    handcricket:
      enabled: true
      disabled_user_ids: [164193566]
      enabled_percentage: 10
      enabled_for_uae: true
      enabled_for_zomans: true
  edition:
    enabled: true
    enabled_for_zomans: true
    enabled_user_perc:
      min: 0
      max: 100
    card:
      enabled: true
    wallet:
      enabled: true
  coupons_section:
    enabled: true
    is_empty_block_enabled: false
    membership_coupons_enabled: true
  pure_veg_mode_section:
    enabled: true
    enabled_for_zomans: true
    is_pure_veg_updated_at_flow_enabled: true
    is_pure_veg_video_flow_enabled: true
    live_cities: ["1"]
    pure_veg_v2_enabled: true
    pure_veg_v2_enabled_user_ids: []
    pure_veg_v2_enabled_zomans: true
    radio_buttons_v2_text_enabled: true
    smart_veg_mode_enabled_user_ids: []
    smart_veg_mode_enabled_for_zomans: true
    smart_veg_mode_enabled: false
    smart_veg_mode_exp_bypass_enabled: true
  personalised_rating:
    enabled: true
    enabled_for_zomans: true
    enabled_for_all: true
  new_dark_mode_bottomsheet:
    enabled: true
  dark_mode_toggle:
    enabled: true
    enabled_user_ids: []
    enabled_for_eternals: true
    enabled_for_eternal_linked: false
    enabled_for_all: false
    enabled_percentage: 5
  new_dark_mode:
    enabled: true
    enabled_for_all: false
    enabled_user_ids: [*********, ********, ********, *********, *********, *********]
  dark_mode_bottomsheet:
    enabled: true
    enabled_for_eternals: true
    enabled_for_eternal_linked: false
    enabled_percentage: 0
    enabled_user_ids: []
  delete_account:
    v2_flow_enabled_percent: 10
  feeding_india_section:
    enabled: true
    enabled_user_percentage: 100
    enabled_user_ids: []
  zomato_nye_look_back:
    enabled: true
    enabled_user_percentage: 100
    enabled_user_ids: []
    enabled_for_zomans: true
  train_ordering:
    enabled: true
    enabled_user_percentage: 100
    enabled_user_ids: []
    enabled_for_zomans: true
  user_profile_image:
    accessibility_info:
      enabled_for_zomans: true
      enabled_for_all: true
  manage_recommendations:
    new_landing_page_enabled: true
store_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: store-service
  get_store:
    timeout_ms: 1000
photos_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: photos-service
payment_router_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: payment-router-service
  timeout_in_ms:
    checkout_page: 4000ms
    default: 2000ms
menu_aggregator_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: "menu-aggregator-service"
  max_receive_msg_length_mb: 4
  timeout: 20000
  get_recommended_carts:
    enabled: true
    request_timeout_ms: 1500
    per_try_timeout: 1000
  get_user_order_recommendations:
    timeout: 500
  get_items_detail:
    timeout: 500
  get_user_order_details:
    timeout: 1000

serviceability_agg_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: serviceability-aggregator-service
  get_serviceability_v1:
    timeout: 1000ms
  get_serviceability:
    request_timeout_ms: 10000
  insert_subscription:
    request_timeout_ms: 10000
  get_slot_serviceability:
    request_timeout_ms: 10000
  get_slot_serviceability_api:
    enabled: true
    slot_duration_mins: 30
    slot_start_lead_time_mins: 120
    slot_end_lead_time_hrs: 48
    should_check_ioos: true
    order_planner_show_location_snippet: true
    should_show_location_snippet: false
  get_all_available_scheduling_slots:
    enabled: true
    request_timeout_ms: 10000
  default_slots:
    get_slots_from_sa:
      enabled: true
serviceability_manager_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: sm-manager-grpc-sm-manager-svc
demand_manager_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: demand-manager-service
consumer_menu:
  offer_items_recommendations:
    test_catalogue_ids: ["ctl_576028671", "ctl_576028672"]
    v2:
      enabled: true
  mandatory_modifier_group:
    sub_title_text:
      enabled_max_value: 5
  footer_button_config:
    bg_color_enabled: true
  dish_sharing:
    redirection_enabled: true
    owner_redirection_enabled: true
  pet_tag_spacing:
    enabled: true
  test_schedule_blocker:
    enabled_user_ids: []
  multi_salt:
    enabled: true
    multi_promo_category_enabled: true
    snackbar:
      savings_state_type_enabled: true
    freebie:
      enabled: true
  exclusive_discount:
    enabled: true
    enabled_for_zomans: true
    hide_offer_text: true
  auto_scroll_to_category_for_droid: true
  crazy_drops:
    global_limit: 10
    item_limit: 3
    should_set_item_limit: true
  footer_optimisation_enabled: true
  tag_separator:
    enabled: true
  user_personalised_rating:
    search_params_enabled: true
  rating_color_threshold_change:
    enabled: true
  accessibility_mode:
    enabled: true
  filters:
    color_gradient_for_filter_enabled: true
  bestseller_new_tag:
    enabled: true
  only_for_pets_tag:
    enabled: true
  remove_auto_apply_filter:
    enabled: true
  barebone:
    enabled: true
    enabled_user_ids: []
    enabled_user_mod: 0
    allowed_level: 3
    endpoints:
      getconsumermenu:
        enabled: true
        killed_user_ids: []
        killed_user_mod: 0
      getfooterdata:
        enabled: true
        killed_user_ids: []
        killed_user_mod: 0
      getrecommendeditemsonmenu:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      getmenusearchconfig:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      getmenuconfig:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      getcsaodata:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      getinterstitialdata:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      getmenustartdata:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      updateresvisibility:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      processfeatureevent:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      getitemavailability:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      processserviceabilityreminder:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      getpriceparityinfo:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      raisepriceparityticket:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      feedback:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      matchscorefeedback:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      getreviewinsights:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      getcuratedpack:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      cataloguehandler:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      getfriendsrecommendations:
        enabled: true
        killed_user_ids: [ ]
        killed_user_mod: 0
      getofferitemsrecommendation:
        enabled: true
        killed_user_ids: []
        killed_user_mod: 0
    rpc:
      getconsumermenu:
        killed_user_ids: []
        killed_user_mod: 0
      getfooterdata:
        killed_user_ids: []
        killed_user_mod: 0
      getheaderdata:
        killed_user_ids: []
        killed_user_mod: 0
      getmenuoffers:
        killed_user_ids: []
        killed_user_mod: 0
      getaccesstokendetails:
        killed_user_ids: []
        killed_user_mod: 0
      getmenusearchconfig:
        killed_user_ids: []
        killed_user_mod: 0
      getbucket:
        killed_user_ids: []
        killed_user_mod: 0
      getcsaorecommendations:
        killed_user_ids: []
        killed_user_mod: 0
      getcartitemsdetail:
        killed_user_ids: []
        killed_user_mod: 0
      getcuratedpack:
        killed_user_ids: []
        killed_user_mod: 0
      getitemdetail:
        killed_user_ids: []
        killed_user_mod: 0
      getresinfo:
        killed_user_ids: []
        killed_user_mod: 0
      getusereventinfo:
        killed_user_ids: []
        killed_user_mod: 0
      processfeatureevent:
        killed_user_ids: []
        killed_user_mod: 0
      uploadimageinstruction:
        killed_user_ids: []
        killed_user_mod: 0
      getstore:
        killed_user_ids: []
        killed_user_mod: 0
      getcampaigns:
        killed_user_ids: []
        killed_user_mod: 0
      getactivesubscriptionsforentity:
        killed_user_ids: []
        killed_user_mod: 0
      batchgetusers:
        killed_user_ids: []
        killed_user_mod: 0
      fetchuserprofilefromuserid:
        killed_user_ids: []
        killed_user_mod: 0
      retrieveimageinstruction:
        killed_user_ids: []
        killed_user_mod: 0
      getapiauthdetailsbyapikey:
        killed_user_ids: []
        killed_user_mod: 0
      getconsumermenuforirctc:
        killed_user_ids: []
        killed_user_mod: 0
      getconfigmenu:
        killed_user_ids: []
        killed_user_mod: 0
  res_badges:
    enabled: true
    positive_callout:
      enabled: true
    negative_callout:
      enabled: true
      hardcoded_res_ids: []
  bottom_timer:
    enabled: true
    enabled_user_ids: []
    trigger_action_when_menu_on_top: true
  express_food:
    quick_enabled: true
    enabled: true
    enabled_user_ids: []
    pill_info:
      enabled: true
    success_toast:
      enabled: true
    failure_toast:
      enabled: true
  should_show_only_filter_applied_config: true
  interstitial:
    remove_header_snippet: true
    rpc_enabled: true
    enabled: true
    snackbar:
      enabled: true
      enabled_user_ids: [ 249319746 ]
    response_log_enabled: true
  spillage_free_cake_campaign:
    enabled: true
  cake_zero_spill_guarantee:
    enabled: true
    enabled_user_ids: [63274230]
    enabled_for_all: true
  dynamic_modifier_group:
    ios_enabled: true
    android_enabled: true
  report_menu_enabled: false
  curated_pack:
    ios_enabled: true
    android_enabled: true
  reviews:
    disabled: true
  dynamic_add_on:
    dynamic_discounted_price_enabled: true
  aerobar_menu_items_enabled: true
  menu_config:
    onboarding_config:
      enabled: true
      animation_max_limit: 1
      max_res_fetch_count: 1
    search_config:
      enabled: true
    media_config:
      default_image_background_enabled: true
    get_config_menu:
      enabled: false
  durga_puja_tags:
    enabled: true
  ganesh_chathurthi:
    enabled_user_ids: [1447355027]
    enabled_for_all: true
    enabled_res_ids: [5172]
    items_limit:
      enabled: true
      max_limit_count: 2
  should_disable_new_bxgy_logic: false
  cinema_flow_eta_enabled: true
  most_ordered_combos:
    enabled:
      ios: true
    show_all_salts:
      enabled_user_ids: []
      enabled_for_all: false
  time_service_migration:
    enabled_user_ids: [12341]
    enabled_user_mod: 0
  media_tags:
    packaging_tag:
      enabled: true
    ar_icon_on_image:
      enabled: true
  user_call_migration:
    enabled_user_ids: [266349740]
    enabled_percentage: 100
  chain_selector_res_thumb_url:
    enabled: true
  spill_safe_cakes_flow:
    enabled: true
  scheduling_info:
    pass_contextual_menu_items: true
    show_time_with_scheduling: true
    show_time_with_scheduling_enabled_user_ids: []
    fathers_day:
      flow_enabled: true
      enabled_user_ids: []
      testing_enabled: true
      testing_dates: ["20250530"]
      tooltip_enabled: true
  single_dish_price_on_cart:
    enabled: true
  sub_info_container:
    enabled: true
    should_animate: true
    should_animate_shine: true
  sorting_filter_on_top:
    enabled: true
  haldiram_loyalty_program:
    enabled: true
  dynamic_dietary_tag:
    enabled: true
    enabled_user_ids: [266349740]
    multiplier: 17
    enabled_user_mod: 100
    zoman_enabled: true
  mx_escalation:
    enabled: true
  gift_hampers:
    enabled_res_ids: [306071, 302115]
    enabled_for_all_res: false
  friends_recommendations:
    header_pill:
      enabled_for_all: true
      test_user_ids: []
    disable_user_id_in_collection_deeplink: true
  bookmark_collections:
    shared_collection_pill_enabled: true
    recommended_collections_pill:
      enabled_for_all: true
      enabled_user_ids: []
      enabled_city_ids_for_latest_ui: []
    item_card_ui_enabled: true
    enabled: true
  video_in_customisation:
    enabled: true
  cart_limit:
    max_items_allowed_count: 10
    enabled_res_ids: [306071]
  dark_mode:
    enabled: true
  searchbar_animation:
    enabled: true
  permanently_off:
    enabled: true
    enabled_res_ids: []
    enabled_chain_ids: []
  override_rain_icon: true
  hide_customisation:
    enabled: true
    ios_enabled: true
    android_enabled: true
    enabled_for_items_with_no_variants: true
  bottom_searchbar:
    enabled: true
    tooltip_enabled: false
  bottom_buttons:
    schedule_for_later:
      enabled_user_ids: []
      enabled_perc_mod: 100
  header_v3:
    zume:
      enabled_user_ids: []
      enabled: true
      mock:
        enabled: true
        restaurants_count: 8
      should_show_right_image: false
    unserviceable_res_spacing_fix:
      enabled: true
    priority_delivery_banner:
      enabled: true
    timestamp_time_string:
      enabled: true
    delayed_prep_banner:
      enabled: true
    distance_updated:
      force_enable_user_ids: [189856521]
    surge_charge_info:
      enabled_user_ids: [189856521]
      enabled_city_ids: [1]
      enabled: true
    unserviceable_assets_v2:
      enabled: true
    section_count_updation_enabled: true
    pre_order_only_res:
      enabled: true
      enabled_user_ids: [ ]
    enable_on_higher_version: false
    multiple_promo_title:
      text: "Get 10% off"
      enabled: true
    unserviceable:
      enabled: true
    free_delivery:
      enabled: true
    closing_soon:
      enabled: true
    rating:
      tooltip_disabled: true
      static_tooltip_disabled: false
      res_ratings:
        new_color_theme:
          enabled: true    
    enabled: true
    res_awards:
      enabled: true
    match_score:
      enabled: true
      v2_feedback_flow_enabled: true
    on_time_guarantee:
      enabled: true
      enabled_user_ids: []
      masthead:
        enabled_user_ids: []
        enabled: false
      enable_tnc_faq: true
    hygiene:
      enabled: true
      view_report_enabled: true
      enable_new_bottom_sheet: true
    irctc:
      enabled: true
    pure_veg:
      enabled: true
    price_parity:
      enabled: true
    cake_callout:
      enabled: true
    hybrid_dietary_res:
      enabled: true
    hide_res:
      enabled: true
      auth_for_guest_users: true
    rain_delay:
      enabled: true
    high_rush:
      enabled: true
    late_relay:
      enabled: true
    price_parity_v1:
      enabled: true
    self_logs:
      enabled: true
  innovation:
    enabled: true
    disable_os_versions: true
    social:
      enabled_res_ids: []
      enabled_for_all_res: false
    brand_logo:
      enabled_user_ids: []
      enabled_for_zomans: true
      enabled_for_all: true
    default_state: expanded
  iconic:
    enabled: true
  res_info:
    kill_switch:
      user_profile: false
      experiment: false
      profile_store: false
    fetch_from_agg: true
    adverse:
      enabled: true
    empty:
      enabled: true
    food_hygiene:
      enabled: true
      enabled_for_ios: true
      enabled_for_android: true
      view_report_enabled: true
    fssai:
      enabled: true
    gst_data:
      enabled: true
    hide_res:
      enabled: true
    image_header:
      enabled: true
    legal_name:
      enabled: true
    multi_brand:
      enabled: true
    offers_dining:
      enabled: true
    order_count:
      enabled: true
    report_fraud:
      enabled: true
    res_details:
      enabled: true
      phone:
        enabled: true
      direction:
        enabled: true
      dining:
        enabled: true
      customer_complaints:
        enabled: true
    tenure:
      enabled: true
    res_timing:
      enabled: true
    res_images_carousel:
      enabled: true
    tnc:
      enabled: true
  photo_cakes:
    enabled: true
  res_awards:
    enabled_user_ids: []
    enabled_for_zomans: true
    enabled_for_all: true
    winner_enabled: false
  group_ordering:
    skip_cart_item_data: true
    enabled_for_all: true
    zoman_enabled: true
    show_in_nav_more_btn_enabled: true
    enabled_user_ids: []
    auth_for_guest_users: true
    masthead:
      enabled_user_ids: []
      enabled_for_all: false
    max_groups_limit_check:
      enabled: true
      limit: 20
      disabled_for_zomans: false
      disabled_user_ids: []
    header_button:
      disabled_res_ids: []
      disabled_chain_ids: []
      enabled_user_ids: []
      eternal_enabled: true
      enabled_city_ids: []
      enabled_for_all: true
  preselection_customisation:
    empty_bundles_enabled: true
  promo:
    uid_availability_check_bypassed: true
    payday:
      enabled: true
      enable_droid_hardcoding: true
    filter_brunch_oos_offers: true
    should_hide_offer_available_snippet_in_freebie: true
    new_sorting_enabled: true
    cat_sorting:
      enabled: true
    filter_mismatched_promos:
      enabled: true
    filter_referral_promo: false
    recycling_fix_enabled: true
    should_ignore_active_salt_filtering: true
    filter_user_salt: true
    variant_multiple_offers:
      enabled: true
      hide_final_price: true
    discount_pack:
      enabled: true
    remove_non_promo_items_from_mov: true
    enable_item_level_offer_for_full_res_salt: true
    freebies:
      show_hidden_items: true
      auto_remove_freebie_for_reduced_mov: true
  large_orders:
    promo_image:
      enabled_user_ids: []
    auto_scroll_enabled: true
    check_large_order_flow: false
  menu_banned_res_ids: []
  menu_banned_chain_ids: []
  permanently_closed_res_ids: []
  cake_pizza_templates_disabled: true
  pinch_to_zoom_enabled: true
  image_instruction_upload:
    max_file_size_in_mb: 4
    max_file_size_in_mb_ios: 4
    max_file_size_in_mb_android: 4
    cropped_photo_compression_quality: 100
    hardcode_enabled: true
    initial_polling_time_ms: 2000
    user_image: ""
    user_path: ""
    variant_id_1: "ctl_4567"
    preview_image_1: ""
    preview_path_1: ""
    final_template_image_1: ""
    final_template_path_1: ""
    variant_id_2: "ctl_4567"
    preview_image_2: ""
    preview_path_2: ""
    final_template_image_2: ""
    final_template_path_2: ""
    variant_id_3: "ctl_4567"
    preview_image_3: ""
    preview_path_3: ""
    final_template_image_3: ""
    final_template_path_3: ""
    category_ids: ["ctg_5678"]
  addon_grid_view_enabled: true
  pickup_flow:
    promo_enabled: true
    fab_enabled: true
    navigation_actions_enabled: true
  cart_item_details:
    enabled: false
    timeout_ms: 1000
  pure_veg_mode:
    enabled: true
    v2_flow_enabled: true
    hide_non_veg_filter_enabled: true
    v2_flow:
      enabled: true
      enabled_for_zomans: true
      enabled_user_ids: [315743757]
      hide_non_veg_filter_enabled: true
  additional_info:
    mx_escalation:
      enabled: false
    hybrid_dietary_res_callout:
      enabled: true
  clear_customisation:
    enabled: true
    instant:
      enable_clear_button: true
  group_selection_type:
    enabled: true
  healthier_recommendation:
    min_replaceable_item_rail: 5
  ancillary_data:
    enabled: true
  item_details:
    should_force_set_res_data: false
    pass_added_item_in_menu_call:
      enabled: true
    post_body_in_req_enabled: true
    promo:
      enabled: true
    social_buttons:
      enabled: true
      enabled_user_ids: []
    hide_customisation:
      enabled: true
      ios_enabled: true
      android_enabled: true
  items_detail:
    clear_customisation:
      enabled: true
  mithai_o2:
    enabled: true
    enabled_res: [302115, 306071]
    preprocess_killed: false
    o2_enabled: false
    portion_size_v2_enabled: true
    portion_size_migrated: true
  price_parity:
    detractor_snippet:
      enabled: false
      enabled_res: [302115, 306071]
    promoter_snippet:
      enabled: false
      enabled_res: [302115, 306071]
    promoter_v2_snippet:
      max_photo_upload: 5
      max_ticket_raise: 5
      enabled_city_ids: [1]
  process_feature_event:
    enabled: true
  serviceability_reminder_api:
    enabled: true
    response_v2_enabled: true
    response_v2_enabled_user_ids: []
  multi_brand_kitchen_enabled: true
  carnival_flow_enabled: true
  gold:
    rain_surge_ui:
      enabled: true
    add_gold_flow_enabled: true
    add_gold_flow_whitelisted_user_ids: []
    add_gold_flow_menu_states_whitelisted_user_ids: []
    add_gold_flow_user_id_start_mod: 0
    add_gold_flow_user_id_end_mod: 0
  carnival:
    enabled: true
    blocker_enabled: true
    bxgy_snackbar_enabled: true
    percentage_snackbar_enabled: true
    min_ios_version: "17.49.0"
    min_android_version: "2001"
    min_android_beta_version: "196"
    chef:
      enabled_for_everyone: true
      whitelisted_user_ids: [*********]
      header_aspect_ratio: 0
      header_video_url: ""
      oos_header_image_url: ""
      fallback_header_image_url: ""
      opening_soon_header_image_url: ""
      opening_tomorrow_header_image_url: ""
      default_unserviceable_header_image_url: ""
      veg_item_info_image_url: ""
      non_veg_item_info_image_url: ""
      item_aspect_ratio: 1
      veg_info_bottomsheet_image_url: ""
      non_veg_info_bottomsheet_image_url: ""
      bottomsheet_aspect_ratio: 1
      listing_header_image_url: ""
      listing_header_image_aspect_ratio: 1
      veg_item_details_android_image_url: ""
      non_veg_item_details_android_image_url: ""
      oos_veg_item_details_android_image_url: ""
      oos_non_veg_item_details_android_image_url: ""
      item_details_android_image_aspect_ratio: 1
      veg_item_details_image_url: ""
      non_veg_item_details_image_url: ""
      oos_veg_item_details_image_url: ""
      oos_non_veg_item_details_image_url: ""
      item_details_image_aspect_ratio: 1
      eligible_members_header_image_url: ""
      non_member_veg_item_info_image_url: ""
      non_member_non_veg_item_info_image_url: ""
      listing_header_text: ""
  tag_animation:
    enabled: true
    repeat_count: 2
    gap: 1000
  add_on_media_disabled:
    chain_ids: []
    res_ids: []
  video_stories:
    disable_audio: true
  item_videos:
    enabled_for_instant: true
    enabled_user_perc: 100
  distance_rounding_enabled: true
  surge_snackbar_kill_enabled: true
  pure_veg_migration_enabled: true
  header:
    top_video:
      enabled: true
    bg_media_campaign:
      enabled: true
      url: ""
      multiple_time_ranges:
        start_time_array: [1707364800]
        end_time_array: [1707369800]
  promo_salt_subtotal_enabled: true
  promo_bogo_auto_add_item_enabled: true
  go_routine_logging_enabled: true
  celebration:
    enabled: true
    header:
      bg_image: "instant_food/7f344afa84ffa9877abc10ba9c5f68cf1684755601.png"
      logo_image: "data/o2_assets/c5736ecd73df13dca294b8a564d185ba1675495871.png"
      benefit_1_img: "instant-food/f6269a6e0bd919bb2009c03e306530361674202434.png"
      benefit_2_img: "instant-food/b079b091d49dd25ae69813cfe384907e1674202354.png"
      benefit_3_img: "instant-food/946d30335c805cd136fb17a3e5f094351674202297.png"
      benefit_1_text: "Menu changes\neveryday"
      benefit_2_text: "Freshly\nprepared"
      benefit_3_text: "Freshly\nprepared"
      servicable_title: "Delivery in %s mins"
      not_servicable_title: "Not delivering right now"
  menu_v19_ui:
    enabled: true
  distance_based_rating:
    info_container_enabled: true
  store_migration:
    enabled_user_perc: 100
  explorer_view:
    item_name:
      enabled: false
      zoman_enabled: true
  header_v2:
    irctc_empty_pnr_flow:
      enabled: true
    otg_applicable_enabled: true
    otg_unapplicable_enabled: true
  promo_snackbar:
    expanded_view_enabled: true
    post_state_transition_delay: 10
    max_check_enabled: false
    snackbar_v2:
      enabled: true
  instant_food:
    dish_card_v2:
      enabled: true
      bookmarks_item_card_enabled: false
      bookmarks_cust_sheet_enabled: false
      sharing_item_card_enabled: false
      sharing_cust_sheet_enabled: false
    promos:
      enabled: true
    header:
      announcement_info:
        enabled_res_ids: [20137786]
      weekly_calendar_enabled: true
    header_v3:
      enabled_for_all: true
      enabled_for_zomans: false
      enabled_user_ids: []
    header_v4:
      enabled_for_all: true
      enabled_for_zomans: false
      enabled_user_ids: []
    res_rating:
      disabled_res_ids: [21006413, 21006420]
    footer:
      corporate_orders_banner_enabled: true
      scheduling_banner_enabled: true
    deactivated_res_state:
      enabled: true
    addon_grid_view:
      enabled_for_all: true
      enabled_for_zomans: false
      enabled_user_percentage: 0
  scheduling:
    disabled_on_dotd: true
    enabled: true
    instant_scheduling:
      enabled_for_all: true
      enabled_for_zomans: true
      disabled_res_ids: [21113335, 21041058, 21079465, 21133070, 21133075]
  open_repeat_customisation_bottom_sheet_on_cart: false
  res_visibility:
    timeout_ms: 100000
  override_consumer_menu_loc: 'true'
  override_consumer_menu_offers_loc: 'true'
  push_applied_filters_to_front: 'true'
  remove_empty_access_token: 'true'
  dc_config_migration_enabled: 'true'
  switch_homestyle_to_bestin: 'true'
  separate_new_relic:
    enabled: 'false'
    res_ids: []
    chain_ids: []
  hide_savings_snackbar: 'false'
  apply_veg_filter:
    enabled: true
    force_enabled_user_ids: [249319746]
    enabled_user_ids: [249319746]
  footer_api:
    enabled: true
  force_menu_serviceable:
    enabled: 'false'
    mov: 50
  similar_res:
    res_badges_for_pc_res: true
    promo_with_only_title_enabled: true
    near_and_fast_tag:
      ui_revamp_enabled: true
    unserviceable_v3:
      enabled: true
      enabled_for_android: true
    enabled_user_ids: [249319746, 64462438]
    enabled_res_ids: [306071, 302115]
    bottom_sheet:
      enabled: true
    enabled: true
    similar_res_button_enabled: true
    near_and_faster:
      enabled: true
      suffix_button:
        enabled: true
    user_personalised_rating:
      new_rating_text_enabled: true
      new_res_cards_for_pc_res: true
  kill_switch:
    old_timings_killed: 'true'
    promo: 'false'
    store: 'false'
    res_redirect: 'true'
    location: 'false'
    experiment: 'false'
    user_valid_subscription: 'false'
    recommended_res_cards: 'false'
    user_profile: 'false'
    profile_store: 'false'
    orp: 'false'
    menu: 'false'
    user: 'false'
    disable_reviews_page: 'false'
    menu_header: 'false'
    orp_from_web:
      killed_globally: 'false'
      killed_zomans: 'false'
      killed_user_ids: []
      killed_percentage: 0
    disable_reviews_tooltip: 'true'
    disable_ios_cart_caching: 'false'
  por:
    placeholder_image_enabled: 'true'
  superhit_items:
    hide_share_option: 'true'
    hide_like_option: 'true'
  customer_delight:
    getTicket:
      max_raised_ticket: 3
      timeout_ms: 1000000000
    bypass_check:
      level: 3
    createTicket:
      timeout_ms: 1000000000
      authPwd: "3ae9809e66b00df6484b0e7537fdb9e0"
      host: "http://internal-staging-entry-internal-alb-1153158782.ap-southeast-1.elb.amazonaws.com/unified-support"
      username: "*******"
      bypass: false
  orp:
    timeout_ms: 10000
    endpoint: "v2/restaurant_orp/"
    set_menu_from_service: 'true'
    set_service_menu:
      enabled: 'true'
      enabled_user_ids: [1447354240]
      disabled_user_ids: [1447354424]
      instant:
        enabled: 'false'
        enabled_user_ids: [1447354240]
      intercity:
        enabled: 'false'
        enabled_user_ids: [1447354240]
      intracity:
        enabled: 'false'
        enabled_user_ids: [1447354240]
      packages:
        enabled: 'false'
        enabled_user_ids: [1447354240]
      re_order:
        enabled: 'false'
        enabled_user_ids: [1447354240]
        enabled_perc: 100
    set_static_keys:
      enabled: 'false'
      enabled_user_ids: [1447354424]
      v2_enabled: true
      v2_enabled_user_ids: [1447354613]
      v2_enabled_perc: 0
      v3_enabled: true
      v3_enabled_user_ids: [1447354424]
      v3_enabled_perc: 0
      v4_enabled: true
      v4_enabled_user_ids: [1447354424]
      v4_enabled_perc: 0
    set_fab_data:
      enabled: 'true'
      enabled_user_ids: [1447354633]
  res_redirect:
    timeout_ms: 10000
    endpoint: "v2/menu_service/res_redirect"
  header_migration:
    has_online_delivery_moved: 'false'
    serviceability_enabled: 'false'
    res_details_enabled: 'false'
    test_user_ids: [186163479]
    header_v2_enabled: true
    header_v2_video_enabled_res_ids: [306071, 302115]
    header_v2_nudge_enabled_res_ids: [306071, 302115]
    header_v2_rain_serviceable_enabled_res_ids: [306071, 302115]
    res_migration_enabled: false
    header_data_kill: true
    postback_migration_enabled: true
  orp_migration:
    enabled: false
    test_user_ids: [186163479]
    enabled_percentage: 0
  recommended_res_cards:
    enabled: false
    test_user_ids: [205252819]
  serviceability_data:
    enabled: 'true'
    endpoint: "api/v2/menu_service/get_serviceability_data"
    timeout_ms: 100000
  serviceability_migration:
    enabled: 'false'
    enabled_user_ids: [64462438]
    with_address_coordinates_shadow_perc: 100
  cache_testing:
    enabled: 'false'
    enabled_user_ids: [1447354240]
    enabled_user_perc: 0
    no_cache_header: 'true'
  footer_snippet:
    consumer_feedback_enabled: 'true'
    similar_res_ads_enabled: true
    plastic_free_banner_enabled: true
    pvr_disclaimer_enabled: 'true'
  collapse_subcategory:
    enabled: 'true'
    default_expanded_count: '3'
  fab_button:
    subcategory_visibility_enabled: 'true'
    header_item:
      top_normal_ctg_ids: []
      top_large_order_ctg_ids: []
      large_order_ctg_ids: []
      full_menu_ctg_ids: []
      fill_from_service: true
    tooltip_ttl: 20
    fetch_ps_data: 'true'
    enabled_user_ids: [1447354633]
    migration:
      enabled: true
      enabled_perc: 0
      enabled_user_ids: [1447354633]
  menu_item_carousel_autoscroll_enabled: 'true'
  should_expand_item_images: 'true'
  aerobar:
    quick_checkout_enabled: 'true'
  item_bookmarks:
    enabled: 'false'
    item_card_enabled: 'false'
    cust_sheet_enabled: 'true'
  sharing:
    enabled: 'true'
    dynamic_preview_enabled: 'true'
    item_card_enabled: 'false'
    cust_sheet_enabled: 'true'
  subscription:
    user_valid_subscription:
      enable: 'true'
  benefits_service:
    get_menu_offers:
      enabled: true
      timeout: 500
  gateway_cart:
    ps_call:
      enabled: true
    rollout:
      blacklisted_ids: []
      enable_as_default: true
      new_users:
        enabled_percentage: 100
      ui:
        enabled_user_ids: [1447354240]
      shadow:
        enabled_user_ids: [1447354240]
  cart_service:
    fetch_group_order_by_group_id:
      enabled: true
      timeout: 1000
    get_groups_for_member:
      enabled: true
      timeout: 1000
  promos_migration:
    enabled: true
  blocker_items_migration:
    enabled: 'true'
  navigation_actions_migration:
    enabled: 'true'
    enabled_perc: 100
  blocker_items:
    arat_high_ddt_warning:
      popup_enabled: true
  ar_menu:
    enabled: 'false'
    enable_unsupported_devices:
      android: 'false'
      ios: 'false'
    right_icon:
      enabled: 'true'
    three_dimensional_config:
      enabled: true
      enabled_for_zomans: false
      image_url:
        android: "https://b.zmtcdn.com/data/o2_assets/3d_model/3d_model_android.png"
        ios: "https://b.zmtcdn.com/data/o2_assets/3d_model/3d_model_ios.png"
    ar_model:
      enabled: true
      enabled_for_zomans: false
      three_dimensional_y_orientation: 45
      three_dimensional_scale: 1.8
  location:
    tabbed_home_mismatch:
      user_perc: 100
      enabled_user_ids: [64462438]
  cinema:
    enabled: 'true'
    default_grid_mode_enabled: true
    force_fetch_promo_data: true
    rating:
      enabled: true
      enabled_res_ids: []
  rating:
    repeat_rate:
        enabled: true
        tooltip_enabled: true
        enabled_res_ids: []
        mock_enabled: false
        max_sessions: 3
  mithai:
    enabled: 'true'
  background_cart:
    debounce_time_in_ms: 500
    max_count: 1
  error_state:
    search_error_enabled: 'true'
  smart_filter:
    enabled: 'true'
    force_enable_sorting_filters: 'true'
    should_show_multi_rail_filter_tooltip: 'true'
    limit_promo_filter_count: 'true'
  category_config:
    item_count_enabled: 'true'
    boost_categories_based_on_name:
      enabled: 'true'
      chain_ids_to_add: []
      chain_ids_to_remove: []
  navigation_actions:
    res_visibility_btn:
      enabled_user_ids: [205252819]
      enabled: 'false'
  menu_open_event:
    enabled: 'true'
    should_send_offer_data: 'true'
  item_rating:
    enabled_for_instant: true
tabbed_location:
  gateway_response:
    enabled: true
    user_percentage_enabled: 100
    user_percentage_enabled_for_android: 100
    user_percentage_enabled_for_ios: 100
    enabled_user_ids: []
    timeout: 5000ms
athena:
  publish_athena_api_events:
    enabled: true
    timeout: 5s
  dev_handlers:
    map_permissions:
      is_enabled: true
    complete_order:
      is_enabled: true
      logistics_api_key: "abcd"
tabbed_home:
  zmoney_pills:
    enabled: true
    enabled_user_ids: []
    enabled_for_zomans: true
    enabled_percentage: 100
  quick_delivery:
    enabled: true
    education_wherobar_enabled_percentage: 100
    enabled_city_ids: []
    allowed_location_check_enabled: true
    max_impression_count: 10
    max_tap_count: 5
  district_bottom_bar:
    enabled: true
    enabled_user_percentage: 100
    enabled_user_ids: []
    snippet_image_1:
      url: ""
      aspect_ratio: 1.1
    snippet_image_2:
      url: ""
      aspect_ratio: 1.1
    url: ""
    aspect_ratio: 1.1
    trailing_url: ""
    trailing_url_aspect_ratio: 1.1
    app_open_webview_url: ""
    app_install_onelink_url: ""
  district_bottom_bar_v2:
    enabled: true
    enabled_user_percentage: 100
    enabled_user_ids: []
    enabled_for_zomans: true
    movies_deeplink_url: "zomato://webview?url=https://link.district.in/DSTRKT/btsmoviestab"
    meal_deeplink_url: "zomato://webview?url=https://link.district.in/DSTRKT/btsDining"
    events_deeplink_url: "zomato://webview?url=https://link.district.in/DSTRKT/btsEvents"
    activities_deeplink_url: "zomato://webview?url=https://link.district.in/DSTRKT/btsActivities"
    app_install_onelink_url: "https://onelink.to/district"
    carousel_movie_image_url: "https://b.zmtcdn.com/data/o2_assets/328e286832534e29c02c64843bd2a5431752137601.png"
    carousel_meal_image_url: "https://b.zmtcdn.com/data/o2_assets/1d3500fe6c3687624782a727803327681751889019.png"
    carousel_concert_image_url: "https://b.zmtcdn.com/data/o2_assets/4571aa51b530443f54ba7f18445721001751894550.png"
    carousel_memory_image_url: "https://b.zmtcdn.com/data/o2_assets/9f0810ba896190707c12697d4309ab7d1751894317.png"
  highway:
    enabled: true
    ttl: 1440m
    eternal_enabled: false
    enabled_percent: 0
    image_url: "https://b.zmtcdn.com/data/o2_assets/9a145912d290fd673bf7f0682b8f5a561727021101.png"
    image_aspect_ratio: 1.1
    image_url_dark_mode: "https://b.zmtcdn.com/data/o2_assets/9487d33693576e39481c7de5e14503731727021288.png"
    image_dark_mode_aspect_ratio: 1.1
    skip_nearby_highway_poi: true
  enable_place_id_grafana_metrics: true
  enable_prompt_counter_grafana_metrics: true
  kill_switch: 'true'
  disable_gateway_tabbed_home: false
  chat_icon_config_enabled: true
  subscription_call_kill_switch: false
  return_from_gateway_for_zoman_enabled: true
  read_from_gateway_for_zoman_enabled: true
  user_defined_coordinates_return_percent_for_android: 100
  user_defined_coordinates_return_percent_for_ios: 100
  user_defined_coordinates_return_for_zoman_enabled: true
  user_defined_coordinates_return_enabled_user_ids: [1447354786, 74129826]
  return_from_gateway_enabled_user_ids: [1447354786]
  read_from_gateway_enabled_user_ids: [1447354786, 74129826]
  return_from_gateway_only_for_india: false
  gateway_response_enabled_for_logged_out_users: true
  cleaned_up_request_logging_enabled: true
  tabbed_home_event_enabled: true
  read_percent: 100
  return_percent: 100
  return_percent_for_ios: 100
  beta_read_percent: 100
  old_api_timeout: 5000
  log_tabbed_response: false
  log_tabbed_response_v2: true
  app_icon_flow_enabled_for_everyone: true
  app_icon_whitelisted_user_ids: [*********, ********]
  facebook_only_users_bottom_sheet_enabled: true
  facebook_only_users_bottom_sheet_v2_enabled: true
  facebook_only_users_order_count_threshold: 3
  facebook_only_users_bottom_sheet_image: "https://b.zmtcdn.com/data/o2_assets/035e913576b7071fe29be8b0cf59f3461684923510.png"
  facebook_login_method_last_date: "30 May"
  gold_membership_expired_bottom_sheet_enabled: true
  gold_lite_membership_expired_bottom_sheet_enabled: true
  gold_membership_device_limiting_bottom_sheet_enabled: true
  gold_membership_city_sale_bottom_sheet_enabled: true
  gold_membership_poi_sale_bottom_sheet_enabled: false
  gold_membership_new_user_bottom_sheet_enabled: false
  gold_membership_flash_sale_bottom_sheet_enabled: false
  gold_membership_lapsed_user_bottom_sheet_enabled: true
  gold_lite_purchase_nudge_bottom_sheet_enabled: true
  should_hide_flash_sale_bs_top_image_for_android: false
  gold_profile_tooltip_enabled: true
  dc_packs_purchase_nudge_bottom_sheet_enabled: true
  gold_membership_expired_bottom_sheet_v2_enabled: true
  gold_blocker_item_ttl_enabled: true
  gold_full_page_bottomsheet_enabled: true
  gold_distance_extension_bottomsheet_enabled: true
  use_location_object_feature_flags_percent: 100
  disable_blocker_items_for_new_user: true
  disable_blocker_items_for_new_user_percentage: 50
  food_rescue:
    mqtt:
      qos: 1
      username: ""
      password: ""
      key_prefix: "dev"
      event_retention_time: 600
  irctc:
    bottom_sheet_enabled_for_all: true
    bottom_sheet_enabled_for_zomans: true
    bottom_sheet_enabled_pois: [90268, 90269, 90270, 90271, 90272, 92157]
    bottom_sheet_enabled_user_ids: [244909242]
    within_timing_check_enabled: true
  irctc_v2:
    enabled_percentage: 100
    update_address_v2_enabled: true
    bottom_sheet_enabled_for_all: true
    bottom_sheet_enabled_user_ids: [244909242]
    force_display_info_ignored: true
    journey_page_enabled_user_percentage: 100
  cinema:
    bottom_sheet_enabled_for_all: true
    bottom_sheet_enabled_for_zomans: true
    bottom_sheet_enabled_pois: [96558]
  office_flow:
    tooltip_enabled_for_zomans: true
    tooltip_enabled_for_all: true
    tooltip_impression_per_session: 1
    poi_walker_flag_check_enabled: true
  gps_snapping:
    home_time_in_ms: 15000
    cart_time_in_ms: 10000
    menu_time_in_ms: 10000
    crystal_time_in_ms: 10000
  gold:
    auto_add_bottom_sheet_enabled: true
    profile_animation_enabled: true
    gold_uae_enabled: true
    pro_uae_enabled: true
    dc_packs_upgraded_bottom_sheet_enabled: true
    new_user_login:
      android_enabled_percentage: 50
      ios_enabled_percentage: 50
  explore_tab:
    is_enabled: true
    disabled_cities: []
    layout_v2:
      enabled_countries: ["1", "112", "166", "208", "214"]
  home_tab:
    is_enabled: true
    disabled_cities: []
  takeaway_tab:
    enabled_cities: ["63", "74", "297"]
  dailymenu_tab:
    enabled_cities: ["84", "93", "111", "174", "179", "185", "186", "188", "189", "191", "192", "193", "197", "198", "199", "200", "204", "205", "206", "207", "208", "209", "212", "213", "215", "219", "227", "228", "231", "239", "242"]
  dining_zomato_pay_flow:
    enabled_cities: ["1", "2", "3", "4", "5", "6", "7", "8", "10", "11", "12", "13", "51", "56", "57", "92"]
  dining_tooltip:
    enabled: true
    enabled_user_ids: [*********, 140152590]
    enabled_cities: ["1"]
    enabled_percentage: 30
    top_enabled_cities: ["1"]
    next_emerging_enabled_cities: []
  location:
    gps_off_tag: false
    user_defined_lat_lon_distance_enabled: true
    create_location_info_from_user_address_percent: 0
    entity_lat_lon_in_user_defined_lat_lon_enabled: true
    add_address_redesign:
      enabled: false
      should_skip_map_on_confirm_location: false
      should_skip_map_on_confirm_location_zoman_enabled: true
      should_skip_map_on_confirm_location_ios: true
      should_skip_map_on_confirm_location_android: true
      android_enabled: true
      ios_enabled: true
    gps_off_bottomsheet_enabled_for_ios: true
    default_location_prompt_enabled_for_ios: true
    cell_grocery_serviceable_check_enabled: true
    should_align_location_icon_with_title: true
    blinkit_serviceable_flag_in_config_enabled: true
  app_update_soft_blocker:
    enabled: true
    enabled_percentage: 100
    min_android_version: "715"
    max_android_version: "750"
    min_ios_version: "v17.9.0"
    max_ios_version: "v17.48.0"
  new_dark_mode:
    enabled: true
  saavan_expiry_bottomsheet:
    enabled: true
    expiry_threshold: 14
  dark_mode_bottomsheet:
    enabled: true
    enabled_for_eternals: true
    enabled_for_eternal_linked: false
    enabled_percentage: 0
    enabled_user_ids: []
  feeding_india_tab:
    active_event_id: "36"
    tab_animation_url: "https://b.zmtcdn.com/data/file_assets/883f5786913d55a144dd66daf2d4a07d1724341729.json"
    animation_url_repeat_count: 1
    show_live_tab: true
  zpl_tab:
    enabled_cities: ["51", "56", "57"]
  sdk:
    access_key: "access_key"
    access_secret: "access_key"
    domain: "domain"
    enable_karma_device_info_events: false
    enable_karma_play_integrity: false
  show_lang_options:
    disabled: false
    disabled_cities: []
  dining:
    carnival_lottie:
      enabled_city_ids: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]
      enabled_user_mods: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    maps_floating_view:
      enabled: false
      enabled_for_zomans: false
      enabled_cities: [1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13]
      enabled_countries: [1, 214]
      enabled_percentage: 0
      maps_deeplink: "zomato://goout_maps?theme_context=dark&page_view_mode=heat_map&zoom=15.1"
      starbucks_campaign:
        enabled_city_ids: [1, 2, 3]
        enabled_for_all: true
    events:
      live_cities: ["1", "2", "3", "4", "5", "6", "7", "8", "10", "11", "12", "13"]
      enabled: false
      whitelisted_users: ["-1", "4"]
    ramadan:
      enabled_cities: []
      enabled_for_all: false
    blocker:
      enabled: false
      enabled_for_all: false
      whitelisted_users: []
      enabled_city_ids: []
      is_city_check_enabled: false
      is_district_check_enabled: false
      experiment_whitelisted_users: []
  blinkit:
    blinkit_disabled_city_list: []
    soft_kill_tab: true
    bottom_bar_enabled: true
    bottom_tag_image_url: "https://b.zmtcdn.com/data/o2_assets/da595c9c9d217ef31de101b7623dc1241677652074.png"
    tabs_greater_than_threshold:
      count_of_tabs: 4
      bottom_tag_image_url: "data/o2_assets/b15ac0f15dfdeab5e1772db6e42b8d721718686629.png"
      bottom_tag_aspect_ratio: 1.15
  zomato_money:
    is_enabled: true
    edition_card:
      is_enabled: true
    zomato_wallet:
      is_enabled: false
  zlive:
    pip:
      enabled: true
  zpl:
    pip:
      enabled: true
      enabled_for_zomans: true
      app_update_available: false
      aspect_ratio: 0.5625
      video_id: "0dd5dc9e-9260-41ed-81eb-709993ebdbeb"
      video_url: "https://b.zmtcdn.com/data/file_assets/5f97298ef65bf3baf2bf716ae9474ea71724609224.mp4"
      thumb_image_url: "https://b.zmtcdn.com/data/o2_assets/693c7ba9473bbaa7632f92f15fe2d3141680106938.png"
      deeplink: "zomato://live/event/details?theme_context=dark&event_id=49042"
      bottom_button_text: "Book tickets"
  instant:
    pip:
      enabled: false
      enabled_for_zomans: false
      aspect_ratio: 0.57
      video_id: "6610cca9-6a01-4aa3-91df-9f30ef0c3012"
      video_url: "https://video.zmtcdn.com/data/ZEverydayPIP/02052023/6610cca9-6a01-4aa3-91df-9f30ef0c3012.mp4"
      thumb_image_url: "https://b.zmtcdn.com/data/video_thumbnail_path/9de99b9ff756e35959376cf0a185f6241683035563.png"
  awards_tab:
    enabled_countries: [214]
    enabled_for_all: true
    enabled_for_zomans: true
  is_address_revamp_v19_flow_enabled: true
  reorder:
    is_enabled: true
    active_users: [72629103, 136345283, 63274230, 169547103, 310924952]
    blacklisted_users: []
    enabled_percentage: 0
    enabled_for_zomans: false
    enabled_for_eternals: false
    enabled_for_all: false
    enabled_on_ios: true
    killed_for_eternals: false
    experiment_enabled: true
    v2_flow:
      enabled_for_all: true
      whitelisted_user_ids: []
      enabled_for_eternals: true
      enabled_percentage: 0
  icon_config:
    enabled_user_ids: []
    enabled_for_zomans: true
    enabled_for_all: true
  kill_order_history_favorite_subtab: false
order_history:
  mx_price_match_guarantee:
    city_ids: []
  dynamic_addon_flow:
    enabled: true
  order_deletion_flow:
    enabled: true
    enabled_for_zomans: true
    enabled_for_eternals: true
    whitelisted_user_ids: []
    enabled_percentage: 100
  order_history_v2:
    enabled: true
    enabled_for_zomans: true
    enabled_for_eternals: true
    whitelisted_user_ids: []
    enabled_percentage: 100
  mro_card:
    enabled: true
    whitelisted_user_ids: []
    enabled_percentage: 0
  filters:
    enabled: true
  non_user_cancelled_reason:
    consumer_order_status_migration:
      is_enabled: true
  pagination:
    page_size: 5
    reorder_page_size: 5
  get_search_order_history:
    timeout: 1000ms
  get_order_history_with_search_key:
    timeout: 1000ms
  order_history_enrichment:
    timeout: 1000ms
  get_reorder_search_order_history:
    timeout: 1000ms
  get_consumer_order_history:
    timeout: 1000ms
  get_consumer_order_history_with_search_key:
    timeout: 1000ms
  benchmark_force_interceptor:
    enabled: false
  pure_veg_mode:
    enabled: true
  res_rating_v3_flow:
    enabled: true
    enabled_for_zomans: true
    enabled_percentage: 100
  order_rating_flow:
    enabled: true
  celeb_media:
    enabled: true
    image_url: "https://b.zmtcdn.com/data/o2_assets/0ad3304006011ad32ec530b957608d031715251720.png"
    item_title_text: "Video message for your mom"
  mx_price_match_guarantee_flow:
    enabled: true
  order_history_request_res_ids_threshold: 48
  order_history_request_res_ids_threshold_default: 16
  get_consumer_order_history_details:
    timeout: 500ms
    size: 100
  change_refund_method:
    enabled: true
    is_enabled_for_zomans: true
    enabled_for_test_users: true
    enabled_user_ids: []
    rollout_percentage: 100
  external_app_order:
    show_mmt_tag_for_user_ids: ["12345"]
order_summary:
  test_user_ids: []
  enabled: true
  enabled_for_zomans: true
  user_percentage: 0
  reconcile:
    enabled: true
  change_refund_method:
    enabled: true
    is_enabled_for_zomans: true
    enabled_for_test_users: true
    enabled_user_ids: []
    rollout_percentage: 100
crystal:
  get_crystal_view:
    test_user_ids: []
    rollout:
      enabled: false
      is_enabled_for_zomans: false
      enabled_for_test_users: false
      enabled_user_ids: []
      disabled_user_ids: []
      rollout_percentage: 0
    web_endpoint:
      timeout: 1000ms
    recon_enabled: true
  cache_operation:
    admin_user_ids: []
  masthead:
    enabled: true
    scratch_card:
      enabled: true
    treasure_chest:
      enabled: true
  delivered_snippet:
    enabled: true
  refund_timeline_snippet:
    enabled: true
  res_snippet:
    enabled: true
    order_summary:
      enabled: true
    group_order_summary:
      enabled: true
    cooking_instructions:
      enabled: true
    add_item:
      enabled: true
    merchant_feedback:
      enabled: true
    price_parity:
      enabled: true
    post_order_feedback:
      enabled: true
    carousel_banner:
      enabled: true
  rider_snippet:
    enabled: true
    tip:
      enabled: true
    feedback:
      enabled: true
    dpsafety:
      enabled: true
    call:
      enabled: true
    chat:
      enabled: true
  delivery_details_snippet:
    enabled: true
    user_info:
      enabled: true
    user_address:
      enabled: true
    delivery_time:
      enabled: true
    delivery_instruction:
      enabled: true
  support_snippet:
    enabled: true
    cancel_order:
      enabled: true
    order_sharing:
      enabled: true
    cancel_train_order:
      enabled: true
  push_permission:
    enabled: true
  scratch_card_snippet:
    enabled: true
  featured_carousel:
    enabled: true
  rejected_snippet:
    enabled: true
  pay_now_snippet:
    enabled: true
  hospital_order_snippet:
    enabled: true
  bottom_rail:
    header:
      enabled: true
    banners:
      enabled: true
  address_mismatch_snippet:
    enabled: true
  mqtt:
    android:
      enabled: true
    ios:
      enabled: true
  map_timeline_banner:
    enabled: true
    fixed_banner:
      enabled: true
    carousel_banner:
      enabled: true
  order_switcher:
    enabled: true
  offline_snippet:
    header_snippet:
      enabled: true
    rider_snippet:
      enabled: true
    res_snippet:
      enabled: true
    order_details_snippet:
      enabled: true
    user_snippet:
      enabled: true
    offline_chat_snippet:
      enabled: true
    retry_snippet:
      enabled: true
  theme_data:
    enabled: true
  footer_data:
    enabled: true
  blocker_data:
    enabled: true
money_tab:
  gift_offer:
    enabled: true
    zoman_enabled: false
    whitelisted_user_ids: []
    user_rollout: 100
    campaign_id_from_profile_store_enabled: false
    campaign_name_from_profile_store: "123"
    older_app_update_banner_rollout: 100
    gift_offer_snippet_in_money_page_enabled: true
    android_version: "1000"
    android_beta_version: "1000"
    ios_version: "30.0.0"
  landing_v3:
    enabled: true
    whitelisted_user_ids: []
    zoman_enabled: false
    user_rollout: 100
    ios_version: "30.0.0"
    android_version: "1000"
    android_beta_version: "1000"
    zomato_money_dining_banner_enabled: true
  dark_mode:
    enabled: true
    rollout_percentage: 100
    is_enabled_for_zomans: true
    test_user_ids: [341363812]
  enabled: true
  disabled_cities: []
  allow_zomans: true
  rollout_percentage: 100
  gift_card:
    enabled: true
    gift_card_section_image: "https://b.zmtcdn.com/data/o2_assets/027bfc8f64c7040338a7f7d5f7fa1f231686312210.png"
  upi:
    enabled: true
    promotion_enabled: false
    disabled_cities: ["51", "56", "57", "92", "253", "254", "293", "310"]
  wallet:
    enabled: true
    enabled_for_zomans: true
    whitelisted_user_ids: []
    enabled_user_perc:
      min: 0
      max: 100
  edition_card:
    enabled: true
  zomato_balance_transaction_details:
    enabled: true
    rollout_percentage: 100
    test_users: []
  enterprise_meals:
    enabled: true
    lead_banner_enabled: false
    enabled_for_zomans: true
    whitelisted_user_ids: []
    enabled_user_perc:
      min: 0
      max: 50
  combined_balance_revamp:
    enabled: true
    test_users: [282345100]
    rollout_percentage: 0
  auto_add_money_section:
    enabled: true
    whitelisted_users: []
    blacklisted_users: []
    rollout:
      min: 0
      max: 100
  payments:
    enabled: true
  fetch_balance_from_wallet:
    shadow:
      rollout: 100
    actual:
      rollout: 100
  transaction_history:
    enabled: false
    test_user_ids: []
    rollout: 100
    should_display_gift_card_reference_id: true
    eof_flag_enabled: true
    bottom_sheet_device:
      android: true
      ios: true
ads:
  similar_ads_rail_enabled: false
  similar_ads_rail_timeout_ms: 500
  dashboard_rpc_kill_switch: false
  add_bulk_operation_rpc_kill_switch: false
post_order_feedback:
  v2_layout:
    recommendation_checkbox:
      enabled: true
    edit_flow:
      get_photos:
        enabled: true
        timeout_ms: 200
    success_actions:
      migrated_to_submit_api: true
  winback_flow:
    enabled: true
  order_state_check:
    enabled: false
  private_reviews:
    enabled: true
    enabled_for_zomans: true
    rollout_percentage: 10
    enabled_for_all: true
    save_feedback_on_bottomsheet_load: true
    add_voice_note:
      enabled: true
    merchant_customer_connect:
      enabled: true
    recommendation_checkbox:
      enabled: true
      show_already_recommended_res: true
      should_hide_on_low_ratings: true
      autocheck_enabled: true
      autocheck_for_all_reviews: true
      use_flag_from_request:
        enabled: true
        enabled_for_zomans: true
        rollout_percentage: 100
      min_rating_check_enabled: false
    dark_mode:
      enabled: true
  pure_veg_mode:
    enabled: true
  food_rescue_rating_text:
    enabled: true
    whitelisted_user_ids: []
tpl_callback:
  action: ["update-order-status"]
  traffic_percent: 100
  allowed_internal_action: ["BUDDY_DRIVER_ASSIGNED"]
upi:
  onboarding_enabled:
    android: true
    ios: true
gift_card:
  promo:
    is_enabled: true
    test_users: ["282345100"]
    rollout: 0
  festive_flow:
    enabled: true
    test_user_ids: [169547103, 287672203, 171424095, 173534457, 90645709, 215467499, 143519263, 220472083, 108252284, 260074760, 114135352, 305313648, *********, 282345100, 33085966, 126542939]
    reference_name: "diwali"
    entry_point: "home_page_diwali"
    enabled_for_zomans: true
    billboard:
      image:
        url: "https://b.zmtcdn.com/data/o2_assets/ea858f0460634306b45970efb10322dc1698330648.png"
        aspect_ratio: 1.4705882353
      animation:
        aspect_ratio: 1.4705882353
        repeat_count: 4
  purchase_limit:
    is_enabled: true
  global_purchase_kill_switch: false
  dark_mode:
    enabled: true
    rollout_percentage: 100
    is_enabled_for_zomans: true
    test_user_ids: [341363812]
  celeb_ai_flow:
    is_enabled: true
    rollout: 100
    test_user_ids: [169547103, 287672203]
    lotties:
      media1_url: ""
      media2_url: ""
      media3_url: ""
    show_purchased_gc_video: true
  claim_celebration:
    enabled: true
    rollout_percentage: 0
    test_user_ids: [326961473, 342738787,*********]
    lotties:
      media1_url: ""
      media2_url: ""
      media3_url: ""
  claim_bottomsheet:
    enabled: true
    rollout_percentage: 0
    test_user_ids: [326961473, 342738787,*********]
  is_bulk_purchase_snippet_enabled: true
  dashboard:
    admin_users: [*********, *********, *********]
dining:
  tr_page_v3:
    whitelisted_user_ids: ["302115"]
    enabled_for_all: true
    enabled_for_zomans: true
    enabled_percentage: 100
  tr_crystal:
    repeat_la_user_promo_banner:
      is_enabled: true
    new_user_promo_banner:
      is_enabled: true
      disabled_user_mods: [6]
    loyalty_program_banner:
      is_enabled: true
      enabled_res_ids: ["302115"]
      enabled_user_ids: [*********]
    bank_offers_section:
      is_enabled: true
    booking_history_section:
      enabled: true
  bookmark:
    click_action_migration_enabled: true
    get_all_chains_for_user_collections_enabled: true
  offer_wall:
    should_show_mov: false
    multiple_promo_sections:
      whitelisted_users: []
      enabled_for_zomans: false
      enabled_for_all: false
  reserve_with_google:
    authorisation_enabled: false
    username: "rwg_abc_20976544"
    rate_limiting:
      enabled: true
      allowed_requests_per_sec: 0.1
    password: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  pure_veg_mode:
    enabled_user_ids: [*********, *********]
    is_enabled: true
  tr_page_v2:
    is_enabled: true
    enabled_user_ids: [*********, *********, *********, *********]
    enabled_for_zomans: true
    carnival:
      is_enabled: false
      enabled_for_zomans: true
    default_booking_options_header:
      is_disabled: true
    aerobar_v2:
      is_enabled: true
    price_aggregation:
      is_enabled: true
    guest_selection_blocker:
      enabled_percentage: 100
  tr_cart_v2:
    is_enabled: false
    enabled_user_ids: [*********, *********, *********, *********]
    enabled_for_zomans: true
  axis_bank_campaign:
    is_enabled: true
  cart:
    district_redirection_campaign:
      enabled: true
      image_url: "dining/49bb27574c39ceca76efccf27a2aa7b21733290929.png"
      redirection_url: "https://link.district.in/DSTRKT/zdininghpbanner"
  offline_view:
    is_enabled: true
    disabled_cities: [51, 56, 57, 92]
  restriction:
    dining_routes:
      disabled: false
    dashboards:
      write_whitelisted_users: [********, *********, *********, *********, *********, *********, ********, *********]
      write_restriction_enabled: false
  order_crystal_lottie_v2:
    enabled_percentage: 10
  order_crystal_video:
    is_enabled: true
    whitelisted_business_ids: ["306071"]
    enabled_for_all: false
  stories:
    is_insta_login_enabled: false
  bill_payment_crystal:
    heist_banner:
      enabled: true
    ratings_section:
      is_enabled: true
    gold_savings_section:
      is_enabled: true
    similar_res_section:
      is_enabled: true
      timeout: 500
      crystal_ads:
        enabled: true
        whitelisted_user_buckets: [6]
        res_count: 10
        rail_limit: 10
      crystal_ads_v2:
        enabled: true
        whitelisted_user_buckets: [6]
        rail_limit: 10
    rewards_section:
      is_enabled: true
      timeout: 500
      pop_up_animation_enabled_percentage: 95
      uae_dine_n_win_assets_enabled: true
      zomato_promo_codes: ["BINGE100", "BINGE150", "BINGE200"]
    promotions_section:
      is_enabled: true
      banner_header: "Go out with District"
      banner_name: "district_promo"
      banner_image_url: "https://b.zmtcdn.com/data/o2_assets/49bb27574c39ceca76efccf27a2aa7b21733290929.png"
      banner_aspect_ratio: 1.1
      banner_cta_link: "https://link.district.in/DSTRKT/zdininghpbanner"
    cashback_section:
      is_enabled: true
      promo_code: TESTISHAN
      whitelisted_users: [303121518]
      pop_up_animation_enabled: true
    loyalty_campaign:
      whitelisted_users: [303121518, 326762912]
      is_enabled: true
      # image_light_url: "data/o2_assets/67f8c9e2db420bdf060e584676464a9a1755773879.png"
      # image_dark_url: "data/o2_assets/67f8c9e2db420bdf060e584676464a9a1755773879.png"
      # image_aspect_ratio: 1.927
      # lottie_url: "data/file_assets/7598f591b186e1a5f7d1640cabd133311697717591.json"
      # lottie_aspect_ratio: 1.927
    tip_section:
      is_enabled: true
    enterprise_section:
      is_enabled: true
      test_users: [303121518]
      test_payment_id: ""
    max_go_routine_count: 4
  gold_removal:
    enabled: true
    enabled_countries: [1]
  business_funnel_events:
    enabled: true
  experiences:
    enabled_for_zomans: true
    is_enabled: true
  table_booking_order_history:
    is_v2_enabled: true
  dark_mode:
    enabled_for_all: false
    enabled_for_zomans: false
    enabled_user_ids: []
  ads_service_mbb_creation_flow:
    enabled: true
    enabled_user_ids: []
  res_group_mbb_creation:
    enabled: true
    enabled_user_ids: [*********]
    enabled_for_all: false
enterprise_meals:
  revamp_enabled:
    remove_zfe_from_money_page: true
    remove_onboarding_dependency_from_money_page: true
    dashboard_transactions: true
  enable_full_kyc: false
  food_wallet:
    enabled: true
  full_kyc:
    enabled: false
    test_user_ids: []
  exit_company:
    enabled: false
    test_user_ids: []
  faq_page:
    agent_support_snippet:
      enabled: false
  business_order_details_bottomsheet:
    stepper_question_type_enabled: true
    api_call_on_dismiss:
      enabled: true
    api_call_on_confirm:
      enabled: true
    auto_suggest_supported:
      enabled: true
      whitelisted_users: []
      rollout_percentage: 0
    image_upload:
      max_file_size_in_mb: 10
      max_count: 3
      timeout: 10
      should_upload_by_client: true
    presigned_url_timeout: 10
  dark_mode:
    enabled: true
    rollout_percentage: 100
    is_enabled_for_zomans: true
    test_user_ids: [*********]
  consumer_main_dashboard:
    space_fixing:
      enabled: true
  entry_point:
    enabled_from_money_page: false
  lead_gen:
    enabled_on_money_page : false
  dining:
    banner_enabled : true
    whitelisted_user_ids: [*********]
    enabled_for_all: false
  features:
    budget_request:
      enabled: true
      whitelisted_user_ids: []
      enabled_for_all: true

go_out:
  res_page:
    scan_and_pay_v2_enabled: true
    rating_color_v2:
      enabled: true
      enabled_for_all: true
      experiment_enabled_for_all: true
      whitelisted_user_ids: ["343615174", "344247300", "*********"]
    z_to_d_user_migration:
      enabled: true
      light_image_url: "https://b.zmtcdn.com/data/o2_assets/d76883382373502d3eb2b8c4e1a982fc1736930993.png"
      dark_image_url: "https://b.zmtcdn.com/data/o2_assets/a820fa6f44c4499329e5b278dfc89a2c1736931033.png"
      aspect_ratio: 1.106
      experiment_creatives_enabled: true
      incremental_offer_creatives_enabled: true
      enabled_user_ids: ["264300898"]
      res_based_blocker:
        enabled: true
        enabled_res_ids: ["302115"]
        blocker_configs:
          - res_id: "302115"
            light_theme_image_url: "https://b.zmtcdn.com/data/o2_assets/75f21b3b6f2524e513400637cb35a3ae1738746235.png"
            dark_theme_image_url: "https://b.zmtcdn.com/data/o2_assets/3dceb14709f9c21c405d6693c9520dc61738746323.png"
            aspect_ratio: "1.16"
      pay_blocker:
        light_theme_image_url: "https://b.zmtcdn.com/data/o2_assets/75f21b3b6f2524e513400637cb35a3ae1738746235.png"
        dark_theme_image_url: "https://b.zmtcdn.com/data/o2_assets/3dceb14709f9c21c405d6693c9520dc61738746323.png"
        aspect_ratio: 1.16
    header_section:
      peak_hour_banner:
        is_enabled: true
        image_url: "https://b.zmtcdn.com/data/o2_assets/e9c685fd9e59cbadb60b51c1c64e2e6e1742383072.png"
        image_aspect_ratio: 1.99
      district_benefits_banner_enabled: true
      timing_migration:
        enabled_for_zomans: true
        enabled_for_all: true
        enabled_res_ids: ["302115", "21447529"]
        merge_slots_enabled: true
        merge_overnight_slot_same_day_enabled: false
        time_diff_to_merge_in_minute: 1
        bottom_sheet_migration_enabled: true
    user_booking_section:
      experience_enabled: true
    share_res_button_enabled: true
    share_district_link_enabled: true
    cuisine_v2:
      enabled: true
      enabled_for_everyone: true
      enabled_for_zomans: true
      enabled_cities: [1]
      enabled_user_mods: [0,1,2,3,4,5,6,7,8,9]
      enabled_res_ids: [302115, 5604446, 306071]
    cnt_award_enabled: true
    walkin_offers_custom_snippets:
      enabled_for_all: false
      enabled_for_zomans: true
    minimal_res_page:
      enabled_for_all: true
      enabled_for_zomans: true
      enabled_user_ids: [*********, 344247300]
      enabled_percentage: 100
    group_map_view:
      enabled_for_all: true
      enabled_for_zomans: true
      enabled_percentage: 100
      enabled_user_ids: [*********, 344247300]
    menus_v2:
      enabled_for_all: false
      enabled_for_zomans: true
      enabled_percentage: 100
    scan_and_pay:
      whitelisted_res_ids: []
      blocked_for_all: true
      blocked_res_ids: []
    gallery_v2:
      enabled_for_all: false
      enabled_for_zomans: false
      enabled_user_ids: []
      enabled_percentage: 0
      deeplink_v2_enabled_percentage: 69
    compile_dark_mode_md_enabled: true
    web_client:
      api_key: "njvebrv82392306j"
    max_go_routine_count:
      bottom_rails: 10
    reorder_business_page_entities_dashboard:
      is_enabled: false
    delights_bank_offers:
      is_enabled: false
      whitelisted_users: ["*********"]
    disabled_cities: [0]
    should_prioritize_featured_image: false
    should_prioritize_featured_v2_image: false
    info_banners:
      gold_heist_enabled: true
    ramadan:
      is_enabled: true
    downstreams:
      res_metadata_migration_enabled_percentage: 100
      enrichment_svc_enabled_percent: 100
      should_fetch_from_enrichment_svc: true
      is_migration_enabled: true
      ads_section:
        distance_bucket_range: 5
        enabled_user_buckets: [6]
      store_service_migration:
        enabled_for_all: true
        enabled_percentage: 100
      similar_res_section:
        max_res_ids_to_fetch: 20
        whitelisted_user_buckets: [6]
      ads_rpc_migration_enabled: true
    res_rail:
      remove_inactive_res:
        enabled_for_zomans: true
        enabled_user_percentage: 50
    top_dishes_rail:
      enabled: true
    pay_section:
      snackbar:
        vcs:
          ios_version: "18.44.2"
          android_version: "908"
          android_beta_version: "908"
        enabled: true
        enabled_for_all: false
        enabled_for_zomans: true
        whitelisted_user_ids: ["319495491"]
        enabled_user_percentage: 0
        enabled_countries: [1]

    ar_catalogue:
      enabled_for_all: false
      enabled_for_zomans: true
    sections_count_per_pagination: 3
    context_sharing:
      tr_page:
        is_enabled: true
    brand_story:
      whitelisted_res_ids: ["*"]
      whitelisted_chain_ids: []
      image_url: "https://b.zmtcdn.com/data/o2_assets/ecf2b622b3034dc14f9b095df556d48e1733480603.png"
      aspect_ratio: 5.7
      deeplink: "https://link.district.in/DSTRKT/drestpage?link_params={\"res_id\":\"$RES_ID\"}"
      assured_voucher_campaign:
        is_enabled: true
        image_url: "https://b.zmtcdn.com/data/o2_assets/04a10eaa9b9066d8eee72e2a329860151739445038.png"
        aspect_ratio: 2.78
        deeplink: "https://link.district.in/DSTRKT/drestpage?link_params={\"res_id\":\"$RES_ID\"}"
      campaign:
        is_enabled: true
        starbucks_image_url: "https://b.zmtcdn.com/data/o2_assets/04a10eaa9b9066d8eee72e2a329860151739445038.png"
        starbucks_image_aspect_ratio: 2.78
        starbucks_whitelisted_chain_ids: [301011, 43344, 6503729, 56603, 96507, 18630948]
    view_360:
      enabled_for_zomans: true
      enabled_for_all: true
  tabbed_home:
    dining_tab:
      live_cities: ["1", "2", "3", "4", "5", "6", "7", "8", "10", "11", "12", "13"]
      enabled_for_all: true
      enabled_for_zomans: true
      whitelisted_users: []
    nightlife_tab:
      live_cities: ["1", "2", "3", "4", "5", "6", "7", "8", "10", "11", "12", "13"]
      enabled_for_all: true
      enabled_for_zomans: true
      whitelisted_users: []
    events_tab:
      live_cities: ["1", "2", "3", "4", "5", "6", "7", "8", "10", "11", "12", "13"]
      enabled_for_all: true
      enabled_for_zomans: true
      whitelisted_users: []
    new_res_page:
      live_cities: [1, 13]
      whitelisted_users: [53453452]
      enabled_for_zomans: true
      rollout_percentage: 2
  side_menu:
    dining:
      live_cities: ["1"]
      pay_without_deals_cities: []
    table_booking:
      live_cities: ["1"]
    dining_events:
      live_cities: ["1"]
      chat_enabled: true
    dining_unlock:
      live_cities: ["1"]
    language_section:
      enabled: true
    subscription:
      enabled: true
      gold: true
      gold_selling_section_enabled: true
    zomaland:
      chat_enabled: true
  back_office:
    event_ids: [1, 2]
    bypass_auth: false
    whitelisted_users: [1, 2, 3]
    whitelisted_ticket_ids: [1, 2, 3]
hand_cricket:
  apis_enabled: true
  enable_share_button: true
  rematch_enabled: true
  update_team_rpc_timeout: 5000
  rewards_enabled: false
  previous_winners_banner_enabled: false
  enabled_for_uae: false
  play_with_friend:
    banner_enabled: false
    apis_enabled: true
    enabled_for_zomans: true
    mqtt_data:
      qos: 0
      username: "zemqtt"
      password: "2Lw2JUq0QYA0zT"
      keepalive: 900
  spotlight_rail:
    enabled: true
  promotional_rail:
    enabled: true
    sponsor_1:
      image_url: "https://b.zmtcdn.com/data/o2_assets/2146dc089ef185314f91ccf33433a3a71698330690.png"
      deeplink: "zomato://zpl?url=https%3A%2F%2Fwww.zomato.com%2Fwebview%2Fgeneric%3Fpage_type%3Dzomato_2024_lookback_update%20app&should_disbale_bounce_on_webview=true&navigation_bar_type=transparent"
    sponsor_2:
      image_url: ""
      deeplink: ""
    sponsor_3:
      image_url: "https://b.zmtcdn.com/data/o2_assets/2146dc089ef185314f91ccf33433a3a71698330690.png"
      deeplink: "zomato://zpl?url=https%3A%2F%2Fwww.zomato.com%2Fwebview%2Fgeneric%3Fpage_type%3Dzomato_2024_lookback_update%20app&should_disbale_bounce_on_webview=true&navigation_bar_type=transparent"
  rpcs_timeout: 50000
  initial_state:
    refresh_timeout: 3
  initial_game_state:
    refresh_timeout: 3
    dual_player_refresh_timeout: 6
    post_timeout: 5.0
  initial_transition_state:
    refresh_timeout: 3
  transition_state:
    refresh_timeout: 3
  ball_result_game_state:
    refresh_timeout: 3
  is_rewards_enabled: true
  rewards_view_time: 18
  update_rpc_timeout: 5000
  is_leaderboard_enabled: true
  final_result_processing_state:
    enabled: false
    processing_banner: https://b.zmtcdn.com/data/o2_assets/292ef8430d372765d97a5a69cf3f78831700139828.png
    final_bg_image: https://b.zmtcdn.com/data/o2_assets/866a764e37d49c6554a2f3ba00e9344a1700238310.png
  final_result:
    enabled: false
    mega_prize_image: https://b.zmtcdn.com/data/o2_assets/c27abc4e7dad74934687a5e6040079d81700196929.png
    mega_prize_not_won_image: https://b.zmtcdn.com/data/o2_assets/48e04a860fa84e8ea4ea2bd0d37091ba1700197875.png
    bottom_info_image: https://b.zmtcdn.com/data/o2_assets/1426f7757ca9750509742949b0e9c63d1700197171.png
    prize_nissan_magnite: https://b.zmtcdn.com/data/o2_assets/1e2ae89fe7ffa67d8e8761b72cf4bc901700157851.png
    prize_ear_buds: https://b.zmtcdn.com/data/o2_assets/f9d1b53ee5d159550994709395c9dd581700202706.png
    prize_speaker: https://b.zmtcdn.com/data/o2_assets/8fa37d145ed3b0dc3d76b4a6dab41b091700202509.png
    prize_smart_watch: https://b.zmtcdn.com/data/o2_assets/0f2c062ce6c369bcc2c5d0bdd3aede3e1700202294.png
    prize_smart_phone: https://b.zmtcdn.com/data/o2_assets/87febec9277d79c755f8df7b823f1a9d1700202467.png
    milestones_winners_banner: https://b.zmtcdn.com/data/o2_assets/62c0ce8b245a3ef77e926f3d3c41b9121700215763.png
    prize_headphone: https://b.zmtcdn.com/data/o2_assets/0f2c062ce6c369bcc2c5d0bdd3aede3e1700202294.png
    prize_tv: https://b.zmtcdn.com/data/o2_assets/0f2c062ce6c369bcc2c5d0bdd3aede3e1700202294.png
    super_winner_banner_deeplink: zomato://zpl?url=https%3A%2F%2Fwww.zomato.com%2Fwebview%2Fgeneric%3Fpage_type%3Dww_india_hc&should_disbale_bounce_on_webview=true&navigation_bar_type=transparent
    prize_not_won_image: https://b.zmtcdn.com/data/o2_assets/e5d2198314f14cf3ed61dc34c5b456cc1716651652.png
    prize_not_won_ar: 1.0322
    prize_ar: 0.465
    mega_prize_ar: 2.44
  wins_for_reward: 2
  last_chance_banner: https://b.zmtcdn.com/data/o2_assets/508527a4ee46f632d3a53f758a8105571700144283.png
  uae:
    is_consent_banner_enabled: true
    final_result:
      enabled: true
      mega_prize_image: "https://b.zmtcdn.com/data/o2_assets/c878358b004b0b13024b61953158ea2c1700211418.png"
      mega_prize_not_won_image: "https://b.zmtcdn.com/data/o2_assets/37194cb8326a31f597e750bf9c66ca6d1700206443.png"
      prize_mi_pad: "https://b.zmtcdn.com/data/o2_assets/764586720cb4fcfea7460a7b55bff7481700202771.png"
      prize_semi_final_tickets: "https://b.zmtcdn.com/data/o2_assets/c8e9074ba66bb60dcbc1b0145c07fe3e1700202940.png"
      prize_final_tickets: "https://b.zmtcdn.com/data/o2_assets/18a4a6dbd3b2f05d72353841eae7dfbf1700202956.png"
      prize_international_trip: "https://b.zmtcdn.com/data/o2_assets/c01458041a03e044e2044f8ac266bac31700202972.png"
      prize_aed_cash: "https://b.zmtcdn.com/data/o2_assets/86d297e8bd4074878d29b018f457d2f81700202989.png"
intercity:
  enabled: true
  redirection_url_enabled: true
  gold_trial_enabled: true
  order_history:
    web_view_enabled: true
  new_lottie_enabled: true
  is_lottie_enabled: true
  menu:
    new_header_image_enabled: true
    filter_pill_enabled: true
    is_stories_enabled_ios: true
    is_stories_enabled_android: true
  cart:
    dark_mode_enabled: true
  cutoff_time: 19.5
consumer_order_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: "order-service"
  rpc:
    get_order:
      whitelisted_users: [*********, 159328115]
    publish_last_orders:
      chunk_size: 100
      chunk_sleep: 20ms
      whitelisted_users: [*********]
merchant_outlet_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: "merchant-outlet-service"
cult_app:
  zoho_api_token: ""
  zoho_client_id: ""
  aws:
    region: ap-south-1
  s3:
    bucket: "cult-app-uploads"
    key: "Z-Cult_Cropped.pdf"
    code_of_conduct:
      key: "COC.pdf"
    insurance:
      key: "Insurance_PLUM.pdf"
    leave:
      key: "Leave.pdf"
    pit:
      key: "PIT.pdf"
    posh:
      key: "POSH.pdf"
    travel:
      key: "Travel_Policy.pdf"
    holiday_calendar:
      key: "Holiday_Calendar.pdf"
zlive:
  dark_mode:
    enabled_for_all: true
    enabled_for_zomans: true
    enabled_user_ids: ["*********", "*********"]
  events_home_page:
    enabled_cities: ["1"]
    past_enabled_cities: []
    enabled_for_zomans: false
    whitelisted_users: ["*********", "*********", "*********"]
    enabled_for_all: false
  events_listing_home_page:
    video_header:
      enabled: true
      enabled_for_zomans: true
      enabled_for_all: false
    enabled: true
    enabled_cities: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11464"]
    enabled_for_zomans: false
    whitelisted_users: ["*********", "*********", "*********", "*********"]
    enabled_for_all: false
    search_placeholder_animation_enabled: false
    vertical_rail_snippet_enabled: true
    blur_search_bar_enabled: true
  tooltip:
    enabled_cities: ["5"]
    session_count: 3
    impression_per_session: 3
    tooltip_id_suffix: "zlive"
    title: "Zomato Live"
    subtitle: "Book tickets on Zomato Live"
    image_url: "https://b.zmtcdn.com/data/o2_assets/770cd1b8f778f016069fa8292a21352b1702285412.png"
    deeplink: "zomato://live/event/details?theme_context=dark&event_id=51767"
  nye:
    tab_animation_url: "https://b.zmtcdn.com/data/file_assets/93a9e57c74e3239e1c854bb87b50000b1710907167.json"
    default_tab_animation_url: "https://b.zmtcdn.com/data/file_assets/a83ae6fc196dfe2d52a29486aa28cab41711694852.json"
    animation_url_enabled_cities: []
    animation_url_repeat_count: 0
    home_page:
      enabled_cities: ["11464"]
      enabled_for_zomans: false
      whitelisted_users: ["*********", "*********", "*********", "1727483", "37485059", "266349661", "170131010", "130605922", "319387797", "*********", "********"]
      enabled_for_all: false
  event_details_page:
    book_tickets:
      disabled_user_mods: []
m2p_wallet:
  m2p_whitelisted_ips: ************ ************ ************* ************ *************** ************ ************** *************
  m2p_credentials:
    username: "stage_user"
    password: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx%"
delight_service:
  video_callback:
    is_enabled: true
  validate_input_args:
    is_enabled: true
  validate_post_order_video:
    is_enabled: true
  third_party:
    true_fan:
      whitelisted_ips: ["************", "************"]
one_support:
  automation_worker_nat_ips: ["************", "*************", "*************"]
  automation_credentials:
    username: "user"
    password: "pass"
service_stats_interceptor:
  enabled: true
eatfit:
  inventory_sync:
    ips: ["127.0.0.1"]
    hmac_private_key: ""
    hmac_header_name: ""
legends_service:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority_header: "legends-service"
  inventory_sync_messages_topic: ""
  whitelisted_users: [209907968]
  update_order_status:
    enabled: false
    timeout_ms: 1000
  get_order_details:
    enabled: false
    timeout_ms: 1000
  update_res_story:
    enabled: true
    timeout_ms: 5000
  launch_buzz_details:
    enabled: true
    timeout_ms: 1000
  launch_buzz_claimed:
    enabled: true
    timeout_ms: 1000
  launch_buzz_eligibility:
    enabled: true
    timeout_ms: 1000
  reconcile_order_status:
    enabled: true
    timeout_ms: 1000
  mov:
    min_value: 10000
    whitelisted_users_mov: 100
    source_charge_service_enabled: false
    cart_checkout_blocker_enabled: true
everyday:
  get_all_slots_api_failure_snippet:
    enabled: true
    whitelisted_users: [239405860]
  open_on_sunday:
    enabled_res_ids: [20137786]
weather_service:
  host: kuma-gateway.eks.zdev.net
  port: '80'
  authority_header: weather-service
  weather_api:
    daily_limit: 1000
  api_kill_switch:
    enabled: true
  city_detail:
    timeout: 5000ms
  city_weather_data:
    timeout: 5000ms
  current_weather_data_from_locality:
    timeout: 5000ms
  current_weather_data_from_geopoint:
    timeout: 5000ms
  download_locality_weather:
    timeout: 5000ms
  user_profile:
    timeout: 2000ms
  prediction:
    timeout: 2000ms
  get_all_weather_devices:
    timeout: 5000ms
    api_kill_switch:
      enabled: true
  get_pings_for_device:
    timeout: 5000ms
    max_time_range: 86400
    api_kill_switch:
      enabled: true
  get_ping_count_for_devices:
    timeout: 5000ms
    api_kill_switch:
      enabled: true
  webhooks:
    enabled: true
    whitelisted_user_ids: []
  weather_dashboard:
    update_mapping_status_for_store_mapping:
      timeout: 2000ms
      kill_switch: true
    get_mappings_for_device:
      timeout: 2000ms
      kill_switch: true
    update_weather_device:
      timeout: 2000ms
      kill_switch: true
    bulk_update_weather_device:
      timeout: 2000ms
      kill_switch: true
    get_weather_devices_for_city:
      timeout: 2000ms
      kill_switch: true
    get_weather_device:
      timeout: 2000ms
      kill_switch: true
    get_all_zws_cities:
      timeout: 2000ms
      kill_switch: true
    get_city_level_manual_rain_config:
      timeout: 2000ms
      kill_switch: false
    update_city_level_manual_rain_config:
      timeout: 2000ms
      kill_switch: false
    fetch_rain_data:
      timeout: 2000ms
      kill_switch: false
    trigger_manual_rain:
      timeout: 2000ms
      kill_switch: false
    get_bulk_device_locality_mapping_from_city:
      timeout: 5000ms
      kill_switch: false
    update_device_locality_mapping:
      timeout: 5000ms
      kill_switch: false
    get_bulk_device_locality_mapping_from_device_ids:
      timeout: 5000ms
      kill_switch: false
    get_entity:
      timeout: 5000ms
      kill_switch: false
    get_entities_for_city:
      timeout: 5000ms
      kill_switch: false
    get_mappings_for_entity:
      timeout: 5000ms
      kill_switch: false
brand_ads:
  upsert_config_rpc:
    enabled_user_ids: [306106888, 291732962]
lead_management_service:
  host: localhost
  port: "4700"
  authority_header: "lead-management-service"
  delivery_leads_partner:
    apis:
      onboard_user:
        enabled: true
      fetch_user_by_phone_no:
        enabled: true
      fetch_user_profile:
        enabled: true
      update_user:
        enabled: true
      generate_qc_doc_pre_sign_url:
        enabled: true
      submit_qc_docs:
        enabled: true
      list_users:
        enabled: true
      generate_bulk_leads_upload_csv_presign_url:
        enabled: true
      bulk_leads_upload_csv_ack:
        enabled: true
        timeout_ms: 2000
        config_id: "cnf_1748515782620469"
      fetch_bulk_upload_history:
        enabled: true
      fetch_error_file_via_bulkops:
        enabled: true
      single_lead_upload:
        enabled: true
      generate_lead_report:
        enabled: true
      lead_reports:
        enabled: true
      get_carriers:
        enabled: true
      get_businesses:
        enabled: true
      get_report_types:
        enabled: true
      get_required_docs:
        enabled: true
      get_cities:
        enabled: true
      get_tags:
        enabled: true
      generate_b2b_credentials:
        enabled: true
      invalidate_b2b_credentials:
        enabled: true
driver_service:
  host: kuma-gateway.eks.zdev.net
  port: "80"
  authority_header: "driver-service"
  fleet_partner_dashboard:
    hotlines:
      allowed_hotlines: ["***********"]
    apis:
      generate_presign_url_for_image:
        enabled: true
        ratelimit: 100
      rider_doc_list:
        enabled: true
        ratelimit: 100
      ev_battery_types:
        enabled: true
        ratelimit: 100
      get_all_vendors:
        enabled: true
        ratelimit: 100
      create_vendor:
        enabled: true
        ratelimit: 1
      update_vendor:
        enabled: true
        ratelimit: 1
      get_users_for_vendor:
        enabled: true
        ratelimit: 100
      create_vendor_user:
        enabled: true
        ratelimit: 100
      update_vendor_user:
        enabled: true
        ratelimit: 100
      get_user_profile_by_id:
        enabled: true
        ratelimit: 100
      get_all_vehicles:
        enabled: true
        ratelimit: 100
      get_vehicle_by_id:
        enabled: true
        ratelimit: 100
      create_vehicle:
        enabled: true
        ratelimit: 10
      update_vehicle:
        enabled: true
        ratelimit: 10
      get_vendor_profile:
        enabled: true
        ratelimit: 200
      get_store_for_vendor:
        enabled: true
        ratelimit: 100
      create_store:
        enabled: true
        ratelimit: 100
      update_store:
        enabled: true
        ratelimit: 100
      get_inventory_for_vendor:
        enabled: true
        ratelimit: 100
      create_inventory:
        enabled: true
        ratelimit: 100
      update_inventory:
        enabled: true
        ratelimit: 100
      get_zones:
        enabled: true
        ratelimit: 200
      get_cities:
        enabled: true
        ratelimit: 200
      get_leads:
        enabled: true
        ratelimit: 200
      get_lead_status_map:
        enabled: true
        ratelimit: 200
      update_lead:
        enabled: true
        ratelimit: 100
      generate_otp:
        enabled: true
        ratelimit: 100
      get_rented_vehicles:
        enabled: true
        ratelimit: 200
      vehicle_handover_initiate:
        enabled: true
        ratelimit: 100
      vehicle_handover_complete:
        enabled: true
        ratelimit: 100
      vehicle_surrender_initiate:
        enabled: true
        ratelimit: 100
      vehicle_surrender_complete:
        enabled: true
        ratelimit: 100
      generate_csv_presign_url:
        enabled: true
        ratelimit: 100
      ack_rental_csv_upload:
        enabled: true
        ratelimit: 100
      ack_zsp_csv_upload:
        enabled: true
        ratelimit: 100
      get_tags:
        enabled: true
        ratelimit: 100
      get_vehicle_carrier_types:
        enabled: true
        ratelimit: 100
      generate_rider_metrics_report:
        enabled: true
        ratelimit: 100
      get_rider_metrics_report:
        enabled: true
        ratelimit: 100
      vehicle_csv_upload_ack:
        enabled: true
        ratelimit: 100
      get_vehicle:
        enabled: true
        ratelimit: 100
      get_all_make_models:
        enabled: true
        ratelimit: 100
      get_make_model_by_id:
        enabled: true
        ratelimit: 100
      create_make_model:
        enabled: true
        ratelimit: 10
      update_make_model:
        enabled: true
        ratelimit: 10
      get_driver_license_number:
        enabled: true
        ratelimit: 100
  external:
    enable_api_whitelisting: false
    apis:
      vehicles_ev_models:
        enabled: true
        ratelimit: 1
      vehicles_handover_initiate:
        enabled: true
        ratelimit: 100
      vehicles_handover_complete:
        enabled: true
        ratelimit: 100
      vehicles_surrender_initiate:
        enabled: true
        ratelimit: 100
      vehicles_surrender_complete:
        enabled: true
        ratelimit: 100
      rider_validate:
        enabled: true
        ratelimit: 100
      lead_status_update:
        enabled: true
        ratelimit: 100
      vehicles_ev_models_v2:
        enabled: true
        ratelimit: 1
      vehicles_handover_initiate_v2:
        enabled: true
        ratelimit: 100
      vehicles_handover_complete_v2:
        enabled: true
        ratelimit: 100
      vehicles_surrender_initiate_v2:
        enabled: true
        ratelimit: 100
      vehicles_surrender_complete_v2:
        enabled: true
        ratelimit: 100
      rider_validate_v2:
        enabled: true
        ratelimit: 100
      lead_status_update_v2:
        enabled: true
        ratelimit: 100
    ratelimit:
      vehicles_ev_models: 5
      vehicles_handover_initiate: 100
      vehicles_handover_complete: 100
      vehicles_surrender_initiate: 100
      vehicles_surrender_complete: 100
      rider_validate: 200
benchmarking:
  buffer_size: 1000
  enabled: false
  authority: api-gateway
  whitelisted_apis:
    - path: /gw/goout/restaurant/info
      method: POST
  kafka:
    topic: "zomato.benchmarking-service.api-gateway"
    cluster_type: ""
akamai:
  redirect_host: "https://accounts.eks.zdev.net"
logistics_merchant_service:
  host: "*************"
  port: "4650"
  authority_header: "logistics-merchant-service"
zomato_money_linking:
  enabled: true
  enabled_for_zomans: true
  whitelisted_user_ids: [*********]
  enabled_user_perc:
    min: 0
    max: 100
  settings:
    change_number:
      enabled: true
    manage_linked_devices:
      enabled: false
    order_history:
      enabled: false
    faq:
      enabled: true
  enterprise:
    verify_email: false
zomato_money_v2:
  enabled: true
  configure:
    enabled: true
    rollout: 0
    rollout_perc:
      min: 0
      max: 100
  ppi_container_merge:
    enabled: true
  hide_zcredits:
    enabled: true
  new_bottom_sheet_title:
    enabled: true
  company_font_fallback:
    enabled: true
  company_color_fallback:
    enabled: true
  claim_page_flow_via_pag:
    enabled: true
    whitelisted_users: [ ********* ]
    whitelisted_entities: ['ZOMATO_USER']
    rollout:
      min: 0
      max: 100
  claim_gc:
    district:
      android_version: "100"
      ios_version: "1.0.0"
  customization_page_flow_via_pag:
    enabled: true
    whitelisted_users: [ ********* ]
    rollout:
      min: 0
      max: 100
  dashboard:
    user_list : [*********,*********,*********]
  money_bottomsheet:
    expiry_snippet:
      enabled: true
      whitelisted_user_ids: [*********]
      rollout:
        min: 0
        max: 100
  home_page_balance:
    wallet_sdk_version:
      ios: "18.12.0"
      android: "871"
    android_version: "100"
    android_beta_version: "100"
    ios_version: "1.0.0"
    enabled: true
    whitelisted_user_ids: [*********]
    enabled_for_zomans: true
    enabled_for_eternals: true
    rollout: 100
    increased_font_size: true
item_availability:
  search_refresh:
    enabled: true
menu_rpc_handler:
  whitelisted_user_ids: []
menu_dish_handler:
  whitelisted_user_ids: []
user_preferences_service:
  host: "kuma-gateway.eks.zdev.net"
  port: 80
  authority_header: "user_preferences_service"
  timeout: 500ms
  update_user_contact_meta:
    timeout_ms: 500ms
  update_generic_preference_for_user:
    timeout_ms: 500ms
  update_generic_preference_for_user_v2:
    timeout_ms: 500ms
  get_bulk_collection_mapping_for_entity:
    timeout_ms: 500ms
  get_all_chains_for_user_collections:
    timeout_ms: 500ms
  get_user_generic_preferences:
    timeout_ms: 5000ms
  get_collections:
    timeout_ms: 5000ms
  create_collection:
    timeout_ms: 5000ms
  subscribe_collection:
    timeout_ms: 5000ms
  unsubscribe_collection:
    timeout_ms: 5000ms
  delete_collection:
    timeout_ms: 5000ms
  bulk_update_collection:
    timeout_ms: 5000ms
  sync_contacts:
    timeout_ms: 50000ms
  get_user_contacts:
    timeout_ms: 5000ms
  bulk_update_subscribed_recommendation:
    timeout_ms: 500ms
  notify_contacts_to_recommend:
    timeout_ms: 500ms
  update_recommendations_collection:
    timeout_ms: 500ms
  unsync_contacts:
    timeout_ms: 500ms
  get_user_contact_details:
    timeout_ms: 500ms
  bulk_contacts_from_hash:
    timeout_ms: 500ms
  get_collection_details:
    timeout_ms: 500ms
  update_collection_meta:
    timeout_ms: 500ms
  collections:
    enabled: true
    enabled_for_zomans: true
    enabled_user_ids: [266349740]
    enabled_for_all: true
    bottomsheet_ttl: 1800
    add_to_bookmarks_enabled: true
  recommendations:
    incremental_writes_enabled: true
    incremental_writes_via_recommendations_rpc_enabled: true
    rating_recommendations_enabled: true
    enabled: true
    enabled_for_zomans: true
    enabled_for_all: true
    experiment_check_enabled: false
    manage_contacts_filter_enabled: true
    clear_all_contacts_filter_enabled: true
    recommendable_order_count_check:
      enabled: true
    show_all_contacts_enabled: true
    subscribe_all_contacts_while_sync_enabled: true
    refresh_search_on_contacts_update: true
    loading_your_recos_lottie: "https://b.zmtcdn.com/data/file_assets/0a2dd6f0eb2902fb783ca24a08044be71730881986.json"
    loading_your_recos_dark_mode_lottie: "https://b.zmtcdn.com/data/file_assets/0a2dd6f0eb2902fb783ca24a08044be71730881986.json"
    collection_deeplink:
      hide_owner_user_id: true
    manage_contacts:
      enabled: true
      delete_all_contacts_enabled: true
    sync_contacts:
      revamped_flow_enabled: true
    onboarding_bottom_sheet:
      lottie_animation_url: "https://b.zmtcdn.com/data/file_assets/3c024ba8a0f8f13f85c23b4c27709fce1749474960.json"
      dark_mode_lottie_animation_url: "https://b.zmtcdn.com/data/file_assets/4c06366c4ba22db7a765bd6a9de90af01733487470.json"
    manage_recommendations:
      enabled: true
      dummy_response_enabled: true
      test_user_ids: [200502813]
      contacts_preference_enabled: true
      veg_preference_enabled: true
      loved_by_suggested_rail_lottie: "https://b.zmtcdn.com/data/file_assets/484465092ffab588844787178927e1691749035465.json"
      dish_preferences:
        max_items_count_in_chains: 5
        max_chain_limit: 5
        cards_threshold: 5
      get_order_details_enabled: true
  dismiss_action:
    enabled: true
  update_generic_preference:
    enabled: false
  tinder:
    dish_preferences:
      enabled: true
      dummy_response_enabled: true
      all_cards_swipped_lottie: "https://b.zmtcdn.com/data/o2_assets/df711882e7d899b0d2ed4964c895921e1743103329.png"
      show_more_lottie: "https://b.zmtcdn.com/data/o2_assets/64dd83c186baa2362603aa67df951abb1744293154.png"
      education_lottie: "https://b.zmtcdn.com/data/file_assets/95c6071bfd9e0d71c58ba209f4397d5e1743514185.json"
      max_items_count_in_chains: 5
      max_chain_limit: 5
      max_items_per_chain_filtering_enabled: true
      max_items_count_per_chain_upper_limit: 5
      max_items_count_per_chain_lower_limit: 3
  contact:
    should_send_contact_user_id: true
new_dark_mode:
  enabled: true
  enabled_user_ids: []
  enabled_for_zomans: true
  enabled_for_eternal_linked: true
  enabled_for_all: true
fintech:
  dark_mode:
    is_enabled: true
    is_eternal_linked_enabled: true
    skip_paths: /gw/payments/in/wallet_sdk/configure /gw/payments/in/get-sdk-token
irctc:
  whitelisted_ips: ["*************", "***********", "**************", "*************", "***************", "************", "************", "**************", "*************", "**************", "***************", "************", "*************", "**************", "*************", "*************", "*************", "**************", "**********", "***********"]
  client_id: "zomato_irctc_v1"
  cart:
    test_phone_number: "9876543210"
    checkout_flow:
      max_retry_count: 3
  listing:
    default_mov: 149
    search_resp_enabled: false
    empty_order_by_time_enabled: true
  logging_enabled: true
mmt:
  whitelisted_ips: []
  client_id: "zomato_mmt_v1"
  logging_enabled: true
  hmac_salt: "abcx12x"
  customer_support_number: ""
  train_ordering:
    enabled: true
    station_listing:
      enabled: true
    res_listing:
      enabled: true
      default_mov: 149
      search_resp_enabled: false
      empty_order_by_time_enabled: true
      use_mmt_flow_type: true
    res_menu_listing:
      enabled: true
    get_cart:
      enabled: true
    place_order:
      enabled: true
      test_flow:
        mock_response:
          enabled: true
        mock_user:
          enabled: true
          test_phone_numbers: ["9876543210"]
    order_info:
      enabled: true
    cancel_order:
      enabled: true
    update_seat:
      enabled: true
    walker_info:
      enabled: true
    refund_transaction:
      enabled: true
highway:
  enabled: true
  landing_page_bg_image_url: "https://b.zmtcdn.com/data/o2_assets/9c72ece246b28bbae7b33473b737dca91727103365.png"
  landing_page_bg_image_aspect_ratio: 1.5
  landing_page_bg_image_ar: 0.7
datadog:
  service_name: "zomato-api-gateway"
  tracing_enabled: true
  env: "dev"
  enabled: "true"
  daemon_enabled: true
  profiling:
    enabled: false
    goroutine_profiling:
      enabled: false
    block_profiling:
      enabled: false
      rate: 1000
    mutex_profiling:
      enabled: false
      rate: 100

tracer:
  level: debug
  enabled: true
  env: "dev"
  debug: false
  service:
    name: "zomato-api-gateway"
  agent:
    addr: "localhost:8126"
  cost:
    enabled: true
    fallback_to_dd: false

reorder_tab:
  enabled: true
  dish_tab_rails_ui_app_versions: []
  dish_tab_rails_enabled_user_ids: []
  dish_tab_rails_enabled_for_eternals: true
  dish_tab_rails_enabled_for_user_start_mod: 0
  dish_tab_rails_enabled_for_user_end_mod: 100
  dish_tab_rails_enabled_for_all: true
  relevance_aggregation_enabled: true
  show_total_order_count_enabled: true
  fetch_consumer_dish_ids_enabled: true
  near_and_fast_filter_enabled: true
  default_sort_by_relevance_enabled: false
  consumer_order_history_enabled_for_search_key: false
  show_education_wherobar_rollout_percentage: 0
  past_dishes_disabled_user_ids: []
  past_dishes_disable_rollout_percentage: 0
  past_order_property_killed: false
  should_show_filters: true
  mro:
    enabled_for_all: false
    ui_app_versions: []
    enabled_user_ids: []
  v2_flow:
    enabled_user_ids: []
    enabled_for_all: true
    ui_app_versions: []
    show_similar_res_rail: true
    show_static_similar_res_rail: true
    skip_undelivered_orders: true
    ratings_test_flow_enabled: false

order_planner:
  enabled: true
  enabled_for_all: true
  enabled_user_ids: [106573766]
  enabled_for_zomans: true

otlp_collector:
  host: localhost
  port: 4317

merchant_comms_service:
  host: "kuma-gateway.eks.zdev.net"
  port: 80
  authority_header: "merchant-comms-service"

pdf_generator_v2_service:
  host: "kuma-gateway.eks.zdev.net"
  port: 80
  authority_header: "pdf-generator-v2-service"

jumbo_v2:
  enabled: true
  client_id: zomato-api-service

add_money_claim_kill_switch_functionality_enabled: true

login_page:
  sort_countries:
    enabled: true

communications_dashboard:
  max_date_range: 30
  allowed_bulk_ops_names: []
  bulk_sms:
    max_recipients: 1000
    timeout: 5000
    config_id: ""
  bulk_email:
    max_recipients: 1000
    timeout: 5000
    config_id: ""
    allowed_from_emails: []
  bulk_ivr:
    max_recipients: 1000
    timeout: 5000
    config_id: ""
  bulk_whatsapp:
    max_recipients: 1000
    timeout: 5000
    config_id: ""
  bulk_tracking:
    limit: 100
    timeout: 5000
  insights_limit: 5000
  allowed_user_ids:
    admin: ["1", "2", "3"]
    bulk: ["1", "2", "3"]
    test: ["1", "2", "3"]
    number_search: ["1", "2", "3"]
    email_search: ["1", "2", "3"]
    custom_test: ["1", "2", "3"]
  blocked_user_ids: ["4", "5"]
  system_handler_endpoints:
    sms:
      update_provider_load_priority: ""
      update_provider_data: ""
      clear_cache: ""
      test_sms_via_provider: ""
      get_provider_load_priority: ""
    email:
      db_handlers: ""
    telecom:
      allocate_masked_number: ""
      deallocate_masked_number: ""
      dual_leg_call: ""
      ivr_call: ""
  template_dashboard:
    sms: ""
    email: ""

experimentation_package:
  host: kuma-gateway.eks.zdev.net
  port: 80
  authority: experimentation-service
  enabled: false
  read_enabled: false
  read_enabled_percentage: 0

address_page_revamp:
  enabled: true
  low_confidence_address_enabled: true
  entity_zoom_level_enabled: true
  poi_zoom_level: 16
  tower_zoom_level: 16
  house_zoom_level: 16
  default_zoom_level: 16
  restrict_receiver_snippet:
    enabled_countries: [214]
  add_place_o2_serviceability_flag: true
  grafana_metrics_enabled: true
  center_align_current_location_pill: true
  allow_location_prompt_single_time: true
  lower_bound_distance_for_far_away_nudge: 50
  device_location_swap:
    enabled: true
    distance_threshold: 100
  low_confidence_bottomsheet:
    threshold_distance: 250
  show_device_location_off_nudge: true
  new_bottom_container_enabled: true
premium_referral:
  enabled: true
  whitelisted_user_ids_for_referring: [-1]
  whitelisted_user_ids_for_claiming_referral: [-1]
  min_ios_version: "20.20.0"
  min_android_version: "2000"
  min_android_beta_version: "2000"
  show_locked_games_in_eligible: true
  user_split: 10
  target_groups: [6]
  min_threshold_for_see_more_contacts_button: 6
  show_see_more_contacts_button: true
  side_menu:
    enabled: true
    claim_button_enabled: true
    invite_button_enabled: true

promo:
  offer_wall:
    banner_carousel:
      enabled: true
      whitelisted_users: [327539258]
      enabled_for_all: true
      banner_aspect_ratio: 4.08
      tata_neu:
        enabled: true
        banner:
          image_url: "https://b.zmtcdn.com/promo-service/santa-dashboard/production/04-2025/b1667855-7be4-40d6-b1d5-2ca4ba1d59de"
        bottom_sheet:
          image_url: "https://b.zmtcdn.com/promo-service/santa-dashboard/production/04-2025/e4d9115d-5cb9-419e-b42e-3cdac10d34ed"
          dark_mode_image_url: "https://b.zmtcdn.com/promo-service/santa-dashboard/production/04-2025/220aa4ff-6dd1-4960-b51f-bf8bd68c9940"
          aspect_ratio: 0.56
      kotak:
        enabled: true
        banner:
          image_url: "https://b.zmtcdn.com/promo-service/santa-dashboard/production/04-2025/2779f027-8087-4684-8d7c-bd2e19eb7052"
        bottom_sheet:
          image_url: "https://b.zmtcdn.com/promo-service/santa-dashboard/production/04-2025/5df3fa69-0bea-4e3e-8680-c3e4a8a3fb0c"
          dark_mode_image_url: "https://b.zmtcdn.com/promo-service/santa-dashboard/production/04-2025/759c2107-0632-4e1b-9c14-62892b4a5c19"
          aspect_ratio: 0.63

dining_peak_hour:
  enabled: false

carbon_tech:
  kobotoolbox:
    topic: zomato.one-support.kobotoolbox-form-webhook
    auth_username: "zomato_carbon_tech"
    auth_password: "fdjklvnewkl;w"
    auth_header: "x-api-key"
    auth_header_value: "fjkdkl343vknve"

eternal_form_service:
  host: kuma-gateway.eks.zdev.net
  port: "80"
  authority_header: eternal-form-service
  api_kill_switch:
    enabled: false
  timeout: 5000ms
  api_config:
    client_list_user_forms:
      enabled: true
      ratelimit: 10
    get_client_form:
      enabled: true
      ratelimit: 10
    generate_file_upload_urls:
      enabled: true
      ratelimit: 10
    submit_form:
      enabled: true
      ratelimit: 10
  rpc_config:
    create_form:
      enabled: true
      timeout: 5000
    list_forms:
      enabled: true
      timeout: 5000
    update_form_settings:
      enabled: true
      timeout: 5000
    update_form:
      enabled: true
      timeout: 5000
    get_admin_form:
      enabled: true
      timeout: 5000
    get_registry:
      enabled: true
      timeout: 5000
    deploy_form_version:
      enabled: true
      timeout: 5000
    generate_file_upload_urls:
      enabled: true
      timeout: 5000
    get_client_form:
      enabled: true
      timeout: 5000
    client_submit_form:
      enabled: true
      timeout: 5000
    client_list_user_forms:
      enabled: true
      timeout: 5000
    update_form_sharing:
      enabled: true
      timeout: 5000
    get_form_sharing:
      enabled: true
      timeout: 5000
    ai_generate_form_config:
      enabled: true
      timeout: 30000
    get_form_settings:
      enabled: true
      timeout: 5000
    get_form_responses:
      enabled: true
      timeout: 5000
    get_form_responses_summary:
      enabled: true
      timeout: 5000
    get_form_version_details:
      enabled: true
      timeout: 5000
    delete_form:
      enabled: true
      timeout: 5000
  whitelisted_user_ids: [1]
  whitelisted_email_domains: ["zomato.com", "gmail.com", "grofers.com"]
  enable_whitelist_check: false
priority_tracking_events_config:
  enabled: true
  experiment_check_enabled: false
  blacklisted_event_names: []
  priority_levels: [3]
  priority_event_tables: ["zsearch_events_log", "menu_entities_app_tracking"]
  event_priority:
    filtering_config:
      - priority_level: 3
        event_sources:
          - table_name: "zsearch_events_log"
            events:
              - event_name: "listing_res_card_tap"
                event_action: "tap"
          - table_name: "menu_entities_app_tracking"
            events:
              - event_name: "O2MenuItemLongImpression"
                event_action: "long_impression"
location_selection_page:
  enabled: true
  initial_addresses_shown_count: 3
  recent_locations_shown_count: 3
  nearby_addresses_shown_count: 3
  import_address_from_blinkit_enabled: true
  rain_banner_enabled: true
get_location_details:
  enabled: true
  jumbo_logging_enabled: true
location_search_page:
  enabled: true
  web_response_timeout: 1000ms
  nearby_addresses_shown_count: 3
  recent_locations_shown_count: 3
experimentation_dashboard:
  users_enabled : []
  timeout : 100000

partner_rewards:
  min_android_version: "5000"
  min_android_beta_version: "278"
  min_ios_version: "18.45.0"
