package main

import (
	"context"
	"fmt"
	"net/http"
	"runtime/debug"
	"time"

	"github.com/Zomato/auth-service-client-golang/pkg/auth"
	cartv1 "github.com/Zomato/cart-service-client-golang/cart-service/cart/v1"
	groupv1 "github.com/Zomato/cart-service-client-golang/cart-service/group-ordering/v1"
	orderv1 "github.com/Zomato/delivery-consumer-order-service-client-golang/v2/order/v1"
	delightv1 "github.com/Zomato/gamification-service-client-golang/proto/delight/v1"
	"github.com/Zomato/go/inventory/gogin"
	"github.com/Zomato/go/jumbo"
	zjumbo_v2 "github.com/Zomato/go/jumbo-v2"
	ztracer "github.com/Zomato/go/tracer"
	"github.com/Zomato/merchant-outlet-service-client-golang/proto/outlet"
	trainjourneyv1 "github.com/Zomato/train-fulfillment-service-client-golang/proto/train-fulfillment-service/trainjourney/v1"
	"github.com/Zomato/user-service-client-golang/useraddress"
	delightSvcLib "github.com/Zomato/zomato-api-gateway/internal/delight_service/lib"
	"github.com/Zomato/zomato-api-gateway/internal/includes/system"
	"github.com/Zomato/zomato-api-gateway/internal/pubsub"
	"github.com/Zomato/zomato-api-gateway/internal/pubsub/kafka/enqueue"
	"github.com/Zomato/zomato-api-gateway/internal/upload_service"
	"github.com/Zomato/zomato-api-gateway/internal/uploads"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"

	"github.com/Shopify/sarama"
	"github.com/Zomato/go/config"
	"github.com/Zomato/go/grpc"
	"github.com/Zomato/go/grpc/credentials/insecure"
	"github.com/Zomato/go/i18n"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/metrics"
	"github.com/Zomato/go/newrelic"
	"github.com/Zomato/go/profiler"
	zruntime "github.com/Zomato/go/runtime"
	s3uploads "github.com/Zomato/zomato-api-gateway/internal/s3_uploads"
	"github.com/gin-gonic/gin"
	goGrpc "google.golang.org/grpc"

	"github.com/Zomato/go/experimentation"
	menu_agg "github.com/Zomato/menu-aggregator-service-client-golang/consumer"
	sharev1 "github.com/Zomato/reviews-service-client-golang/share-service/pkg/proto/share/service"
	"github.com/Zomato/zomato-api-gateway/handlers/aerobar"
	emailMetrics "github.com/Zomato/zomato-api-gateway/handlers/email/metrics"
	"github.com/Zomato/zomato-api-gateway/handlers/feedback"
	lookbackmetrics "github.com/Zomato/zomato-api-gateway/handlers/gamification/badges/lookback/common/metrics"
	"github.com/Zomato/zomato-api-gateway/handlers/gamification/crazydrops"
	"github.com/Zomato/zomato-api-gateway/handlers/order/live_order"
	paymentsClient "github.com/Zomato/zomato-api-gateway/handlers/payments"
	paymentsApiGwClient "github.com/Zomato/zomato-api-gateway/handlers/payments_api_gw"
	tabbedhome "github.com/Zomato/zomato-api-gateway/handlers/tabbed_home"
	"github.com/Zomato/zomato-api-gateway/internal/cache"
	cartServicLib "github.com/Zomato/zomato-api-gateway/internal/cart/lib"
	cartMetrics "github.com/Zomato/zomato-api-gateway/internal/cart/metrics"
	crystalMetrics "github.com/Zomato/zomato-api-gateway/internal/crystal/metrics"
	"github.com/Zomato/zomato-api-gateway/internal/cult_app_downloads"
	"github.com/Zomato/zomato-api-gateway/internal/env"
	"github.com/Zomato/zomato-api-gateway/internal/ephemeral_uploads"
	"github.com/Zomato/zomato-api-gateway/internal/interceptor"
	"github.com/Zomato/zomato-api-gateway/internal/jwt"
	locationMetrics "github.com/Zomato/zomato-api-gateway/internal/location/metrics"
	menuMetrics "github.com/Zomato/zomato-api-gateway/internal/menu/metrics"
	subscriptionMetrics "github.com/Zomato/zomato-api-gateway/internal/subscription/metrics"
	weatherMetrics "github.com/Zomato/zomato-api-gateway/internal/weather/metrics"
	"github.com/Zomato/zomato-api-gateway/internal/webclient"
	"github.com/Zomato/zomato-api-gateway/pkg/jumbo_v2"
	trace "github.com/Zomato/zomato-api-gateway/pkg/tracer"
	"github.com/Zomato/zomato-api-gateway/router"
)

func main() {
	// initialise config
	config.Init()
	ctx, err := config.TODOContext()
	if err != nil {
		log.WithError(err).Panic("error while getting context from config")
	}

	// configure logging
	err = log.Initialize(
		log.Formatter(config.GetString(ctx, "log.format")),
		log.Level(config.GetString(ctx, "log.level")),
	)
	if err != nil {
		log.WithError(err).Panic("unable to initialise logger")
	}
	defer log.Sync() //nolint:errcheck
	initializeRuntime(ctx)

	if config.GetBool(ctx, "datadog.enabled") {
		opts := []tracer.StartOption{
			tracer.WithService(config.GetString(ctx, "datadog.service_name")),
			tracer.WithTraceEnabled(config.GetBool(ctx, "datadog.tracing_enabled")),
			tracer.WithEnv(config.GetString(ctx, "datadog.env")),
		}
		if config.GetBool(ctx, "datadog.daemon_enabled") {
			if zruntime.InstanceIP() != "" {
				opts = append(opts, tracer.WithAgentAddr(zruntime.InstanceIP()+":8126"))
			}
		}

		provider := ztracer.NewDatadogProvider(ctx, opts...)
		if err := ztracer.Initialize(provider); err != nil {
			log.FromContext(ctx).Errorf("failed to initialize datadog tracer %s", err)
			return
		}

		defer provider.Close()
	}

	if config.GetBool(ctx, "profiler.enabled") {
		profiler.Initialize(
			ctx,
			profiler.WithProfilerType(profiler.ProfilerTypePyroscope),
			profiler.WithPyroscopeAuthToken(config.GetString(ctx, "profiler.credentials")),
			profiler.WithFallbackAppName(config.GetString(ctx, "service.name")),
			profiler.WithGoroutineProfiling(config.GetBool(ctx, "profiler.goroutine_profile.enabled")),
			profiler.WithBlockProfiling(config.GetBool(ctx, "profiler.block_profile.enabled"), 1000), // profile 1 blocking event per 1000ns spent blocked on average
			profiler.WithMutexProfiling(config.GetBool(ctx, "profiler.mutex_profile.enabled"), 100),  // 100 == 1% (1/100) of mutex contention events are profiled.
		)
	}

	if config.GetBool(ctx, "datadog.profiling.enabled") {
		profiler.Initialize(
			ctx,
			profiler.WithProfilerType(profiler.ProfilerTypeDatadog),
			profiler.WithGoroutineProfiling(config.GetBool(ctx, "datadog.profiling.goroutine_profiling.enabled")),
			profiler.WithBlockProfiling(config.GetBool(ctx, "datadog.profiling.block_profiling.enabled"), config.GetUint(ctx, "datadog.profiling.block_profiling.rate")),
			profiler.WithMutexProfiling(config.GetBool(ctx, "datadog.profiling.mutex_profiling.enabled"), config.GetUint(ctx, "datadog.profiling.mutex_profiling.rate")),
		)
	}

	err = cache.Initialize()
	if err != nil {
		log.WithError(err).Error("could not initialise cache")
	}

	// newrelic
	nrApp := newrelic.Initialize(
		&newrelic.Options{
			Name:    config.GetString(ctx, "service.name"),
			License: config.GetString(ctx, "newrelic.key"),
			Enabled: config.GetBool(ctx, "newrelic.enabled"),
		},
	)

	// initialise localization
	err = i18n.Initialize(i18n.WithRootPath("./localization"))
	if err != nil {
		log.WithError(err).Error("unable to initialise localization")
	}

	// // offline msk kafka cluster client
	offlineMSKPubsubClient, err := pubsub.NewPubSubCloser(pubsub.Config{
		Backend: pubsub.BackendTypeKafka,
		KafkaConfig: pubsub.KafkaConfig{
			ClientID:      config.GetString(ctx, "service.name"), // TODO: add unique identifier like container id
			Brokers:       config.GetStringSlice(ctx, "kafka.offline_msk.brokers"),
			SASLUserName:  config.GetString(ctx, "kafka.offline_msk.sasl_username"),
			SASLPassword:  config.GetString(ctx, "kafka.offline_msk.sasl_password"),
			SASLMechanism: config.GetString(ctx, "kafka.offline_msk.sasl_mechanism"),
			KafkaVersion:  config.GetString(ctx, "kafka.offline_msk.version"),
		},
	})
	if err != nil {
		log.WithError(err).Panic("unable to initialise offline pubsub")
	}
	defer offlineMSKPubsubClient.Close()

	onlineConfluentPubsubClient, err := pubsub.NewPubSubCloser(pubsub.Config{
		Backend: pubsub.BackendTypeKafka,
		KafkaConfig: pubsub.KafkaConfig{
			ClientID:     config.GetString(ctx, "service.name"),
			Brokers:      config.GetStringSlice(ctx, "kafka.online_confluent.brokers"),
			KafkaVersion: config.GetString(ctx, "kafka.online_confluent.version"),
			SASLUserName: config.GetString(ctx, "kafka.online_confluent.sasl_username"),
			SASLPassword: config.GetString(ctx, "kafka.online_confluent.sasl_password"),
		},
	})
	if err != nil {
		log.WithError(err).Panic("unable to initialise online confluent pubsub")
	}
	defer onlineConfluentPubsubClient.Close()

	o2PlacementFulfillmentConfluentPubSubClient, err := pubsub.NewPubSubCloser(pubsub.Config{
		Backend: pubsub.BackendTypeKafka,
		KafkaConfig: pubsub.KafkaConfig{
			ClientID:     config.GetString(ctx, "service.name"),
			Brokers:      config.GetStringSlice(ctx, "kafka.o2_placement_fulfillment_confluent.brokers"),
			KafkaVersion: config.GetString(ctx, "kafka.o2_placement_fulfillment_confluent.version"),
			SASLUserName: config.GetString(ctx, "kafka.o2_placement_fulfillment_confluent.sasl_username"),
			SASLPassword: config.GetString(ctx, "kafka.o2_placement_fulfillment_confluent.sasl_password"),
		},
	})
	if err != nil {
		log.WithError(err).Panic("unable to initialise o2 placement fulfillment confluent pubsub")
	}
	defer o2PlacementFulfillmentConfluentPubSubClient.Close()

	if config.GetBool(ctx, "kafka.enqueue.enabled") {
		log.Debug("enqueue is enabled")
		initializeKafkaEnqueuer(ctx)

		tabbedhome.InitialiseTabbedHomeEventEnqueuer(ctx, tabbedhome.TabbedHomeEventTopic, enqueue.OnlineClusterType)

		aerobar.InitialiseAerobarEventEnqueuer(ctx, aerobar.AerobarEventTopic, enqueue.OnlineClusterType)

		crazydrops.InitializeResSlotsInfoEventEnqueuer(ctx, crazydrops.ResSlotsInfoEventsTopic, enqueue.OnlineClusterType)

		live_order.InitialiseCrystalReconEnqueuer(ctx, live_order.CrystalReconTopic, enqueue.OfflineClusterType)

		defer enqueue.CloseClient()
	}

	// Jumbo client
	jc, err := InitializeJumbo(ctx)
	if err != nil {
		log.WithError(err).Error("could not initialise jumbo")
	}
	defer jc.Close()

	// Jumbo v2 client
	var jcV2 *zjumbo_v2.JumboClient
	if config.GetBool(ctx, "jumbo_v2.enabled") {
		jcV2, err = InitializeJumboV2(ctx)
		if err != nil {
			log.WithError(err).Error("could not initialise jumbo v2")
		}
		defer jcV2.Close()
	}

	// metrics
	err = metrics.Initialize(metrics.Config{
		Name:                  "zomato_api_gateway",
		OtelCollectorProtocol: metrics.GRPC,
		OtelCollectorAddr:     config.GetString(ctx, "otlp_collector.host") + ":" + config.GetString(ctx, "otlp_collector.port"),
	})
	if err != nil {
		log.WithError(err).Error("could not initialise metrics")
	}

	err = delightSvcLib.InitializeErrorMetrics()
	if err != nil {
		log.WithError(err).Error("could not initialise delight service metrics")
	}

	err = cartServicLib.InitializeErrorMetrics()
	if err != nil {
		log.WithError(err).Error("could not initialise delight service metrics")
	}

	// Menu Service
	menuServiceConn, err := grpc.Dial(
		config.GetString(ctx, "menu_service.host")+":"+config.GetString(ctx, "menu_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "menu_service.authority_header")),
		goGrpc.WithChainUnaryInterceptor(
			interceptor.StatsClientInterceptor(),
		),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to menu service")
	}
	defer menuServiceConn.Close()

	// Menu Merchant Service
	menuMxServiceConn, err := grpc.Dial(
		config.GetString(ctx, "menu_mx_service.host")+":"+config.GetString(ctx, "menu_mx_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "menu_mx_service.authority_header")),
		goGrpc.WithChainUnaryInterceptor(
			interceptor.StatsClientInterceptor(),
		),
	)
	if err != nil {
		log.WithError(err).Error("could not connect to menu mx service")
	}
	defer menuMxServiceConn.Close()

	// Menu Aggregator Service
	menuAggServiceConn, err := grpc.Dial(
		config.GetString(ctx, "menu_aggregator_service.host")+":"+config.GetString(ctx, "menu_aggregator_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "menu_aggregator_service.authority_header")),
		grpc.WithDefaultCallOptions(goGrpc.MaxCallRecvMsgSize(config.GetInt(ctx, "menu_aggregator_service.max_receive_msg_length_mb")*1024*1024)),
		goGrpc.WithChainUnaryInterceptor(
			interceptor.StatsClientInterceptor(),
		),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to menu aggregator service")
	}
	defer menuAggServiceConn.Close()

	driverServiceConn, err := grpc.Dial(
		config.GetString(ctx, "driver_service.host")+":"+config.GetString(ctx, "driver_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "driver_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to driver service")
	}
	defer driverServiceConn.Close()

	lmsServiceConn, err := grpc.Dial(
		config.GetString(ctx, "lead_management_service.host")+":"+config.GetString(ctx, "lead_management_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "lead_management_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to lead_management_service service")
	}
	defer lmsServiceConn.Close()

	logisticsMerchantServiceConn, err := grpc.Dial(
		config.GetString(ctx, "logistics_merchant_service.host")+":"+config.GetString(ctx, "logistics_merchant_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "logistics_merchant_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to logistics merchant service")
	}
	defer logisticsMerchantServiceConn.Close()

	// Aerobar Service
	aerobarServiceConn, err := grpc.Dial(
		config.GetString(ctx, "aerobar_service.host")+":"+config.GetString(ctx, "aerobar_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "aerobar_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to aerobar_service")
	}
	defer aerobarServiceConn.Close()

	// Bulk Operation Service
	var bulkOpsServiceConn *grpc.ClientConn
	if config.GetBool(ctx, "bulk_operation_service.enabled") {
		bulkOpsServiceConn, err = grpc.Dial(
			config.GetString(ctx, "bulk_operation_service.host")+":"+config.GetString(ctx, "bulk_operation_service.port"),
			grpc.WithTransportCredentials(insecure.NewCredentials()),
			grpc.WithAuthority(config.GetString(ctx, "bulk_operation_service.authority_header")),
			grpc.WithDefaultCallOptions(goGrpc.MaxCallRecvMsgSize(config.GetInt(ctx, "bulk_operation_service.max_receive_msg_length_mb")*1024*1024)),
			goGrpc.WithChainUnaryInterceptor(
				interceptor.StatsClientInterceptor(),
			),
		)
		if err != nil {
			log.WithError(err).Panic("could not connect to bulk operation service")
		}
		defer bulkOpsServiceConn.Close()
	}

	// Serviceability Aggregator Service
	serviceabilityAggServiceConn, err := grpc.Dial(
		config.GetString(ctx, "serviceability_agg_service.host")+":"+config.GetString(ctx, "serviceability_agg_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "serviceability_agg_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to serviceability aggregator service")
	}
	defer serviceabilityAggServiceConn.Close()

	// Serviceability Manager Service
	serviceabilityManagerServiceConn, err := grpc.Dial(
		config.GetString(ctx, "serviceability_manager_service.host")+":"+config.GetString(ctx, "serviceability_manager_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "serviceability_manager_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to serviceability manager service")
	}
	defer serviceabilityManagerServiceConn.Close()

	// Demand Manager Service
	demandManagerServiceConn, err := grpc.Dial(
		config.GetString(ctx, "demand_manager_service.host")+":"+config.GetString(ctx, "demand_manager_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "demand_manager_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to demand manager service")
	}
	defer demandManagerServiceConn.Close()

	// Kitchen Service
	var kitchenServiceConn *grpc.ClientConn
	if config.GetBool(ctx, "kitchen_service.enabled") {
		kitchenServiceConn, err = grpc.Dial(
			config.GetString(ctx, "kitchen_service.host")+":"+config.GetString(ctx, "kitchen_service.port"),
			grpc.WithTransportCredentials(insecure.NewCredentials()),
			grpc.WithAuthority(config.GetString(ctx, "kitchen_service.authority_header")),
		)
		if err != nil {
			log.WithError(err).Panic("could not connect to kitchen service")
		}
		defer kitchenServiceConn.Close()
	}

	// Promo Service
	promoServiceConn, err := grpc.Dial(
		config.GetString(ctx, "promo_service.host")+":"+config.GetString(ctx, "promo_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "promo_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to promo service")
	}
	defer promoServiceConn.Close()

	// Promo Ext Service
	promoExtServiceConn, err := grpc.Dial(
		config.GetString(ctx, "promo_ext_service.host")+":"+config.GetString(ctx, "promo_ext_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "promo_ext_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to promo ext service")
	}
	defer promoExtServiceConn.Close()

	// Location Service
	locationServiceConn, err := grpc.Dial(
		config.GetString(ctx, "location_service.host")+":"+config.GetString(ctx, "location_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "location_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to location service")
	}
	defer locationServiceConn.Close()

	// NPS Service
	npsServiceConn, err := grpc.Dial(
		config.GetString(ctx, "nps_service.host")+":"+config.GetString(ctx, "nps_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "nps_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to nps service")
	}
	defer npsServiceConn.Close()

	// Edition Service
	editionServiceConn, err := grpc.Dial(
		config.GetString(ctx, "edition_service.host")+":"+config.GetString(ctx, "edition_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "edition_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to edition service")
	}
	defer editionServiceConn.Close()

	// Logistics order service
	logisticsOrderServiceConn, err := grpc.Dial(
		config.GetString(ctx, "logistics_order_service.host")+":"+config.GetString(ctx, "logistics_order_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "logistics_order_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to logistics order service")
	}
	defer logisticsOrderServiceConn.Close()

	// Reviews Service
	reviewsServiceConn, err := grpc.Dial(
		config.GetString(ctx, "reviews_service.host")+":"+config.GetString(ctx, "reviews_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "reviews_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to reviews service")
	}
	defer reviewsServiceConn.Close()

	authServiceConn, err := grpc.Dial(
		config.GetString(ctx, "auth_service.host")+":"+config.GetString(ctx, "auth_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "auth_service.authority_header")),
		goGrpc.WithChainUnaryInterceptor(
			interceptor.StatsClientInterceptor(),
		),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to auth service")
	}
	defer authServiceConn.Close()

	userServiceConn, err := grpc.Dial(
		config.GetString(ctx, "user_service.host")+":"+config.GetString(ctx, "user_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "user_service.authority_header")),
		goGrpc.WithChainUnaryInterceptor(
			interceptor.StatsClientInterceptor(),
		),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to user service")
	}
	defer userServiceConn.Close()

	userPreferenceServiceConn, err := grpc.Dial(
		config.GetString(ctx, "user_preferences_service.host")+":"+config.GetString(ctx, "user_preferences_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "user_preferences_service.authority_header")),
		goGrpc.WithChainUnaryInterceptor(
			interceptor.StatsClientInterceptor(),
		),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to user service")
	}
	defer userPreferenceServiceConn.Close()

	CorporateFundsServiceConn, err := grpc.Dial(
		config.GetString(ctx, "corporate_funds_service.host")+":"+config.GetString(ctx, "corporate_funds_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "corporate_funds_service.authority_header")),
		goGrpc.WithChainUnaryInterceptor(
			interceptor.UnaryTimeoutInterceptor(
				config.GetDuration(ctx, "corporate_funds_service.timeout"),
			),
		),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to corporate funds service")
	}
	defer CorporateFundsServiceConn.Close()

	GiftCardsServiceConn, err := grpc.Dial(
		config.GetString(ctx, "gift_cards_service.host")+":"+config.GetString(ctx, "gift_cards_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "gift_cards_service.authority_header")),
		goGrpc.WithChainUnaryInterceptor(
			interceptor.UnaryTimeoutInterceptor(
				config.GetDuration(ctx, "gift_cards_service.timeout"),
			),
		),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to gift cards service")
	}
	defer GiftCardsServiceConn.Close()

	smsServiceConn, err := grpc.Dial(
		config.GetString(ctx, "sms_service.host")+":"+config.GetString(ctx, "sms_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "sms_service.authority_header")),
	)
	if err != nil {
		log.Panicf("unable to create sms service client: %s", err)
	}
	defer smsServiceConn.Close()

	telecomServiceConn, err := grpc.Dial(
		config.GetString(ctx, "telecom_service.host")+":"+config.GetString(ctx, "telecom_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "telecom_service.authority_header")),
	)
	if err != nil {
		log.Panicf("unable to create telecom service client: %s", err)
	}
	defer telecomServiceConn.Close()

	searchServiceConn, err := grpc.Dial(
		config.GetString(ctx, "search_service.host")+":"+config.GetString(ctx, "search_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "search_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to search service")
	}
	defer searchServiceConn.Close()

	searchWorkerServiceConn, err := grpc.Dial(
		config.GetString(ctx, "search_worker_service.host")+":"+config.GetString(ctx, "search_worker_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "search_worker_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Error("Could not connect to search worker service")
	}
	defer searchWorkerServiceConn.Close()

	benefitsServiceConn, err := grpc.Dial(
		config.GetString(ctx, "benefits_service.host")+":"+config.GetString(ctx, "benefits_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "benefits_service.authority_header")),
		goGrpc.WithChainUnaryInterceptor(
			interceptor.StatsClientInterceptor(),
		),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to benefits service")
	}
	defer benefitsServiceConn.Close()

	doteServiceConn, err := grpc.Dial(
		config.GetString(ctx, "dote_service.host")+":"+config.GetString(ctx, "dote_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "dote_service.authority_header")),
	)
	// doteServiceConn, err := grpc.Dial(":8088", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.WithError(err).Panic("Could not connect to dote service")
	}
	defer doteServiceConn.Close()

	storiesServiceConn, err := grpc.Dial(
		config.GetString(ctx, "stories_service.host")+":"+config.GetString(ctx, "stories_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "stories_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to stories service")
	}
	defer storiesServiceConn.Close()

	profilestoreServiceConn, err := grpc.Dial(
		config.GetString(ctx, "profile_store_service.host")+":"+config.GetString(ctx, "profile_store_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "profile_store_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to profile store service")
	}
	defer profilestoreServiceConn.Close()

	blinkitProfilestoreServiceConn, err := grpc.Dial(
		config.GetString(ctx, "profile_store_service.blinkit.host")+":"+config.GetString(ctx, "profile_store_service.blinkit.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "profile_store_service.blinkit.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to blinkit profile store service")
	}
	defer blinkitProfilestoreServiceConn.Close()

	diningServiceConn, err := system.GetDiningServiceConn(ctx)
	if err != nil {
		log.WithError(err).Panic("Could not connect to dining service")
	}
	defer diningServiceConn.Close()

	// ads target service
	adsTargetServiceConn, err := grpc.Dial(
		config.GetString(ctx, "ads_target_service.host")+":"+config.GetString(ctx, "ads_target_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "ads_target_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to ads target service")
	}
	defer adsTargetServiceConn.Close()

	// email service
	emailServiceConn, err := grpc.Dial(
		config.GetString(ctx, "email_service.host")+":"+config.GetString(ctx, "email_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "email_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to email service")
	}
	defer emailServiceConn.Close()

	butterflyServiceConn, err := grpc.Dial(
		config.GetString(ctx, "butterfly_service.host")+":"+config.GetString(ctx, "butterfly_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "butterfly_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to butterfly service")
	}
	defer butterflyServiceConn.Close()

	fireflyServiceConn, err := grpc.Dial(
		config.GetString(ctx, "firefly_service.host")+":"+config.GetString(ctx, "firefly_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "firefly_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to firefly service")
	}
	defer fireflyServiceConn.Close()

	// ML Data Moderation Tool
	moderationToolConn, err := grpc.Dial(
		config.GetString(ctx, "moderation_tool_service.host")+":"+config.GetString(ctx, "moderation_tool_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "moderation_tool_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to moderation tool")
	}
	defer moderationToolConn.Close()

	// Ai Insights Service
	aiInsightsServiceConn, err := grpc.Dial(
		config.GetString(ctx, "ai-insights-service.host")+":"+config.GetString(ctx, "ai-insights-service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "ai-insights-service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to ai insights service")
	}
	defer aiInsightsServiceConn.Close()

	// weather Service
	weatherServiceConn, err := grpc.Dial(
		config.GetString(ctx, "weather_service.host")+":"+config.GetString(ctx, "weather_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "weather_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to weather service")
	}
	defer weatherServiceConn.Close()

	merchantDiscountingServiceConn, err := grpc.Dial(
		config.GetString(ctx, "merchant_discounting_service.host")+":"+config.GetString(ctx, "merchant_discounting_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "merchant_discounting_service.authority_header")),
	)

	if err != nil {
		log.WithError(err).Panic("could not connect to merchant discounting service")
	}
	defer merchantDiscountingServiceConn.Close()

	registerMetrics()
	trace.Init(ctx)

	initializeSaramaPanicHandler()

	// Logistics Support Service
	supportServiceConn, err := grpc.Dial(
		config.GetString(ctx, "logistics_support_service.host")+":"+config.GetString(ctx, "logistics_support_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "logistics_support_service.authority_header")),
	)
	if err != nil {
		log.Panicf("could not connect to logistics support service: %s", err)
	}
	defer supportServiceConn.Close()

	// initialise jwt signers
	signerUnsubscribeLink, err := jwt.NewSigner(config.GetString(ctx, "notification_preferences.unsubscribe_url.token"))
	if err != nil {
		log.WithError(err).Error("Unable to create a jwt signer for the unsubscribe_url token key")
	}
	signerProfile, err := jwt.NewSigner(config.GetString(ctx, "notification_preferences.profile.token"))
	if err != nil {
		log.WithError(err).Error("Unable to create a jwt signer for the profile token key")
	}

	expServiceConn, err := grpc.Dial(
		config.GetString(ctx, "exp_service.host")+":"+config.GetString(ctx, "exp_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "exp_service.authority_header")),
		goGrpc.WithChainUnaryInterceptor(
			interceptor.StatsClientInterceptor(),
		),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to experimentation service")
	}
	defer expServiceConn.Close()

	var experimentationClient experimentation.ExperimentClient
	if config.GetBool(ctx, "experimentation_package.enabled") {
		experimentationClient, err = initializeExperimentationClient(ctx)
		if err != nil {
			log.Errorf("failed to initialize experimentation package client, error: %v", err)
		}
	}

	// Hermes Service
	hermesServiceConn, err := grpc.Dial(
		config.GetString(ctx, "hermes_service.host")+":"+config.GetString(ctx, "hermes_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "hermes_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to hermes service")
	}
	defer hermesServiceConn.Close()

	// Ads Service
	adsServiceConn, err := grpc.Dial(
		config.GetString(ctx, "ads_service.host")+":"+config.GetString(ctx, "ads_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "ads_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to ads service")
	}
	defer adsServiceConn.Close()

	// Subscription Service
	subscriptionServiceConn, err := grpc.Dial(
		config.GetString(ctx, "subscription_service.host")+":"+config.GetString(ctx, "subscription_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "subscription_service.authority_header")),
		goGrpc.WithChainUnaryInterceptor(
			interceptor.StatsClientInterceptor(),
		),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to subscription service")
	}
	defer subscriptionServiceConn.Close()

	// wallet service
	walletServiceConn, err := grpc.Dial(
		config.GetString(ctx, "wallet_service.host")+":"+config.GetString(ctx, "wallet_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "wallet_service.authority_header")),
		goGrpc.WithChainUnaryInterceptor(
			interceptor.UnaryTimeoutInterceptor(
				config.GetDuration(ctx, "wallet_service.timeout"),
			),
		),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to wallet service")
	}
	defer walletServiceConn.Close()

	// merchant service
	merchantServiceConn, err := grpc.Dial(
		config.GetString(ctx, "merchant_service.host")+":"+config.GetString(ctx, "merchant_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "merchant_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to merchant service")
	}
	defer merchantServiceConn.Close()

	// Credit-line Service
	creditLineServiceConn, err := grpc.Dial(
		config.GetString(ctx, "credit_line_service.host")+":"+config.GetString(ctx, "credit_line_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "credit_line_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to credit-line service")
	}
	defer creditLineServiceConn.Close()

	// Delivery Time Service
	timeServiceConn, err := grpc.Dial(
		config.GetString(ctx, "time_service.host")+":"+config.GetString(ctx, "time_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "time_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to time service")
	}
	defer timeServiceConn.Close()

	// Osrm Aggregator Service
	osrmAggregatorConn, err := grpc.Dial(
		config.GetString(ctx, "osrm_aggregator.host")+":"+config.GetString(ctx, "osrm_aggregator.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "osrm_aggregator.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to Osrm Aggregator Service")
	}
	defer osrmAggregatorConn.Close()

	// Vernacular Service
	vernacularServiceConn, err := grpc.Dial(
		config.GetString(ctx, "vernacular_service.host")+":"+config.GetString(ctx, "vernacular_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "vernacular_service.authority_header")),
	)

	if err != nil {
		log.WithError(err).Panic("could not connect to vernacular service")
	}
	defer vernacularServiceConn.Close()

	// Composite order service
	compositeOrderServiceConn, err := grpc.Dial(
		config.GetString(ctx, "composite_order_service.host")+":"+config.GetString(ctx, "composite_order_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "composite_order_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to composite order service")
	}
	defer compositeOrderServiceConn.Close()

	// payment router conn
	paymentRouterConn, err := grpc.Dial(
		config.GetString(ctx, "payment_router_service.host")+":"+config.GetString(ctx, "payment_router_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "payment_router_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to payment router service")
	}
	defer paymentRouterConn.Close()

	// Store service
	storeServiceConn, err := grpc.Dial(
		config.GetString(ctx, "store_service.host")+":"+config.GetString(ctx, "store_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "store_service.authority_header")),
		goGrpc.WithChainUnaryInterceptor(
			interceptor.StatsClientInterceptor(),
		),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to store service")
	}
	defer storeServiceConn.Close()

	// Dish service
	dishServiceConn, err := grpc.Dial(
		config.GetString(ctx, "dish_service.host")+":"+config.GetString(ctx, "dish_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "dish_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("Could not connect to dish service")
	}
	defer dishServiceConn.Close()

	// Photos service
	photosServiceConn, err := grpc.Dial(
		config.GetString(ctx, "photos_service.host")+":"+config.GetString(ctx, "photos_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "photos_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to photos service")
	}
	defer photosServiceConn.Close()

	// Whatsapp Notification service
	whatsappServiceConn, err := grpc.Dial(
		config.GetString(ctx, "whatsapp_service.host")+":"+config.GetString(ctx, "whatsapp_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "whatsapp_service.authority_header")),
	)

	if err != nil {
		log.WithError(err).Panic("could not connect to whatsapp service")
	}
	defer whatsappServiceConn.Close()

	// Karma Service
	karmaServiceConn, err := grpc.Dial(
		config.GetString(ctx, "karma_service.host")+":"+config.GetString(ctx, "karma_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "karma_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to karma service")
	}
	defer karmaServiceConn.Close()

	// Gamification service
	gamificationServiceConn, err := grpc.Dial(
		config.GetString(ctx, "gamification_service.host")+":"+config.GetString(ctx, "gamification_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "gamification_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to gamification service")
	}
	defer gamificationServiceConn.Close()

	// Train Fulfillment Service
	trainFulfillmentServiceConn, err := grpc.Dial(
		config.GetString(ctx, "train_fulfillment_service.host")+":"+config.GetString(ctx, "train_fulfillment_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "train_fulfillment_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to train fulfillment service")
	}
	defer trainFulfillmentServiceConn.Close()

	// Flywheel Ads Service
	flywheelAdsServiceConn, err := grpc.Dial(
		config.GetString(ctx, "flywheel_ads_service.host")+":"+config.GetString(ctx, "flywheel_ads_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "flywheel_ads_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to flywheel ads service")
	}
	defer flywheelAdsServiceConn.Close()

	// Brand Ads Target Service
	brandAdsTargetServiceConn, err := grpc.Dial(
		config.GetString(ctx, "brand_ads_target_service.host")+":"+config.GetString(ctx, "brand_ads_target_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "brand_ads_target_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to brand ads target service")
	}
	defer brandAdsTargetServiceConn.Close()

	// Jumbo insights service
	jumboInsightsServiceConn, err := grpc.Dial(
		config.GetString(ctx, "jumbo_insights_service.host")+":"+config.GetString(ctx, "jumbo_insights_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		// grpc.WithAuthority(config.GetString(ctx, "jumbo_insights_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to jumbo insights service")
	}
	defer jumboInsightsServiceConn.Close()

	// Initialise the ephemeralUploadsClient
	ephemeralUploadsClient, err := ephemeral_uploads.NewEphemeralUploadsClient(
		config.GetString(ctx, "ephemeral_uploads.s3.bucket"),
		config.GetString(ctx, "ephemeral_uploads.aws.region"),
	)
	if err != nil {
		log.WithError(err).Panic("could not initialise ephemeralUploadsClient")
	}
	cultAppDownloadsClient, err := cult_app_downloads.NewCultAppDownloadsClient(
		config.GetString(ctx, "cult_app.s3.bucket"),
		config.GetString(ctx, "cult_app.aws.region"),
	)
	if err != nil {
		log.WithError(err).Panic("could not initialise cultAppDownloadsClient")
	}

	// Reward Service
	rewardServiceConn, err := grpc.Dial(
		config.GetString(ctx, "reward_service.host")+":"+config.GetString(ctx, "reward_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "reward_service.authority")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to reward service")
	}
	defer rewardServiceConn.Close()

	// Graph Service
	graphServiceConn, err := grpc.Dial(
		config.GetString(ctx, "graph_service.host")+":"+config.GetString(ctx, "graph_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "graph_service.authority")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to graph service")
	}
	defer graphServiceConn.Close()

	// Phone Verification Service
	phoneVerificationServiceConn, err := grpc.Dial(
		config.GetString(ctx, "phone_verification_service.host")+":"+config.GetString(ctx, "phone_verification_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "phone_verification_service.authority")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to phone verification service")
	}
	defer phoneVerificationServiceConn.Close()

	// Pdf Generator V2 Service
	pdfGeneratorV2ServiceConn, err := grpc.Dial(
		config.GetString(ctx, "pdf_generator_v2_service.host")+":"+config.GetString(ctx, "pdf_generator_v2_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "pdf_generator_v2_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to PDF Generator V2 service")
	}
	defer pdfGeneratorV2ServiceConn.Close()

	ephemeralClient, err := s3uploads.NewClient(config.GetString(ctx, "ephemeral_uploads.s3.bucket"))
	if err != nil {
		log.WithError(err).Error("[upload to s3] could not initialise the upload to s3 client")
	}

	consumerOrderServiceClient, err := system.GetConsumerOrderServiceClient(ctx)
	if err != nil {
		log.WithError(err).Panic("could not connect to consumer order service")
	}

	cartServiceClient, err := system.GetCartServiceClient(ctx)
	if err != nil {
		log.WithError(err).Panic("could not connect to cart service")
	}

	s3UploadClient, err := uploads.NewS3Client(ctx, config.GetString(ctx, "s3.region"))
	if err != nil {
		log.WithError(err).Error("could not initialise the upload client")
	}

	cartPublishers, err := system.GetCartPublishers(ctx)
	if err != nil {
		log.WithError(err).Error("not able to initialise cart publishers")
	}

	merchantOutletServiceClient, err := system.GetMerchantOutletServiceClient(ctx)
	if err != nil {
		log.WithError(err).Panic("could not connect to merchant outlet service")
	}

	legendsServiceConn, err := system.GetLegendsServiceClient(ctx)
	if err != nil {
		log.WithError(err).Panic("could not connect to legends service")
	}

	// central billing service rpc
	centralBillingServiceRpcConn, err := system.GetCentralBillingServiceClient(ctx)
	if err != nil {
		log.WithError(err).Panic("could not connect to central billing service rpc")
	}
	defer centralBillingServiceRpcConn.Close()

	// live order service conn
	liveOrderServiceConn, err := system.GetLiveOrderServiceConn(ctx)
	if err != nil {
		log.WithError(err).Error("could not connect to live order service")
	}
	defer liveOrderServiceConn.Close()

	// template service
	templateServiceConn, err := grpc.Dial(
		config.GetString(ctx, "template_service.host")+":"+config.GetString(ctx, "template_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "template_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to template service")
	}
	defer templateServiceConn.Close()

	// merchant comms service conn
	merchantCommsServiceConn, err := system.GetMerchantCommsServiceConn(ctx)
	if err != nil {
		log.WithError(err).Error("could not connect to merchant comms service")
	}
	defer merchantCommsServiceConn.Close()

	// eternal form service conn
	eternalFormServiceConn, err := grpc.Dial(
		config.GetString(ctx, "eternal_form_service.host")+":"+config.GetString(ctx, "eternal_form_service.port"),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithAuthority(config.GetString(ctx, "eternal_form_service.authority_header")),
	)
	if err != nil {
		log.WithError(err).Panic("could not connect to eternal form service")
	}
	defer eternalFormServiceConn.Close()

	ev := env.NewEnv(
		env.WithO2PlacementFulfillmentConfluentClient(o2PlacementFulfillmentConfluentPubSubClient),
		env.WithOnlineConfluentClient(onlineConfluentPubsubClient),
		env.WithJumboClient(jc),
		env.WithJumboV2Client(jcV2),
		env.WithOfflineMSKPubsubClient(offlineMSKPubsubClient),
		env.WithDriverServiceConn(driverServiceConn),
		env.WithLeadManagementServiceConn(lmsServiceConn),
		env.WithMenuServiceConn(menuServiceConn),
		env.WithMenuMxServiceConn(menuMxServiceConn),
		env.WithLogisticsMerchantServiceConn(logisticsMerchantServiceConn),
		env.WithMenuAggregatorServiceConn(menuAggServiceConn),
		env.WithServiceabilityAggregatorServiceConn(serviceabilityAggServiceConn),
		env.WithKitchenServiceConn(kitchenServiceConn),
		env.WithServiceabilityManagerServiceConn(serviceabilityManagerServiceConn),
		env.WithDemandManagerServiceConn(demandManagerServiceConn),
		env.WithPromoServiceConn(promoServiceConn),
		env.WithPromoExtServiceConn(promoExtServiceConn),
		env.WithLocationServiceConn(locationServiceConn),
		env.WithNPSServiceConn(npsServiceConn),
		env.WithEditionServiceConn(editionServiceConn),
		env.WithLogisticsOrderServiceConn(logisticsOrderServiceConn),
		env.WithAuthServiceConn(authServiceConn),
		env.WithReviewsServiceConn(reviewsServiceConn),
		env.WithBulkOperationServiceConn(bulkOpsServiceConn),
		env.WithUserServiceConn(userServiceConn),
		env.WithUserPreferenceServiceConn(userPreferenceServiceConn),
		env.WithCorporateFundsServiceConn(CorporateFundsServiceConn),
		env.WithGiftCardsServiceConn(GiftCardsServiceConn),
		env.WithSMSServiceConn(smsServiceConn),
		env.WithSearchServiceConn(searchServiceConn),
		env.WithSearchWorkerServiceConn(searchWorkerServiceConn),
		env.WithBenefitsServiceConn(benefitsServiceConn),
		env.WithDoteServiceConn(doteServiceConn),
		env.WithStoriesServiceConn(storiesServiceConn),
		env.WithDiningServiceConn(diningServiceConn),
		env.WithAdsTargetServiceConn(adsTargetServiceConn),
		env.WithDishServiceConn(dishServiceConn),
		env.WithProfileStoreServiceConn(profilestoreServiceConn),
		env.WithBlinkitProfileStoreServiceConn(blinkitProfilestoreServiceConn),
		env.WithEmailServiceConn(emailServiceConn),
		env.WithButterflyServiceConn(butterflyServiceConn),
		env.WithFireflyServiceConn(fireflyServiceConn),
		env.WithModerationToolConn(moderationToolConn),
		env.WithAiInsightsServiceConn(aiInsightsServiceConn),
		env.WithSupportServiceConn(supportServiceConn),
		env.WithJWTSigner(config.GetString(ctx, "notification_preferences.unsubscribe_url.name"), signerUnsubscribeLink),
		env.WithJWTSigner(config.GetString(ctx, "notification_preferences.profile.name"), signerProfile),
		env.WithExpServiceConn(expServiceConn),
		env.WithHermesServiceConn(hermesServiceConn),
		env.WithAdsServiceConn(adsServiceConn),
		env.WithSubscriptionServiceConn(subscriptionServiceConn),
		env.WithLegendsServiceConn(legendsServiceConn),
		env.WithWalletServiceConn(walletServiceConn),
		env.WithMerchantServiceConn(merchantServiceConn),
		env.WithCreditLineServiceConn(creditLineServiceConn),
		env.WithVernacularServiceConn(vernacularServiceConn),
		env.WithCompositeOrderServiceConn(compositeOrderServiceConn),
		env.WithStoreServiceConn(storeServiceConn),
		env.WithTelecomServiceConn(telecomServiceConn),
		env.WithEphemeralUploadsClient(ephemeralUploadsClient),
		env.WithWhatsappServiceConn(whatsappServiceConn),
		env.WithKarmaServiceConn(karmaServiceConn),
		env.WithTimeServiceConn(timeServiceConn),
		env.WithOsrmAggregatorConn(osrmAggregatorConn),
		env.WithRewardServiceConn(rewardServiceConn),
		env.WithGraphServiceConn(graphServiceConn),
		env.WithPhoneVerificationServiceConn(phoneVerificationServiceConn),
		env.WithPhotosServiceConn(photosServiceConn),
		env.WithStoreServiceConn(storeServiceConn),
		env.WithGamificationServiceConn(gamificationServiceConn),
		env.WithTrainFulfillmentServiceConn(trainFulfillmentServiceConn),
		env.WithFlywheelAdsServiceConn(flywheelAdsServiceConn),
		env.WithBrandAdsTargetServiceConn(brandAdsTargetServiceConn),
		env.WithJumboInsightsServiceConn(jumboInsightsServiceConn),
		env.WithPaymentRouterServiceConn(paymentRouterConn),
		env.WithAerobarServiceConn(aerobarServiceConn),
		env.WithEphemeralClient(ephemeralClient),
		env.WithTrainJourneyClient(trainjourneyv1.NewTrainJourneyServiceClient(trainFulfillmentServiceConn)),
		env.WithCultAppDownloadsClient(cultAppDownloadsClient),
		env.WithConsumerOrderAdminService(orderv1.NewOrderAdminServiceClient(consumerOrderServiceClient), orderv1.NewOrderServiceClient(consumerOrderServiceClient)),
		env.WithConsumerOrderService(orderv1.NewOrderServiceClient(consumerOrderServiceClient)),
		env.WithCartService(cartv1.NewCartAPIServiceClient(cartServiceClient)),
		env.WithCartPublishers(cartPublishers),
		env.WithLiveOrderService(liveOrderServiceConn),
		env.WithMerchantOutletService(outlet.NewMerchantOutletServiceClient(merchantOutletServiceClient)),
		env.WithUserAddressClient(useraddress.NewUserAddressServiceClient(userServiceConn)),
		env.WithAuthServiceClient(auth.NewAuthServiceClient(authServiceConn)),
		env.WithWeatherServiceConn(weatherServiceConn),
		env.WithDelightService(delightv1.NewDelightServiceClient(gamificationServiceConn)),
		env.WithMerchantDiscountingService(merchantDiscountingServiceConn),
		env.WithGroupOrderingService(groupv1.NewGroupOrderingServiceClient(cartServiceClient)),
		env.WithMenuAggregatorServiceClient(menu_agg.NewConsumerClient(menuAggServiceConn)),
		env.WithCentralBillingServiceRpcConn(centralBillingServiceRpcConn),
		env.WithShareServiceConn(sharev1.NewShareServiceClient(reviewsServiceConn)),
		env.WithPdfGeneratorV2ServiceConn(pdfGeneratorV2ServiceConn),
		env.WithS3UploadClient(s3UploadClient),
		env.WithTemplateServiceConn(templateServiceConn),
		env.WithMerchantCommsServiceConn(merchantCommsServiceConn),
		env.WithExperimentationClient(experimentationClient),
		env.WithEternalFormServiceConn(eternalFormServiceConn),
	)

	webclient.SetZomatoAPIInternalDetails(
		config.GetString(ctx, "api_internal.protocol"),
		config.GetString(ctx, "api_internal.host"),
		config.GetString(ctx, "api_internal.api_key"),
		config.GetString(ctx, "api_internal.header_host"),
	)

	upload_service.SetZomatoUploadSvcDetails(
		config.GetString(ctx, "upload_service.protocol"),
		config.GetString(ctx, "upload_service.host"),
		config.GetString(ctx, "upload_service.endpoint"),
		config.GetDuration(ctx, "upload_service.timeout"),
		config.GetBool(ctx, "upload_service.enabled"),
	)

	paymentsClient.SetPaymentsApiInfo(
		config.GetString(ctx, "payments.host"),
		config.GetString(ctx, "payments.host_header"),
		config.GetString(ctx, "payments.auth_username"),
		config.GetString(ctx, "payments.auth_password"),
	)

	paymentsApiGwClient.SetPaymentsApiGwInfo(
		config.GetString(ctx, "payments_api_gw.host"),
		config.GetString(ctx, "payments_api_gw.host_header"),
	)

	gin.SetMode(gin.ReleaseMode)
	// setup router and start server
	r := router.Initialize(ctx, ev, nrApp)

	// Initialize api inventory in background
	go func() {
		// Handle any panics
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("panic in API Inventory: %v\nstack: %s", r, string(debug.Stack()))
			}
		}()

		ai, err := gogin.NewApiInventory("zomato_api_gateway")
		if err != nil {
			log.WithError(err).Error("could not initialize api inventory")
			return
		}
		err = ai.Init(r)
		if err != nil {
			log.WithError(err).Error("something went wrong")
			return
		}
	}()
	srv := &http.Server{
		Addr:         ":" + config.GetString(ctx, "listen.port"),
		Handler:      r,
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,
	}
	err = srv.ListenAndServe()
	if err != nil {
		log.WithError(err).Error("gin run error")
	}
}

func registerMetrics() {
	// register email-service metric counters
	emailMetrics.RegisterCounters()
	// register menu-service metric counters
	menuMetrics.RegisterCounters()
	// register subscription-service metric counters
	subscriptionMetrics.RegisterCounters()
	// register cart metrics counter
	cartMetrics.RegisterCounters()
	// register cart service metrics
	cartMetrics.InitializeServiceMetrics()
	// register locations mertics counter
	locationMetrics.RegisterCounters()
	// register stats interceptor metrics
	interceptor.InitInterceptorMetrics()
	// register crystal metric counters
	crystalMetrics.RegisterCounters()
	// reviews metric counters
	feedback.RegisterCounters()
	// register lookback metrics counters.
	lookbackmetrics.RegisterCounters()
	// register weather service request metrics
	weatherMetrics.RegisterCounters()
}

func initializeSaramaPanicHandler() {
	sarama.PanicHandler = func(r interface{}) {
		log.Errorf("[PANIC] %v\nstacktrace: %s", r, string(debug.Stack()))
	}
}

func initializeKafkaEnqueuer(ctx context.Context) {
	enqueue.InitialiseClient(ctx, enqueue.OnlineClusterType)
	enqueue.InitialiseClient(ctx, enqueue.OfflineClusterType)
}

// nolint
func InitializeJumbo(ctx context.Context) (*jumbo.JumboClient, error) {
	jumboClient, jumboErr := jumbo.NewClient(
		jumbo.WithClientID("zomato-api-gateway"),
		jumbo.WithKafkaBrokers(config.GetStringSlice(ctx, "jumbo.brokers")),
		jumbo.WithKafkaTopic(config.GetString(ctx, "jumbo.topic")),
		jumbo.WithRealtimeKafkaTopic(config.GetString(ctx, "jumbo.realtime_topic")),
	)
	if jumboErr != nil {
		return nil, jumboErr
	}
	return jumboClient, nil
}

func InitializeJumboV2(ctx context.Context) (*zjumbo_v2.JumboClient, error) {
	config := jumbo_v2.InitConfig(
		jumbo_v2.SetOtelSidecarAddress(config.GetString(ctx, "otlp_collector.host")+":"+config.GetString(ctx, "otlp_collector.port")),
		jumbo_v2.SetClientID(config.GetString(ctx, "jumbo_v2.client_id")),
	)

	jumboClient, jumboErr := jumbo_v2.Initialize(ctx, config)
	if jumboErr != nil {
		return nil, jumboErr
	}

	log.Infof("Jumbo V2 client initialized")

	return jumboClient, nil
}

func initializeRuntime(ctx context.Context) {
	memlimitEnabled := config.GetBool(ctx, "go_runtime.mem_limit.enabled")
	metricsEnabled := config.GetBool(ctx, "go_runtime.metrics.enabled")
	gogc := config.GetInt(ctx, "go_runtime.gogc")
	statsdPath := config.GetString(ctx, "metrics.statsd_addr")

	zruntime.Init(
		zruntime.WithGoMemLimitEnabled(memlimitEnabled),
		zruntime.WithGCMetricsCollectorEnabled(metricsEnabled, statsdPath),
		zruntime.WithGOGC(gogc), // this overrides the GOGC env variable
	)
}

func initializeExperimentationClient(ctx context.Context) (experimentation.ExperimentClient, error) {
	options := []experimentation.Option{
		experimentation.WithAllExperiments(),
		experimentation.WithOtelSidecarAddr(config.GetString(ctx, "otlp_collector.host") + ":" + config.GetString(ctx, "otlp_collector.port")),
		experimentation.WithExperimentationClientAddr(fmt.Sprintf("%s:%s", config.GetString(ctx, "experimentation_package.host"), config.GetString(ctx, "experimentation_package.port"))),
		experimentation.WithExperimentationClientAuthority(config.GetString(ctx, "experimentation_package.authority")),
	}

	return experimentation.LoadExperiments(ctx, options...)
}
