{"services": [{"ecsClusterName": "stagingcluster", "ecsServiceName": "preprod-jumbo-insights-service", "ssmParameterPathPrefix": "/zservices/jumbo-insights-service/preprod/", "containers": [{"isGrpc": true, "image": "<container-image>", "name": "preprod-jumbo-insights-service-container", "ssmParameterNamePrefix": "JUMBO_INSIGHTS_SERVICE_", "ssmParameters": {"LOCAL_GRPC_SERVICE_PORT": "/zservices/jumbo-insights-service/preprod/JUMBO_INSIGHTS_SERVICE_SERVICE_PORT", "SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}, "environmentVariables": {"APP_MODE": "grpc", "DD_VERSION": "<version>", "DD_TRACE_REPORT_HOSTNAME": "true", "CONFIG_SOURCE": "appconfig:preprod-jumbo-insights-service,preprod-global|dataplatform"}, "ssmParameterSubstitutionMethod": "REPLACE"}, {"authz-sidecar": true, "name": "preprod-jumbo-insights-service-authz-sidecar", "ssmParameterNamePrefix": "JUMBO_INSIGHTS_SERVICE_", "image": "ssm://auth/preprod/AUTHZ_SIDECAR_IMAGE", "ssmParameterTemplate": "ssm://auth/preprod/AUTHZ_SIDECAR_SSM_PARAMETERS"}]}, {"ecsClusterName": "stagworkercluster", "ecsServiceName": "preprod-jumbo-insights-service-trino-worker", "ssmParameterPathPrefix": "/zservices/jumbo-insights-service/preprod/", "containers": [{"image": "<container-image>", "name": "preprod-jumbo-insights-service-trino-worker-container", "ssmParameterNamePrefix": "JUMBO_INSIGHTS_SERVICE_", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}, "environmentVariables": {"APP_MODE": "trino_worker", "DD_VERSION": "<version>", "DD_TRACE_REPORT_HOSTNAME": "true", "CONFIG_SOURCE": "appconfig:preprod-jumbo-insights-service,preprod-global|dataplatform"}, "ssmParameterSubstitutionMethod": "REPLACE"}]}], "serviceName": "jumbo-insights-service", "appconfigEnvironment": "preprod", "disableConfigInjection": true, "otelCollectorNamespace": "", "enableGlobalDPAppconfig": true}