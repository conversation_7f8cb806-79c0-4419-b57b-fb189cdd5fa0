version: 0.2

env:
  variables:
    GITHUB_REPO_NAME: "jumbo-insights-service"
    GITHUB_API_BASE: "https://api.github.com/repos/Zomato/jumbo-insights-service"
    GO_CLIENT_REPO_URI: "github.com/Zomato/jumbo-insights-service-client-golang.git"
    PHP_CLIENT_REPO_URI: "github.com/Zomato/jumbo-insights-service-client-php.git"
    JAVA_CLIENT_REPO_URI: "github.com/Zomato/jumbo-insights-service-client-java.git"
    DOCKER_BUILDKIT: 1

phases:
  install:
    runtime-versions:
      docker: 18
    commands:
      - echo "Starting"
      - echo "machine github.com login $GITHUB_BOT_NAME password $GITHUB_BOT_PAC" > .netrc

  pre_build:
    commands:
      - # setting git config
      - git config --global user.name $GITHUB_BOT_NAME
      - git config --global user.email $GITHUB_BOT_EMAIL

      - IMAGE_TAG=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - "export RELEASE_TAG=$(curl -H 'Authorization: token '\"$GITHUB_BOT_PAC\"'' \"$GITHUB_API_BASE\"/releases/latest | jq -r  '.tag_name')"
      - "export ARTIFACT_VERSION=$(echo $RELEASE_TAG | sed 's/v//g')"

  build:
    commands:
      - $(aws ecr get-login --region $AWS_DEFAULT_REGION --no-include-email)
      - MANIFEST=$(aws ecr batch-get-image --repository-name $ECR_REPOSITORY_NAME_DEFAULT --image-ids imageTag=$IMAGE_TAG --query 'images[].imageManifest' --output text)
      - aws ecr put-image --repository-name $ECR_REPOSITORY_NAME_DEFAULT --image-tag $RELEASE_TAG --image-manifest "$MANIFEST"

  post_build:
    commands:
      ### pushing go client ############################################################
      - cd $CODEBUILD_SRC_DIR
      - GO_CLIENT_REPO_NAME=$(basename $GO_CLIENT_REPO_URI .git)

      - # cloning go client repository
      - git clone https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$GO_CLIENT_REPO_URI

      - # pushing latest changes
      - cd $GO_CLIENT_REPO_NAME && git tag -a $RELEASE_TAG $(git rev-parse :/$IMAGE_TAG) -m "[AUTO GENERATED TAG] $RELEASE_TAG" && git push --tags

      ### pushing php client ###########################################################
      - cd $CODEBUILD_SRC_DIR
      - PHP_CLIENT_REPO_NAME=$(basename $PHP_CLIENT_REPO_URI .git)

      - # cloning php client repository
      - git clone https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$PHP_CLIENT_REPO_URI

      - # pushing latest changes
      - cd $PHP_CLIENT_REPO_NAME && git tag -a $RELEASE_TAG $(git rev-parse :/$IMAGE_TAG) -m "[AUTO GENERATED TAG] $RELEASE_TAG" && git push --tags

      ### pushing java client ###########################################################
      - cd $CODEBUILD_SRC_DIR
      - JAVA_CLIENT_REPO_NAME=$(basename $JAVA_CLIENT_REPO_URI .git)

      - # cloning java client repository
      - git clone https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$JAVA_CLIENT_REPO_URI

      - # pushing latest changes
      - cd $JAVA_CLIENT_REPO_NAME && git tag -a $RELEASE_TAG $(git rev-parse :/$IMAGE_TAG) -m "[AUTO GENERATED TAG] $RELEASE_TAG" && git push --tags

      - # publish java package
      - chmod +x ./gradlew && ./gradlew clean build
      - ./gradlew publish
      - ./gradlew clean

      ### updating image definition ####################################################
      - cd $CODEBUILD_SRC_DIR
      - sed -i "s#<container-image>#$ECR_REPOSITORY_URI_DEFAULT:$RELEASE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-prod.json
      - sed -i "s#<version>#MAS:$IMAGE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-prod.json

artifacts:
  secondary-artifacts:
    ConfigArtifacts:
      discard-paths: yes
      files:
        - "configs/config.yaml"
    ImageDefinitionArtifacts:
      discard-paths: yes
      files:
        - "deployment/codebuild/image-definition-prod.json"