version: 0.2

env:
  variables:
    GITHUB_REPO_NAME: "jumbo-insights-service"
    GO_CLIENT_REPO_URI: "github.com/Zomato/jumbo-insights-service-client-golang.git"
    PHP_CLIENT_REPO_URI: "github.com/Zomato/jumbo-insights-service-client-php.git"
    JAVA_CLIENT_REPO_URI: "github.com/Zomato/jumbo-insights-service-client-java.git"
    DOCKER_BUILDKIT: 1

phases:
  install:
    runtime-versions:
      docker: 18
    commands:
      - echo "Starting"
      - echo "machine github.com login $GITHUB_BOT_NAME password $GITHUB_BOT_PAC" > .netrc

  pre_build:
    commands:
      - # setting git config
      - git config --global user.name $GITHUB_BOT_NAME
      - git config --global user.email $GITHUB_BOT_EMAIL

      - echo "Logging in to Amazon ECR..."
      - $(aws ecr get-login --registry-id 912841491139 --region ap-south-1 --no-include-email)
      - $(aws ecr get-login --registry-ids 154774607397 --region ap-southeast-1 --no-include-email)
      - $(aws ecr get-login --registry-ids 931301707529 --region ap-southeast-1 --no-include-email)
      - IMAGE_TAG=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)

  build:
    commands:
      - # run source code tests here
      - echo "building image"
      - make build
      - docker tag latest-dev ${ECR_REPOSITORY_URI_DEFAULT}:${IMAGE_TAG}
      - docker tag latest-dev ${ECR_REPOSITORY_URI_DEFAULT}:latest-dev
      - docker push ${ECR_REPOSITORY_URI_DEFAULT}:${IMAGE_TAG}
      - docker push ${ECR_REPOSITORY_URI_DEFAULT}:latest-dev

  post_build:
    commands:
      ### pushing go client ############################################################
      - cd $CODEBUILD_SRC_DIR
      - GO_CLIENT_REPO_NAME=$(basename $GO_CLIENT_REPO_URI .git)

      - # cloning go client repository
      - git clone --single-branch --branch dev https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$GO_CLIENT_REPO_URI

      - # removing everything from protos & adding newly generated files
      - mkdir -p $GO_CLIENT_REPO_NAME
      - find $GO_CLIENT_REPO_NAME/* -type d -maxdepth 0 -exec rm -rf {} \;
      - cp -r client/golang/$GITHUB_REPO_NAME/* $GO_CLIENT_REPO_NAME/

      - # pushing latest changes
      - cd $GO_CLIENT_REPO_NAME && git checkout -B dev && git add . && git commit --allow-empty -m "[AUTO GENERATED] updated client to https://github.com/Zomato/$GITHUB_REPO_NAME/commit/$IMAGE_TAG"
      - git push -f origin dev

      ### pushing php client ############################################################
      - cd $CODEBUILD_SRC_DIR
      - PHP_CLIENT_REPO_NAME=$(basename $PHP_CLIENT_REPO_URI .git)

      - # cloning php client repository
      - git clone --single-branch --branch dev https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$PHP_CLIENT_REPO_URI

      - # remove everything from src and adding newly generated files
      - mkdir -p $PHP_CLIENT_REPO_NAME/src
      - rm -rf $PHP_CLIENT_REPO_NAME/src/*
      - cp -r client/php/src/* $PHP_CLIENT_REPO_NAME/src/
      - cp client/php/composer.json $PHP_CLIENT_REPO_NAME/composer.json

      - # pushing latest changes
      - cd $PHP_CLIENT_REPO_NAME && git checkout -B dev && git add . && git commit --allow-empty -m "[AUTO GENERATED] updated client to https://github.com/Zomato/$GITHUB_REPO_NAME/commit/$IMAGE_TAG"
      - git push -f origin dev
    
      ### pushing java client ############################################################
      - cd $CODEBUILD_SRC_DIR
      - JAVA_CLIENT_REPO_NAME=$(basename $JAVA_CLIENT_REPO_URI .git)

      - # cloning php client repository
      - git clone --single-branch --branch dev https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$JAVA_CLIENT_REPO_URI

      - # remove everything from src and adding newly generated files
      - mkdir -p $JAVA_CLIENT_REPO_NAME/src/main/java && rm -rf $JAVA_CLIENT_REPO_NAME/src/main/java/* && cp -r client/java/src/* $JAVA_CLIENT_REPO_NAME/src/main/java/ && cp -r client/java/gradle $JAVA_CLIENT_REPO_NAME/ && cp client/java/gradlew $JAVA_CLIENT_REPO_NAME/gradlew && cp client/java/build.gradle $JAVA_CLIENT_REPO_NAME/build.gradle && cp client/java/settings.gradle $JAVA_CLIENT_REPO_NAME/settings.gradle
      
      - # pushing latest changes
      - cd $JAVA_CLIENT_REPO_NAME && git checkout -B dev && git add . && git commit --allow-empty -m "[AUTO GENERATED] updated client to https://github.com/Zomato/$GITHUB_REPO_NAME/commit/$IMAGE_TAG"
      - git push -f origin dev
      
      - # [Java Only] Run gradle build to test successful artifact builds
      - chmod +x ./gradlew && ./gradlew clean build
      - ./gradlew clean

      ### updating image definition #######################################################
      - cd $CODEBUILD_SRC_DIR
      - printf '[{"name":"jumbo-insights-service-container","imageUri":"%s"}]' $ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG > $CODEBUILD_SRC_DIR/imagedefinitions.json
      - sed -i "s#<container-image>#$ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-dev.json
      - sed -i "s#<version>#MAS:$IMAGE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-dev.json


artifacts:
  base-directory: $CODEBUILD_SRC_DIR
  files:
    - imagedefinitions.json
  secondary-artifacts:
    ConfigArtifacts:
      discard-paths: yes
      files:
        - "configs/config.yaml"
    ImageDefinitionArtifacts:
      discard-paths: yes
      files:
        - "deployment/codebuild/image-definition-dev.json"
