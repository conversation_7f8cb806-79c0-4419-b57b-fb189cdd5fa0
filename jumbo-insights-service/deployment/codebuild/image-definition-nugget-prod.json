{"services": [{"ecsClusterName": "prod-cluster", "ecsServiceName": "prod-jumbo-insights-service", "ssmParameterPathPrefix": "/zservices/jumbo-insights-service/prod/", "containers": [{"isGrpc": true, "image": "<container-image>", "name": "main", "ssmParameterNamePrefix": "JUMBO_INSIGHTS_SERVICE_", "ssmParameters": {"LOCAL_GRPC_SERVICE_PORT": "/zservices/jumbo-insights-service/prod/JUMBO_INSIGHTS_SERVICE_SERVICE_PORT", "SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}, "environmentVariables": {"APP_MODE": "grpc", "DD_VERSION": "<version>", "DD_TRACE_REPORT_HOSTNAME": "true", "CONFIG_SOURCE": "appconfig:prod-jumbo-insights-service,prod-global|dataplatform"}, "ssmParameterSubstitutionMethod": "REPLACE"}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-jumbo-insights-service-trino-worker", "ssmParameterPathPrefix": "/zservices/jumbo-insights-service/prod/", "containers": [{"image": "<container-image>", "name": "main", "ssmParameterNamePrefix": "JUMBO_INSIGHTS_SERVICE_", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}, "environmentVariables": {"APP_MODE": "trino_worker", "DD_VERSION": "<version>", "DD_TRACE_REPORT_HOSTNAME": "true", "CONFIG_SOURCE": "appconfig:prod-jumbo-insights-service,prod-global|dataplatform"}, "ssmParameterSubstitutionMethod": "REPLACE"}]}], "serviceName": "jumbo-insights-service", "appconfigEnvironment": "prod", "disableConfigInjection": true, "otelCollectorNamespace": "", "enableGlobalDPAppconfig": true}