{"appconfigEnvironment": "dev", "disableConfigInjection": true, "serviceName": "jumbo-insights-service", "enableGlobalDPAppconfig": true, "otelCollectorNamespace": "", "services": [{"containers": [{"environmentVariables": {"APP_MODE": "grpc", "DD_VERSION": "<version>", "DD_TRACE_REPORT_HOSTNAME": "true", "CONFIG_SOURCE": "appconfig:dev-jumbo-insights-service,dev-global|dataplatform"}, "image": "<container-image>", "isGrpc": true, "name": "main", "ssmParameterNamePrefix": "JUMBO_INSIGHTS_SERVICE_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"LOCAL_GRPC_SERVICE_PORT": "/zservices/jumbo-insights-service/dev/JUMBO_INSIGHTS_SERVICE_SERVER_PORT"}}, {"authz-sidecar": true, "name": "dev-jumbo-insights-service-authz-sidecar", "ssmParameterNamePrefix": "JUMBO_INSIGHTS_SERVICE_", "image": "ssm://auth/dev/AUTHZ_SIDECAR_IMAGE", "ssmParameterTemplate": "ssm://auth/dev/AUTHZ_SIDECAR_SSM_PARAMETERS"}], "ecsClusterName": "dev-cluster", "ecsServiceName": "dev-jumbo-insights-service", "ssmParameterPathPrefix": "/zservices/jumbo-insights-service/dev/"}, {"containers": [{"environmentVariables": {"APP_MODE": "trino_worker", "DD_VERSION": "<version>", "DD_TRACE_REPORT_HOSTNAME": "true", "CONFIG_SOURCE": "appconfig:dev-jumbo-insights-service,dev-global|dataplatform"}, "image": "<container-image>", "name": "main", "ssmParameterNamePrefix": "JUMBO_INSIGHTS_SERVICE_"}], "ecsClusterName": "dev-worker-cluster", "ecsServiceName": "dev-dev-jumbo-insights-service-trino-worker", "ssmParameterPathPrefix": "/zservices/jumbo-insights-service/dev/"}]}