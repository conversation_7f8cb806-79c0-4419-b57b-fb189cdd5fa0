version: 0.2

env:
  variables:
    GITHUB_REPO_NAME: "jumbo-insights-service"
    GO_CLIENT_REPO_URI: "github.com/Zomato/jumbo-insights-service-client-golang.git"
    PHP_CLIENT_REPO_URI: "github.com/Zomato/jumbo-insights-service-client-php.git"
    JAVA_CLIENT_REPO_URI: "github.com/Zomato/jumbo-insights-service-client-java.git"
    DOCKER_BUILDKIT: 1

phases:
  install:
    runtime-versions:
      docker: 18
    commands:
      - echo "Starting"
      - echo "machine github.com login $GITHUB_BOT_NAME password $GITHUB_BOT_PAC" > .netrc

  pre_build:
    commands:
      - # setting git config
      - git config --global user.name $GITHUB_BOT_NAME
      - git config --global user.email $GITHUB_BOT_EMAIL

      - IMAGE_TAG=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)

  build:
    commands:
      - $(aws ecr get-login --registry-id 912841491139 --region $DEV_AWS_DEFAULT_REGION --no-include-email)
      - docker pull $DEV_ECR_REPOSITORY_URI_DEFAULT:latest-dev
      - docker tag $DEV_ECR_REPOSITORY_URI_DEFAULT:latest-dev $ECR_REPOSITORY_URI_DEFAULT:latest-preprod
      - docker tag $DEV_ECR_REPOSITORY_URI_DEFAULT:latest-dev $ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG

      - $(aws ecr get-login --region $AWS_DEFAULT_REGION --no-include-email)
      - docker push $ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG

  post_build:
    commands:
      ### pushing go client ############################################################
      - cd $CODEBUILD_SRC_DIR
      - GO_CLIENT_REPO_NAME=$(basename $GO_CLIENT_REPO_URI .git)

      - # cloning go client repository
      - git clone https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$GO_CLIENT_REPO_URI

      - # pushing latest changes
      - cd $GO_CLIENT_REPO_NAME && git checkout master && git reset --hard origin/dev && git commit --allow-empty -m "[AUTO GENERATED] updated client to https://github.com/Zomato/$GITHUB_REPO_NAME/commit/$IMAGE_TAG"
      - git push -f origin master

      ### pushing php client ###########################################################
      - cd $CODEBUILD_SRC_DIR
      - PHP_CLIENT_REPO_NAME=$(basename $PHP_CLIENT_REPO_URI .git)

      - # cloning php client repository
      - git clone https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$PHP_CLIENT_REPO_URI

      - # pushing latest changes
      - cd $PHP_CLIENT_REPO_NAME && git checkout master && git reset --hard origin/dev && git commit --allow-empty -m "[AUTO GENERATED] updated client to https://github.com/Zomato/$GITHUB_REPO_NAME/commit/$IMAGE_TAG"
      - git push -f origin master

      ### pushing php client ###########################################################
      - cd $CODEBUILD_SRC_DIR
      - JAVA_CLIENT_REPO_NAME=$(basename $JAVA_CLIENT_REPO_URI .git)

      - # cloning php client repository
      - git clone https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$JAVA_CLIENT_REPO_URI

      - # pushing latest changes
      - cd $JAVA_CLIENT_REPO_NAME && git checkout master && git reset --hard origin/dev && git commit --allow-empty -m "[AUTO GENERATED] updated client to https://github.com/Zomato/$GITHUB_REPO_NAME/commit/$IMAGE_TAG"
      - git push -f origin master

      ### updating image definition ####################################################
      - cd $CODEBUILD_SRC_DIR
      - sed -i "s#<container-image>#$ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-preprod.json
      - sed -i "s#<version>#MAS:$IMAGE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-preprod.json

artifacts:
  secondary-artifacts:
    ConfigArtifacts:
      discard-paths: yes
      files:
        - "configs/config.yaml"
    ImageDefinitionArtifacts:
      discard-paths: yes
      files:
        - "deployment/codebuild/image-definition-preprod.json"