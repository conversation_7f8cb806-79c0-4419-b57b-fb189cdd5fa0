version: 0.2

env:
  variables:
    AWS_DEFAULT_ACCOUNT_ID: "************"
    AWS_DEFAULT_REGION: ap-south-1
    GITHUB_REPO_NAME: "jumbo-insights-service"
    GO_CLIENT_REPO_URI: "github.com/Zomato/jumbo-insights-service-client-golang.git"
    PHP_CLIENT_REPO_URI: "github.com/Zomato/jumbo-insights-service-client-php.git"
    JAVA_CLIENT_REPO_URI: "github.com/Zomato/jumbo-insights-service-client-java.git"
    ECR_REPOSITORY_URI_DEFAULT: ************.dkr.ecr.ap-south-1.amazonaws.com/services/jumbo-insights-service
    DOCKER_BUILDKIT: 1

phases:
  install:
    commands:
      - echo "Starting"
      - echo "machine github.com login $GITHUB_BOT_NAME password $GITHUB_BOT_PAC" > .netrc

  pre_build:
    commands:
      - # setting git config
      - git config --global user.name $GITHUB_BOT_NAME
      - git config --global user.email $GITHUB_BOT_EMAIL

      - IMAGE_TAG=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)

  build:
    commands:
      - $(aws ecr get-login --registry-id ************ --region $AWS_DEFAULT_REGION --no-include-email)
      - make build
      - docker tag latest-dev $ECR_REPOSITORY_URI_DEFAULT:latest-preprod
      - docker tag latest-dev $ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG
      - docker push $ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG

  post_build:
    commands:

      ### updating image definition ####################################################
      - cd $CODEBUILD_SRC_DIR
      - sed -i "s#<container-image>#$ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-nugget-preprod.json
      - sed -i "s#<version>#MAS:$IMAGE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-nugget-preprod.json

artifacts:
  secondary-artifacts:
    ConfigArtifacts:
      discard-paths: yes
      files:
        - "configs/config.yaml"
    ImageDefinitionArtifacts:
      discard-paths: yes
      files:
        - "deployment/codebuild/image-definition-preprod.json"