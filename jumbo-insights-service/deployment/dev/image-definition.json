{"account": "************", "appconfigName": "dev-jumbo-insights-service", "otelCollectorNamespace": "", "deploymentGroups": [{"deployStage": {"stage": "dev"}, "services": [{"containers": [{"environmentVariables": {"APP_MODE": "grpc", "DD_TRACE_REPORT_HOSTNAME": "true", "DD_VERSION": "<version>"}, "image": "<container-image>", "isGrpc": true, "name": "main", "ssmParameterNamePrefix": "JUMBO_INSIGHTS_SERVICE_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"LOCAL_GRPC_SERVICE_PORT": "/zservices/jumbo-insights-service/dev/JUMBO_INSIGHTS_SERVICE_SERVER_PORT"}}, {"authzSidecar": true, "name": "dev-jumbo-insights-service-authz-sidecar", "image": "ssm://auth/dev/AUTHZ_SIDECAR_IMAGE_MULTIARCH", "ssmParameterNamePrefix": "JUMBO_INSIGHTS_SERVICE_", "ssmParameterTemplate": "ssm://auth/dev/AUTHZ_SIDECAR_SSM_PARAMETERS"}], "ecsClusterName": "dev-cluster", "ecsServiceName": "dev-jumbo-insights-service", "ssmParameterPathPrefix": "/zservices/jumbo-insights-service/dev/"}, {"containers": [{"environmentVariables": {"APP_MODE": "trino_worker", "DD_TRACE_REPORT_HOSTNAME": "true", "DD_VERSION": "<version>"}, "image": "<container-image>", "name": "main", "ssmParameterNamePrefix": "JUMBO_INSIGHTS_SERVICE_"}], "ecsClusterName": "dev-worker-cluster", "ecsServiceName": "dev-dev-jumbo-insights-service-trino-worker", "ssmParameterPathPrefix": "/zservices/jumbo-insights-service/dev/"}]}], "region": "ap-south-1"}