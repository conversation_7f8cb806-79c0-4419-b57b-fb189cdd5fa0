env:
  parameter-store:
    GITHUB_BOT_EMAIL: /sre/github/codebuild/GITHUB_BOT_EMAIL
    GITHUB_BOT_NAME: /sre/github/codebuild/GITHUB_BOT_NAME
    GITHUB_BOT_PAC: /sre/github/codebuild/GITHUB_BOT_PAC
  variables:
    AWS_DEFAULT_ACCOUNT_ID: "************"
    AWS_DEFAULT_REGION: ap-south-1
    DOCKER_BUILDKIT: "1"
    ECR_REPOSITORY_NAME_DEFAULT: services/jumbo-insights-service
    ECR_REPOSITORY_URI_DEFAULT: ************.dkr.ecr.ap-south-1.amazonaws.com/services/jumbo-insights-service
    GITHUB_REPO_NAME: jumbo-insights-service
    GO_CLIENT_REPO_URI: github.com/Zomato/jumbo-insights-service-client-golang.git
    JAVA_CLIENT_REPO_URI: github.com/Zomato/jumbo-insights-service-client-java.git
    PHP_CLIENT_REPO_URI: github.com/Zomato/jumbo-insights-service-client-php.git
commands:
- ""
- git config --global user.name $GITHUB_BOT_NAME
- git config --global user.email $GITHUB_BOT_EMAIL
- apt-get update && apt-get install -y openjdk-8-jdk
- echo "machine github.com login $GITHUB_BOT_NAME password $GITHUB_BOT_PAC" > .netrc
- echo "Logging in to Amazon ECR..."
- aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_DEFAULT_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
- aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin ************.dkr.ecr.ap-southeast-1.amazonaws.com
- aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin ************.dkr.ecr.ap-southeast-1.amazonaws.com
- export IMAGE_TAG=$(echo $COMMIT_TAG | cut -c 1-7)
- ""
- echo "building image"
- make build
- docker tag latest-dev ${ECR_REPOSITORY_URI_DEFAULT}:${IMAGE_TAG}
- docker tag latest-dev ${ECR_REPOSITORY_URI_DEFAULT}:latest-dev
- docker push ${ECR_REPOSITORY_URI_DEFAULT}:${IMAGE_TAG}
- docker push ${ECR_REPOSITORY_URI_DEFAULT}:latest-dev
- cd $GITHUB_WORKSPACE
- GO_CLIENT_REPO_NAME=$(basename $GO_CLIENT_REPO_URI .git)
- ""
- git clone --single-branch --branch dev https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$GO_CLIENT_REPO_URI
- ""
- mkdir -p $GO_CLIENT_REPO_NAME
- find $GO_CLIENT_REPO_NAME/* -type d -maxdepth 0 -exec rm -rf {} \;
- cp -r client/golang/$GITHUB_REPO_NAME/* $GO_CLIENT_REPO_NAME/
- ""
- cd $GO_CLIENT_REPO_NAME && git checkout -B dev && git add . && git commit --allow-empty
  -m "[AUTO GENERATED] updated client to https://github.com/Zomato/$GITHUB_REPO_NAME/commit/$IMAGE_TAG"
- git push -f origin dev
- cd $GITHUB_WORKSPACE
- PHP_CLIENT_REPO_NAME=$(basename $PHP_CLIENT_REPO_URI .git)
- ""
- git clone --single-branch --branch dev https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$PHP_CLIENT_REPO_URI
- ""
- mkdir -p $PHP_CLIENT_REPO_NAME/src
- rm -rf $PHP_CLIENT_REPO_NAME/src/*
- cp -r client/php/src/* $PHP_CLIENT_REPO_NAME/src/
- cp client/php/composer.json $PHP_CLIENT_REPO_NAME/composer.json
- ""
- cd $PHP_CLIENT_REPO_NAME && git checkout -B dev && git add . && git commit --allow-empty
  -m "[AUTO GENERATED] updated client to https://github.com/Zomato/$GITHUB_REPO_NAME/commit/$IMAGE_TAG"
- git push -f origin dev
- cd $GITHUB_WORKSPACE
- JAVA_CLIENT_REPO_NAME=$(basename $JAVA_CLIENT_REPO_URI .git)
- ""
- git clone --single-branch --branch dev https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$JAVA_CLIENT_REPO_URI
- ""
- mkdir -p $JAVA_CLIENT_REPO_NAME/src/main/java && rm -rf $JAVA_CLIENT_REPO_NAME/src/main/java/*
  && cp -r client/java/src/* $JAVA_CLIENT_REPO_NAME/src/main/java/ && cp -r client/java/gradle
  $JAVA_CLIENT_REPO_NAME/ && cp client/java/gradlew $JAVA_CLIENT_REPO_NAME/gradlew
  && cp client/java/build.gradle $JAVA_CLIENT_REPO_NAME/build.gradle && cp client/java/settings.gradle
  $JAVA_CLIENT_REPO_NAME/settings.gradle
- ""
- cd $JAVA_CLIENT_REPO_NAME && git checkout -B dev && git add . && git commit --allow-empty
  -m "[AUTO GENERATED] updated client to https://github.com/Zomato/$GITHUB_REPO_NAME/commit/$IMAGE_TAG"
- git push -f origin dev
- ""
- chmod +x ./gradlew && ./gradlew clean build
- ./gradlew clean
- cd $GITHUB_WORKSPACE
- printf '[{"name":"jumbo-insights-service-container","imageUri":"%s"}]' $ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG
  > $GITHUB_WORKSPACE/imagedefinitions.json
- sed -i "s#<container-image>#$ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG#" $GITHUB_WORKSPACE/deployment/dev/image-definition.json
- sed -i "s#<version>#MAS:$IMAGE_TAG#" $GITHUB_WORKSPACE/deployment/dev/image-definition.json
artifacts:
  ConfigArtifacts:
    files:
    - configs/config.yaml
    - configs/feature.yaml
  ImageDefinitionArtifact:
    file: deployment/dev/image-definition.json
