{"account": "************", "appconfigName": "preprod-jumbo-insights-service", "deploymentGroups": [{"deployStage": {"stage": "preprod"}, "services": [{"containers": [{"environmentVariables": {"APP_MODE": "grpc", "DD_TRACE_REPORT_HOSTNAME": "true", "DD_VERSION": "<version>"}, "image": "<container-image>", "isGrpc": true, "name": "preprod-jumbo-insights-service-container", "ssmParameterNamePrefix": "JUMBO_INSIGHTS_SERVICE_", "ssmParameterSubstitutionMethod": "REPLACE", "ssmParameters": {"LOCAL_GRPC_SERVICE_PORT": "/zservices/jumbo-insights-service/preprod/JUMBO_INSIGHTS_SERVICE_SERVICE_PORT", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC"}}, {"authzSidecar": true, "image": "ssm://auth/preprod/AUTHZ_SIDECAR_IMAGE", "name": "preprod-jumbo-insights-service-authz-sidecar", "ssmParameterNamePrefix": "JUMBO_INSIGHTS_SERVICE_", "ssmParameterTemplate": "ssm://auth/preprod/AUTHZ_SIDECAR_SSM_PARAMETERS"}], "ecsClusterName": "stagingcluster", "ecsServiceName": "preprod-jumbo-insights-service", "ssmParameterPathPrefix": "/zservices/jumbo-insights-service/preprod/"}, {"containers": [{"environmentVariables": {"APP_MODE": "trino_worker", "DD_TRACE_REPORT_HOSTNAME": "true", "DD_VERSION": "<version>"}, "image": "<container-image>", "name": "preprod-jumbo-insights-service-trino-worker-container", "ssmParameterNamePrefix": "JUMBO_INSIGHTS_SERVICE_", "ssmParameterSubstitutionMethod": "REPLACE", "ssmParameters": {"SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC"}}], "ecsClusterName": "stagworkercluster", "ecsServiceName": "preprod-jumbo-insights-service-trino-worker", "ssmParameterPathPrefix": "/zservices/jumbo-insights-service/preprod/"}]}], "otelCollectorNamespace": "", "region": "ap-southeast-1"}