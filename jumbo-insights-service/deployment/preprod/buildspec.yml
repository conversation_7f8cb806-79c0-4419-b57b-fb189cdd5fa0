env:
  parameter-store:
    GITHUB_BOT_EMAIL: /sre/github/codebuild/GITHUB_BOT_EMAIL
    GITHUB_BOT_NAME: /sre/github/codebuild/GITHUB_BOT_NAME
    GITHUB_BOT_PAC: /sre/github/codebuild/GITHUB_BOT_PAC
  variables:
    AWS_DEFAULT_ACCOUNT_ID: "************"
    AWS_DEFAULT_REGION: ap-southeast-1
    DEV_AWS_DEFAULT_REGION: ap-south-1
    DEV_ECR_REPOSITORY_URI_DEFAULT: ************.dkr.ecr.ap-south-1.amazonaws.com/services/jumbo-insights-service
    DOCKER_BUILDKIT: "1"
    ECR_REPOSITORY_NAME_DEFAULT: services/jumbo-insights-service
    ECR_REPOSITORY_URI_DEFAULT: ************.dkr.ecr.ap-southeast-1.amazonaws.com/services/jumbo-insights-service
    GITHUB_REPO_NAME: jumbo-insights-service
    GO_CLIENT_REPO_URI: github.com/Zomato/jumbo-insights-service-client-golang.git
    JAVA_CLIENT_REPO_URI: github.com/Zomato/jumbo-insights-service-client-java.git
    PHP_CLIENT_REPO_URI: github.com/Zomato/jumbo-insights-service-client-php.git
commands:
  - echo "Starting"
  - echo "machine github.com login $GITHUB_BOT_NAME password $GITHUB_BOT_PAC" > .netrc
    

  # setting git config
  - git config --global user.name $GITHUB_BOT_NAME
  - git config --global user.email $GITHUB_BOT_EMAIL
    
  - export IMAGE_TAG=$(echo $COMMIT_TAG | cut -c 1-7)
    
  - aws ecr get-login-password --region $DEV_AWS_DEFAULT_REGION | docker login --username AWS --password-stdin ************.dkr.ecr.$DEV_AWS_DEFAULT_REGION.amazonaws.com
  - docker pull $DEV_ECR_REPOSITORY_URI_DEFAULT:latest-dev
  - docker tag $DEV_ECR_REPOSITORY_URI_DEFAULT:latest-dev $ECR_REPOSITORY_URI_DEFAULT:latest-preprod
  - docker tag $DEV_ECR_REPOSITORY_URI_DEFAULT:latest-dev $ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG
    
  - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_DEFAULT_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
  - docker push $ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG
    
  ### pushing go client ############################################################
  - cd $GITHUB_WORKSPACE
  - GO_CLIENT_REPO_NAME=$(basename $GO_CLIENT_REPO_URI .git)
    

  # cloning go client repository
  - git clone https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$GO_CLIENT_REPO_URI
    

  # pushing latest changes
  - cd $GO_CLIENT_REPO_NAME && git checkout master && git reset --hard origin/dev && git commit --allow-empty -m "[AUTO GENERATED] updated client to https://github.com/Zomato/$GITHUB_REPO_NAME/commit/$IMAGE_TAG"
  - git push -f origin master
    
  ### pushing php client ###########################################################
  - cd $GITHUB_WORKSPACE
  - PHP_CLIENT_REPO_NAME=$(basename $PHP_CLIENT_REPO_URI .git)
    

  # cloning php client repository
  - git clone https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$PHP_CLIENT_REPO_URI
    

  # pushing latest changes
  - cd $PHP_CLIENT_REPO_NAME && git checkout master && git reset --hard origin/dev && git commit --allow-empty -m "[AUTO GENERATED] updated client to https://github.com/Zomato/$GITHUB_REPO_NAME/commit/$IMAGE_TAG"
  - git push -f origin master
    
  ### pushing php client ###########################################################
  - cd $GITHUB_WORKSPACE
  - JAVA_CLIENT_REPO_NAME=$(basename $JAVA_CLIENT_REPO_URI .git)
    

  # cloning php client repository
  - git clone https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$JAVA_CLIENT_REPO_URI
    

  # pushing latest changes
  - cd $JAVA_CLIENT_REPO_NAME && git checkout master && git reset --hard origin/dev && git commit --allow-empty -m "[AUTO GENERATED] updated client to https://github.com/Zomato/$GITHUB_REPO_NAME/commit/$IMAGE_TAG"
  - git push -f origin master
    
  ### updating image definition ####################################################
  - cd $GITHUB_WORKSPACE
  - sed -i "s#<container-image>#$ECR_REPOSITORY_URI_DEFAULT:$IMAGE_TAG#" $GITHUB_WORKSPACE/deployment/preprod/image-definition.json
  - sed -i "s#<version>#MAS:$IMAGE_TAG#" $GITHUB_WORKSPACE/deployment/preprod/image-definition.json
    
artifacts:
  ConfigArtifacts:
    files:
    - configs/config.yaml
  ImageDefinitionArtifact:
    file: deployment/preprod/image-definition.json
