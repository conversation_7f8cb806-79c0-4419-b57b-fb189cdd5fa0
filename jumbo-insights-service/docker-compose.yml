version: '3'

services:
  jumbo-insights-service:
    build:
      context: .
    ports:
      - 7001:7001
    container_name: jumbo-insights-service
    restart: unless-stopped
    environment:
      - CONFIG_SOURCE=local
      - APP_MODE=grpc
      - AWS_DEFAULT_REGION=ap-south-1
      - AWS_ACCESS_KEY_ID=local
      - AWS_SECRET_ACCESS_KEY=local
      - AWS_EC2_METADATA_DISABLED=true
    # depends_on:
    #   - pinot-broker

  jumbo-insights-service-trino-async-worker:
    build:
      context: .
    container_name: jumbo-insights-service-trino-async-worker
    restart: unless-stopped
    environment:
      - APP_MODE=trino_worker
      - AWS_DEFAULT_REGION=ap-south-1
      - AWS_ACCESS_KEY_ID=local
      - AWS_SECRET_ACCESS_KEY=local
      - CONFIG_SOURCE=local
      - AWS_EC2_METADATA_DISABLED=true
    depends_on:
      jumbo-insights-service-localstack:
        condition: service_healthy

  jumbo-insights-service-redis:
    image: redis
    command: ["redis-server", "--bind", "redis", "--port", "6379"]
    hostname: redis
    container_name: jumbo-insights-service-redis
    ports:
      - 6379

  # zookeeper:
  #   image: zookeeper:latest
  #   hostname: zookeeper
  #   ports:
  #     - "2181:2181"
  #   environment:
  #     ZOO_MY_ID: 1
  #     ZOO_PORT: 2181
  #     ZOO_SERVERS: server.1=zookeeper:2888:3888;2181

  # kafka:
  #   image: confluentinc/cp-kafka:5.3.0
  #   hostname: kafka
  #   ports:
  #     - "9092:9092"
  #   environment:
  #     KAFKA_ADVERTISED_LISTENERS: LISTENER_DOCKER_INTERNAL://kafka:19092,LISTENER_DOCKER_EXTERNAL://kafka:9092
  #     KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: LISTENER_DOCKER_INTERNAL:PLAINTEXT,LISTENER_DOCKER_EXTERNAL:PLAINTEXT
  #     KAFKA_INTER_BROKER_LISTENER_NAME: LISTENER_DOCKER_INTERNAL
  #     KAFKA_ZOOKEEPER_CONNECT: "zookeeper:2181/kafka"
  #     KAFKA_BROKER_ID: 1
  #     KAFKA_LOG4J_LOGGERS: "kafka.controller=INFO,kafka.producer.async.DefaultEventHandler=INFO,state.change.logger=INFO"
  #     KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
  #   depends_on:
  #     - zookeeper

  # pinot-controller:
  #   image: apachepinot/pinot:0.10.0
  #   hostname: pinot-controller
  #   restart: unless-stopped
  #   ports:
  #     - "9010:9000"
  #   command: StartController -zkAddress zookeeper:2181
  #   depends_on:
  #     - kafka
  #     - zookeeper

  # pinot-broker:
  #   image: apachepinot/pinot:0.10.0
  #   hostname: pinot-broker
  #   restart: unless-stopped
  #   ports:
  #     - "9001:8099"
  #   command: StartBroker -zkAddress zookeeper:2181
  #   depends_on:
  #     - zookeeper
  #     - kafka
  #     - pinot-controller

  # pinot-server:
  #   image: apachepinot/pinot:0.10.0
  #   hostname: pinot-server
  #   restart: unless-stopped
  #   ports:
  #     - "8098:8098"
  #   command: StartServer -zkAddress zookeeper:2181
  #   depends_on:
  #     - zookeeper
  #     - kafka
  #     - pinot-controller
  
  # statsd-exporter:
  #   hostname: statsd-exporter
  #   container_name: statsd-exporter
  #   image: prom/statsd-exporter:v0.16.0
  #   volumes:
  #     - ./configs/prometheus/statsd-mapping.yml:/etc/prometheus/statsd-mapping.yml
  #   command:
  #     - --statsd.mapping-config=/etc/prometheus/statsd-mapping.yml
  #   ports:
  #     - 9102:9102
  #     - 9125:9125/udp
  #   restart: unless-stopped

  # prometheus:
  #   hostname: prometheus
  #   image: prom/prometheus:v2.26.0
  #   container_name: prometheus
  #   volumes:
  #     - ./configs/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
  #     - prometheus_data:/prometheus
  #   command:
  #     - "--config.file=/etc/prometheus/prometheus.yml"
  #     - "--storage.tsdb.path=/prometheus"
  #     - "--web.console.libraries=/etc/prometheus/console_libraries"
  #     - "--web.console.templates=/etc/prometheus/consoles"
  #     - "--storage.tsdb.retention.time=168h"
  #     - "--web.enable-lifecycle"
  #     - "--web.enable-admin-api"
  #   ports:
  #     - 9090
  #   restart: unless-stopped

  jumbo-insights-service-localstack:
    image: localstack/localstack:3.8.1
    hostname: localstack
    container_name: jumbo-insights-service-localstack
    ports:
      - 4566:4566
      - 8055:8055
    environment:
      - SERVICES=s3,sqs,ssm
      - HOSTNAME=localstack
      - DEFAULT_REGION=ap-south-1
      - DOCKER_HOST=unix:///run/podman/podman.sock
      - DEBUG=1
    restart: unless-stopped
    volumes:
      - "./scripts/docker-entrypoint-extend.sh:/etc/localstack/init/ready.d/extend.sh"
    healthcheck:
      test:
        - CMD
        - bash
        - -c
        - awslocal s3 ls
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 10s

  jumbo-insights-service-dynamodb:
    image: amazon/dynamodb-local:latest
    volumes:
      - ./data/dynamodb:/home/<USER>/data
    working_dir: /home/<USER>
    command: "-jar DynamoDBLocal.jar -sharedDb -dbPath ./data"
    ports:
      - "8000:8000"
    container_name: jumbo-insights-service-dynamodb

  jumbo-insights-service-dynamoGui:
    image: aaronshaf/dynamodb-admin
    environment:
      - DYNAMO_ENDPOINT=http://jumbo-insights-service-dynamodb:8000
    ports:
      - "8001:8001"
    container_name: jumbo-insights-service-dynamodb-admin-ui

  # datadog-agent:
  #    container_name: datadog-agent
  #    image: "gcr.io/datadoghq/agent:latest"
  #    ports:
  #     - "8126:8126"
  #    environment:
  #     DD_APM_ENABLED: true
  #     DD_APM_NON_LOCAL_TRAFFIC: true
  #     DD_API_KEY: xxxxxxxxxxxxxxx # replace with datadog api key
  #     DD_SITE: datadoghq.com
  #     DD_HOSTNAME: datadog-agent
  #     LOG_LEVEL: DEBUG
  #     TRACE_LANGUAGE: golang
  #    logging:
  #     driver: "json-file"
  #     options:
  #       max-size: "10m"
  #       max-file: "10"

  otlp-collector:
    image: otel/opentelemetry-collector-contrib:0.109.0
    ports:
      - 4317:4317  # OTLP gRPC
      - 4318:4318  # OTLP HTTP
      - 9103:9103  # Prometheus metrics
  clickhouse:
    hostname: clickhouse
    image: clickhouse/clickhouse-server:23.8-alpine
    container_name: clickhouse
    ports:
    - 8123:8123
    - 9000:9000
    - 9009:9009
    ulimits:
      nofile: 
        soft: 262144
        hard: 262144

volumes:
  prometheus_data: {}
