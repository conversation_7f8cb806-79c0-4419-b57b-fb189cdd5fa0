/*
 * This file was generated by the Gradle 'init' task.
 *
 * This generated file contains a sample Java library project to get you started.
 * For more details take a look at the 'Building Java & JVM projects' chapter in the Gradle
 * User Manual available at https://docs.gradle.org/7.0/userguide/building_java_projects.html
 */

plugins {
    // Apply the java-library plugin for API and implementation separation.
    id "java-library"
    id "maven-publish"
}

repositories {
    // Use Maven Central for resolving dependencies.
    mavenCentral()
}

group = "com.zomato.jumboinsightsservice"
version = System.getenv("ARTIFACT_VERSION") ?: "dev-SNAPSHOT"
archivesBaseName = "jumbo-insights-service-client-java"

description = '''Jumbo Insights Service Java Client'''

// Minimum java version compatibility
java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}

// Using latest versions of grpc & protobuf dependencies
def grpcVersion = "1.40.2"
def protobufVersion = "3.19.1"

dependencies {
    // This dependency is exported to consumers, that is to say found on their compile classpath.
    api "org.apache.commons:commons-math3:3.6.1"
    api "com.google.protobuf:protobuf-java:${protobufVersion}"
    api "com.google.protobuf:protobuf-java-util:${protobufVersion}"
    api "io.grpc:grpc-protobuf:${grpcVersion}"
    api "io.grpc:grpc-stub:${grpcVersion}"
    api "io.grpc:grpc-netty-shaded:${grpcVersion}"

    // This dependency is used internally, and not exposed to consumers on their own compile classpath.
    implementation "com.google.guava:guava:30.0-jre"
    implementation "javax.annotation:javax.annotation-api:1.3.2"
}

publishing {
    repositories {
	maven {
	    name = "GitHubPackages"
            url = uri("https://maven.pkg.github.com/zomato/jumbo-insights-service-client-java")
            credentials {
                username = System.getenv("GITHUB_BOT_NAME")
                password = System.getenv("GITHUB_BOT_PAC")
            }
        }
    }
    publications {
        maven(MavenPublication) {
            from(components.java)
        }
    }
}