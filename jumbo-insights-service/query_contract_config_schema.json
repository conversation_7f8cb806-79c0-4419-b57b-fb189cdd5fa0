{"type": "object", "required": ["query_backend", "tenant", "table", "identifier", "audit", "caching_ttl", "refresh_interval"], "properties": {"aggregations": {"type": "array", "items": {"type": "string"}}, "columns": {"type": "array", "items": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}, "func": {"type": "string"}, "source_column": {"type": "string"}}}}, "query_backend": {"type": "string"}, "audit": {"type": "object", "required": ["service", "description", "author_email", "pd_service_name", "team_email"], "properties": {"service": {"type": "string"}, "description": {"type": "string"}, "author_email": {"type": "string", "pattern": "^.*@zomato\\.com$|^.*@grofers\\.com$"}, "pd_service_name": {"type": "string"}, "team_email": {"type": "string", "pattern": "^.*@zomato\\.com$|^.*@grofers\\.com$"}}}, "filters": {"type": "array", "items": {"type": "string"}}, "tenant": {"type": "string"}, "table": {"type": "string"}, "refresh_interval": {"type": "number", "maximum": 3600, "minimum": 60}, "sla": {"type": "number"}, "rate_limit": {"type": "number"}, "identifier": {"type": "string"}, "caching_ttl": {"type": "number", "maximum": 7200, "minimum": 300}, "sql": {"type": "string"}, "type": {"type": "string", "enum": ["sql", "dsl", "open_sql"]}, "presign_time_hours": {"type": "number", "maximum": 50, "minimum": 1}}}