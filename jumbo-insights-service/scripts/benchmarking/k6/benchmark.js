import grpc from 'k6/net/grpc';
import {
    check,
    sleep
} from 'k6';

import {
    SharedArray
} from 'k6/data';
import exec from 'k6/execution';
import { Rate } from 'k6/metrics';

const errorRate = new Rate('errorRate');



// export const options = {
//     summaryTrendStats: ["min", "med", "max",  "p(90)", "p(95)",  "p(99)", "p(99.9)"],
//     stages: [{
//             duration: '10m',
//             target: 30
//         },
//         {
//             duration: '10m',
//             target: 40
//         }
//         // {
//         //     duration: '1m',
//         //     target: 100
//         // },
//         // {
//         //     duration: '1m',
//         //     target: 0
//         // },
//     ],
// };

export const options = {
    summaryTrendStats: ["min", "med", "max",  "p(90)", "p(95)",  "p(99)", "p(99.9)"],
    thresholds: {
        errorRate: [
          // more than 10% of errors will abort the test
          { threshold: 'rate < 0.1', abortOnFail: true, delayAbortEval: '1m' },
        ],
      },
    scenarios: {
      constant_request_rate: {
        executor: 'constant-arrival-rate',
        rate: 300,
        timeUnit: '1s', // 1000 iterations per second, i.e. 1000 RPS
        duration: '30m',
        preAllocatedVUs: 20, // how large the initial pool of VUs would be
        maxVUs: 50, // if the preAllocatedVUs are not enough, we can initialize more
      },
    },
  };

// Stress test
// export const options = {
//     summaryTrendStats: ["min", "med", "max",  "p(90)", "p(95)",  "p(99)", "p(99.9)"],
//     stages: [
//       { duration: '2m', target: 30 }, // below normal load
//       { duration: '5m', target: 30 },
//       { duration: '2m', target: 60 }, // normal load
//       { duration: '5m', target: 60 },
//       { duration: '2m', target: 80 }, // around the breaking point
//       { duration: '5m', target: 80 },
//       { duration: '1m', target: 120 }, // beyond the breaking point
//       { duration: '2m', target: 120 },
//       { duration: '10m', target: 0 }, // scale down. Recovery stage.
//     ],
//   };
  
// Load test
// export const options = {
//     summaryTrendStats: ["min", "med", "max",  "p(90)", "p(95)",  "p(99)", "p(99.9)"],
//     stages: [
//         { duration: '5m', target: 50 }, // simulate ramp-up of traffic from 1 to 50 users over 5 minutes.
//         { duration: '10m', target: 50 }, // stay at 50 users for 10 minutes
//         { duration: '5m', target: 0 }, // ramp-down to 0 users
//     ]
// };

const data = new SharedArray('data', function() {
    // here you can open files, and then do additional processing or generate the array with data dynamically
    const f = JSON.parse(open('./data.json'));
    return f; // f must be an array[]
});

const connections = new SharedArray('connections', function() {
    // here you can open files, and then do additional processing or generate the array with data dynamically
    const f = JSON.parse(open('./connections.json'));
    return f; // f must be an array[]
});

const client = new grpc.Client();
client.load(['../proto'], 'jumbo-insights-service/querier/v1/querier.proto');

export default () => {
    const randomConnection = connections[exec.scenario.iterationInInstance % 3];
    client.connect(randomConnection, {
        plaintext: true
    });

    var pageToken = 0;
    const randomData = data[exec.scenario.iterationInInstance % 300000];
    var re = /page_token_placeholder/gi;
    do {
        var newstr = randomData.query.replace(re, pageToken);
        var response = client.invoke('zomato.jumboinsightsservice.querier.v1.QuerierService/Query', JSON.parse(newstr));
        if (response.status != grpc.StatusOK) {
            console.log(JSON.parse(newstr))
            console.log(JSON.stringify(response.message));
        }
        errorRate.add(response.status != grpc.StatusOK);
        check(response, {
            'status is OK': (r) => r && r.status === grpc.StatusOK,
        });
        check(response, {
            'query return rows > 0': (r) => r && r.message.result && r.message.result.rows.length > 0,
        });
        pageToken = response.message.nextToken
    } while (pageToken != 0)
    client.close();
};