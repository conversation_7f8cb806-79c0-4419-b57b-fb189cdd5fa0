[{"mode": "grpc", "service": "QuerierService", "method": "Query", "rpc_package_name": "zomato.jumboinsightsservice.querier.v1", "relative_proto_path": "jumbo-insights-service/querier/v1/querier.proto", "input": {"matches": {"client_id": "{{any `dummy-client`}}", "query_id": "query-123", "querier": {"filters": [{"field": "user_id", "value": {"string_value": "12345"}}], "interval": {"start_time": "1625097600", "end_time": "1627776000", "datetime_field": "created_at"}}, "pagination_options": {"limit": 100, "sort_key": "created_at"}}}, "output": {"data": {"status": {"state": true, "message": "Query executed successfully", "error_code": 0}, "result": {"rows": [{"columns": [{"string_value": "12345"}, {"int_value": "5"}]}, {"columns": [{"string_value": "67890"}, {"int_value": "3"}]}], "schema": {"column_schema": ["string", "int64"], "column_name": ["user_id", "order_count"]}}, "next_token": 1}}}]