package service

import (
	"context"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/metrics"
	"github.com/Zomato/go/tracer"
	asyncquerypb "github.com/Zomato/jumbo-insights-service-client-golang/asyncquerier/v1"
	commonpb "github.com/Zomato/jumbo-insights-service-client-golang/common"
	"github.com/Zomato/jumbo-insights-service/internal/asyncquerier"
	metric "github.com/Zomato/jumbo-insights-service/internal/metrics"
	"github.com/Zomato/jumbo-insights-service/internal/querier"
	"github.com/Zomato/jumbo-insights-service/internal/serde"
	"go.opentelemetry.io/otel/attribute"
)

func (svc Service) ListAsyncQueries(ctx context.Context, msg *asyncquerypb.ListAsyncQueryRequest) (*asyncquerypb.ListAsyncQueryResponse, error) {
	labels := []metrics.Label{metrics.String(metric.ClientIDDimension, msg.GetClientId()),
		metrics.String(metric.SourceDimension, "listAsyncQueriesRPC"),
	}
	metric.AddValueToInt64Counter(ctx, metric.QueriesRequestsTotal, 1, labels...)

	clientIdAttribute := attribute.String("client_id", msg.GetClientId())
	tracer.SetAttribute(ctx, clientIdAttribute)

	queryExecutionInfos, err := asyncquerier.ListUserQueries(ctx, msg.GetClientId(), msg.GetUser())
	if err != nil {
		metric.AddValueToInt64Counter(ctx, metric.QueriesConfigFailedTotal, 1, labels...)
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name": "list_user_queries",
			"client_id":  msg.GetClientId(),
		}).Error("Got error while listing user queries")
		tracer.NoticeError(ctx, err)
		response := &asyncquerypb.ListAsyncQueryResponse{
			Status: &commonpb.Status{
				State:     false,
				Message:   err.Error(),
				ErrorCode: 3, // INVALID_ARGUMENT
			},
		}
		return response, err
	}

	var queryExecutionInfoPBs []*asyncquerypb.QueryExecutionInfo
	for _, queryExecutionInfo := range queryExecutionInfos {
		queryExecutionInfoPBs = append(queryExecutionInfoPBs, serde.GetQueryExecutionInfoPB(queryExecutionInfo))
	}
	return &asyncquerypb.ListAsyncQueryResponse{
		Status: &commonpb.Status{
			State: true,
		},
		QueryExecutionInfo: queryExecutionInfoPBs,
	}, nil
}

func (svc Service) AsyncQuery(ctx context.Context, msg *asyncquerypb.AsyncQueryRequest) (*asyncquerypb.AsyncResponse, error) {
	labels := []metrics.Label{metrics.String(metric.ClientIDDimension, msg.GetClientId()),
		metrics.String(metric.QueryIDDimension, msg.GetContractId()),
		metrics.String(metric.SourceDimension, "asyncQueryRPC"),
	}

	clientIdAttribute := attribute.String("client_id", msg.GetClientId())
	queryIdAttribute := attribute.String("contract_id", msg.GetContractId())
	tracer.SetAttribute(ctx, clientIdAttribute, queryIdAttribute)

	dtoQuerier := serde.InitQuerierDto(msg.GetQuerier())
	dtoPaginationOptions := serde.InitPaginationOptionsDto(msg.GetPaginationOptions())

	queryConfig, query, err := querier.GetQueryMeta(ctx, msg.GetContractId(), msg.GetContractVersion(), dtoQuerier, dtoPaginationOptions)
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name":  "async_queries",
			"client_id":   msg.GetClientId(),
			"contract_id": msg.GetContractId(),
		}).Error("Got error while submitting async query")
		metric.AddValueToInt64Counter(ctx, metric.QueriesConfigFailedTotal, 1, labels...)
		tracer.NoticeError(ctx, err)
		response := &asyncquerypb.AsyncResponse{
			Status: &commonpb.Status{
				State:     false,
				Message:   err.Error(),
				ErrorCode: 3, // INVALID_ARGUMENT
			},
			QueryExecutionInfo: &asyncquerypb.QueryExecutionInfo{
				QueryState: asyncquerypb.QueryState_FAILED,
			},
		}
		return response, err
	}

	labels = append(labels, metrics.String(metric.TableNameDimension, queryConfig.Table))
	metric.AddValueToInt64Counter(ctx, metric.QueriesRequestsTotal, 1, labels...)

	dtoQueryContext := serde.InitAsyncQueryContext(msg.GetQueryContext())
	contractVersion := msg.GetContractVersion()
	if contractVersion == "" {
		contractVersion = "v1"
	}
	dtoQueryExecutionInfo, err := asyncquerier.SubmitQuery(ctx, msg.GetClientId(), msg.GetContractId(), contractVersion, query, dtoQueryContext, queryConfig)
	if err != nil {
		metric.AddValueToInt64Counter(ctx, metric.QueriesFailedTotal, 1, labels...)
		tracer.NoticeError(ctx, err)
		return &asyncquerypb.AsyncResponse{
			QueryExecutionInfo: serde.GetQueryExecutionInfoPB(dtoQueryExecutionInfo),
			Status: &commonpb.Status{
				State:     false,
				Message:   err.Error(),
				ErrorCode: 13, // INTERNAL
			},
		}, err
	}

	return &asyncquerypb.AsyncResponse{
		QueryExecutionInfo: serde.GetQueryExecutionInfoPB(dtoQueryExecutionInfo),
		Status: &commonpb.Status{
			State: true,
		},
	}, nil
}

func (svc Service) GetQueryResult(ctx context.Context, msg *asyncquerypb.GetQueryResultRequest) (*asyncquerypb.AsyncResponse, error) {
	labels := []metrics.Label{metrics.String(metric.ClientIDDimension, msg.GetClientId()),
		metrics.String(metric.QueryIDDimension, msg.GetContractId()),
		metrics.String(metric.SourceDimension, "getQueryResultRPC"),
	}
	metric.AddValueToInt64Counter(ctx, metric.QueriesRequestsTotal, 1, labels...)

	clientIdAttribute := attribute.String("client_id", msg.GetClientId())
	queryIdAttribute := attribute.String("contract_id", msg.GetContractId())
	tracer.SetAttribute(ctx, clientIdAttribute, queryIdAttribute)

	dtoQueryExecutionInfo, err := asyncquerier.GetQueryInfo(ctx, msg.GetClientId(), msg.GetExecutionId())
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name":  "get_query_result",
			"client_id":   msg.GetClientId(),
			"contract_id": msg.GetContractId(),
		}).Error("Got error while getting async query result")
		metric.AddValueToInt64Counter(ctx, metric.QueriesFailedTotal, 1, labels...)
		tracer.NoticeError(ctx, err)
		return &asyncquerypb.AsyncResponse{
			Status: &commonpb.Status{
				State:     false,
				Message:   err.Error(),
				ErrorCode: 13, // INTERNAL
			},
		}, err
	}

	return &asyncquerypb.AsyncResponse{
		QueryExecutionInfo: serde.GetQueryExecutionInfoPB(dtoQueryExecutionInfo),
		Status: &commonpb.Status{
			State: true,
		},
	}, nil
}
