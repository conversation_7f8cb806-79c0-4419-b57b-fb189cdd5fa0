package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/metrics"
	"github.com/Zomato/go/tracer"
	"go.opentelemetry.io/otel/attribute"

	metric "github.com/Zomato/jumbo-insights-service/internal/metrics"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/querier"
	"github.com/Zomato/jumbo-insights-service/internal/serde"
	"github.com/Zomato/jumbo-insights-service/internal/util"
	"github.com/Zomato/jumbo-insights-service/pkg/ratelimiter"
	goredis "github.com/redis/go-redis/v9"

	"google.golang.org/protobuf/proto"

	"github.com/cespare/xxhash/v2"

	commonpb "github.com/Zomato/jumbo-insights-service-client-golang/common"
	querypb "github.com/Zomato/jumbo-insights-service-client-golang/querier/v1"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

var (
	SecondToMillisMultiplier int64 = 1000
)

func (svc Service) Query(ctx context.Context, msg *querypb.QueryRequest) (*querypb.QueryResponse, error) {
	dtoQuerier := serde.InitQuerierDto(msg.GetQuerier())
	dtoPaginationOptions := serde.InitPaginationOptionsDto(msg.GetPaginationOptions())

	clientIdAttribute := attribute.String("client_id", msg.GetClientId())
	queryIdAttribute := attribute.String("contract_id", msg.GetQueryId())
	tracer.SetAttribute(ctx, clientIdAttribute, queryIdAttribute)

	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"client_id":   msg.GetClientId(),
		"contract_id": msg.GetQueryId(),
	})

	queryConfig, query, err := querier.GetQueryMeta(ctx, msg.GetQueryId(), msg.GetQueryVersion(), dtoQuerier, dtoPaginationOptions)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "submit_query",
		}).Error("Got error while submitting query")
		// Table name is unknown at this point since queryConfig return err
		labels := []metrics.Label{metrics.String(metric.ClientIDDimension, msg.GetClientId()),
			metrics.String(metric.QueryIDDimension, msg.GetQueryId()),
			metrics.String(metric.SourceDimension, "queryRPC"),
		}
		metric.AddValueToInt64Counter(ctx, metric.QueriesConfigFailedTotal, 1, labels...)

		tracer.NoticeError(ctx, err)
		response := &querypb.QueryResponse{
			Status: &commonpb.Status{
				State:     false,
				Message:   err.Error(),
				ErrorCode: 3, // INVALID_ARGUMENT
			},
		}
		return response, err
	}

	labels := []metrics.Label{metrics.String(metric.ClientIDDimension, msg.GetClientId()),
		metrics.String(metric.QueryIDDimension, msg.GetQueryId()),
		metrics.String(metric.TableNameDimension, queryConfig.Table),
		metrics.String(metric.SourceDimension, "queryRPC"),
	}
	metric.AddValueToInt64Counter(ctx, metric.QueriesRequestsTotal, 1, labels...)

	var response *querypb.QueryResponse

	msgHash := getMsgHash(ctx, msg, msg.GetQueryId())
	cacheExist, lastUpdatedAt, err := querier.CheckIfCacheExist(ctx, msg.GetQueryId(), msgHash)

	var useCache bool
	switch err {
	case querier.ErrKeyNotFound:
		useCache = false
	case goredis.Nil:
		useCache = true
	default:
		tracer.NoticeError(ctx, err)
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "check_cache_exist",
		}).Error("Got error while checking if metadata key exists")
		useCache = false
	}

	now := time.Now().UnixNano() / int64(time.Millisecond)
	log.WithFields(map[string]interface{}{
		"event_name":       "caching_info",
		"use_cache":        useCache,
		"cache_exist":      cacheExist,
		"time_since_cache": now - lastUpdatedAt,
	}).Info("Caching Info")
	if useCache && cacheExist && now-lastUpdatedAt < queryConfig.RefreshInterval*SecondToMillisMultiplier {
		response = getCachedResult(ctx, msg.GetQueryId(), msgHash, msg.GetPaginationOptions().GetPageToken())
	}

	if response != nil {
		log.WithFields(map[string]interface{}{
			"event_name": "response",
			"response":   response,
		}).Debug("Query served from cache")
		metric.AddValueToInt64Counter(ctx, metric.QueriesCacheHitTotal, 1, labels...)
		return response, nil
	}

	var resultTable *models.ResultTable
	resultTable, err = querier.GetQueryResult(ctx, queryConfig, query, msg.GetClientId(), msg.GetQueryId(), msg.GetQueryVersion())
	switch {
	case err == util.ErrNoResult:
		valueSet := map[string]interface{}{
			"total_chunks": 0,
			"last_updated": time.Now().UnixNano() / int64(time.Millisecond),
		}
		err = querier.SetResultCache(ctx, msg.GetQueryId(), msgHash, valueSet, queryConfig.CachingTTL)
		if err != nil {
			tracer.NoticeError(ctx, err)
			log.WithError(err).WithFields(map[string]interface{}{
				"event_name": "set_empty_result_cache",
				"query_hash": msgHash,
			}).Error("Got following error while setting cache for empty result")
		} else {
			err = util.ErrNoResult
		}
		metric.QueryTotalRows.Record(ctx, 0, labels...)
		return &querypb.QueryResponse{
			Status: &commonpb.Status{
				State:   true,
				Message: err.Error(),
			},
			Result: &querypb.ResultTable{},
		}, nil
	case errors.As(err, &ratelimiter.BucketOverflowError{}):
		metric.AddValueToInt64Counter(ctx, metric.QueriesFailedTotal, 1, labels...)
		return &querypb.QueryResponse{
			Status: &commonpb.Status{
				State:     false,
				Message:   err.Error(),
				ErrorCode: 8, // RESOURCE_EXHAUSTED
			},
		}, err
	case err != nil:
		metric.AddValueToInt64Counter(ctx, metric.QueriesFailedTotal, 1, labels...)
		tracer.NoticeError(ctx, err)
		return &querypb.QueryResponse{
			Status: &commonpb.Status{
				State:     false,
				Message:   err.Error(),
				ErrorCode: 2, // UNKNOWN
			},
		}, err
	}
	// record query runtime and total rows
	metric.QueryTotalRows.Record(ctx, int64(resultTable.RowCount), labels...)
	metric.QueryRunTime.Record(ctx, int64(resultTable.RunTimeMS), labels...)

	resultTablePB, nextPageToken, state, err := getAndUpdateCache(ctx, resultTable, msg.GetPaginationOptions().GetPageToken(), queryConfig.CachingTTL, msg.GetQueryId(), msgHash)

	if err != nil {
		tracer.NoticeError(ctx, err)
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "get_and_update_cache",
			"query_hash": msgHash,
			"page_token": msg.GetPaginationOptions().GetPageToken(),
		}).Error("Got following error while getting result and updating cache")
	}

	if !state {
		metric.AddValueToInt64Counter(ctx, metric.QueriesFailedTotal, 1, labels...)

		return &querypb.QueryResponse{
			Status: &commonpb.Status{
				State:     state,
				Message:   err.Error(),
				ErrorCode: 3, // INVALID_ARGUMENT
			},
		}, err
	}

	return &querypb.QueryResponse{
		Status: &commonpb.Status{
			State: state,
			Message: fmt.Sprintf("returning %d rows with %d columns\n",
				len(resultTablePB.Rows),
				resultTable.ColumnCount),
		},
		Result:    resultTablePB,
		NextToken: nextPageToken,
	}, err
}

func getMsgHash(ctx context.Context, querier *querypb.QueryRequest, queryIdentifier string) uint64 {
	var msgHash uint64
	msgProtoBinary, err := proto.Marshal(querier)
	if err != nil {
		tracer.NoticeError(ctx, err)
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name":  "marshal_request",
			"contract_id": queryIdentifier,
		}).Error("Unable to marshal request querier")
	} else {
		msgHash = xxhash.Sum64(msgProtoBinary)
	}
	return msgHash
}

func getColumnValue(value interface{}, dataType string) *commonpb.AnyValue {
	var return_proto *commonpb.AnyValue
	var ok bool
	switch dataType {
	case "UINT":
		var casted_value uint64
		casted_value, ok = value.(uint64)
		return_proto = &commonpb.AnyValue{
			Value: &commonpb.AnyValue_UintValue{UintValue: casted_value},
		}
	case "INT":
		var casted_value int64
		casted_value, ok = value.(int64)
		return_proto = &commonpb.AnyValue{
			Value: &commonpb.AnyValue_IntValue{IntValue: casted_value},
		}
	case "DOUBLE":
		var casted_value float64
		casted_value, ok = value.(float64)
		return_proto = &commonpb.AnyValue{
			Value: &commonpb.AnyValue_DoubleValue{DoubleValue: casted_value},
		}
	case "BOOLEAN":
		var casted_value bool
		casted_value, ok = value.(bool)
		return_proto = &commonpb.AnyValue{
			Value: &commonpb.AnyValue_BoolValue{BoolValue: casted_value},
		}
	case "STRING":
		var casted_value string
		casted_value, ok = value.(string)
		return_proto = &commonpb.AnyValue{
			Value: &commonpb.AnyValue_StringValue{StringValue: casted_value},
		}
	default:
		log.WithFields(map[string]interface{}{
			"data_type": dataType,
			"value":     value,
		}).Error("Data type not supported, converting to string")
		return &commonpb.AnyValue{
			Value: &commonpb.AnyValue_StringValue{StringValue: fmt.Sprint(value)},
		}
	}
	if !ok {
		log.WithFields(map[string]interface{}{
			"data_type": dataType,
			"value":     value,
		}).Error("Data type not supported, converting to string")
		return &commonpb.AnyValue{
			Value: &commonpb.AnyValue_StringValue{StringValue: fmt.Sprint(value)},
		}
	} else {
		return return_proto
	}
}

func getCachedResult(ctx context.Context, queryIdentifier string, queryHash uint64, pageToken int32) *querypb.QueryResponse {
	resultTableBytes, nextPageToken, state, err := querier.GetCachedResult(ctx, queryIdentifier, queryHash, pageToken)
	if err != nil {
		if err == util.ErrNoResult {
			return &querypb.QueryResponse{
				Status: &commonpb.Status{
					State:   true,
					Message: err.Error(),
				},
				Result: &querypb.ResultTable{},
			}
		}
		tracer.NoticeError(ctx, err)
		return nil
	}

	resultTable := &querypb.ResultTable{}
	err = proto.Unmarshal(resultTableBytes, resultTable)
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name":  "proto_unmarshal",
			"contract_id": queryIdentifier,
			"hash":        queryHash,
			"page_token":  pageToken,
		}).Error("Got error while unmarshalling the result")
		tracer.NoticeError(ctx, err)
		return nil
	}
	return &querypb.QueryResponse{
		Status: &commonpb.Status{
			State: state,
			Message: fmt.Sprintf("returning %d rows with %d columns\n",
				len(resultTable.Rows),
				len(resultTable.Schema.ColumnName)),
		},
		Result:    resultTable,
		NextToken: nextPageToken,
	}
}

func getAndUpdateCache(ctx context.Context,
	resultTable *models.ResultTable,
	pageToken int32,
	cachingTTL int64,
	queryID string,
	queryHash uint64) (*querypb.ResultTable, int32, bool, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "querier/GetAndUpdateCache", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	valueSet := make(map[string]interface{})
	pageSize := config.GetInt(ctx, "querier.page_size")
	var currentResult *querypb.ResultTable
	var pageIndex int32
	var nextPageToken int32
	var state bool
	var err error

	for start := 0; start < int(resultTable.RowCount); start += pageSize {
		end := start + pageSize
		var results []*querypb.Row
		if end > int(resultTable.RowCount) {
			end = int(resultTable.RowCount)
		}

		for r := start; r < end; r++ {
			var col []*commonpb.AnyValue
			for c := 0; c < int(resultTable.ColumnCount); c++ {
				column := resultTable.Rows[r].Columns[c]
				col = append(col, getColumnValue(column.Value, column.Type))
			}
			results = append(results, &querypb.Row{
				Columns: col,
			})
		}
		resultTable := &querypb.ResultTable{
			Rows: results,
			Schema: &querypb.MessageSchema{
				ColumnSchema: resultTable.Schema.ColumnSchemas,
				ColumnName:   resultTable.Schema.ColumnNames,
			},
		}

		if err == nil {
			var resultTableProtoBinary []byte
			resultTableProtoBinary, err = proto.Marshal(resultTable)
			valueSet[fmt.Sprintf("chunk_%d", pageIndex)] = resultTableProtoBinary
		}

		if pageIndex == pageToken {
			currentResult = resultTable
		}
		pageIndex++
	}

	if err == nil {
		valueSet["total_chunks"] = pageIndex
		valueSet["last_updated"] = time.Now().UnixNano() / int64(time.Millisecond)
		err = querier.SetResultCache(ctx, queryID, queryHash, valueSet, cachingTTL)
	}

	if currentResult == nil {
		return nil, 0, false, querier.ErrPageNotFound
	}

	if pageIndex-1 > pageToken {
		nextPageToken = pageToken + 1
		state = true
	} else {
		nextPageToken = 0
		state = true
	}
	return currentResult, nextPageToken, state, err
}
