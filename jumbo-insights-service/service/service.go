package service

import (
	asyncquerypb "github.com/Zomato/jumbo-insights-service-client-golang/asyncquerier/v1"
	querypb "github.com/Zomato/jumbo-insights-service-client-golang/querier/v1"
	templatepb "github.com/Zomato/jumbo-insights-service-client-golang/template/v1"
)

// Service is the main struct which should implement all the rpc calls
// defined in the proto files
type Service struct {
	querypb.UnimplementedQuerierServiceServer
	asyncquerypb.UnimplementedAsyncQuerierServiceServer
	templatepb.UnimplementedTemplateServiceServer
}
