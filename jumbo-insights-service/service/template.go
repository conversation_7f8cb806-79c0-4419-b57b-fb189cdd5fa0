package service

import (
	"context"
	"time"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/jumbo-insights-service-client-golang/common"
	templatepb "github.com/Zomato/jumbo-insights-service-client-golang/template/v1"
	"github.com/Zomato/jumbo-insights-service/internal/serde"
	"github.com/Zomato/jumbo-insights-service/internal/templates"
)

func (svc Service) RegisterTemplate(ctx context.Context, msg *templatepb.RegisterTemplateRequest) (
	*templatepb.RegisterTemplateResponse, error) {
	templateDetails := serde.ChangePrototoDynamo(msg.GetTemplate())
	templateDetails.TemplateVersion = "1"
	templateDetails.ItemType = "Template"
	err := templates.RegisterTemplate(ctx, templateDetails)
	if err == templates.ErrAlreadyExist {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "register_template",
			"template":   templateDetails,
		}).Error("failed to register this template")
		return &templatepb.RegisterTemplateResponse{
			Status: &common.Status{
				State:     false,
				ErrorCode: 6, //already exist
				Message:   err.Error(),
			},
		}, err
	} else if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "register_template",
			"template":   templateDetails,
		}).Error("failed to register this template")
		return &templatepb.RegisterTemplateResponse{
			Status: &common.Status{
				State:     false,
				ErrorCode: 3,
				Message:   err.Error(),
			},
		}, err
	}
	resp := &templatepb.RegisterTemplateResponse{
		Status: &common.Status{
			State: true,
		},
		TemplateId:      templateDetails.TemplateId,
		TemplateVersion: templateDetails.TemplateVersion,
	}

	return resp, nil

}
func (svc Service) GetTemplate(ctx context.Context, msg *templatepb.GetTemplateRequest) (*templatepb.GetTemplateResponse, error) {
	templateId := msg.GetTemplateId()
	template_version := msg.GetTemplateVersion()
	templateData, err := templates.GetTemplateFromId(ctx, templateId, template_version)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name":       "get_template",
			"template_id":      templateId,
			"template_version": template_version,
		}).Error("failed to get this template")

		var errCode int = 3
		if err == templates.ErrTemplateNotFound {
			errCode = 5 //not found
		} else if err == templates.ErrTemplateIdNotNull {
			errCode = 3 //invalid argrument
		}
		return &templatepb.GetTemplateResponse{
			Status: &common.Status{
				State:     false,
				ErrorCode: int32(errCode), //not found
				Message:   err.Error(),
			},
			Template: &templatepb.Template{},
		}, err
	}
	data := serde.ChangeDynamotemplatetoProto(templateData)
	resp := &templatepb.GetTemplateResponse{
		Status: &common.Status{
			State: true,
		},
		Template: data,
	}
	return resp, nil

}
func (svc Service) UpdateTemplate(ctx context.Context, msg *templatepb.UpdateTemplateRequest) (*templatepb.UpdateTemplateResponse, error) {
	templateData := serde.ChangePrototoDynamo(msg.GetTemplate())
	templateData.AddedAt = time.Now().Format("2006-01-02 15:04:05")
	err := templates.UpdateTemplate(ctx, templateData)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name":  "update_template",
			"template_id": msg.GetTemplate().TemplateId,
		}).Error("update template request failed")

		return &templatepb.UpdateTemplateResponse{
			Status: &common.Status{
				State:     false,
				ErrorCode: 3,
				Message:   err.Error(),
			},
			TemplateId: templateData.TemplateId,
		}, err
	}
	resp := &templatepb.UpdateTemplateResponse{
		Status: &common.Status{
			State: true,
		},
		TemplateVersion: templateData.TemplateVersion,
		TemplateId:      templateData.TemplateId,
	}
	return resp, nil

}
func (svc Service) MoveToProd(ctx context.Context, msg *templatepb.MoveToProdRequest) (*templatepb.MovetoProdResponse, error) {
	templateId := msg.TemplateId
	templateVersion := msg.TemplateVersion
	err := templates.MoveToProd(ctx, templateId, templateVersion)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name":       "move_to_prod",
			"template_id":      msg.TemplateId,
			"template_version": msg.TemplateVersion,
		}).Error("failed to move this template to prod")
		return &templatepb.MovetoProdResponse{
			Status: &common.Status{
				State:     false,
				ErrorCode: 3,
				Message:   err.Error(),
			},
			TemplateId: templateId,
		}, err
	}
	return &templatepb.MovetoProdResponse{
		Status: &common.Status{
			State: true,
		},
		TemplateVersion: templateVersion,
		TemplateId:      templateId,
	}, nil
}

func (svc Service) ListTemplates(ctx context.Context, msg *templatepb.ListTemplatesRequest) (*templatepb.ListTemplatesResponse, error) {
	templateData, err := templates.ListTemplate(ctx)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "list_unique_template",
		}).Error("failed to get all list of templates")
		return &templatepb.ListTemplatesResponse{
			Status: &common.Status{
				State:     false,
				Message:   err.Error(),
				ErrorCode: 3,
			},
		}, err
	}
	data := []string{}
	for _, item := range templateData {
		data = append(data, item.TemplateId)
	}

	return &templatepb.ListTemplatesResponse{
		TemplateId: data,
		Status: &common.Status{
			State: true,
		},
	}, nil
}

func (svc Service) PreviewTemplate(ctx context.Context, msg *templatepb.PreviewTemplateRequest) (*templatepb.PreviewTemplateResponse, error) {
	templateData := msg.GetTemplate()
	templateDetails := serde.ChangePrototoDynamo(templateData)
	templateDetails.TemplateVersion = "0"
	err := templates.RegisterTestTemplate(ctx, templateDetails)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name":  "testing_the_query",
			"template_id": msg.GetTemplate(),
		}).Error("failed to test this template")
		return &templatepb.PreviewTemplateResponse{
			Status: &common.Status{
				State:     false,
				Message:   err.Error(),
				ErrorCode: 3,
			},
		}, err
	}

	return &templatepb.PreviewTemplateResponse{
		Status: &common.Status{
			State: true,
		},
		TemplateId: templateData.TemplateId,
	}, err

}
func (svc Service) ListTemplatesById(ctx context.Context, msg *templatepb.ListTemplatesByIdRequest) (*templatepb.ListTemplatesByIdResponse, error) {
	templateId := msg.GetTemplateId()
	templateData, err := templates.ListTemplateByID(ctx, templateId)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name":  "list_templates_all_version",
			"template_id": msg.TemplateId,
		}).Error("failed to get the list of all version of this templateId")
		return &templatepb.ListTemplatesByIdResponse{
			Templates: []*templatepb.Template{},
			Status: &common.Status{
				State:     false,
				Message:   err.Error(),
				ErrorCode: 3,
			},
		}, err
	}
	data := []*templatepb.Template{}
	for _, item := range templateData {
		template := serde.ChangeDynamotemplatetoProto(item)
		data = append(data, template)
	}

	return &templatepb.ListTemplatesByIdResponse{
		Templates: data,
		Status: &common.Status{
			State: true,
		},
	}, nil
}
func (svc Service) SyncYamlTemplates(ctx context.Context, msg *templatepb.SyncYamlTemplatesRequest) (*templatepb.SyncYamlTemplatesResponse, error) {
	err := templates.YamlToDynamoRegister(ctx)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "sync_yaml_templates_to_dynamo",
		}).Error("failed to get the list of all version of this templateId")
		return &templatepb.SyncYamlTemplatesResponse{
			Status: &common.Status{
				State:     false,
				Message:   err.Error(),
				ErrorCode: 3,
			},
		}, err
	}
	return &templatepb.SyncYamlTemplatesResponse{
		Status: &common.Status{
			State: true,
		},
	}, nil

}
