package csv

import (
	"context"
	"encoding/csv"
	"os"

	log "github.com/Zomato/go/logger"
)

type Writer struct {
	CSVWriter *csv.Writer
	File      *os.File
}

func NewWriter(ctx context.Context, filePath string) (*Writer, error) {
	f, err := os.Create(filePath)
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("Not able to create file")
		return nil, err
	}

	csvS3Writer := &Writer{
		CSVWriter: csv.NewWriter(f),
		File:      f,
	}

	return csvS3Writer, nil
}

func (csvS3Writer *Writer) WriteHeader(ctx context.Context, header []string) error {
	return csvS3Writer.WriteRow(ctx, header)
}

func (csvS3Writer *Writer) WriteRow(ctx context.Context, row []string) error {
	err := csvS3Writer.CSVWriter.Write(row)
	if err != nil {
		log.FromContext(ctx).WithError(err).Error(err)
		return err
	}
	return nil
}

func (csvS3Writer *Writer) Close(ctx context.Context) error {
	csvS3Writer.CSVWriter.Flush()
	if err := csvS3Writer.CSVWriter.Error(); err != nil {
		log.FromContext(ctx).WithError(err).Error("Error flushing data")
		return err
	}
	return csvS3Writer.File.Close()
}
