package tracer

import (
	"context"

	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
)

func InitialiseTracer(ctx context.Context) {
	if !IsZTracerEnabled(ctx) {
		log.Error("Datadog tracing is disabled")
		return
	}

	datadogProvider := tracer.NewDatadogProvider(ctx)
	err := tracer.Initialize(datadogProvider)
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("failed to initialise tracer")
	}
}

func IsZTracerEnabled(ctx context.Context) bool {
	return config.GetBool(ctx, "tracer.enabled")
}
