package s3

import (
	"context"
	"errors"
	"os"
	"time"

	// https://github.com/open-telemetry/opentelemetry-go-contrib/issues/3368
	// awsconfig "github.com/Zomato/go/aws/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/aws/smithy-go"
)

// Config holds sqs client configs
type Config struct {
	Bucket              string
	EndpointRequired    bool
	Endpoint            string
	Region              string
	ForcePathStyle      bool
	UploaderPartSize    int64
	UploaderConcurrency int
	Debug               bool
}

type Client struct {
	S3            *s3.Client
	Uploader      *manager.Uploader
	PresignClient *s3.PresignClient
	config        *Config
}

// NewClient is used to create a S3 client
func NewClient(ctx context.Context, cfg *Config) (*Client, error) {
	clientLogMode := aws.LogDeprecatedUsage | aws.LogRetries
	if cfg.Debug {
		clientLogMode |= aws.LogRequest | aws.LogResponse
	}
	s3Config, err := config.LoadDefaultConfig(
		ctx,
		config.WithRegion(cfg.Region),
		config.WithClientLogMode(clientLogMode),
	)
	if err != nil {
		return nil, err
	}

	if cfg.EndpointRequired {
		s3Config.EndpointResolverWithOptions = aws.EndpointResolverWithOptionsFunc(
			func(service, region string, options ...interface{}) (aws.Endpoint, error) {
				return aws.Endpoint{
					URL:               cfg.Endpoint,
					SigningRegion:     cfg.Region,
					HostnameImmutable: cfg.ForcePathStyle,
				}, nil
			},
		)
	}

	s3Client := s3.NewFromConfig(s3Config)
	uploader := manager.NewUploader(s3Client, func(u *manager.Uploader) {
		u.PartSize = cfg.UploaderPartSize * 1024 * 1024
		u.Concurrency = cfg.UploaderConcurrency
	})

	return &Client{
		S3:            s3Client,
		Uploader:      uploader,
		PresignClient: s3.NewPresignClient(s3Client),
		config:        cfg,
	}, nil
}

// UploadToS3 adds the provided file to the insights service bucket
func (client *Client) UploadToS3(ctx context.Context, bucketName string, key string, value *os.File) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "s3/UploadToS3", tracer.WithSpanKind(tracer.SpanKindClient))
		ctx = tctx
		defer span.End()
	}
	input := &s3.PutObjectInput{
		ACL:    types.ObjectCannedACLBucketOwnerFullControl,
		Body:   value,
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	}
	_, err := client.Uploader.Upload(ctx, input)
	return err
}

// GetPresignURL returns s3 presigned url for uploading
func (client *Client) GetPresignURL(ctx context.Context, bucketName, key string, presignTime int32) (string, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "s3/GetPresignURL", tracer.WithSpanKind(tracer.SpanKindClient))
		ctx = tctx
		defer span.End()
	}
	request, err := client.PresignClient.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	}, func(opts *s3.PresignOptions) {
		opts.Expires = time.Duration(int64(presignTime) * int64(time.Hour))
	})
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("Couldn't get a presigned request to get %v:%v", bucketName, key)
		return "", err
	}

	return request.URL, nil
}

// CheckIfFileExists checks whether the file exists on s3 or not
func (client *Client) CheckIfFileExists(ctx context.Context, bucketName, key string) (bool, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "s3/CheckIfFileExists", tracer.WithSpanKind(tracer.SpanKindClient))
		ctx = tctx
		defer span.End()
	}
	_, err := client.S3.HeadObject(ctx, &s3.HeadObjectInput{
		Bucket: &bucketName,
		Key:    &key,
	})

	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"event_name": "check_s3_file",
		"bucket":     bucketName,
		"key":        key,
	})

	if err != nil {
		var notFound *types.NotFound
		var aerr smithy.APIError
		switch {
		case !errors.As(err, &aerr) || aerr.ErrorCode() == notFound.ErrorCode():
			log.WithError(err).Info("Required key not found in s3")
			return false, nil
		default:
			log.WithError(err).Error("Unknown error occurred while checking for key")
			return false, err
		}
	}
	return true, nil
}
