package ratelimiter

import (
	"context"
	"sync"

	"github.com/Zomato/go/tracer"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

// Config holds rate limiter client configs
type Config struct {
	BurstTraffic int
}

type Client struct {
	RateLimiterMap map[string]*Limiter
	config         *Config
	mutex          sync.RWMutex
}

func NewClient(cfg *Config) *Client {
	return &Client{
		RateLimiterMap: make(map[string]*Limiter),
		config:         cfg,
	}
}

func (client *Client) GetOrCreateRateLimiter(ctx context.Context, queryID string, limit int) *Limiter {
	if ztracer.IsZTracerEnabled(ctx) {
		_, span := tracer.StartSpan(ctx, "ratelimiter/GetOrCreateRateLimiter", tracer.WithSpanKind(tracer.SpanKindInternal))
		defer span.End()
	}

	client.mutex.RLock()
	ratelimiter, ok := client.RateLimiterMap[queryID]
	client.mutex.RUnlock()

	if ok {
		return ratelimiter
	}

	client.mutex.Lock()
	{
		ratelimiter = NewLimiter(limit, client.config.BurstTraffic)
		client.RateLimiterMap[queryID] = ratelimiter
	}
	client.mutex.Unlock()

	return ratelimiter
}
