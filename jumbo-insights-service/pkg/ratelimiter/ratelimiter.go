package ratelimiter

import (
	"context"

	"github.com/Zomato/go/config"
	"github.com/Zomato/go/tracer"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
	"golang.org/x/time/rate"
)

// A Limiter controls how frequently events are allowed to happen.
// It implements a "token bucket" of size b, initially full and refilled
// at rate r tokens per second.
type Limiter struct {
	tokenBucket *rate.Limiter
}

// BucketOverflow error will be returned when you fail to Add() to a bucket
// because the rate limit has been reached.
type BucketOverflowError struct {
}

// Error implements the error interface
func (b BucketOverflowError) Error() string {
	return "Bucket Overflow, rate limit breached"
}

// NewLimiter returns a new Limiter that allows events up to rate r and permits
// bursts of at most b tokens.
func NewLimiter(limit, burst int) *Limiter {
	return &Limiter{
		tokenBucket: rate.NewLimiter(rate.Limit(limit), burst),
	}
}

// Limit takes 1 token from the bucket
func (limiter *Limiter) Limit(ctx context.Context) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "ratelimiter/Limit", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	enabled := config.GetBool(ctx, "ratelimiter.enabled")

	switch {
	case !enabled:
		return nil
	case !limiter.tokenBucket.Allow():
		return BucketOverflowError{}
	default:
		return nil
	}
}
