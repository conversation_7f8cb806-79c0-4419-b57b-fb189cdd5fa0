package sqs

import (
	"context"

	awsconfig "github.com/Zomato/go/aws/config"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
)

// Config holds sqs client configs
type Config struct {
	EndpointNeeded    bool
	Endpoint          string
	Region            string
	MaxRetries        int
	Debug             bool
	WaitTimeout       int32
	MaxMessages       int32
	VisibilityTimeout int32
	WorkerPool        int32
}

type Client struct {
	sqs    *sqs.Client
	Config *Config
}

func NewClient(ctx context.Context, cfg *Config) (*Client, error) {
	clientLogMode := aws.LogDeprecatedUsage | aws.LogRetries
	if cfg.Debug {
		clientLogMode |= aws.LogRequest | aws.LogResponse
	}
	sqsConfig, err := awsconfig.LoadDefaultConfig(
		ctx,
		config.WithRegion(cfg.Region),
		config.WithRetryMaxAttempts(cfg.MaxRetries),
		config.WithClientLogMode(clientLogMode),
	)
	if err != nil {
		return nil, err
	}
	if cfg.EndpointNeeded {
		sqsConfig.EndpointResolverWithOptions = aws.EndpointResolverWithOptionsFunc(
			func(service, region string, options ...interface{}) (aws.Endpoint, error) {
				return aws.Endpoint{
					URL:           cfg.Endpoint,
					SigningRegion: cfg.Region,
				}, nil
			},
		)
	}
	sqsClient := &Client{
		sqs:    sqs.NewFromConfig(sqsConfig),
		Config: cfg,
	}
	return sqsClient, nil
}
