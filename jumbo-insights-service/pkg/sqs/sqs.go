package sqs

import (
	"context"

	log "github.com/Zomato/go/logger"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
)

type Message struct {
	ID            string
	Payload       []byte
	ReceiptHandle string
}

type SendMessageInputParam struct {
	Message  string
	QueueURL string
}

// sendMessage sends given message to given queue URL
func (client *Client) SendMessage(ctx context.Context, inputParam *SendMessageInputParam) (*string, error) {
	input := &sqs.SendMessageInput{
		MessageBody: aws.String(inputParam.Message),
		QueueUrl:    aws.String(inputParam.QueueURL),
	}

	resp, err := client.sqs.SendMessage(ctx, input)
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("Error in sending message in queue")
		return nil, err
	}
	return resp.MessageId, nil
}

// getMessage retrieves the message from given queue URL
func (client *Client) GetMessages(ctx context.Context, queueURL string) ([]*Message, error) {
	input := &sqs.ReceiveMessageInput{
		QueueUrl:            aws.String(queueURL),
		WaitTimeSeconds:     client.Config.WaitTimeout,
		MaxNumberOfMessages: client.Config.MaxMessages,
		VisibilityTimeout:   client.Config.VisibilityTimeout,
	}
	msgResult, err := client.sqs.ReceiveMessage(ctx, input)
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("Error in receiving message from queue")
		return nil, err
	}
	msgs := make([]*Message, 0, cap(msgResult.Messages))
	for _, sqsMsg := range msgResult.Messages {
		message := &Message{
			ID:            *sqsMsg.MessageId,
			Payload:       []byte(*sqsMsg.Body),
			ReceiptHandle: *sqsMsg.ReceiptHandle,
		}
		msgs = append(msgs, message)
	}
	return msgs, nil
}

// deleteMessage deletes message from SQS queue
func (client *Client) DeleteMessage(ctx context.Context, queueURL, recieptHandle string) error {
	input := &sqs.DeleteMessageInput{
		QueueUrl:      aws.String(queueURL),
		ReceiptHandle: aws.String(recieptHandle),
	}
	_, err := client.sqs.DeleteMessage(ctx, input)
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("Error in deleting message from queue")
		return err
	}
	return nil
}
