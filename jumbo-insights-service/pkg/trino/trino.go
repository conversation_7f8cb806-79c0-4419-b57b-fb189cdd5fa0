package trino

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"runtime/debug"
	"sync"

	"database/sql"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"

	"github.com/Zomato/jumbo-insights-service/internal/models"

	// Using trino client
	_ "github.com/trinodb/trino-go-client/trino"
)

var (
	ErrContextClosed = errors.New("context closed while query was running")
)

type Auth struct {
	Enabled  bool
	User     string
	Password string
}

type Config struct {
	Host                  string
	Port                  int
	Auth                  *Auth
	Catalog               string
	Schema                string
	Source                string
	QueryMaxExecutionTime string
}

type Request struct {
	ClientConfig    *models.ClientConfig
	TrinoConfig     *Config
	Query           string
	ReplyChan       chan *Response
	ShutdownChannel *ShutdownChannel
	Wg              *sync.WaitGroup
}

type ShutdownChannel struct {
	Completed chan struct{}
	<PERSON>rror<PERSON><PERSON> chan error
}

type Response struct {
	Schema *models.MessageSchema
	Row    *models.Row
}

func ExecuteSQL(ctx context.Context, request *Request) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Trino/ExecuteSQL", tracer.WithSpanKind(tracer.SpanKindClient))
		ctx = tctx
		defer span.End()
	}

	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"client_id":    request.ClientConfig.ClientId,
		"contract_id":  request.ClientConfig.ContractId,
		"execution_id": request.ClientConfig.ExecutionID,
	})

	// Go routine panic and channel completion handling
	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("[PANIC] | Stacktrace: %s | error: %+v", debug.Stack(), r)
			log.WithError(err).Error("Handling panic")
			request.ShutdownChannel.ErrorChan <- err
		} else {
			log.Info("Batch read completed")
			request.ShutdownChannel.Completed <- struct{}{}
		}
	}()

	// clientConfig := request.ClientConfig
	query := request.Query
	trino, err := getTrinoClient(request.TrinoConfig)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "trino_connection",
		}).Error("Got error while connecting to trino cluster")
		request.ShutdownChannel.ErrorChan <- err
		return
	}

	// TODO: Add X-Trino-* Headers
	query = fmt.Sprintf(`%s %s`, getMetaString(request.ClientConfig), query)
	rows, err := trino.QueryContext(ctx, query, sql.Named("X-Trino-User", request.ClientConfig.User))

	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "trino_query_error",
		}).Error("Got error while running trino query")
		request.ShutdownChannel.ErrorChan <- err
		return
	}

	// Start a goroutine to read the rows
	batchRead(ctx, request.Wg, log, rows, request.ReplyChan, request.ShutdownChannel)
}

func getTrinoClient(trinoConfig *Config) (*sql.DB, error) {
	authPrefix := fmt.Sprintf("http://%s", trinoConfig.Auth.User)
	if trinoConfig.Auth.Enabled {
		authPrefix = fmt.Sprintf("https://%s:%s", trinoConfig.Auth.User, url.QueryEscape(trinoConfig.Auth.Password))
	}
	dsn := fmt.Sprintf("%s@%s:%d?catalog=%s&schema=%s&source=%s",
		authPrefix,
		trinoConfig.Host,
		trinoConfig.Port,
		trinoConfig.Catalog,
		trinoConfig.Schema,
		trinoConfig.Source)
	if trinoConfig.QueryMaxExecutionTime != "" {
		dsn = fmt.Sprintf("%s&session_properties=query_max_execution_time=%s", dsn, trinoConfig.QueryMaxExecutionTime)
	}
	log.Print("DSN")
	log.Info(dsn)
	return sql.Open("trino", dsn)
}

func getMetaString(config *models.ClientConfig) string {
	return fmt.Sprintf(`/* ClientID: %s, ContractID: %s, ExecutionID: %s */`, config.ClientId, config.ContractId, config.ExecutionID)
}

func batchRead(ctx context.Context, wg *sync.WaitGroup, log *log.Logger, rows *sql.Rows, replyChan chan *Response, shutdownChannel *ShutdownChannel) {
	columns, err := rows.Columns()
	if err != nil {
		shutdownChannel.ErrorChan <- err
		return
	}
	colTypes, err := rows.ColumnTypes()
	if err != nil {
		shutdownChannel.ErrorChan <- err
		return
	}
	var columnNames, columnSchemas []string
	for _, colType := range colTypes {
		columnNames = append(columnNames, colType.Name())
		columnSchemas = append(columnSchemas, getColType(colType.DatabaseTypeName()))
	}

	sendSchema := true
	var noOfRows int32
	defer func() {
		log.Info(fmt.Sprintf(`No of rows: %d`, noOfRows))
	}()

	for {
		select {
		// Handling context close
		case <-ctx.Done():
			shutdownChannel.ErrorChan <- ErrContextClosed
			return // Exit the function to avoid leaking the goroutine
		default:
			// Must call rows.Next() in order for errors to be populated correctly
			// because Query() only submits the query, and doesn't handle
			// success/failure. Next() is the method which inspects the submitted
			// queries status and causes errors to get stored in the sql.Rows object.
			if rows.Next() {
				// Create a slice of interface{}'s to represent each column,
				// and a second slice to contain pointers to each item in the columns slice.
				cols := make([]interface{}, len(columns))
				columnPointers := make([]interface{}, len(columns))
				for i := range columns {
					columnPointers[i] = &cols[i]
				}
				// Scan the result into the column pointers...
				if err := rows.Scan(columnPointers...); err != nil {
					shutdownChannel.ErrorChan <- err
					return
				}
				var processedcols []*models.AnyValue
				for i := range columns {
					processedcols = append(processedcols, &models.AnyValue{
						Type:  columnSchemas[i],
						Value: *columnPointers[i].(*interface{}),
					})
				}
				// Send the row data on the channel
				row := &models.Row{
					Columns: processedcols,
				}
				response := &Response{
					Row: row,
				}
				if sendSchema {
					response.Schema = &models.MessageSchema{
						ColumnSchemas: columnSchemas,
						ColumnNames:   columnNames,
					}
					sendSchema = false
				}
				wg.Add(1)
				noOfRows++
				replyChan <- response
			} else {
				// The error from rows.Err() could be the result of a variety of errors in the rows.Next() loop.
				// The loop might exit for some reason other than finishing the loop normally, so you always need to
				// check whether the loop terminated normally or not
				// http://go-database-sql.org/errors.html
				if err := rows.Err(); err != nil {
					log.WithError(err).Error("Error while processing the query result")
					shutdownChannel.ErrorChan <- err
				}

				if err = rows.Close(); err != nil {
					log.WithError(err).Error("Got error while closing the rows")
					shutdownChannel.ErrorChan <- err
				}
				return // Exit the function after processing all rows
			}
		}
	}
}

func getColType(dbType string) string {
	switch dbType {
	case "integer", "bigint", "smallint", "tinyint":
		return "INT"
	case "varchar":
		return "STRING"
	case "double", "real":
		return "DOUBLE"
	case "boolean":
		return "BOOLEAN"
	default:
		return dbType
	}
}
