package clickhouse

import (
	"context"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

type Config struct {
	Brokers                []string
	AuthConfig             *AuthConfig
	DialTimeoutSeconds     int
	MaxOpenConns           int
	MaxIdleConns           int
	ConnMaxLifetimeSeconds int
	Settings               clickhouse.Settings
}
type Client struct {
	connection clickhouse.Conn
	config     *Config
}

func NewClient(ctx context.Context, config *Config) (*Client, error) {
	conn, err := clickhouse.Open(&clickhouse.Options{
		Addr: config.Brokers,
		Auth: clickhouse.Auth{
			Database: config.AuthConfig.Database,
			Username: config.AuthConfig.Username,
			Password: config.AuthConfig.Password,
		},
		// Debug:           true,
		DialTimeout:      time.Duration(config.DialTimeoutSeconds) * time.Second,
		MaxOpenConns:     config.MaxOpenConns,
		MaxIdleConns:     config.MaxIdleConns,
		ConnMaxLifetime:  time.Duration(config.ConnMaxLifetimeSeconds) * time.Second,
		ConnOpenStrategy: clickhouse.ConnOpenRoundRobin,
		Compression: &clickhouse.Compression{
			Method: clickhouse.CompressionZSTD,
		},
		Settings: config.Settings,
	})
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name": "clickhouse_broker_connection",
		}).Error("Got error while connecting to clickhouse brokers")
		return nil, err
	}
	return &Client{
		connection: conn,
		config:     config,
	}, nil
}

type AuthConfig struct {
	Database string
	Username string
	Password string
}

func (client *Client) ExecuteSQL(ctx context.Context, tableName string, query string) (driver.Rows, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "ClickHouse/ExecuteSQL", tracer.WithSpanKind(tracer.SpanKindClient))
		ctx = tctx
		defer span.End()
	}
	err := ensureConnection(ctx, client.connection)
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name": "clickhouse_broker_connection",
			"table_name": tableName,
			"query":      query,
		}).Error("Got error while connecting to clickhouse brokers")
		return nil, err
	}
	rows, err := client.connection.Query(ctx, query)
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name": "clickhouse_execute_query",
			"table_name": tableName,
			"query":      query,
		}).Error("Got error while executing the query")
		return rows, err
	}
	return rows, nil
}

func ensureConnection(ctx context.Context, conn clickhouse.Conn) error {
	if err := conn.Ping(ctx); err != nil {
		return err
	}
	return nil
}
