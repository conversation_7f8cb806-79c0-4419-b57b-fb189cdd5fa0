package crypto

import (
	"context"
	"crypto/aes"
	"encoding/base64"
	"errors"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

type DecryptInputParam struct {
	CipherText   string
	SourceColumn string
}

var (
	ErrConfigNotFound error = errors.New("aes config not found for the source column")
)

func (client *Client) Decrypt(ctx context.Context, inputParam *DecryptInputParam) (string, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "crypto/Decrypt", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	decyptionConfig, ok := client.config.DecryptionMap[inputParam.SourceColumn]
	if !ok {
		log.FromContext(ctx).WithError(ErrConfigNotFound).Error("Failed to get AES Key")
		return "", ErrConfigNotFound
	}
	cipherKey := decyptionConfig.AESKey

	cipherText, err := base64.StdEncoding.DecodeString(inputParam.CipherText)
	if err != nil {
		tracer.NoticeError(ctx, err)
		log.FromContext(ctx).WithError(err).Error("Failed to base64 decode")
		return "", err
	}
	cipher, err := aes.NewCipher([]byte(cipherKey))
	if err != nil {
		tracer.NoticeError(ctx, err)
		log.FromContext(ctx).WithError(err).Error("Failed to get cipher client")
		return "", err
	}

	pt := make([]byte, len(cipherText))
	cipher.Decrypt(pt, cipherText)

	padAmount := int(pt[len(pt)-1])
	if padAmount > len(pt) {
		return "", errors.New("invalid padding")
	}
	return string(pt[:len(pt)-padAmount]), nil
}
