package log

import (
	"context"

	log "github.com/Zomato/go/logger"
	"github.com/google/uuid"
	"google.golang.org/grpc"
)

type Metadata<PERSON>ey string

const (
	// TraceIDKey is tagged in all the incoming request
	TraceIDKey MetadataKey = "x-request-id"
)

// UnaryServerInterceptor returns a new unary server interceptor that adds trace-id to the context.
func TraceIDServerInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		traceID := uuid.New().String()
		tags := map[MetadataKey]string{
			TraceIDKey: traceID,
		}
		ctx = GetContextWithLogger(ctx, tags)
		ctx = context.WithValue(ctx, TraceIDKey, traceID)
		// Call the handler
		h, err := handler(ctx, req)
		return h, err
	}
}

// GetContextWithLogger Function adds Logger to the context so that request level fields could easily be logged
func GetContextWithLogger(parentContext context.Context, fields map[Metada<PERSON>Key]string) context.Context {
	var inputFields []log.Field
	for key, value := range fields {
		inputFields = append(inputFields, log.String(string(key), value))
	}
	return log.ContextWithFields(parentContext, inputFields...)
}
