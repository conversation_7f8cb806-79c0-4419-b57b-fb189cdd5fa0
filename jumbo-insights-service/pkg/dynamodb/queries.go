package dynamodb

import (
	"context"
	"errors"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/util"
	"github.com/aws/aws-sdk-go-v2/aws"
	dynamodbattribute "github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/expression"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

type Keys struct {
	PartitionKey string `json:"partition_key" dynamodbav:"partition_key"`
	SortKey      string `json:"sort_key" dynamodbav:"sort_key"`
}

type GetItemParams struct {
	KeyCondition Keys
}

type UpdateItemParams struct {
	ValuesToBeUpdated map[string]interface{}
	ValuesToBeRemoved []string
	KeyCondition      Keys
}

type BatchGetItemParams struct {
	KeyCondition   []Keys
	ProjectionKeys []string
}

type QueryItemsParams struct {
	ProjectionKeys   []string
	FilterParamsList []ItemParams
	PartitionKey     string
	SortKey          ItemParams
}

type ItemParams struct {
	KeyName       string
	ConditionName string
	Value         interface{}
}

type DeleteItemParams struct {
	KeyCondition Keys
}
type QueryItemRequest struct {
	Expression        expression.Builder `validate:"required"`
	Limit             int32
	ConsistentRead    bool
	ExclusiveStartKey map[string]types.AttributeValue
	ScanIndexForward  bool
}
type ResponseParams struct {
	TemplateId string `json:"partition_key" dynamodbav:"partition_key"`
}

const VERSION = "1"

func (client *Client) GetItem(ctx context.Context, inputRequest GetItemParams, outputItem interface{}) (interface{}, error) {
	row, err := marshalMap(ctx, inputRequest.KeyCondition)
	if err != nil {
		return nil, err
	}
	input := &dynamodb.GetItemInput{
		TableName: aws.String(client.table),
		Key:       row,
	}
	result, err := client.getItem(ctx, input)
	if err != nil {
		return nil, err
	}
	err = unmarshalMap(ctx, result.Item, outputItem)
	return outputItem, err
}

func (client *Client) PutItem(ctx context.Context, values interface{}) error {
	row, err := marshalMap(ctx, values)
	if err != nil {
		return err
	}
	input := &dynamodb.PutItemInput{
		Item:                row,
		TableName:           aws.String(client.table),
		ConditionExpression: aws.String("attribute_not_exists(partition_key)"),
	}
	_, err = client.putItem(ctx, input)
	return err
}

func (client *Client) UpdateItem(ctx context.Context, inputRequest *UpdateItemParams, outputItem interface{}) error {
	expr, err := makeUpdateExpression(inputRequest)
	if err != nil {
		return err
	}

	key, err := marshalMap(ctx, inputRequest.KeyCondition)

	if err != nil {
		return err
	}

	queryInput := &dynamodb.UpdateItemInput{
		TableName:                 aws.String(client.table),
		Key:                       key,
		UpdateExpression:          expr.Update(),
		ExpressionAttributeValues: expr.Values(),
		ExpressionAttributeNames:  expr.Names(),
		ReturnValues:              types.ReturnValueAllNew,
	}

	res, err := client.updateItem(ctx, queryInput)

	if err != nil {
		return err
	}

	if outputItem != nil {
		err = unmarshalMap(ctx, res.Attributes, outputItem)
	}

	return err
}

func makeUpdateExpression(inputRequest *UpdateItemParams) (expression.Expression, error) {
	update := expression.UpdateBuilder{}
	if inputRequest.ValuesToBeUpdated != nil {
		inputRequest.ValuesToBeUpdated["last_updated"] = util.GetCurrentTimeString()
	} else {
		inputRequest.ValuesToBeUpdated = map[string]interface{}{
			"last_updated": util.GetCurrentTimeString(),
		}
	}

	for key, val := range inputRequest.ValuesToBeUpdated {
		update = update.Set(
			name(key),
			value(val),
		)
	}

	for _, val := range inputRequest.ValuesToBeRemoved {
		update = update.Remove(
			name(val),
		)
	}

	expr, err := expression.NewBuilder().
		WithUpdate(update).
		Build()

	if err != nil {
		return expr, err
	}

	return expr, nil
}
func (client *Client) GetListTemplateFromDb(ctx context.Context, templateId string) ([]*models.DynamoTemplate, error) {
	expr, err := expression.NewBuilder().WithKeyCondition(
		expression.Key("partition_key").Equal(expression.Value(templateId)),
	).WithFilter(expression.Name("item_type").Equal(expression.Value("Template"))).Build()
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "build_expression_for_template_list",
			"templateId": templateId,
		}).Error("got the following error while building the dynamo expression")
		return nil, err
	}
	queryInput := &dynamodb.QueryInput{
		TableName:                 aws.String(client.table),
		KeyConditionExpression:    expr.KeyCondition(),
		ExpressionAttributeValues: expr.Values(),
		FilterExpression:          expr.Filter(),
		ExpressionAttributeNames:  expr.Names(),
		ScanIndexForward:          aws.Bool(false),
	}
	templates := []*models.DynamoTemplate{}
	queryOutput, err := client.queryItems(ctx, queryInput)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "query_list_template",
			"templateId": templateId,
		}).Error("got the following error while getting list of the template")
		return templates, err
	}
	// If no items found, return an error
	if len(queryOutput.Items) == 0 {
		TemplateNotFound := errors.New("no versions are there for this templateid")
		log.WithError(TemplateNotFound).WithFields(map[string]interface{}{
			"event_name": "get_list_template",
			"templateId": templateId,
		}).Error("no versions are there for this templateid")
		return templates, TemplateNotFound
	}

	// unmarshal result into array of templates
	for _, item := range queryOutput.Items {
		template := &models.DynamoTemplate{}
		err = dynamodbattribute.UnmarshalMap(item, &template)
		if err != nil {
			continue
		}
		templates = append(templates, template)
	}
	return templates, nil
}
func (client *Client) GetUniqueTemplateList(ctx context.Context) ([]ResponseParams, error) {
	// latest_version := fmt.Sprintf("%s_%s", config.GetString(ctx, "service.env"), "latest")
	expr, err := expression.NewBuilder().WithKeyCondition(
		expression.Key("sort_key").Equal(expression.Value(VERSION)),
	).WithFilter(expression.Name("item_type").Equal(expression.Value("Template"))).Build()
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "build_expression_for_unique_template_list",
		}).Error("got the following error while building the dynamo expression for unique template list")
		return nil, err
	}
	input := &dynamodb.QueryInput{
		TableName:                 aws.String(client.table),
		IndexName:                 aws.String("TemplateIdIndex"), // Query the GSI
		KeyConditionExpression:    expr.KeyCondition(),
		FilterExpression:          expr.Filter(),
		ProjectionExpression:      aws.String("partition_key"),
		ExpressionAttributeValues: expr.Values(),
		ExpressionAttributeNames:  expr.Names(),
		ScanIndexForward:          aws.Bool(false),
	}
	templateIds := []ResponseParams{}
	result, err := client.queryItems(ctx, input)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "query_list_template",
		}).Error("got the following error while getting list of the unique templates")
		return templateIds, err
	}
	for _, item := range result.Items {
		t := ResponseParams{}
		err = dynamodbattribute.UnmarshalMap(item, &t)
		if err != nil {
			log.WithError(err).WithFields(map[string]interface{}{
				"event_name": "unmarshaling_the_json",
				"templateId": t,
			}).Error("got the following error while unmarshalling the templateId")
			continue
		}
		templateIds = append(templateIds, t)
	}
	// Return the retrieved items and the LastEvaluatedKey for pagination
	return templateIds, nil

}
func (client *Client) PutItemTemplate(ctx context.Context, values interface{}) error {
	row, err := marshalMap(ctx, values)
	if err != nil {
		return err
	}
	input := &dynamodb.PutItemInput{
		Item:      row,
		TableName: aws.String(client.table),
	}
	_, err = client.putItem(ctx, input)
	return err
}
