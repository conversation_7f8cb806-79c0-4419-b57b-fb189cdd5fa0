package dynamodb

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/expression"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
)

type QueryParams struct {
	PrimaryKeyName  string
	PrimaryKeyValue interface{}
	SortKeyName     string
	SortKeyValue    interface{}
}

func (client *Client) GetItemFromGSI(ctx context.Context, inputRequest QueryParams, index string, outputItem interface{}) (interface{}, error) {
	primaryCond := expression.Name(inputRequest.PrimaryKeyName).Equal(expression.Value(inputRequest.PrimaryKeyValue))
	sortCond := expression.Name(inputRequest.SortKeyName).Equal(expression.Value(inputRequest.SortKeyValue))
	filt := expression.Name("item_type").Equal(expression.Value("Query"))
	expr, err := newBuilder().WithCondition(primaryCond).WithCondition(sortCond).WithFilter(filt).Build()

	if err != nil {
		return nil, err
	}

	input := &dynamodb.QueryInput{
		TableName:                 aws.String(client.table),
		IndexName:                 aws.String(index),
		KeyConditionExpression:    expr.Condition(),
		FilterExpression:          expr.Filter(),
		ExpressionAttributeValues: expr.Values(),
		ExpressionAttributeNames:  expr.Names(),
	}

	queryOutput, err := client.queryItems(ctx, input)
	if err != nil {
		return nil, err
	}

	if len(queryOutput.Items) == 0 {
		return outputItem, nil
	}

	err = unmarshalMap(ctx, queryOutput.Items[0], outputItem)

	return outputItem, err
}

func (client *Client) QueryItemsFromGSI(ctx context.Context, inputRequest QueryParams, index string, outputItem interface{}) (interface{}, error) {
	primaryCond := expression.Name(inputRequest.PrimaryKeyName).Equal(expression.Value(inputRequest.PrimaryKeyValue))
	if inputRequest.SortKeyName != "" {
		primaryCond = primaryCond.And(expression.Name(inputRequest.SortKeyName).Equal(expression.Value(inputRequest.SortKeyValue)))
	}
	filt := expression.Name("item_type").Equal(expression.Value("Query"))

	expr, err := newBuilder().WithCondition(primaryCond).WithFilter(filt).Build()
	if err != nil {
		return nil, err
	}

	input := &dynamodb.QueryInput{
		TableName:                 aws.String(client.table),
		IndexName:                 aws.String(index),
		KeyConditionExpression:    expr.Condition(),
		FilterExpression:          expr.Filter(),
		ExpressionAttributeValues: expr.Values(),
		ExpressionAttributeNames:  expr.Names(),
	}

	queryOutput, err := client.queryItems(ctx, input)
	if err != nil {
		return nil, err
	}

	err = unmarshalListOfMaps(ctx, queryOutput.Items, outputItem)

	return outputItem, err
}
