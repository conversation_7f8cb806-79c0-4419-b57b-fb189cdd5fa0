package dynamodb

import (
	"context"
	"time"

	awsconfig "github.com/Zomato/go/aws/config"
	log "github.com/Zomato/go/logger"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/aws/retry"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/expression"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

type Config struct {
	Endpoint        string
	EndpointNeeded  bool
	Region          string
	MaxRetries      int
	MaxRetryBackoff int
	Debug           bool
	TableName       string
}

type Client struct {
	dynamoDB *dynamodb.Client
	config   *Config
	table    string
}

func NewClient(ctx context.Context, cfg *Config) (*Client, error) {
	clientLogMode := aws.LogDeprecatedUsage | aws.LogRetries
	if cfg.Debug {
		clientLogMode |= aws.LogRequest | aws.LogResponse
	}

	dynamoCfg, err := awsconfig.LoadDefaultConfig(ctx, config.WithRegion(cfg.Region),
		config.WithRetryMaxAttempts(cfg.MaxRetries),
		config.WithClientLogMode(clientLogMode),
	)

	if err != nil {
		return nil, err
	}

	if cfg.EndpointNeeded {
		dynamoCfg.EndpointResolverWithOptions = aws.EndpointResolverWithOptionsFunc(
			func(service, region string, options ...interface{}) (aws.Endpoint, error) {
				return aws.Endpoint{
					URL:           cfg.Endpoint,
					SigningRegion: cfg.Region,
				}, nil
			},
		)
	}

	if cfg.MaxRetries > 1 {
		dynamoCfg.Retryer = func() aws.Retryer {
			return retry.NewStandard(func(o *retry.StandardOptions) {
				o.MaxAttempts = cfg.MaxRetries
				o.MaxBackoff = time.Duration(cfg.MaxRetryBackoff) * time.Millisecond
			})
		}
	}
	return &Client{
		dynamoDB: dynamodb.NewFromConfig(dynamoCfg),
		config:   cfg,
		table:    cfg.TableName,
	}, nil
}

func (client *Client) queryItems(ctx context.Context, input *dynamodb.QueryInput) (*dynamodb.QueryOutput, error) {
	response, err := client.dynamoDB.Query(ctx, input)
	return response, err
}
func (client *Client) putItem(ctx context.Context, input *dynamodb.PutItemInput) (*dynamodb.PutItemOutput, error) {
	response, err := client.dynamoDB.PutItem(ctx, input)
	return response, err
}

func (client *Client) getItem(ctx context.Context, input *dynamodb.GetItemInput) (*dynamodb.GetItemOutput, error) {
	response, err := client.dynamoDB.GetItem(ctx, input)
	return response, err
}

func (client *Client) updateItem(ctx context.Context, input *dynamodb.UpdateItemInput) (*dynamodb.UpdateItemOutput, error) {
	response, err := client.dynamoDB.UpdateItem(ctx, input)
	return response, err
}

func marshalMap(ctx context.Context, item interface{}) (map[string]types.AttributeValue, error) {
	attributeValue, err := attributevalue.MarshalMap(item)
	if err != nil {
		log.FromContext(ctx).Errorf("Error while marshalling item : %v", err.Error())
		return nil, err
	}
	return attributeValue, nil
}

func unmarshalMap(ctx context.Context, value map[string]types.AttributeValue, item interface{}) error {
	err := attributevalue.UnmarshalMap(value, item)
	if err != nil {
		log.FromContext(ctx).Errorf("Error while unmarshaling result from dynamoDb: %v", err.Error())
		return err
	}
	return nil
}

func name(name string) expression.NameBuilder {
	return expression.Name(name)
}

func value(value interface{}) expression.ValueBuilder {
	return expression.Value(value)
}

func newBuilder() expression.Builder {
	return expression.NewBuilder()
}

func unmarshalListOfMaps(ctx context.Context, value []map[string]types.AttributeValue, items interface{}) error {
	err := attributevalue.UnmarshalListOfMaps(value, items)
	if err != nil {
		log.FromContext(ctx).WithError(err).Errorf("Error while marshalling item")
		return err
	}
	return nil
}

// func NewQueryItemRequest(
// 	scanIndexForward bool,
// 	expr expression.Builder,
// 	consistentRead bool,
// 	exclusiveStartKey map[string]types.AttributeValue,
// ) *QueryItemRequest {
// 	return &QueryItemRequest{
// 		Expression:        expr,
// 		ConsistentRead:    consistentRead,
// 		ExclusiveStartKey: exclusiveStartKey,
// 		ScanIndexForward:  scanIndexForward,
// 	}
// }
