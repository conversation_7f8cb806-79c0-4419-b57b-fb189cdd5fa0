package pinot

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"time"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/util"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
	pinot "github.com/startreedata/pinot-client-go/pinot"
)

type Auth struct {
	Enabled     bool
	User        string
	Password    string
	HttpHeaders map[string]string
	Host        string
}

type Config struct {
	Broker       string
	Auth         *Auth
	Version      string
	MaxRetries   int
	RetryDelayMS int
}

// CustomTransport is a wrapper around http.RoundTripper that sets a default Host header
type CustomTransport struct {
	Transport http.RoundTripper
	Host      string
}

// RoundTrip implements the RoundTripper interface to set the custom Host header
func (t *CustomTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	// Set the custom Host header
	req.Host = t.Host
	return t.Transport.RoundTrip(req)
}

func ExecuteSQL(ctx context.Context, tableName string, query string, config *Config) (*pinot.BrokerResponse, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Pinot/ExecuteSQL", tracer.WithSpanKind(tracer.SpanKindClient))
		ctx = tctx
		defer span.End()
	}

	var brokerResp *pinot.BrokerResponse

	uri := config.Broker
	if config.Auth.Enabled {
		encodedPassword := url.QueryEscape(config.Auth.Password)
		uri = fmt.Sprintf("https://%s:%s@%s", config.Auth.User, encodedPassword, config.Broker)
	}

	client := &http.Client{}
	if config.Auth.Host != "" {
		// This is needed since startree client does not support setting custom Host header
		customTransport := &CustomTransport{
			Transport: http.DefaultTransport,
			Host:      config.Auth.Host,
		}

		// Create an HTTP client with the custom transport
		client = &http.Client{
			Transport: customTransport,
		}
	}

	pinotClient, err := pinot.NewWithConfigAndClient(
		&pinot.ClientConfig{
			BrokerList:      []string{uri},
			ExtraHTTPHeader: config.Auth.HttpHeaders,
		},
		client,
	)
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name": "pinot_broker_connection",
			"table_name": tableName,
			"query":      query,
		}).Error("Got error while connecting to pinot brokers")
		return nil, err
	}

	retryCount := 0
	for {
		brokerResp, err = pinotClient.ExecuteSQL(tableName, query)
		switch {
		case err != nil:
			log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
				"event_name": "pinot_execute_query",
				"table_name": tableName,
				"query":      query,
			}).Error("Got error while executing the query")
			return brokerResp, err
		case brokerResp.Exceptions != nil && isTransientBrokerException(brokerResp.Exceptions) && retryCount < config.MaxRetries:
			log.FromContext(ctx).WithFields(map[string]interface{}{
				"event_name":  "transient_broker_exceptions",
				"table_name":  tableName,
				"exceptions":  brokerResp.Exceptions,
				"retry_count": retryCount + 1,
			}).Warn("Got error while executing the query, sleeping for 10ms")
			retryCount++
			time.Sleep(time.Duration(config.RetryDelayMS) * time.Millisecond)
		case brokerResp.Exceptions != nil && len(brokerResp.Exceptions) > 0:
			log.FromContext(ctx).WithError(util.ErrPinotBrokerResponseException).WithFields(map[string]interface{}{
				"event_name": "broker_response_exceptions",
				"table_name": tableName,
				"exceptions": brokerResp.Exceptions,
			}).Error("Got error while executing the query")
			return brokerResp, util.ErrPinotBrokerResponseException
		case brokerResp.ResultTable == nil:
			return nil, util.ErrNoResult
		case len(brokerResp.ResultTable.Rows) == 0:
			return brokerResp, util.ErrNoResult
		default:
			return brokerResp, nil
		}
	}
}

func isTransientBrokerException(exceptions []pinot.Exception) bool {
	for _, pinotException := range exceptions {
		if pinotException.ErrorCode == 200 {
			return true
		}
	}
	return false
}
