syntax = "proto3";

package zomato.jumboinsightsservice.template.v1;

option go_package = "github.com/Zomato/jumbo-insights-service/template/v1";
option php_namespace = "Zomato\\JumboInsightsService\\Template\\V1";
option php_metadata_namespace = "Zomato\\JumboInsightsService\\Metadata\\Template\\V1";
option java_multiple_files = true;
option java_package = "com.zomato.jumboinsightsservice.template.v1";
option java_outer_classname = "TemplateProto";
option ruby_package = "Zomato::JumboInsightsService::Template::V1";
import "jumbo-insights-service/common/common.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "jumbo-insights-service/querier/v1/querier.proto";


// TenantType holds all the values of tenants
enum Tenant {
  // used when tenant field is unknown
  TENANT_TYPE_UNSPECIFIED = 0;
  // Zomato tenant
  ZOMATO = 1;
  // Hyperpure tenant
  HYPERPURE = 2;
  // BLINKIT tenant
  BLINKIT = 3;
}
message Template {
  // Basic contract information
  string template_id = 1; // This will be pk of dynamodb
  string template_version = 2; // This will be sk of dynamodb
  TemplateStatus template_status = 3;
  Tenant tenant = 4;         // e.g., "Zomato"
  
  // Table config
  TableConfig table_config = 5;

  // Audit information for tracking contract ownership and purpose
  AuditInfo audit = 6;
  
  // Performance and caching configurations
  PerformanceConfig performance = 7;
  
  // Query configuration
  QueryConfig query = 8;
  
  // Optional decryption configuration
  repeated DecryptionConfig decryption = 9;
    // timestamp of when template is created
  google.protobuf.Timestamp added_at = 10;
  //columns
  repeated Column columns=11;
  //aggregations
  repeated string aggregations=12;
}

// TemplateStatus has enum values of status
enum TemplateStatus {
  TEMPLATE_STATUS_UNSPECIFIED = 0;
  DEV = 1;
  PREPROD = 2;
  PROD = 3;
  UNUSED = 4;
}
//Column
message Column {
  //name
  string name=1;
  //function name
  string func=2;
  // source column
  string source_column=3;
}

// Table config
message TableConfig {
  TableBackend backend = 1;  // e.g., "pinot", "trino"
  string catalog = 2;
  string schema = 3;
  string table = 4;          // e.g., "zomato_chat_agent_metrics"
}

// AuditInfo contains metadata about contract ownership and purpose
message AuditInfo {
  string author_email = 1;      // e.g., "<EMAIL>"
  string team_email = 2;        // e.g., "<EMAIL>"
  string service = 3;           // e.g., "chat-v2-backend-service"
  string pd_service_name = 4;   // e.g., "chat-backend-v2-service"
  string description = 5;       // e.g., "Avg metric value for agents of vendors"
}

// PerformanceConfig defines caching and rate limiting parameters
message PerformanceConfig {
  // All time values are in milliseconds unless otherwise specified
  int32 caching_ttl = 1;        // Time to live for cache in seconds
  int32 refresh_interval = 2;   // Refresh interval in seconds, 0 to disable
  int32 sla = 3;               // Service level agreement in milliseconds
  int32 rate_limit = 4;        // Maximum requests per second
}

// QueryConfig contains the actual query definition and filters
message QueryConfig {
  string type = 1;             // e.g., "sql"
  string sql = 2;              // The actual SQL query
  repeated string filters = 3;  // List of filter parameters
}

// DecryptionConfig specifies columns that need decryption
message DecryptionConfig {
  string sql_column = 1;       // Column name in SQL result
  string source_column = 2;    // Source column for decryption
}
// Tablebackend Enum 
enum TableBackend{
  TABLE_BACKEND_UNSPECIFIED=0;
  PINOT=1;
  TRINO=2;
}
// RegisterTemplateRequest holds details to create new template
message RegisterTemplateRequest {
  // template holds details of new template
  Template template = 1;
}

// RegisterTemplateResponse consists of the results of RegisterTemplate RPC
message RegisterTemplateResponse {
  // status of RegisterTemplate RPC
  zomato.jumboinsightsservice.common.Status status = 1;
  //template_id
  string template_id=2;
  // template_version
  string template_version=3;
}

// GetTemplateContentsRequest object consists of required details to get
// template's contents
message GetTemplateRequest {
  // ID of required template
  string template_id = 1;
  //version of template
  string template_version=2;
}

// GetTemplateContentsResponse consists of results of GetTemplateContents RPC
message GetTemplateResponse {
  // status of GetTemplateContents RPC call
  zomato.jumboinsightsservice.common.Status status = 1;
  // template holds all the template details
  Template template = 2;
}

// UpdateTemplateRequest object consists of template struct used for UpdateTemplate rpc
message UpdateTemplateRequest {
  Template template = 1;
}

// UpdateTemplateResponse object consists of UpdateTemplate RPC Response
message UpdateTemplateResponse {
  // status of UpdateTemplate RPC call
  zomato.jumboinsightsservice.common.Status status = 1;
  //new template version
  string template_version=2;
  //template id
  string template_id=3;
}
message ListTemplatesRequest {

}

// ListTemplatesResponse object consists of templates listed according to
// given parameters
message ListTemplatesResponse {
  // status of ListTemplates rpc call
  zomato.jumboinsightsservice.common.Status status = 1;
  // array of templates listed
  repeated string template_id = 2;
}

message PreviewTemplateRequest{
  //template
  Template template=1; 
  //
  zomato.jumboinsightsservice.querier.v1.Querier querier=2;
  
}

message PreviewTemplateResponse{
  //status of run query request
  zomato.jumboinsightsservice.common.Status status = 1;
  // template_id 
  string template_id=2;
}

message MoveToProdRequest {
  //template id
  string template_id=1;
  //template version
  string template_version=2;
}

message MovetoProdResponse {
  zomato.jumboinsightsservice.common.Status status = 1;
  string template_version=2;
  string template_id=3;
}
message ListTemplatesByIdRequest{
  //template_id for getting all ther version of this id
  string template_id=1;
}
message ListTemplatesByIdResponse{
  //
  zomato.jumboinsightsservice.common.Status status = 1;
  //list templates
  repeated Template templates=2;
}
//
message SyncYamlTemplatesRequest{

}
message SyncYamlTemplatesResponse{
  //
  zomato.jumboinsightsservice.common.Status status = 1;
}

// message Row {
//   repeated zomato.jumboinsightsservice.common.AnyValue columns = 1;
// }

// TemplateService service consists of all rpcs related to templates
service TemplateService {
  // RegisterTemplate rpc saves the given template in database
  rpc RegisterTemplate(RegisterTemplateRequest) returns(RegisterTemplateResponse);
  // GetTemplateContents rpc returns details of requested template
  rpc GetTemplate(GetTemplateRequest) returns(GetTemplateResponse);
  // UpdateTemplate rpc updates the given template with given details
  rpc UpdateTemplate(UpdateTemplateRequest) returns(UpdateTemplateResponse);
  // ListTemplates rpc lists the templates
  rpc ListTemplates (ListTemplatesRequest) returns(ListTemplatesResponse);
  // Run the query
  rpc PreviewTemplate(PreviewTemplateRequest) returns (PreviewTemplateResponse);
  //movetoprod
  rpc MoveToProd(MoveToProdRequest) returns (MovetoProdResponse);
  //
  rpc ListTemplatesById(ListTemplatesByIdRequest) returns (ListTemplatesByIdResponse);
  //
  rpc SyncYamlTemplates(SyncYamlTemplatesRequest) returns (SyncYamlTemplatesResponse) ;

}
