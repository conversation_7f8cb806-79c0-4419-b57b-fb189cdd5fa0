syntax = "proto3";

package zomato.jumboinsightsservice.common;

option go_package = "github.com/Zomato/jumbo-insights-service-client-golang/common";
option php_namespace = "Zomato\\JumboInsightsService\\Common";
option php_metadata_namespace = "Zomato\\JumboInsightsService\\Metadata\\Common";

// AnyValue is used to represent any type of attribute value. AnyValue may contain a
// primitive value such as a string or integer or it may contain an arbitrary nested
// object containing arrays, key-value lists and primitives.
message AnyValue {
  // The value is one of the listed fields. It is valid for all values to be unspecified
  // in which case this AnyValue is considered to be "empty".
  oneof value {
    // string value 
    string string_value = 1;
    // bool value 
    bool bool_value = 2;
    // int value 
    int64 int_value = 3;
    // double value
    double double_value = 4;
    // array value
    ArrayValue array_value = 5;
    // map value
    KeyValueList kvlist_value = 6;
    // byte value
    bytes bytes_value = 7;
    // between value
    BetweenValue between_value = 8;
    // uint value
    uint64 uint_value = 9;
  }
}

// ArrayValue is a list of AnyValue messages. We need ArrayValue as a message
// since oneof in AnyValue does not allow repeated fields.
message ArrayValue {
  // Array of values. The array may be empty (contain 0 elements).
  repeated AnyValue values = 1;
}

// BetweenValue is a message of from and to AnyValue message. 
message BetweenValue {
  // from_value contains the from value field of between op
  AnyValue from_value = 1;
  // to_value contains the to value field of between op
  AnyValue to_value = 2;
}

// KeyValueList is a list of KeyValue messages. We need KeyValueList as a message
// since `oneof` in AnyValue does not allow repeated fields. Everywhere else where we need
// a list of KeyValue messages (e.g. in Span) we use `repeated KeyValue` directly to
// avoid unnecessary extra wrapping (which slows down the protocol). The 2 approaches
// are semantically equivalent.
message KeyValueList {
  // A collection of key/value pairs of key-value pairs. The list may be empty (may
  // contain 0 elements).
  // The keys MUST be unique (it is not allowed to have more than one
  // value with the same key).
  repeated KeyValue values = 1;
}

// KeyValue is a key-value pair that is used to store Span attributes, Link
// attributes, etc.
message KeyValue {
  string key = 1;
  AnyValue value = 2;
}