syntax = "proto3";

package zomato.jumboinsightsservice.common;

option go_package = "github.com/Zomato/jumbo-insights-service-client-golang/common";
option php_namespace = "Zomato\\JumboInsightsService\\Common";
option php_metadata_namespace = "Zomato\\JumboInsightsService\\Metadata\\Common";

// Status conveys the status of RPC call
message Status {
  // state denotes the state of RPC call
  bool state = 1;
  // message consists of important acknowledgements/ errors
  // returned from RPC call
  string message = 2;
  // error_code denotes the code of error, if encountered
  int32 error_code = 3;
}