syntax = "proto3";

package zomato.jumboinsightsservice.querier.v1;

option go_package = "github.com/Zomato/jumbo-insights-service-client-golang/querier/v1";
option php_namespace = "Zomato\\JumboInsightsService\\Querier\\V1";
option php_metadata_namespace = "Zomato\\JumboInsightsService\\Metadata\\Querier\\V1";

import "jumbo-insights-service/common/anyvalue.proto";
import "jumbo-insights-service/common/common.proto";

// The filter to apply.
message Filter {

  // as use-cases come up we can add more operators, ex: like, in etc.
  enum ComparisonOperator {
    EQUALS = 0;
    NOT_EQUALS = 1;
    GREATER_THAN = 2;
    LESS_THAN = 3;
    IN = 4;
    NOT_IN = 5;
    BETWEEN = 6;
  }

  // field name on which the filter expression will be applied
  string field = 1;
  // The operator to filter by.
  ComparisonOperator op = 2;
  // The value to compare the property to.
  zomato.jumboinsightsservice.common.AnyValue value = 3;
}
// table names used in the query
message TableName {
  // name of the table variable
  string name = 1;

  // value of the table variable
  string value = 2;
}

// Time range on which to perform the desired insights
message Interval {
  // The start time of the interval
  // This will be in epoch seconds
  int64 start_time = 1;
  // The end time of the interval
  // This will be in epoch seconds
  int64 end_time = 2;
  // name of the time series field
  string datetime_field = 3;
}

// Querier contain the query params
message Querier {
  // The filter to apply.
  repeated Filter filters = 1;
  // Time range on which to perform the desired insights
  Interval interval = 2;
  // table names used in the query
  repeated TableName table_names=3;
}

// Pagination options for making list requests
message PaginationOptions {
  // the max number of results to return. Default max is 100k
  int32 limit = 1;
  // the property that we want to sort the results by
  string sort_key = 2;
  // the sort order of the results
  SortOrder sort_order = 3;
  // the token to pass to fetch the next page
  int32 page_token = 4;

  enum SortOrder {
    DESC = 0;
    ASC = 1;
  }
}

// QueryRequest consists of all details required to perform the insight
message QueryRequest {
  // client_id refers to service which requested to QueryRequest
  string client_id = 1;
  // Use a query query_id for which you want to retrieve the artifacts
  string query_id = 2;
  // Querier contain the query params
  Querier querier = 3;
  // Pagination options to get a page of artifacts
  PaginationOptions pagination_options = 4;
  //contract version
  string query_version=5;
}

// MessageSchema is response schema
message MessageSchema {
  // list of column data type based on the given query
  repeated string column_schema = 1;
  // list of column name based on the given query
  repeated string column_name = 2;
}

// Row contains the array of values of all columns 
message Row {
  repeated zomato.jumboinsightsservice.common.AnyValue columns = 1;
}

message ResultTable {
  // The list of rows
  repeated Row rows = 1;
  // MessageSchema is response schema
  MessageSchema schema = 2;
}

message QueryResponse {
  // status gives the status of RPC call
  zomato.jumboinsightsservice.common.Status status = 1;
  // ResultTable contains the result 
  ResultTable result = 2;
  // If next page is available then this will contain the next page token value
  int32 next_token = 4;
}

// QuerierService service consists of all querier service RPC
service QuerierService {
  // Get insight with the given details
  rpc Query(QueryRequest) returns(QueryResponse);
}