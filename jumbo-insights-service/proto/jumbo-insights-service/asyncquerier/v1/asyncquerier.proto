syntax = "proto3";

package zomato.jumboinsightsservice.asyncquerier.v1;

option go_package = "github.com/Zomato/jumbo-insights-service-client-golang/asyncquerier/v1";
option php_namespace = "Zomato\\JumboInsightsService\\AsyncQuerier\\V1";
option php_metadata_namespace = "Zomato\\JumboInsightsService\\Metadata\\AsyncQuerier\\V1";

import "jumbo-insights-service/common/common.proto";
import "jumbo-insights-service/querier/v1/querier.proto";
import "google/protobuf/timestamp.proto";


// Represents the different states of query
enum QueryState {
  // Default query state if unspecified
  QUERY_STATE_UNSPECIFIED = 0;
  // State of query when the service has inserted it in the table but not in sqs
  SUBMITTED = 1;
  // State of query when the service has enqueued it in the SQS for the workers to pick
  ENQUEUED = 2;
  // State of query when the worker is running it
  RUNNING = 3;
  // State of query when it has finished execution
  FINISHED = 4;
  // State of query when it has failed
  FAILED = 5;
}

// QueryExecutionInfo contains the information about the query
message QueryExecutionInfo {
  // Unique identifier for which the req is made
  string user = 1;
  // execution_id returns the unique ID of the query that ran as a result of this request.
  string execution_id = 2;
  // state of the query
  QueryState query_state = 3;
  // s3_presigned_link contains the presigned s3 url of the data
  string s3_presigned_link = 4;
  // Timestamp that the query was submitted
  google.protobuf.Timestamp submission_time = 5;
  // Timestamp that the query was completed(Either failure/success)
  google.protobuf.Timestamp completion_time = 6;
  // Error contain error string
  string error = 7;
}

// ListAsyncQueryRequest contains the details required to get the active requests
message ListAsyncQueryRequest {
  // client_id refers to service which requested to QueryRequest
  string client_id = 1;
  // Use a entity_id for which you want to retrieve the artifacts
  string user = 2;
}

// GetAllAsyncQueryResponse contains the active/successfull requests 
message ListAsyncQueryResponse {
  // status gives the status of RPC call
  zomato.jumboinsightsservice.common.Status status = 1;
  // state of the query
  repeated QueryExecutionInfo query_execution_info = 2;
}

// AsyncQueryRequest consists of all details required to perform the insight
message AsyncQueryRequest {
  // client_id refers to service which requested to QueryRequest
  string client_id = 1;
  // Use a query contract_id for which you want to retrieve the artifacts
  string contract_id = 2;
  // Querier contain the query params
  zomato.jumboinsightsservice.querier.v1.Querier querier = 3;
  // Pagination options to get a page of artifacts
  zomato.jumboinsightsservice.querier.v1.PaginationOptions pagination_options = 4;
  // AsyncQueryContext contains the client side req metadata
  AsyncQueryContext query_context = 5;
  //contract version
  string contract_version=6;
}

// AsyncQueryContext contains the client side req metadata
message AsyncQueryContext {
  // Unique identifier for which the req is made
  string user = 1;
  // File name contains the file name of the zip/csv file
  string file_name = 2;
  // Metadata contains the req metadata
  map<string,string> metadata = 3;
  // Result type
  ResultType result_type = 5;

  enum ResultType {
    // Default result type if unspecified
    RESULT_TYPE_UNSPECIFIED = 0;
    // CSV 
    CSV = 1;
    // ZIP
    ZIP = 2;
  }
}

// AsyncResponse returns the query information about query
message AsyncResponse {
  // status gives the status of RPC call
  zomato.jumboinsightsservice.common.Status status = 1;
  // state of the query
  QueryExecutionInfo query_execution_info = 2;
}

// ResultRequest consists of all details required to fetch the result
message GetQueryResultRequest {
  // client_id refers to service which requested to QueryRequest
  string client_id = 1;
  // Use a query contract_id for which you want to retrieve the artifacts
  string contract_id = 2;
  // execution_id of the query for which you want to retrive the information
  string execution_id = 3;
  //Contract version
  string contract_version=4;
}

// QuerierService service consists of all querier service RPC
service AsyncQuerierService {
  // Get insight with the given details
  rpc ListAsyncQueries(ListAsyncQueryRequest) returns(ListAsyncQueryResponse);
  // Get insight with the given details
  rpc AsyncQuery(AsyncQueryRequest) returns(AsyncResponse);
  // Get insight with the given details
  rpc GetQueryResult(GetQueryResultRequest) returns(AsyncResponse);
}