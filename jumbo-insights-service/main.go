package main

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/Zomato/go/authz"
	"github.com/Zomato/go/config"
	"github.com/Zomato/go/grpc"
	"github.com/Zomato/go/health"
	jumbo "github.com/Zomato/go/jumbo-v2"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/metrics"
	"github.com/Zomato/go/profiler"
	"github.com/Zomato/go/redis"

	// "github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/app"
	"github.com/Zomato/jumbo-insights-service/internal/env"
	metric "github.com/Zomato/jumbo-insights-service/internal/metrics"
	"github.com/Zomato/jumbo-insights-service/internal/serde"
	"github.com/Zomato/jumbo-insights-service/pkg/clickhouse"
	"github.com/Zomato/jumbo-insights-service/pkg/crypto"
	"github.com/Zomato/jumbo-insights-service/pkg/dynamodb"
	"github.com/Zomato/jumbo-insights-service/pkg/pinot"
	"github.com/Zomato/jumbo-insights-service/pkg/ratelimiter"
	"github.com/Zomato/jumbo-insights-service/pkg/s3"
	"github.com/Zomato/jumbo-insights-service/pkg/sqs"
	"github.com/Zomato/jumbo-insights-service/pkg/tracer"
	"github.com/Zomato/jumbo-insights-service/pkg/trino"
	"github.com/Zomato/jumbo-insights-service/service"
	"github.com/spf13/cast"

	"github.com/Zomato/go/grpc/middlewares"
	zruntime "github.com/Zomato/go/runtime"
	asyncquerypb "github.com/Zomato/jumbo-insights-service-client-golang/asyncquerier/v1"
	querypb "github.com/Zomato/jumbo-insights-service-client-golang/querier/v1"
	templatepb "github.com/Zomato/jumbo-insights-service-client-golang/template/v1"
	jislog "github.com/Zomato/jumbo-insights-service/pkg/log"
)

func GetConfigAsMap(ctx context.Context, configKey string) map[string]interface{} {
	configMap := cast.ToStringMap(config.Get(ctx, "."))
	for _, value := range strings.Split(configKey, ".") {
		configMap = configMap[value].(map[string]interface{})
	}
	return configMap
}

func main() {
	// initialise config
	config.Init()
	ctx, err := config.TODOContext()
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("error while getting context from appconfig")
	}
	serviceName := config.GetString(ctx, "service.name")
	// port := 8001

	// configure logging
	err = log.Initialize(
		log.Formatter(config.GetString(ctx, "log.format")),
		log.Level(config.GetString(ctx, "log.level")),
	)

	if err != nil {
		log.FromContext(ctx).WithError(err).Panic("failed to initialise logger client")
	}

	defer func() {
		// to ensure buffered logs are written before app exit
		if ferr := log.FromContext(ctx).Sync(); ferr != nil {
			log.FromContext(ctx).WithError(ferr).Error("failed to sync logs via logger client during app exit")
		}
	}()

	// initialise zruntime
	statsdPath := config.GetString(ctx, "otel_collector.host") + ":" + config.GetString(ctx, "otel_collector.port")
	zruntime.Init(
		zruntime.WithGoMemLimitEnabled(config.GetBool(ctx, "go_runtime.mem_limit.enabled")),
		zruntime.WithGCMetricsCollectorEnabled(config.GetBool(ctx, "go_runtime.metrics.enabled"), statsdPath),
		zruntime.WithGOGC(config.GetInt(ctx, "go_runtime.mem_limit.go_gc_percentage")),
	)

	// initialise tracer
	tracer.InitialiseTracer(ctx)

	// initialise redis
	rdb, err := redis.NewClient(&redis.Config{
		Hosts:                         config.GetStringSlice(ctx, "redis.hosts"),
		ClusterMode:                   config.GetBool(ctx, "redis.cluster_mode"),
		ServeReadsFromMasterAndSlaves: config.GetBool(ctx, "redis.serve_reads_from_master_and_slaves"),
	})
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("Redis Cluster ping failed")
	}

	_, err = rdb.Ping(ctx).Result()
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("Redis Cluster ping failed")
	}

	port := config.GetUint64(ctx, "service.port")

	metricErr := metrics.Initialize(metrics.Config{
		Name:                  serviceName,
		OtelCollectorAddr:     statsdPath,
		OtelCollectorProtocol: metrics.GRPC,
	})
	if metricErr != nil {
		log.FromContext(ctx).WithError(metricErr).Error("failed to initialise metrics client")
	}
	metric.RegisterMetrics()

	// Initialise JumboV2 Client
	jumboClient, jumboErr := jumbo.NewClient(ctx,
		jumbo.WithClientID(serviceName),
		jumbo.WithOtelSidecardAddr(config.GetString(ctx, "otel_collector.host")+":"+config.GetString(ctx, "otel_collector.port")),
	)
	if jumboErr != nil {
		log.FromContext(ctx).WithError(jumboErr).Error("Failed to initialise Jumbo")
	}

	// Rate limiter client
	ratelimiterClient := ratelimiter.NewClient(&ratelimiter.Config{
		BurstTraffic: config.GetInt(ctx, "ratelimiter.burst_traffic"),
	})
	// SQS client
	sqsClient, err := sqs.NewClient(ctx, &sqs.Config{
		EndpointNeeded:    config.GetBool(ctx, "sqs.endpoint_needed"),
		Endpoint:          config.GetString(ctx, "sqs.endpoint"),
		Debug:             config.GetBool(ctx, "sqs.debug"),
		Region:            config.GetString(ctx, "sqs.region"),
		MaxRetries:        config.GetInt(ctx, "sqs.max_retries"),
		WaitTimeout:       config.GetInt32(ctx, "sqs.wait_timeout"),
		MaxMessages:       config.GetInt32(ctx, "sqs.max_messages"),
		VisibilityTimeout: config.GetInt32(ctx, "sqs.visibility_timeout"),
		WorkerPool:        config.GetInt32(ctx, "sqs.worker_pool"),
	})
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("Failed to initialise sqs client")
	}

	// Dynamodb Client
	dynamoDBClient, err := dynamodb.NewClient(ctx, &dynamodb.Config{
		TableName:       config.GetString(ctx, "dynamodb.table_name"),
		EndpointNeeded:  config.GetBool(ctx, "dynamodb.endpoint_needed"),
		Endpoint:        config.GetString(ctx, "dynamodb.endpoint"),
		Debug:           config.GetBool(ctx, "dynamodb.debug"),
		Region:          config.GetString(ctx, "dynamodb.region"),
		MaxRetries:      config.GetInt(ctx, "dynamodb.max_retries"),
		MaxRetryBackoff: config.GetInt(ctx, "dynamodb.max_retry_backoff"),
	})
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("Failed to initialise dynamodb client")
	}

	// S3 Client
	s3Client, err := s3.NewClient(ctx, &s3.Config{
		Region:              config.GetString(ctx, "s3.region"),
		ForcePathStyle:      config.GetBool(ctx, "s3.force_path_style"),
		EndpointRequired:    config.GetBool(ctx, "s3.endpoint_required"),
		Endpoint:            config.GetString(ctx, "s3.endpoint"),
		UploaderPartSize:    config.GetInt64(ctx, "s3.uploader_part_size_mb"),
		UploaderConcurrency: config.GetInt(ctx, "s3.uploader_concurrency"),
		Debug:               config.GetBool(ctx, "s3.debug"),
	})
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("Failed to initialise s3 client")
	}

	log.Info("Initialising query engine configs")
	// TODO: Fetch tenants dynamically from appconfig for respective services
	// config.Get() unable to get map from appconfig
	tenants := []string{"blinkit", "zomato", "zanalytics", "nugget-analytics"}

	trinoConfigs := make(map[string]*trino.Config)

	for _, tenant := range tenants {
		tenantConfig := fmt.Sprintf("trino.%s.host", tenant)
		prefix := fmt.Sprintf("trino.%s.", tenant)
		if !config.Exists(ctx, tenantConfig) {
			log.FromContext(ctx).WithFields(map[string]interface{}{
				"tenant":       tenant,
				"query_engine": "trino",
			}).Warn("Tenant config does not exist")
			continue
		}

		trinoConfigs[tenant] = &trino.Config{
			Host: config.GetString(ctx, prefix+"host"),
			Port: config.GetInt(ctx, prefix+"port"),
			Auth: &trino.Auth{
				Enabled:  config.GetBool(ctx, prefix+"auth.enabled"),
				User:     config.GetString(ctx, prefix+"auth.user"),
				Password: config.GetString(ctx, prefix+"auth.password"),
			},
			Catalog:               config.GetString(ctx, prefix+"catalog"),
			Schema:                config.GetString(ctx, prefix+"schema"),
			Source:                config.GetString(ctx, prefix+"source"),
			QueryMaxExecutionTime: config.GetString(ctx, prefix+"session_properties.query_max_execution_time"),
		}
	}

	pinotConfigs := make(map[string]*pinot.Config)

	for _, tenant := range tenants {
		tenantConfig := fmt.Sprintf("pinot.%s.broker", tenant)
		prefix := fmt.Sprintf("pinot.%s.", tenant)

		if !config.Exists(ctx, tenantConfig) {
			log.FromContext(ctx).WithFields(map[string]interface{}{
				"tenant":       tenant,
				"query_engine": "pinot",
			}).Warn("Tenant config does not exist")
			continue
		}

		headersString := config.GetString(ctx, prefix+"auth.http_headers")
		headersMap := make(map[string]string)
		for _, h := range strings.Split(headersString, ",") {
			parts := strings.SplitN(h, ":", 2)
			if len(parts) == 2 {
				headersMap[strings.TrimSpace(parts[0])] = strings.TrimSpace(parts[1])
			}
		}

		pinotConfigs[tenant] = &pinot.Config{
			Broker: config.GetString(ctx, prefix+"broker"),
			Auth: &pinot.Auth{
				Enabled:     config.GetBool(ctx, prefix+"auth.enabled"),
				User:        config.GetString(ctx, prefix+"auth.user"),
				Password:    config.GetString(ctx, prefix+"auth.password"),
				HttpHeaders: headersMap,
				Host:        config.GetString(ctx, prefix+"auth.host"),
			},
			Version:      config.GetString(ctx, prefix+"version"),
			MaxRetries:   config.GetInt(ctx, prefix+"max_retries"),
			RetryDelayMS: config.GetInt(ctx, prefix+"retry_delay_ms"),
		}
	}
	clickhouseClient := make(map[string]*clickhouse.Client)
	for _, tenant := range tenants {
		tenantConfig := fmt.Sprintf("clickhouse.%s.brokers", tenant)
		prefix := fmt.Sprintf("clickhouse.%s.", tenant)

		if !config.Exists(ctx, tenantConfig) {
			log.FromContext(ctx).WithFields(map[string]interface{}{
				"tenant":       tenant,
				"query_engine": "clickhouse",
			}).Warn("Tenant config does not exist")
			continue
		}
		settingsMap := GetConfigAsMap(ctx, prefix+"settings")
		clickhouseConfig := &clickhouse.Config{
			Brokers: config.GetStringSlice(ctx, prefix+"brokers"),
			AuthConfig: &clickhouse.AuthConfig{
				Database: config.GetString(ctx, prefix+"auth.database"),
				Username: config.GetString(ctx, prefix+"auth.username"),
				Password: config.GetString(ctx, prefix+"auth.password"),
			},
			DialTimeoutSeconds:     config.GetInt(ctx, prefix+"dial_timeout"),
			ConnMaxLifetimeSeconds: config.GetInt(ctx, prefix+"conn_max_lifetime"),
			MaxOpenConns:           config.GetInt(ctx, prefix+"max_open_conn"),
			MaxIdleConns:           config.GetInt(ctx, prefix+"max_idle_conn"),
			Settings:               settingsMap,
		}
		clickhouseClient[tenant], err = clickhouse.NewClient(ctx, clickhouseConfig)
		if err != nil {
			log.FromContext(ctx).WithFields(map[string]interface{}{
				"tenant": tenant,
			}).WithError(err).Error("Failed to initialise clickhouse client for tenant")
		}

	}

	cryptoClient := crypto.NewClient(&crypto.Config{
		DecryptionMap: serde.InitDecryptionConfig(ctx),
	})

	environment := env.NewEnv(
		env.WithRedisConnection(rdb),
		env.WithQueryContracts(serde.InitQueryContractMap(ctx)),
		env.WithJumboClient(jumboClient),
		env.WithRateLimiterClient(ratelimiterClient),
		env.WithSQSClient(sqsClient),
		env.WithDynamoDBClient(dynamoDBClient),
		env.WithS3Client(s3Client),
		env.WithTrinoConfigs(trinoConfigs),
		env.WithCryptoClient(cryptoClient),
		env.WithPinotConfigs(pinotConfigs),
		env.WithClickhouseClients(clickhouseClient),
	)

	if config.GetBool(ctx, "datadog.profiling.enabled") {
		profiler.Initialize(
			ctx,
			profiler.WithProfilerType(profiler.ProfilerTypeDatadog), // datadog profiler
			profiler.WithGoroutineProfiling(config.GetBool(ctx, "datadog.profiling.goroutine_profile.enabled")),
		)
	}

	svc := service.Service{}
	authValidator := grpc.NewAuthValidator().
		WithIssuer(config.GetString(ctx, "jwt.iss")).
		WithPublickKeys(config.GetStringSlice(ctx, "jwt.keys"))

	authzInterceptor := authz.NewAuthorizationInterceptor().
		WithHostAndPort(ctx, config.GetString(ctx, "authz_server.host"),
			config.GetString(ctx, "authz_server.port")).
		WithAllowedRPCs(ctx, []interface{}{
			"/zomato.jumboinsightsservice.template.v1.TemplateService/MoveToProd",
			"/zomato.jumboinsightsservice.template.v1.TemplateService/RegisterTemplate",
			"/zomato.jumboinsightsservice.template.v1.TemplateService/GetTemplate",
			"/zomato.jumboinsightsservice.template.v1.TemplateService/UpdateTemplate",
			"/zomato.jumboinsightsservice.template.v1.TemplateService/ListTemplates",
			"/zomato.jumboinsightsservice.template.v1.TemplateService/PreviewTemplate",
			"/zomato.jumboinsightsservice.template.v1.TemplateService/ListTemplatesById",
		})

	srv := grpc.NewServer(grpc.ServerConfig{
		ListenAddr: ":" + strconv.FormatUint(port, 10),
		UnaryInterceptors: []grpc.UnaryServerInterceptor{
			env.UnaryServerInterceptor(environment),
			config.GrpcInterceptor(),
			middlewares.PanicRecoveryUnaryInterceptor(),
			authValidator.AuthenticationUnaryServerInterceptor(),
			authzInterceptor.AuthorizationUnaryServerInterceptor(),
			jislog.TraceIDServerInterceptor(),
		},
	})
	templatepb.RegisterTemplateServiceServer(srv, svc)
	querypb.RegisterQuerierServiceServer(srv.Server, svc)
	asyncquerypb.RegisterAsyncQuerierServiceServer(srv.Server, svc)

	health.RegisterHealthServer(srv.Server, []string{
		serviceName,
	})
	// start reflection
	// reflection.Register(srv.Server)

	// start the server
	ctx = environment.WithContext(ctx)
	zapp := app.NewApp()
	err = zapp.WithGRPCServer(srv).
		WithMode(os.Getenv("APP_MODE")).
		Run(ctx)
	if err != nil {
		defer log.FromContext(ctx).Panicf("could not start: %s", err)
	}
}
