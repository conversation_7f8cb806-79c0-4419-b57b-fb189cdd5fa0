service:
  port: 7001
  name: "jumbo-insights-service"
  env: dev
contract:
  query_contract: "./configs/contracts/query"
  contract_type: yaml
  caching_ttl: 100
  refresh_interval: 100

log:
  level: "info"
  format: "json"

redis:
  cluster_mode: false
  hosts: "redis:6379"
  serve_reads_from_master_and_slaves: true
  enabled: true

profiler:
  port: 6060
  enabled: false
  credentials: ""
  goroutine_profile:
    enabled: false
  block_profile:
    enabled: false
  mutex_profile:
    enabled: false
  gc_runs:
    disabled: true

tracer:
  cost:
    enabled: false
    fallback_to_dd: false
  enabled: false
  debug: true
  env: local
  service:
    name: jumbo-insights-service
  agent:
    addr: "localhost:8126"

datadog:
  profiling:
    enabled: 'false'
    goroutine_profile:
      enabled: 'false'

otel_collector:
  enabled: false
  host: "otlp-collector"
  port: 4317

pinot:
  blinkit:
    broker: proxy.broker.pinot.internal.dev1.grofers.startree.cloud:443
    auth:
      enabled: true
      user: "63712d7fa9fb46d886587390bf64e575"
      password: "Q/IYeGA2D4WrM5wLcDVyqQBOpDpNgl9MY7Fd0scd0YE="
      http_headers: ""
      host: ""
    version: "startree-dev-pinot"
    max_retries: 3
    retry_delay_ms: 10
  zomato:
    broker: pinot-broker:8099
    auth:
      enabled: false
      user: "user"
      password: "password"
      http_headers: ""
      host: ""
    version: "0.11.0"
    max_retries: 3
    retry_delay_ms: 10

querier:
  page_size: 1000
  max_rows: 1000000

jumbo:
  enabled: true

ratelimiter:
  burst_traffic: 500
  enabled: true

sqs:
  endpoint_needed: true
  endpoint: "http://jumbo-insights-service-localstack:4566"
  region: "ap-south-1"
  max_retries: 1
  debug: false
  wait_timeout: 20
  max_messages: 1
  visibility_timeout: 60
  worker_pool: 3

s3:
  bucket: "dev-jumbo-insights-service"
  endpoint_required: true
  endpoint: "http://jumbo-insights-service-localstack:4566"
  region: "ap-south-1"
  force_path_style: true
  uploader_part_size_mb: 5
  uploader_concurrency: 5
  debug: false
  presign_time_hours: 2

queues:
  trino:
    url: "http://jumbo-insights-service-localstack:4566/000000000000/dev-jumbo-insights-service-trino-queue"

dynamodb:
  endpoint: "http://jumbo-insights-service-dynamodb:8000"
  endpoint_needed: true
  region: "ap-southeast-1"
  max_retries: 1
  max_retry_backoff: 100
  debug: true
  table_name: "dev-jumbo-insights-service"
  ttl_hours: 24

trino:
  blinkit:
    host: blinkit-reporting-trino.internal.zomans.com
    port: 443
    auth:
      enabled: true
      user: bi_redash
      password: 7KmfvKATApg8
    catalog: blinkit
    schema: default
    source: jumbo-insights-service
    session_properties:
      query_max_execution_time: "5m"
  zomato:
    host: etl-presto.internal.zomato.farm
    port: 8889
    auth:
      enabled: false
      user: jumbo
      password: "password"
    catalog: hive
    schema: jumbo_dashboards
    source: jumbo-insights-service
    session_properties:
      query_max_execution_time: "15m"

crypto:
  decryption:
    - source_column: zomato4.number_verification
      aes_key: "**********"

go_runtime:
  mem_limit:
    enabled: false
    go_gc_percentage: -1
  metrics:
    enabled: false
jwt:
  iss: accounts.eks.zdev.net
  keys: abcd1234@MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEHGrvUesdB3igsKg5EpE2mV3isFflOgZWoavEtosa6ClsKF0OyL4Z0xgeYVT6IAAkq4Xl5lZcw2OzLWAA8vJOvQ==
authz_server:
  host: localhost
  port: '3000'
clickhouse:
  zomato:
    brokers:
      - "clickhouse:9000"
    auth:
      database: "default"
      username: "default"
      password: ""
    dial_timeout: 40
    max_open_conn: 20
    max_idle_conn: 10
    conn_max_lifetime: 600
    settings:
      max_execution_time: 60
  zanalytics:
    brokers:
      - "clickhouse:9000"
    auth:
      database: "default"
      username: "default"
      password: ""
    dial_timeout: 40
    max_open_conn: 20
    max_idle_conn: 10
    conn_max_lifetime: 600
    settings:
      max_execution_time: 60
  nugget-analytics:
    brokers:
      - "clickhouse:9000"
    auth:
      database: "monitoring"
      username: "monitoring"
      password: "XXXXX"
    dial_timeout: 40
    max_open_conn: 50
    max_idle_conn: 20
    conn_max_lifetime: 400
    settings:
      max_execution_time: 60
      use_query_cache: 1
      query_cache_ttl: 300
