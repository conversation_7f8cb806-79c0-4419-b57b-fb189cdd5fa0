receivers:
  otlp:
    protocols:
      grpc:
        endpoint: "0.0.0.0:4317"  # Default OTLP gRPC port
      http:
        endpoint: "0.0.0.0:4318"

processors:
  batch:
    send_batch_size: 1000  # Optional: adjust batch settings as needed
    timeout: 1s

exporters:
  prometheus:
    endpoint: "0.0.0.0:9103"       # Endpoint where Prometheus will scrape metrics
    namespace: "calculator_service"    # Set your desired namespace/prefix here
    const_labels:
      otlp: "true"
    send_timestamps: true
    metric_expiration: 10m
    enable_open_metrics: true
    add_metric_suffixes: false
    resource_to_telemetry_conversion:
      enabled: true
  debug:
    verbosity: detailed
    sampling_thereafter: 200

service:
  pipelines:
    metrics:
      receivers: [otlp]
      #processors: [batch]
      exporters: [debug, prometheus]
  telemetry:
    metrics:
      level: detailed
    logs:
      level: "debug"
      encoding: "console"  # Optional: "console" or "json"
      # Include other options like sampling, output paths, etc.