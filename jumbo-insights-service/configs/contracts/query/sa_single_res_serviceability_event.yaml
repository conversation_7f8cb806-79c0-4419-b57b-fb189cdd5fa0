# can be pinot/trino
query_backend: trino
tenant: Zomato
catalog: hive
schema: jumbo2
table: sa_single_res_serviceability_events
identifier: sa_single_res_logs
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: serviceability-aggregator
  pd_service_name: serviceability-aggregator-service
  description: serviceability debugger dashboard

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT checkpoint,
      city_id,
      drop_cell_id,
      dsz_id,
      final_status_code,
      has_shown_serviceable,
      is_zero_salience_applicable,
      json_format(CAST(logs_metadata AS JSON)) AS logs_metadata,
      array_join(transform(lp_statuses, x -> json_format(CAST(x AS JSON))), ', ') AS lp_statuses,
      merchant_status_code,
      serviceability_level,
      serviceability_reason,
      serviceability_type,
      session_id,
      time,
      user_lat,
      user_lng,
      array_join(user_subscription_type,',') AS user_subscription_type,
      res_id,
      user_id
      FROM {{.table}}
      WHERE dt={{.date}}
      AND (checkpoint={{.checkpoint}}
      OR {{.checkpoint}} = 'all')
      AND ({{.user_id}} = 'all'
      OR user_id={{.user_id}})
      AND (res_id={{.res_id}}
      OR {{.res_id}} = -1)
      AND ({{.start_time}} = 0
      OR time > {{.start_time}} )
      AND ({{.end_time}} = 0
      OR time < {{.end_time}} )
      AND (final_status_code = {{.final_status_code}}
      OR {{.final_status_code}} = 4000)
filters:
  - res_id
  - start_time
  - end_time
  - checkpoint
  - user_id
  - date
  - final_status_code
