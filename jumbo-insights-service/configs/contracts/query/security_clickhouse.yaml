# can be pinot/trino
query_backend: clickhouse
tenant: Zomato
table: zomato_api_gateway
identifier: security_clickhouse
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: zomato_api-gateway
  pd_service_name: zomato_api-gateway
  description: security_clickhouse

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: > 
  select
      DISTINCT ON (schema_violation['message.log.route'],schema_violation['message.log.method']) *
  from
     {{.table}}
  where
      schema_violation['message.log.level'] LIKE {{.level}}
      AND mapContains(schema_violation, 'message.log.route') = 1
      AND toDateTime(ts) BETWEEN FROM_UNIXTIME({{.ts_from}}) AND FROM_UNIXTIME({{.ts_to}})
  order by
      ts
filters:
  - ts_from
  - ts_to
  - level
