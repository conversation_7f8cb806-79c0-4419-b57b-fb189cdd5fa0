query_backend: pinot
tenant: Zomato
table: dining_ads_res_insights
identifier: dineout_res_visits_pack_stats
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: dining-service
  pd_service_name: dining-service
  description: stats for dineout merchant dashboard

# Time in second
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# Dimensions
type: sql
sql: >
  SELECT *
  FROM {{.table}}
  WHERE res_id = {{.res_id}}
  AND key = {{.key}}
  AND dt IN ({{.dt}})

filters:
  - res_id
  - key
  - dt

