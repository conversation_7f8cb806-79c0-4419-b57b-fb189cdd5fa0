 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: rider_daily_metrics
identifier: fc_weekly_active_fleet_distribution
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Get active fleet distribution of fleetcoach mapped drivers
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select count(delivery_driver_id) as all_dps,
  sum(
    case when
      (lifecycle_status = 'active')
      then 1 else 0 end
  ) as active,
  sum(
    case when
      (dp_type = '21 day churn')
      then 1 else 0 end
  ) as churn21d
  from {{.table}}
  where dt = {{.dt}}
  and zone_id = {{.zone_id}}
  and fleet_coach_id = {{.fleet_coach_id}}
filters:
  - dt
  - zone_id
  - fleet_coach_id
