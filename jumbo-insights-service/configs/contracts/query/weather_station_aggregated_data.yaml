# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: weather_station_events
identifier: weather_station_aggregated_data
audit:
  author_email: aman.sri<PERSON><PERSON><EMAIL>
  team_email: <EMAIL>
  service: weather-service
  pd_service_name: delivery-rules
  description: Weather Station Aggregated data
# Time in seconds
caching_ttl: 3600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 900
# Time in ms
sla: 100
# Request per second
rate_limit: 160
# Dimensions
columns:
  - name: max_temperature_deg_c
    func: max
    source_column: current_temperature_deg_c
  - name: min_temperature_deg_c
    func: min
    source_column: current_temperature_deg_c
  - name: max_wind_speed_ms
    func: max
    source_column: wind_speed_ms
  - name: avg_wind_speed_ms
    func: avg
    source_column: wind_speed_ms  
  - name: max_pressure_hpa
    func: max
    source_column: pressure_hpa
  - name: max_humidity_perc
    func: max
    source_column: current_humidity_perc  
  - name: min_humidity_perc
    func: min
    source_column: current_humidity_perc    
  - name: total_rainfall_accumulation
    func: max
    source_column: rainfall_accumulation_mm        
# Group by columns
aggregations:
  - device_serial_number
filters:
  - report_time
  - device_serial_number
