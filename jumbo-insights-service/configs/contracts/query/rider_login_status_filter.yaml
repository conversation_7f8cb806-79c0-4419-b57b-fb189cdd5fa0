 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: rider_hourly_metrics
identifier: rider_login_status_filter
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Filter riders based on their login status
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT DISTINCT(delivery_driver_id), login_status
  FROM {{.table}}
  WHERE login_status IN ({{.login_status}})
  AND delivery_driver_id IN ({{.delivery_driver_ids}})
  AND dt = ({{.date}})
  AND hour = ({{.hour}})
filters:
  - date
  - login_status
  - delivery_driver_ids
  - hour
