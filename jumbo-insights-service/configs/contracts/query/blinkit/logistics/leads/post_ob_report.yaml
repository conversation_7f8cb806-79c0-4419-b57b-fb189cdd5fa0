# can be pinot/trino
query_backend: trino
tenant: Blinkit
catalog: zomato
schema: insights_etls
table: blinkit_rbr_post_ob_data
identifier: runnr-buddy-blinkit-leads-post-ob-report
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: lead-management-service
  pd_service_name: lead-management-service
  description: Get runnr buddy leads blinkit post-ob report
# Time in second
# caching not implemented in async querier
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 60000 # one minute
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
    creator as user_id,
    user_id as lead_id,
    phone_no_encrypted as driver_phone_number,
    lead_date,
    vendor_name,
    source,
    driver_id,
    name,
    runnr_city,
    blinkit_store_name,
    onboarding_date,
    carrier_id,
    lifecycle_status,
    qc_status,
    qc_rejection_reason,
    bgv_status,
    bgv_completed_date,
    offline_verification_date,
    first_order_date,
    last_order_date,
    a_20_order_date,
    b_40_order_date,
    c_60_order_date,
    d_100_order_date,
    e_120_order_date,
    f_150_order_date,
    g_200_order_date,
    lifetime_orders,
    ob_lead_days,
    fod_store,
    first_slot_booking_time,
    last_slot_booking_time,
    nominee_filled,
    training_completed,
    realtime_qc_status,
    realtime_qc_reason,
    qc_time,
    qc_created_at,
    qc_updated_at,
    referred_dt
  FROM {{.table}}
  WHERE creator IN ({{.user_ids}})
    AND referred_dt >= {{.start_date}}
    AND referred_dt <= {{.end_date}}
  ORDER BY referred_dt DESC
filters:
  - user_ids
  - start_date
  - end_date
decryption:
  - sql_column: driver_phone_number
    source_column: mongo_driver_onboarding_service_production.mongo_v3_onboarding_applications

