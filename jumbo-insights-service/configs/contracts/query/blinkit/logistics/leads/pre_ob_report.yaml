# can be pinot/trino
query_backend: trino
tenant: Blinkit
catalog: zomato
schema: insights_etls
table: blinkit_rbr_pre_ob_data
identifier: runnr-buddy-blinkit-leads-pre-ob-report
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: lead-management-service
  pd_service_name: lead-management-service
  description: Get runnr buddy blinkit leads pre-ob report
# Time in second
# caching not implemented in async querier
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 60000 # one minute
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT 
    creator as user_id,
    lead_id,
    phone_number_encrypted as referred_phone_number,
    referred_date,
    vendor_name,
    name,
    source,
    city_name,
    lead_ageing_days,
    app_download_date,
    in_app_ageing_days,
    last_app_section_update_date,
    store,
    status,
    signup,
    vehicle,
    city,
    store_selected,
    pan_details,
    bank_details,
    selfie,
    ob_fee_paid,
    calling_priority,
    onboarding_app_type
  FROM {{.table}}
  WHERE creator IN ({{.user_ids}})
    AND referred_dt >= {{.start_date}}
    AND referred_dt <= {{.end_date}}
  ORDER BY referred_dt DESC
filters:
  - user_ids
  - start_date
  - end_date
decryption:
  - sql_column: referred_phone_number
    source_column: dynamodb.prod_lead_management_service
