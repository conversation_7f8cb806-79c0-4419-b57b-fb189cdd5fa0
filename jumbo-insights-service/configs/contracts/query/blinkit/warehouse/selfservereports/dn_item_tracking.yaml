query_backend: trino
tenant: Blinkit
table: dn_item_attribution
identifier: dn_item_tracking
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: DN Items Tracking Report
# TTL for cache, Time in seconds
caching_ttl: 3600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 60000 # 60s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
    with 

    base as (
        select 
            sto_date,
            sto_id,
            outlet_id,
            outlet_name,
            store_id,
            store_name,
            demand_id,
            scheduled_dispatch_time,
            invoice_date,
            invoice_id,
            item_id,
            item_name,
            item_l0_category,
            item_l1_category,
            item_l2_category,
            billed_qty,
            dispatch_qty,
            grn_qty,
            dn_short_qty,
            dn_other_qty,
            open_dn_short_qty,
            b2b_qty,
            billed_amt,
            dispatch_amt,
            grn_amt,
            dn_short_amt,
            dn_other_amt,
            open_dn_short_amt,
            b2b_amt,
            landing_price,
            dn_raised_time,
            json_format(cast(array_agg(outbound_container_id) as json)) as outbound_container_ids,
            json_format(cast(map_agg(outbound_container_id, outbound_container_state) as json)) as outbound_container_id_state,
            json_format(cast(map_agg(outbound_container_id, sorted_ts) as json)) as outbound_container_id_sorted_ts,
            json_format(cast(map_agg(outbound_container_id, transit_container_state) as json)) as outbound_container_id_transit_container_state,
            json_format(cast(map_agg(outbound_container_id, transit_container_updated_at) as json)) as outbound_container_id_transit_container_updated_ts,
            listagg(distinct cast(pick_list_id as varchar), ', ') within group (order by cast(pick_list_id as varchar)) as pick_list_id,
            listagg(distinct cast(picking_zone_identifier as varchar), ', ') within group (order by cast(picking_zone_identifier as varchar)) as picking_zone_identifier,
            listagg(distinct cast(packaging_process_type as varchar), ', ') within group (order by cast(packaging_process_type as varchar)) as packaging_process_type,
            listagg(distinct cast(picker_id as varchar), ', ') within group (order by cast(picker_id as varchar)) as picker_id,
            listagg(distinct cast(packer_id as varchar), ', ') within group (order by cast(packer_id as varchar)) as packer_id,
            listagg(distinct cast(driver_id as varchar), ', ') within group (order by cast(driver_id as varchar)) as driver_id,
            listagg(distinct cast(truck_number as varchar), ', ') within group (order by cast(truck_number as varchar)) as truck_number,
            listagg(distinct cast(consignment_id as varchar), ', ') within group (order by cast(consignment_id as varchar)) as consignment_id
        from {{.table}}
        where invoice_date between date({{.start_date}}) AND date({{.end_date}})
        and outlet_id = {{.outlet_id}}
        and dn_short_qty > 0
        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31
    )

    select *
    from base
filters:
  - outlet_id
  - start_date
  - end_date
