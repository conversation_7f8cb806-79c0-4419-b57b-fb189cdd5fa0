query_backend: trino
tenant: Blinkit
table: pre_pkg_crate_details
identifier: picklist_item_container_mapping
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: picklist item to container mapping
# TTL for cache, Time in seconds
caching_ttl: 2400
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 1200
# Time in ms
sla: 40000 # 40s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select distinct
    p.pick_list_id,
    p.date,
    p.outlet_id,
    p.frontend_outlet_id,
    p.shift, 
    p.picking_zone_identifier, 
    p.inbound_container_id,
    p.pick_list_item_id, 
    p.item_id, 
    p.variant_id,
    p.item_name,
    p.picker_id,
    wu.name as picker_name,
    p.picked_qty
  from 
    {{.table}} as p
  left join 
    retail.warehouse_user wu ON wu.emp_id = p.picker_id
  where 
    date BETWEEN date({{.start_date}}) AND date({{.end_date}})
    and outlet_id = {{.outlet_id}}

filters:
  - outlet_id
  - start_date
  - end_date
