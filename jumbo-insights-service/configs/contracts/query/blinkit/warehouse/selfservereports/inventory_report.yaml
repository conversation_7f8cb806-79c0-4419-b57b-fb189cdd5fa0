query_backend: trino
tenant: Blinkit
table: ims_inventory
identifier: blinkit_warehouse_inventory_report
catalog: blinkit
schema: ims
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: ims inventory report
# TTL for cache, Time in seconds
caching_ttl: 3600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 120000 # 120s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
   i.outlet_id as outlet_id,
   r.item_id as item_id,
   i.upc_id as upc_id,
   i.variant_id as variant_id,
   r.name as item_name,
   case
      WHEN r.outlet_type = 1 THEN 'F&V type'
      WHEN r.outlet_type = 2 THEN 'Grocery type'
         ELSE 'Not Assigned'
    end as item_type,
   i.quantity as quantity,
   i.selling_price as sp,
   r.variant_mrp as mrp,
   i.active as active,
   i.created_at as created_at,
   i.updated_at as updated_at
  from
  {{.table}} i
  join
   rpc.product_product r ON r.variant_id = i.variant_id
   where i.outlet_id = {{.outlet_id}}
   and r.active=1
filters:
  - outlet_id
  - start_date
  - end_date
  
