query_backend: trino
tenant: Blinkit
table: wh_inventory_placement_item_wise
identifier: inventory_placement_item_level_retail_reports
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Latest snapshot of item level inventory placement
# TTL for cache, Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 40000 # 40s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select
    outlet_id,
    outlet_name,
    item_id,
    item_name,
    l0_category,
    l1_category,
    shelf_life,
    inward_guidelines,
    outward_guidelines,
    handling_type,
    storage_type,
    zone_type,
    business_type,
    spr,
    case_flag,
    food_type,
    toxic,
    status,
    entities_tagged,
    dimensions,
    weight,
    volumetric_weight,
    floor,
    current_zone,
    mapped_zones,
    current_inv,
    doi,
    locations,
    batches,
    variants,
    entities,
    avg_inventory_30days,
    avg_inbound_frequency
  from
    {{.table}}
  where
    outlet_id  = {{.outlet_id}}
    and process_ts >= ( select date(max(process_ts)) as max_dt from {{.table}} where  outlet_id = {{.outlet_id}} )
filters:
  - outlet_id
