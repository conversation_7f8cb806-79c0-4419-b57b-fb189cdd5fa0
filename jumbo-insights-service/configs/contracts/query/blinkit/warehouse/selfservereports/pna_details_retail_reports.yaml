query_backend: trino
tenant: Blinkit
table: picklist_item_details
identifier: pna_details_retail_reports
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Product not found details for a WH
# TTL for cache, Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 30000 # 30s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select 
    date sto_date, 
    shift,
    outlet_id,
    outlet_name,
    pick_list_item_id,
    item_id,
    item_name,
    zone_identifier as zone,
    picking_zone_identifier,
    item_scan_ts,
    pna_quantity
  from {{.table}}
  where 
    date between date({{.start_date}}) and date({{.end_date}})
    and outlet_id = {{.outlet_id}}
    and pick_list_type = 'IRT_PICK_LIST'
    and pna_quantity >0
filters:
  - outlet_id
  - start_date
  - end_date
