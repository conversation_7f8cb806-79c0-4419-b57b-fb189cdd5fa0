query_backend: trino
tenant: Blinkit
table: ims_inventory_log
identifier: blinkit_warehouse_bad_stock_report
catalog: blinkit
schema: ims
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Get bad stock report as per the parameters
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 180000 # 180s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT 
      table1.*, 
      Mrp * quantity AS Total_mrp, 
      weighted_landing_price * quantity AS Total_landing_Price, 
      cost_price * quantity AS Total_cost_price, 
      (weighted_landing_price * quantity) - (cost_price * quantity) AS Tax 
  FROM (
      SELECT 
          r.item_id AS item,
          i.variant_id AS variant_id,
          i.upc_id AS upc,
          r.name AS Product,
          (CASE 
              WHEN r.outlet_type = 1 THEN 'F&V_type'
              WHEN r.outlet_type = 2 THEN 'Grocery_type'
              ELSE 'Not Assigned' 
          END) AS "Item Type",
          SUM(i.delta) AS quantity,
          r.variant_mrp AS Mrp,
          r.variant_uom_text AS grammage,
          i.outlet_id AS outlet,
          ro.name AS Outlet_name,
          (i.pos_timestamp AT TIME ZONE 'Asia/Kolkata') AS POS_Timestamp,
          i2.name AS Update_type,
          ibur.name,
          i.created_by_name AS created,
          r.is_pl,
          i.weighted_lp AS "weighted_landing_price",
          COALESCE(tax.cess, 0) AS cess,
          COALESCE(tax.cgst, 0) AS cgst,
          COALESCE(tax.sgst, 0) AS sgst,
          i.weighted_lp / (
              1 + (
                  (COALESCE(tax.cgst, 0) + COALESCE(tax.sgst, 0) + COALESCE(tax.cess, 0)) / 100
              )
          ) AS cost_price,
          ibil.remark 
      FROM ims.ims_inventory_log i
      INNER JOIN rpc.product_product r ON r.variant_id = i.variant_id
      INNER JOIN retail.console_outlet ro ON ro.id = i.outlet_id
      INNER JOIN ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id
      LEFT JOIN rpc.product_tax tax ON r.item_id = tax.item_id 
          AND ro.tax_location_id = tax.location
      LEFT JOIN ims.ims_bad_inventory_update_log ibil 
          ON i.inventory_update_id = ibil.inventory_update_id
      LEFT JOIN ims.ims_bad_update_reason ibur 
          ON ibil.reason_id = ibur.id
      WHERE 
          i.insert_ds_ist BETWEEN {{.start_date}} AND {{.end_date}}
          AND i.inventory_update_type_id IN (11, 12, 13, 64, 87, 88, 89)
          AND ro.id IN ({{.outlet_ids}})
      GROUP BY 
          1, 2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21
  ) table1
filters:
  - outlet_ids
  - start_date
  - end_date

