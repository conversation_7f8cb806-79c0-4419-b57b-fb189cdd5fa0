query_backend: trino
tenant: Blinkit
table: bad_stock_transaction_retail_reports
identifier: bad_stock_transaction_retail_reports
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Get bad stock transaction report in IMS
# TTL for cache, Time in seconds
caching_ttl: 1800
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 30000 # 30s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT 
    transaction_date_ist,
    outlet_id,
    outlet_name,
    item_id,
    variant_id,
    upc,
    product_name,
    item_type,
    mrp,
    grammage,
    transaction_ts_ist,
    update_type,
    name update_reason_name,
    created_by,
    weighted_landing_price,
    cost_price,
    quantity,
    total_mrp
  FROM {{.table}}
  WHERE 
    transaction_date_ist BETWEEN {{.start_date}} AND {{.end_date}}
    AND outlet_id = {{.outlet_id}}
filters:
  - outlet_id
  - start_date
  - end_date
