query_backend: trino
tenant: Blinkit
table: qc_discrepancy_packing_flow_retail_reports
identifier: qc_discrepancy_packing_flow_retail_reports
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Get qc discrepancy during packing 
# TTL for cache, Time in seconds
caching_ttl: 3600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 1500
# Time in ms
sla: 30000 # 180s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select * from 
  {{.table}}
  where date BETWEEN date({{.start_date}}) AND date({{.end_date}})
  and outlet_id = {{.outlet_id}}
filters:
  - outlet_id
  - start_date
  - end_date

