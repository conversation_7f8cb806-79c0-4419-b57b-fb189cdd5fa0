query_backend: trino
tenant: Blinkit
table: wh_item_zone_mapping
identifier: blinkit_warehouse_wh_item_zone_mapping
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Warehouse item zone mapping
# TTL for cache, Time in seconds
caching_ttl: 3600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 900
# Time in ms
sla: 45000 # 45s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select  outlet_id, 
          outlet_name, 
          item_id, 
          mapped_zone 
        from {{.table}}
  where outlet_id = {{.outlet_id}}
filters:
  - outlet_id
  - start_date
  - end_date

