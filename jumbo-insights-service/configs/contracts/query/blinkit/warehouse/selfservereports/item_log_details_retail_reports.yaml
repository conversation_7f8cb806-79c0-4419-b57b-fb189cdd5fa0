query_backend: trino
tenant: Blinkit
table: inventory_location_update_logs
identifier: item_log_details_retail_reports
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Location Inventory logs
# TTL for cache, Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 40000 # 40s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT 
    il.date,
    il.outlet_id,
    il.item_id,
    il.variant_id,
    il.batch,
    il.delta as update_qty,
    il.quantity  as quantity,
    il.inventory_update_type as updated_name,
    il.location_id,
    il.location_name,
    il.putlist_id,
    il.picklist_id,
    il.created_by_name emp_id,
    wu.name as emp_name,
    il.audit_id as auditlist_id,
    il.activity_timestamp_ist  update_ts
  FROM 
    {{.table}} il
  left join 
    retail.warehouse_user wu on wu.emp_id = il.created_by_name
  where 
    il.date between date({{.start_date}}) and date({{.end_date}})
    and il.outlet_id = {{.outlet_id}}
    and il.inventory_update_type <> 'audit_check'
filters:
  - outlet_id
  - start_date
  - end_date
