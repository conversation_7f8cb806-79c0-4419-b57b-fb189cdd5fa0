query_backend: trino
tenant: Blinkit
table: raise_audit_and_pna
identifier: ra_update_logs_retail_reports
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Get RA logs 
# TTL for cache, Time in seconds
caching_ttl: 3600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 1500
# Time in ms
sla: 90000 # 90s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
 select 
    ra.outlet_id,
    ra.outlet_name,
    date(ra.date) as sto_date,
    ra.shift,
    date(ra.raise_audit_ts_ist) as raise_audit_date,
    ra.picker_id,
    wu.name as picker_name,
    ra.pick_list_id,
    ra.pick_list_item_id,
    ra.location_scan_ts_ist,
    ra.raise_audit_ts_ist as raise_audit_time,
    ra.item_id,
    b.item_name,
    ra.location_name,
    ra.zone_identifier as put_zone,
    ra.picking_zone_identifier as pick_zone,
    split(ra.location_name,'-')[2]  as aisle_id,
    pid.required_quantity
  from 
    {{.table}} ra
  join supply_etls.item_details b on ra.item_id = b.item_id
  join retail.warehouse_user wu on wu.emp_id = ra.picker_id
  join warehouse_etls.picklist_item_details pid on pid.pick_list_item_id = ra.pick_list_item_id
 where
    ra.date BETWEEN date({{.start_date}}) AND date({{.end_date}})
    and pid.date between date({{.start_date}}) AND date({{.end_date}})
    and ra.outlet_id = {{.outlet_id}}
    and ra.raise_audit_dt_ist <> '' 
filters:
  - outlet_id
  - start_date
  - end_date
