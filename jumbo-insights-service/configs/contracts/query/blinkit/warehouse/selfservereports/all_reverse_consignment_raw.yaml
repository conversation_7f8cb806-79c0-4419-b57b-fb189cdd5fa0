query_backend: trino
tenant: Blinkit
table: all_reverse_consignment_raw
identifier: all_reverse_consignment_raw_retail_reports
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: All reverse consignments
# TTL for cache, Time in seconds
caching_ttl: 1800
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 900
# Time in ms
sla: 30000 # 30s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select 
    trip_id,
    trip_date,
    unloading_completed_at,
    qc_completed_at,
    receiver_outlet_name,
    array_join(cast(json_parse(consignment_ids) as array(varchar)), ',') as consignment_ids,
    array_join(cast(json_parse(source_outlet_names) as array(varchar)), ',') as source_outlet_names,
    expected_type,
    attributed_type,
    document_id,
    upc_id,
    item_id,
    item_name,
    variant_id,
    variant_description,
    landing_price,
    array_join(cast(json_parse(qc_by) as array(varchar)), ',') as qc_by,
    array_join(cast(json_parse(grn_ts) as array(varchar)), ',') as grn_ts,
    expected_quantity,
    grn_quantity,
    good_grn_quantity,
    damaged_grn_quantity,
    near_expiry_grn_quantity,
    expired_grn_quantity,
    short_quantity
  from {{.table}}
  where trip_date BETWEEN date({{.start_date}}) AND date({{.end_date}})
  and outlet_id = {{.outlet_id}}
filters:
  - outlet_id
  - start_date
  - end_date
