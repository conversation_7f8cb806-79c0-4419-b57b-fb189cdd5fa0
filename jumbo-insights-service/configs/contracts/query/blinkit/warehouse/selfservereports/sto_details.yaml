query_backend: trino
tenant: Blinkit
table: sto_details
identifier: sto_details
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Sto item level raw data
# TTL for cache, Time in seconds
caching_ttl: 3600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 1500
# Time in ms
sla: 30000 # 30s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select 
    date,
    outlet_id,
    sto_id,
    sender_outlet_name,
    receiver_outlet_name,
    source_entity_vendor_id,
    source_entity_vendor_name,
    destination_entity_vendor_id,
    destination_entity_vendor_name,
    sto_type,
    sto_state,
    cast(dispatch_time as timestamp) as dispatch_time_ist,
    picking_type,
    invoice_id,
    invoice_state,
    cast(sto_created_at as timestamp) as sto_created_at_ist,
    cast(sto_invoice_created_at as timestamp) as sto_invoice_created_at_ist,
    cast(sto_grn_at as timestamp) as sto_grn_at_ist,
    item_id,
    item_name,
    ordered_quantity,
    billed_quantity,
    grn_quantity,
    sto_zone_id
      from 
  {{.table}}
  where date BETWEEN date({{.start_date}}) AND date({{.end_date}})
  and outlet_id = {{.outlet_id}}
filters:
  - outlet_id
  - start_date
  - end_date
