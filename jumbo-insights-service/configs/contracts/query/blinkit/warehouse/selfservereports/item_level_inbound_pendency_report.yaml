query_backend: trino
tenant: Blinkit
table: item_level_inbound_pendency_retail_reports
identifier: item_level_inbound_pendency_retail_reports
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Get item level inbound pendency report
# TTL for cache, Time in seconds
caching_ttl: 1800
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 30000 # 30s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select 
    * 
  from 
    {{.table}}
  where 
    date BETWEEN date({{.start_date}}) AND date({{.end_date}})
    and outlet_id = {{.outlet_id}}
    and query_run_ts = (select 
                            max(query_run_ts) 
                        from 
                          {{.table}}
                          where 
                            date >= current_date - interval '2' day)
filters:
  - outlet_id
  - start_date
  - end_date
