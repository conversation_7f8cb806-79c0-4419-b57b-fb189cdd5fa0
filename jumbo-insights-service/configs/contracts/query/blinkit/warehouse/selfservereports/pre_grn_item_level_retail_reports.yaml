query_backend: trino
tenant: Blinkit
table: unloading_task
identifier: pre_grn_item_level_retail_reports
catalog: blinkit
schema: wms
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Details of Pre grn items
# TTL for cache, Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 50000 # 50s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select 
    date(ut.created_at + interval '330' minute) date,
    ut.outlet_id,
    ut.reference_number,
    ut.type,
    ut.dock_name,
    vmm.vendor_id,
    ut.vendor_name,
    ut.vendor_invoice_id,
    ut.unloading_id,
    uti.created_by as unloader_id,
    wu.name as unloader_name,
    item_id,
    variant_id,
    product_name,
    json_extract_scalar(uti.meta,'$.upc') upc,
    uti.state as unloading_task_item_state,
    min(uti.created_at + interval '330' minute) as item_unloaded_at,
    sum(quantity) as unloaded_quantity
  from 
    {{.table}} ut
  join
    wms.unloading_task_item uti on ut.id = uti.unloading_task_id
  left join 
    retail.warehouse_user wu on wu.emp_id = uti.created_by
  left join 
    vms.vms_vendor_merchant_mapping vmm on vmm.merchant_id = ut.vendor_id
  where 
    ut.insert_ds_ist between {{.start_date}} and {{.end_date}}
    and uti.insert_ds_ist between {{.start_date}} and cast(date({{.end_date}}) + interval '2' day as varchar)
    and ut.outlet_id = {{.outlet_id}}
  group by 
    1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16

filters:
  - outlet_id
  - start_date
  - end_date
