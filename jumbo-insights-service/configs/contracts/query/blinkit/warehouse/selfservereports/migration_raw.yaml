query_backend: trino
tenant: Blinkit
table: migration_raw_data
identifier: migration_raw_data
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: migration raw data at item level
# TTL for cache, Time in seconds
caching_ttl: 3600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 1500
# Time in ms
sla: 30000 # 30s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select 
    outlet_id,
    Facility,
    date,
    task_id,
    type,
    cast(created_at as timestamp) as created_at_ist,
    cast(updated_at as timestamp) as updated_at_ist,
    created_by,
    updated_by,
    item_id,
    item_name,
    variant_id,
    vendor_name,
    batch,
    state,
    picked_locations,
    picked_zone,
    total_picked_quantity,
    picked_cases,
    picked_case_quantity,
    picked_loose_quantity,
    system_case_size,
    loose_to_case_conversion,
    case_to_loose_conversion,
    case_size_entered,
    putawayed_locations,
    putawayed_zone,
    total_putawayed_quantity,
    putawayed_cases,
    putawayed_case_quantity,
    putawayed_loose_quantity,
    case_ids
    from 
  {{.table}}
  where date BETWEEN date({{.start_date}}) AND date({{.end_date}})
  and outlet_id = {{.outlet_id}}
filters:
  - outlet_id
  - start_date
  - end_date
