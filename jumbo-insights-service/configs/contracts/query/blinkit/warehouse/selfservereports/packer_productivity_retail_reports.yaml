query_backend: trino
tenant: Blinkit
table: pre_pkg_crate_details
identifier: packer_productivity_retail_reports
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Packer productivity details
# TTL for cache, Time in seconds
caching_ttl: 2400
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 1200
# Time in ms
sla: 40000 # 40s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select 
    pre.date,
    pre.shift,
    pre.outlet_id,
    pre.outlet_name,
    pre.packer_id,
    wu1.name as packer_name,
    pre.pick_list_id,
    pre.picking_zone_identifier,
    pre.inbound_container_id,
    pre.item_id,
    pre.variant_id,
    pre.item_name,
    pre.packaging_process_type,
    pre.qc_item_scan_ts,
    sum(pre.packed_qty) packed_qty
  from 
    {{.table}} pre
  join retail.warehouse_user wu1 on wu1.emp_id = pre.packer_id
  WHERE 
    pre.date between date({{.start_date}}) AND date({{.end_date}})
    AND outlet_id = {{.outlet_id}}
  group by
    1,2,3,4,5,6,7,8,9,10,11,12,13,14
  having 
    sum(pre.packed_qty) <>0

filters:
  - outlet_id
  - start_date
  - end_date
