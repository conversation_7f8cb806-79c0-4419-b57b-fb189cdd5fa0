query_backend: trino
tenant: Blinkit
table: ims_manual_updates_retail_reports
identifier: ims_manual_updates_retail_reports
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: IMS manual update logs
# TTL for cache, Time in seconds
caching_ttl: 1800
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 900
# Time in ms
sla: 30000 # 30s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select pos_timestamp,
        outlet_id,
        outlet_name,
        vendor_name,
        upc_id,
        variant_id,
        item_id,
        product_name,
        variant_mrp,
        hot_landing_price,
        grammage,
        inventory_update_type,
        operation,
        change_quantity,
        final_quantity,
        signed_quantity,
        total_change_value,
        location_name,
        audit_id,
        packaging_entity_id,
        pick_list_id,
        coalesce(bpi_attribution,event_type) as bpi_attribution,
        inventory_update_id,
        created_by_name,
        date
  from 
  {{.table}}
  where date BETWEEN date({{.start_date}}) AND date({{.end_date}})
  and outlet_id = {{.outlet_id}}
filters:
  - outlet_id
  - start_date
  - end_date
