query_backend: trino
tenant: Blinkit
table: putlist_segregation_report_retail_reports
identifier: putlist_segregation_report_retail_reports
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Variant level Segregation Raw Data
# TTL for cache, Time in seconds
caching_ttl: 1800
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 900
# Time in ms
sla: 30000 # 180s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select date,
      outlet,
      outlet_id,
      segregation_id,
      segregator_id,
      segregator_name,
      segregation_task_state,
      bucket_type,
      entity_vendor_name,
      variant_id,
      item_id,
      item_name,
      batch,
      segregated_quantity,
      segregated_at
  from 
  {{.table}}
  where date BETWEEN date({{.start_date}}) AND date({{.end_date}})
  and outlet_id = {{.outlet_id}}
filters:
  - outlet_id
  - start_date
  - end_date
