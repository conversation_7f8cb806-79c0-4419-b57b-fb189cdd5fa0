query_backend: trino
tenant: Blinkit
table: b2b_raw_data
identifier: b2b_raw_data
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: B2B Raw data
# TTL for cache, Time in seconds
caching_ttl: 1800
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 900
# Time in ms
sla: 30000 # 30s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select 
    date,
    outlet_id,
    Facility,
    container_id,
    b2b_original_invoice_id,
    variant_id,
    item_id,
    item_name,
    quantity,
    b2b_return_mode
  from {{.table}}
  where date BETWEEN date({{.start_date}}) AND date({{.end_date}})
  and outlet_id = {{.outlet_id}}
filters:
  - outlet_id
  - start_date
  - end_date
