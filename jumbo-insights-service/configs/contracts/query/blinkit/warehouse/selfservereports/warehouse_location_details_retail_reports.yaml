query_backend: trino
tenant: Blinkit
table: dim_warehouse_location
identifier: warehouse_location_details_retail_reports
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Location and related information for a warehouse
# TTL for cache, Time in seconds
caching_ttl: 1800
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 30000 # 30s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select 
    dwl.warehouse_id,
    dwl.outlet_id,
    dwl.outlet_name,
    dwl.location_id,
    dwl.location_name,
    dwl.sequence_id,
    dwl.height_in_m,
    dwl.length_in_m,
    dwl.width_in_m,
    dwl.floor,
    dwl.rack_type,
    wrt.payload,
    wrt.size,
    dwl.picking_zone_identifier,
    dwl.zone as put_zone
  from
    {{.table}} dwl
    left JOIN warehouse_location.warehouse_rack_type wrt ON wrt.id = dwl.rack_type_id
  where
    dwl.is_active = 1
    and dwl.outlet_id = {{.outlet_id}}
filters:
  - outlet_id

