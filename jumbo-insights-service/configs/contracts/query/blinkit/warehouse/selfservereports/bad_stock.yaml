query_backend: trino
tenant: Blinkit
table: bad_stock
identifier: bad_stock_report_retail_reports
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Bad stock in reverse consignment
# TTL for cache, Time in seconds
caching_ttl: 1800
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 900
# Time in ms
sla: 30000 # 30s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select 
    grn_date,
    grn_ts,
    facility_name,
    array_join(cast(json_parse(source_outlet_name) as array(varchar)), ',') as source_outlet_name,
    transit_trip_id,
    array_join(cast(json_parse(drop_zone_container_id) as array(varchar)), ',') as drop_zone_container_id,
    reverse_qc_inward_activity_id,
    type,
    document_id,
    return_invoice_id,
    item_id,
    grn_id,
    upc,
    product_name,
    variant_id,
    l0,
    l1,
    entity_vendor_id,
    vendor_name,
    item_type,
    quantity,
    variant_mrp,
    variant_description,
    grn_value,
    expiry_date,
    rtv_eligiblity_flag,
    array_join(cast(json_parse(drop_zone_name) as array(varchar)), ',') as drop_zone_name
  from {{.table}}
  where grn_date BETWEEN date({{.start_date}}) AND date({{.end_date}})
  and outlet_id = {{.outlet_id}}
filters:
  - outlet_id
  - start_date
  - end_date
