query_backend: trino
tenant: Blinkit
table: warehouse_item_location
identifier: item_location_report
catalog: blinkit
schema: warehouse_location
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Item Location Level Raw Data
# TTL for cache, Time in seconds
caching_ttl: 1800
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 120000 # 120s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select dim.outlet_id,
  item_loc.item_id,
  batch,
  item_loc.location_id,
  product.upc,
  item_loc.variant_id,
  product.name,
  case
  when product.outlet_type = 1 then 'F&V type'
  when product.outlet_type = 2 then 'Grocery type'
  else 'Not Assigned'
  end as item_type,
  product.variant_mrp,
  product.variant_description,
  dim.location_name,
  dim.zone zone_identifier,
  item_loc.quantity as location_quantity
  from {{.table}} item_loc
  join warehouse_etls.dim_warehouse_location dim on item_loc.location_id = dim.location_id
  join rpc.product_product product on product.variant_id=item_loc.variant_id
  where item_loc.active = 1 
  and item_loc.quantity > 0
  and dim.outlet_id = {{.outlet_id}}
filters:
  - outlet_id
  - start_date
  - end_date
