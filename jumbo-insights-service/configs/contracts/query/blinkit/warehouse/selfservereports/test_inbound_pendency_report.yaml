query_backend: trino
tenant: Blinkit
table: warehouse_etls
identifier: test_blinkit_item_level_inbound_pendency_report
catalog: blinkit
schema: warehouse_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: warehouse-metrics
  pd_service_name:  pd-warehouse-warehouse-metrics
  description: Get item level inbound pendency report
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 60000 # 60s
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select * from 
  warehouse_etls.item_level_inbound_pendency_retail_reports
  where outlet_id = {{.outlet_id}}
filters:
  - outlet_id
  - start_date
  - end_date

