# can be pinot/trino
query_backend: trino
tenant: Blinkit
table: search_management_service_keyword_metrics
identifier: search_management_service_keyword_metrics
catalog: blinkit
schema: search_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: search_management_service
  pd_service_name: blinkit_data_platform
  description: query to get city level keyword metrics
# TTL for cache, Time in seconds
caching_ttl: 3600 # 60 minutes
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 1800
# Time in ms
sla: 1000
# Request per second
rate_limit: 30

# contract type
type: sql
sql: >
  SELECT *
  FROM {{.table}}
  WHERE created_at >= current_date - interval {{.interval}} day

filters:
  - interval
