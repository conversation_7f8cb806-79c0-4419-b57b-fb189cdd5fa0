# can be pinot/trino
query_backend: trino
tenant: Blinkit
table: product_knowledge_metadata_change_log
identifier: product_knowledge_metadata_change_log
catalog: zomato
schema: blinkit_jumbo2
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: search_management_service
  pd_service_name: blinkit_data_platform
  description: Query to retrieve product knowledge change log data
# TTL for cache, Time in seconds
caching_ttl: 3600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 1800
# Time in ms
sla: 1000
# Request per second
rate_limit: 30

# contract type
type: sql
sql: >
  SELECT
      entity_type,
      entity_id,
      entity_name,
      kind,
      user_email AS updated_by,
      updated_at,
      dt AS date,
      row_id,
      is_enabled,
      source,
      is_visible,
      extra_data,
      mapped_entity_type,
      mapped_entity_id,
      similar_entity_id,
      method
  FROM {{.table}}
    AND dt >= {{.start_date}}
    AND dt <= {{.end_date}}
    AND ({{.entity_type}} IS NULL OR entity_type = {{.entity_type}})
    AND ({{.entity_id}} IS NULL OR entity_id = {{.entity_id}})
  ORDER BY updated_at DESC

filters:
  - start_date
  - end_date
  - entity_type
  - entity_id
