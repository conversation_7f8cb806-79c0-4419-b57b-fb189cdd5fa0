# can be pinot/trino
query_backend: trino
tenant: Blinkit
table: meta_entity_product_relationship
identifier: mapped_entity_product_mappings
catalog: blinkit
schema: search_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: product_knowledge_metadata
  pd_service_name: blinkit_data_platform
  description: query to get all products tagged with metadata_entities in case it is mapped to another metadata entity
# TTL for cache, Time in seconds
caching_ttl: 7200 # 2 hours
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50000
# Request per second
rate_limit: 10

# contract type
type: sql
sql: >
  with
  max_date as (
  select max(snapshot_ist) as max_snapshot_ist
  from search_etls.meta_entity_product_relationship
  where snapshot_ist > current_date - interval '2' day
  )

  SELECT 
      s.tagged_product_id,
      s.metadata_id,
      s.metadata_type,
      s.metadata_name,
      CASE
          WHEN element_at(array_agg(s.snapshot_ist), 1) = (select max_snapshot_ist from max_date) THEN 'ADDED'
          WHEN element_at(array_agg(s.snapshot_ist), 1) = (select max_snapshot_ist from max_date) - interval '1' DAY THEN 'DISABLED'
      END AS status
  FROM search_etls.meta_entity_product_relationship s
  JOIN dwh.dim_product p 
      ON s.tagged_product_id = p.product_id
      AND p.is_product_enabled -- Ensures only enabled products are included
  WHERE (s.metadata_id, s.metadata_type) IN (
      SELECT entity_id, entity_type
      FROM product_knowledge_metadata.mapped_entities
      WHERE lake_active_record
        AND is_enabled
  )
  AND s.snapshot_ist >=  (select max_snapshot_ist from max_date) - interval '1' day
  AND s.snapshot_ist is not null
  AND s.is_pid_active != 0
  GROUP BY 1,2,3,4
  HAVING cardinality(array_agg(s.snapshot_ist)) = 1

filters: []
