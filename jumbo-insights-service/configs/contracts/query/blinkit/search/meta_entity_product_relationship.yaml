# can be pinot/trino
query_backend: trino
tenant: Blinkit
table: meta_entity_product_relationship
identifier: meta_entity_product_relationship
catalog: blinkit
schema: search_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: product_knowledge_metadata
  pd_service_name: blinkit_data_platform
  description: query to get all products tagged with a particular metadata entity (["KEYWORD","ATTRIBUTE","PERSON","OCCASION","AUTHOR","USECASE","RECIPIENT","BRAND","GENERATED_TAG","KEYTERM","COMPOSITE_KEYWORD"])
# TTL for cache, Time in seconds
caching_ttl: 7200 # 2 hours
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 1000
# Request per second
rate_limit: 30

# contract type
type: sql
sql: >
  select metadata_id, metadata_type, array_agg(distinct tagged_product_id) as product_ids from search_etls.meta_entity_product_relationship 
  where snapshot_ist > current_date - interval {{.interval}} day and is_pid_active != 0 and
  metadata_type = {{.entity_type}} and metadata_id = {{.entity_id}}
  group by 1,2

filters:
  - interval
  - entity_type
  - entity_id
