query_backend: pinot
tenant: Blinkit
table: fact_order_item_details_v3
identifier: frontend_level_asp_metric_hourly
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: retail-ars
  pd_service_name: retail-ars
  description: Frontend level ASP metric
caching_ttl: 3600
refresh_interval: 600
sla: 10
rate_limit: 100
type: sql
sql: >
  SELECT 
      order_date, order_hour,
      AVG(total_item_cost / item_quantity) as ASP
  FROM 
      {{.table}}
  WHERE 
      order_date in ({{.order_dates}})
      AND LOOKUP('product_l0_l1_l2_mapping_v1', {{.category_column_name}}, 'product_id', item_product_id) IN ({{.category_ids}})
      AND LOOKUP('merchant_outlet_facility_mapping_v2', 'pos_outlet_id', 'frontend_merchant_id', merchant_id) IN ({{.frontend_outlet_ids}})
  GROUP BY 
      order_date, order_hour
filters:
  - order_dates
  - category_column_name
  - category_ids
  - frontend_outlet_ids
