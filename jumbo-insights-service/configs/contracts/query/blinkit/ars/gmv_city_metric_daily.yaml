query_backend: pinot
tenant: Blinkit
table: fact_order_item_details_v3
identifier: city_level_gmv_metric_daily
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: retail-ars
  pd_service_name: retail-ars
  description: City level GMV metric
caching_ttl: 3600
refresh_interval: 600
sla: 10
rate_limit: 100
type: sql
sql: >
  SET enableNullHandling = True;
  SET serverReturnFinalResult = True;

  SELECT 
      order_date,
      SUM(total_item_cost) as GMV
  FROM 
      {{.table}}
  WHERE 
      order_date in ({{.order_dates}})
      AND LOOKUP('product_l0_l1_l2_mapping_v1', {{.category_column_name}}, 'product_id', item_product_id) IN ({{.category_ids}})
      AND city_name IN ({{.city_names}})
  GROUP BY 
      order_date
filters:
  - order_dates
  - category_column_name
  - category_ids
  - city_names
