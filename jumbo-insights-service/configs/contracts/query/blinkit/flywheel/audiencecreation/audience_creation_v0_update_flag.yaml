query_backend: trino
tenant: Zomato
table: mobile_event_data
identifier: blinkit_audience_creation_trino_v0_update_flag
catalog: blinkit
schema: lake_events
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: flywheel-ads-service
  pd_service_name: z-flywheel-ads-service
  description: Get audience list of users cohorts of the as per the parameters with an update flag to indicate addition/removal of users
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 10000 # 10s
# Request per second
rate_limit: 10 
# contract type
type: sql
sql: >
  SELECT DISTINCT
        i.user_id,
        di.platform AS source,
        g.sha_email as sha256_email,
        g.sha_phone as mobile,
        di.advertising_id as adid_idfa,
        0 AS update_flag
    FROM blinkit.lake_events.mobile_event_data AS i
    JOIN blinkit.dwh.dim_product AS p
        ON CAST(p.product_id AS VARCHAR) = COALESCE(i.properties__child_widget_id, i.properties__widget_id, i.properties__product_id)
        AND p.is_current
        AND p.is_product_enabled
    LEFT JOIN blinkit.grofers_db.gr_user_sha256 AS g
        ON CAST(g.id AS VARCHAR) = i.user_id
    LEFT JOIN blinkit.consumer_etls.grofers_app_device_uuid AS di
        ON LOWER(di.device_uuid) = LOWER(i.device_uuid)
        AND di.advertising_id IS NOT NULL 
    WHERE i.at_date_ist BETWEEN CURRENT_DATE - INTERVAL {{.day_interval}} DAY AND CURRENT_DATE - INTERVAL '1' DAY
    AND i.name IN ({{.event_names}})
    AND ((CASE
            WHEN {{.l2_mode}} = 'none' THEN FALSE
            WHEN {{.l2_mode}} = 'all' THEN TRUE
            WHEN {{.l2_mode}} = 'include' THEN p.l2_category_id IN ({{.l2_category_ids}})
            WHEN {{.l2_mode}} = 'exclude' THEN p.l2_category_id NOT IN ({{.l2_category_ids}})
            ELSE FALSE
        END)
        OR (CASE
                WHEN {{.l1_mode}} = 'none' THEN FALSE
                WHEN {{.l1_mode}} = 'all' THEN TRUE
                WHEN {{.l1_mode}} = 'include' THEN p.l1_category_id IN ({{.l1_category_ids}})
                WHEN {{.l1_mode}} = 'exclude' THEN p.l1_category_id NOT IN ({{.l1_category_ids}})
                ELSE FALSE
            END)
        OR (CASE
                WHEN {{.l0_mode}} = 'none' THEN FALSE
                WHEN {{.l0_mode}} = 'all' THEN TRUE
                WHEN {{.l0_mode}} = 'include' THEN p.l0_category_id IN ({{.l0_category_ids}})
                WHEN {{.l0_mode}} = 'exclude' THEN p.l0_category_id NOT IN ({{.l0_category_ids}})
                ELSE FALSE
            END))
        AND (CASE
              WHEN {{.brand_mode}} = 'all' THEN TRUE
              WHEN {{.brand_mode}} = 'include' THEN p.brand_id IN ({{.brand_id}})
              WHEN {{.brand_mode}} = 'exclude' THEN p.brand_id NOT IN ({{.brand_id}})
              ELSE FALSE
          END)
    AND try_cast(i.user_id AS bigint) > 0
filters:
  - event_names
  - l0_category_ids
  - l1_category_ids
  - l2_category_ids
  - l0_mode
  - l1_mode
  - l2_mode
  - brand_id
  - day_interval
  - brand_mode
