query_backend: trino
tenant: Zomato
table: mobile_event_data
identifier: blinkit_audience_creation_trino_premium_purchasers_v0
catalog: blinkit
schema: lake_events
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: flywheel-ads-service
  pd_service_name: z-flywheel-ads-service
  description: Get audience list of premium users cohorts of the as per the parameters
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 10000 # 10s
# Request per second
rate_limit: 10 
# contract type
type: sql
sql: >
  SELECT DISTINCT
        i.dim_customer_key as user_id,
        g.platform AS source,
        g.sha_email as sha256_email,
        g.sha_phone as mobile,
        g.advertising_id as adid_idfa
    FROM blinkit.dwh.fact_sales_order_item_details AS i
    JOIN blinkit.dwh.dim_product AS p
        ON CAST(p.product_id AS VARCHAR) = CAST(i.product_id AS VARCHAR)
        AND p.is_current
        AND p.is_product_enabled
    LEFT JOIN flywheel_etls.blinkit_ad_device AS g
        ON g.user_id = i.dim_customer_key
    left join blinkit.rpc.item_product_mapping m on m.product_id = p.product_id
    left join blinkit.consumer_intelligence_etls.personalisation_item_affluence_v2 c on c.item_id = m.item_id
    WHERE order_create_dt_ist BETWEEN CURRENT_DATE - INTERVAL {{.day_interval}} DAY AND CURRENT_DATE - INTERVAL '1' DAY
    AND g.advertising_id IS NOT NULL 
    and premium_flag = 1
    AND ((CASE
            WHEN {{.l2_mode}} = 'none' THEN FALSE
            WHEN {{.l2_mode}} = 'all' THEN TRUE
            WHEN {{.l2_mode}} = 'include' THEN p.l2_category_id IN ({{.l2_category_ids}})
            WHEN {{.l2_mode}} = 'exclude' THEN p.l2_category_id NOT IN ({{.l2_category_ids}})
            ELSE FALSE
        END)
        OR (CASE
                WHEN {{.l1_mode}} = 'none' THEN FALSE
                WHEN {{.l1_mode}} = 'all' THEN TRUE
                WHEN {{.l1_mode}} = 'include' THEN p.l1_category_id IN ({{.l1_category_ids}})
                WHEN {{.l1_mode}} = 'exclude' THEN p.l1_category_id NOT IN ({{.l1_category_ids}})
                ELSE FALSE
            END)
        OR (CASE
                WHEN {{.l0_mode}} = 'none' THEN FALSE
                WHEN {{.l0_mode}} = 'all' THEN TRUE
                WHEN {{.l0_mode}} = 'include' THEN p.l0_category_id IN ({{.l0_category_ids}})
                WHEN {{.l0_mode}} = 'exclude' THEN p.l0_category_id NOT IN ({{.l0_category_ids}})
                ELSE FALSE
            END))
        AND (CASE
              WHEN {{.brand_mode}} = 'all' THEN TRUE
              WHEN {{.brand_mode}} = 'include' THEN p.brand_id IN ({{.brand_ids}})
              WHEN {{.brand_mode}} = 'exclude' THEN p.brand_id NOT IN ({{.brand_ids}})
              ELSE FALSE
          END)
    AND try_cast(i.dim_customer_key AS bigint) > 0
filters:
  - l0_category_ids
  - l1_category_ids
  - l2_category_ids
  - brand_ids
  - l0_mode
  - l1_mode
  - l2_mode
  - brand_mode
  - day_interval
  
