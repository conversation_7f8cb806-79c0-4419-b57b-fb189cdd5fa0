query_backend: trino
tenant: Zomato
table: meta_insights_blinkit
identifier: blinkit_account_meta_reporting_v0
catalog: hive
schema: flywheel_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: flywheel-ads-service
  pd_service_name: z-flywheel-ads-service
  description: Get meta metrics of ad at account level
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 10000 # 10s
# Request per second
rate_limit: 10 
# contract type
type: sql
sql: >
  with active as
  (
      select campaign_id
      from {{.table}} m
      where dt between {{.start_date}} and {{.end_date}}
          and (spend > 0 or impressions >= 1) 
          and insight_type = 'aggregate'
          and cast(account_id as bigint) in ({{.account_id}})
      group by 1
  )

  select *
   from
    (select dt
        ,business_id
        ,business_name
        ,account_id
        ,account_name
        ,campaign_id
        ,campaign_name
        ,adset_id
        ,adset_name
        ,ad_id
        ,ad_name 
        ,images
        ,adset_stop_time
        ,adset_start_time
        ,adset_status
        ,adset_optimization_goal
        ,adset_custom_event_type
        ,campaign_objective
        ,sum(spend) spends
        ,sum(impressions) impressions
        ,sum(reach) reach
        ,sum(inline_link_clicks) clicks
        ,sum(ct_opens) content_view
        ,sum(a2c) as "A2C (Total)"
        ,sum(orders) as orders
        ,sum(gmv) as purchase_value
        ,sum(meta_order_1dclick) meta_order_1dclick
        ,sum(meta_order_7dclick) meta_order_7dclick
        ,sum(meta_order_1dview) meta_order_1dview
    from 
        (select dt,  
                m.business_id  , m.business_name, 
                m.campaign_id  ,m.campaign_name,
                m.adset_id ,m.adset_name,
                adset_custom_event_type,adset_optimization_goal,campaign_objective,
                m.ad_id , m.ad_name,images,adset_stop_time,adset_start_time,adset_status,
                creative_media_type , account_currency,  spend, reach, impressions, frequency , clicks,  unique_clicks, 
                inline_link_clicks, unique_inline_link_clicks,
                catalog_segment_action_app_custom_event_fb_mobile_content_view as ct_opens,
                catalog_segment_action_app_custom_event_fb_mobile_add_to_cart as a2c,
                catalog_segment_action_app_custom_event_fb_mobile_purchase orders,
                catalog_segment_value_app_custom_event_fb_mobile_purchase gmv,
                catalog_segment_value_app_custom_event_fb_mobile_content_view ct_open_value,
                catalog_segment_value_app_custom_event_fb_mobile_add_to_cart carts_value,
                cast(regexp_replace(cast(json_extract(catalog_segment_action_app_custom_event_fb_mobile_purchase_attribution_setting,'$.1d_view') as varchar), '\"','') as int) as meta_order_1dview,
                cast(regexp_replace(cast(json_extract(catalog_segment_action_app_custom_event_fb_mobile_purchase_attribution_setting,'$.1d_click') as varchar), '\"','') as int) as meta_order_1dclick,
                cast(regexp_replace(cast(json_extract(catalog_segment_action_app_custom_event_fb_mobile_purchase_attribution_setting,'$.7d_click') as varchar), '\"','') as int)  as meta_order_7dclick,
                cast(regexp_replace(cast(json_extract(catalog_segment_value_app_custom_event_fb_mobile_purchase_attribution_setting,'$.1d_view') as varchar), '\"','') as double) as order_value_1dview,
                cast(regexp_replace(cast(json_extract(catalog_segment_value_app_custom_event_fb_mobile_purchase_attribution_setting,'$.1d_click') as varchar), '\"','') as double) as order_value_1dclick,
                cast(regexp_replace(cast(json_extract(catalog_segment_value_app_custom_event_fb_mobile_purchase_attribution_setting,'$.7d_click') as varchar), '\"','') as double)  as order_value_7dclick,
                m.account_id, m.account_name
        from {{.table}} m
        join  flywheel_etls.blinkit_meta_ad_info i on m.ad_id=i.ad_id
        where dt <= {{.end_date}}
                and (spend > 0 or impressions >= 1) 
                and insight_type = 'aggregate'
                and m.campaign_id in (select campaign_id from active))
        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18)

filters:
  - start_date
  - end_date
  - account_id
