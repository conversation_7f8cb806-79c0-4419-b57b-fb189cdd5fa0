# can be pinot/trino
query_backend: trino
tenant: Blinkit
table: payments_adonp_carts
identifier: payments_adonp_carts
catalog: blinkit
schema: cd_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: payment-service
  pd_service_name: duck-tales
  description: query to adonp(amount deducted order not placed) carts
# TTL for cache, Time in seconds
caching_ttl: 3600 # 60 minutes
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 1800
# Time in ms
sla: 1000
# Request per second
rate_limit: 30

# contract type
type: sql
sql: >
  SELECT *
  FROM {{.table}}
  WHERE insert_dt >= CAST(current_date - INTERVAL {{.interval}} DAY AS VARCHAR)

filters:
  - interval
