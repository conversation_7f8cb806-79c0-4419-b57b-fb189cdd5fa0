# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: blinkit_chat_agent_metrics
identifier: blinkit_chat_avg_time_channels
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: chat-v2-backend-service
  pd_service_name: chat-backend-v2-service
  description: Avg metric time for agents in some channels

# Time in second
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 200
# Request per second
rate_limit: 1000

# contract type
type: sql
sql: >
  SELECT AVG(CAST(event_value AS INT))
  FROM {{.table}}
  WHERE event_name = {{.event_name}}
  AND channel_id IN ({{.channel_ids}})
  AND "time" BETWEEN {{.start_time}} AND {{.end_time}}
filters:
  - event_name
  - channel_ids
  - start_time
  - end_time
