# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: blinkit_chat_agent_metrics
identifier: blinkit_chat_analytics_channels
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: chat-v2-backend-service
  pd_service_name: chat-backend-v2-service
  description: Chat analytics grouped by channel

# Time in second
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 200
# Request per second
rate_limit: 100

# contract type
type: sql
sql: >
  SELECT channel_id, COUNT(*) as count 
  FROM {{.table}}
  WHERE event_name = {{.event_name}}
  AND business_id = {{.business_id}}
  AND channel_id IN ({{.channel_ids}})
  AND "time" BETWEEN {{.start_time}} AND {{.end_time}}
  GROUP BY channel_id
filters:
  - event_name
  - business_id
  - channel_ids
  - start_time
  - end_time
