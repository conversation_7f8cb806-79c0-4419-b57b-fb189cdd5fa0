query_backend: pinot
tenant: Blinkit
table: storeops_order_metrics_v2
identifier: store_orders_in_time_data
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: store-partner-backend-service
  pd_service_name: store-partner-backend-service
  description: Store Order Metrics
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
 SELECT
    merchant_id,
    SUM(CASE WHEN (order_billed_timestamp - insert_timestamp) <= 150000 THEN 1 ELSE 0 END) AS total_orders_count_under_150_seconds,
    SUM(CASE WHEN (checkout_to_delivered_time > 0 AND checkout_to_delivered_time <= 900000) THEN 1 ELSE 0 END) AS total_orders_count_under_900_seconds,
    SUM(CASE WHEN (order_billed_timestamp - insert_timestamp) <= 150000 AND item_count <= 6 THEN 1 ELSE 0 END) AS total_orders_count_under_150_seconds_and_item_count_less_than_6,
    SUM(CASE WHEN order_billed_timestamp > 0 AND item_count <= 6  THEN 1 ELSE 0 END) AS total_billed_orders_and_item_count_less_than_6
  FROM
      {{.table}} 
  WHERE
      merchant_id IN ({{.frontend_merchant_ids}})
      AND insert_timestamp >= {{.start_time_in_mili}}
      AND insert_timestamp < {{.end_time_in_mili}}
  GROUP BY
      merchant_id     
filters:
  - frontend_merchant_ids
  - start_time_in_mili
  - end_time_in_mili

