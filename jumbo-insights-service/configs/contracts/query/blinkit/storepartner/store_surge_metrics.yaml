query_backend: pinot
tenant: Blinkit
table: ticktock_serviceability_surge_info
identifier: store_health_surge_metrics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: store-partner-backend-service
  pd_service_name: store-partner-backend-service
  description: Store Health Surge Metrics
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100
type: sql
sql: >
  SELECT
    frontend_merchant_id,   
    ToDateTime(window_start_epoch, 'yyyy-MM-dd', 'Asia/Kolkata') AS dt,
    LASTWITHTIME(total_carts, window_end_epoch, 'LONG') AS total_cart_instances,
    LASTWITHTIME(total_surge_carts, window_end_epoch, 'LONG') AS surge_cart_instances,
    LASTWITHTIME(rider_surge_carts, window_end_epoch, 'LONG') AS rider_surge_cart_instances,
    LASTWITHTIME(picker_surge_carts, window_end_epoch, 'LONG') AS picker_surge_cart_instances,
    LASTWITHTIME(rain_surge_carts, window_end_epoch, 'LONG') AS rain_surge_cart_instances
  FROM
    {{.table}}
  WHERE
    frontend_merchant_id in ({{.merchant_id}})
    AND window_start_epoch >= {{.start_time_in_mili}}
    AND window_end_epoch <= {{.end_time_in_mili}}
    GROUP BY frontend_merchant_id, dt
filters:
  - merchant_id
  - start_time_in_mili
  - end_time_in_mili