query_backend: pinot
tenant: Blinkit
table: fact_order_details_v6
identifier: picker_assignment_metrics_v2
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: store-partner-backend-service
  pd_service_name: store-partner-backend-service
  description: Feed Picker Assignment Metrics
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT 
    merchant_id,
    COALESCE(SUM(
      CASE 
        WHEN (picker_assignment_queued_time IS NOT NULL AND picker_assignment_queued_time > 0) 
        THEN 1 
        ELSE 0 
      END
    ), 0.0) AS ready_to_assign_orders,
    COALESCE(SUM(
      CASE 
        WHEN (picker_assignment_queued_time IS NULL OR picker_assignment_queued_time <= 0) 
        THEN 1 
        ELSE 0 
      END
    ), 0.0) AS not_ready_to_assign_orders
  FROM {{.table}}
    WHERE merchant_id IN ({{.frontend_merchant_ids}}) 
      AND insert_timestamp >= {{.start_time_in_mili}} 
      AND insert_timestamp <= {{.end_time_in_mili}} 
      AND picker_employee_id IS NULL 
      AND current_status = 'APPROVED'
    GROUP BY merchant_id
filters:
  - frontend_merchant_ids
  - start_time_in_mili
  - end_time_in_mili