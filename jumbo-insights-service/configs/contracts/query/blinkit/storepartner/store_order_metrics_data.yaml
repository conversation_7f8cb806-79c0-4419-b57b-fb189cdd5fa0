query_backend: pinot
tenant: Blinkit
table: storeops_order_metrics_v2
identifier: store_order_metrics_data
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: store-partner-backend-service
  pd_service_name: store-partner-backend-service
  description: Store Order Metrics
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
 SELECT
    merchant_id,
    SUM(CASE WHEN current_status = 'DELIVERED' THEN 1 ELSE 0 END) AS total_orders,
    SUM(CASE WHEN current_status = 'DELIVERED' AND is_unicorn_order = false THEN procurement_amount ELSE 0 END) AS total_gmv,
    SUM(CASE WHEN current_status = 'CANCELLED' AND is_unicorn_order = false THEN total_cost ELSE 0 END) AS cancelled_gmv,
    SUM(CASE WHEN current_status IN ('CANCELLED', 'DELIVERY_FAILED') THEN 1 ELSE 0 END) AS cancelled_orders,
    SUM(CASE WHEN order_billed_timestamp > 0 THEN 1 ELSE 0 END) AS total_billed_orders
  FROM
      {{.table}} 
  WHERE
      merchant_id IN ({{.frontend_merchant_ids}})
      AND insert_timestamp >= {{.start_time_in_mili}}
      AND insert_timestamp < {{.end_time_in_mili}}
  GROUP BY
      merchant_id     
filters:
  - frontend_merchant_ids
  - start_time_in_mili
  - end_time_in_mili

