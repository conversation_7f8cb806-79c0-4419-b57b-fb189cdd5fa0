query_backend: pinot
tenant: Blinkit
table: fact_order_details_v6
identifier: feed_gmv_metrics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: store-partner-backend-service
  pd_service_name: store-partner-backend-service
  description: Feed GMV Metrics
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT
    merchant_id,
    SUM(CASE WHEN current_status = 'DELIVERED' THEN procurement_amount ELSE 0 END) AS total_gmv,
    SUM(CASE WHEN current_status = 'CANCELLED' THEN total_cost ELSE 0 END) AS cancelled_gmv
  FROM
      {{.table}}
  WHERE
      merchant_id IN ({{.frontend_merchant_ids}})
      AND insert_timestamp >= {{.start_time_in_mili}}
      AND insert_timestamp <= {{.end_time_in_mili}}
      AND is_unicorn_order = false
      AND type = 'RetailForwardOrder'
      GROUP BY merchant_id
filters:
  - frontend_merchant_ids
  - start_time_in_mili
  - end_time_in_mili