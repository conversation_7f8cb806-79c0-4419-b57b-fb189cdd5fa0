query_backend: pinot
tenant: Blinkit
table: storeops_order_metrics_v2
identifier: store_order_time_metrics_data_1
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: store-partner-backend-service
  pd_service_name: store-partner-backend-service
  description: Store Order Metrics
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
 SELECT
    merchant_id,
    SUM(CASE WHEN picker_assignment_queued_time > 0 and order_billed_timestamp > 0 and current_status = 'DELIVERED' THEN order_billed_timestamp - picker_assignment_queued_time ELSE 0 END) / SUM(CASE WHEN current_status = 'DELIVERED' THEN 1 ELSE 0 END) AS avg_in_store_time,
    SUM(CASE WHEN checkout_to_picking_assigned_time > 0 THEN checkout_to_picking_assigned_time ELSE 0 END) / SUM(CASE WHEN checkout_to_picking_assigned_time > 0 THEN 1 ELSE 0 END) AS avg_checkout_to_picker_assigned_time,
    SUM(CASE WHEN order_billed_timestamp > 0 AND picker_assigned_timestamp > 0 THEN order_billed_timestamp - picker_assigned_timestamp ELSE 0 END) / SUM(CASE WHEN order_billed_timestamp > 0 AND picker_assigned_timestamp > 0 THEN 1 ELSE 0 END) AS avg_picker_assigned_to_billed_time,
    SUM(CASE WHEN order_enroute_timestamp > 0 AND order_billed_timestamp > 0 THEN order_enroute_timestamp - order_billed_timestamp ELSE 0 END) / SUM(CASE WHEN order_enroute_timestamp > 0 AND order_billed_timestamp > 0 THEN 1 ELSE 0 END) AS avg_billed_to_rider_handover_time
  FROM
      {{.table}} 
  WHERE
      merchant_id IN ({{.frontend_merchant_ids}})
      AND insert_timestamp >= {{.start_time_in_mili}}
      AND insert_timestamp < {{.end_time_in_mili}}
  GROUP BY
      merchant_id     
filters:
  - frontend_merchant_ids
  - start_time_in_mili
  - end_time_in_mili

