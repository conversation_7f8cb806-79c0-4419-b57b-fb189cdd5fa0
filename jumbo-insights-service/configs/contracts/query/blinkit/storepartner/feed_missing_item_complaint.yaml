query_backend: pinot
tenant: Blinkit
table: storeops_order_complaints
identifier: store_feed_missing_item_complaints
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: store-partner-backend-service
  pd_service_name: store-partner-backend-service
  description: Feed Order Missing Item Count
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100 
type: sql
sql: >
  SELECT
    store_id,
    COUNT(complaint_id) as missing_item_complaints
  FROM
      {{.table}}
  WHERE
      store_id IN ({{.merchant_ids}})
      and complaint_start_time>={{.start_time_in_mili}}
      and complaint_start_time<={{.end_time_in_mili}}
      and complaint_type IN ('item_missing', 'partial_items_missing', 'missing_freebie', 'missing_customer', 'missing_free_gift')
      and product_id != 0
      group by store_id
filters:
  - merchant_ids
  - start_time_in_mili
  - end_time_in_mili