query_backend: pinot
tenant: Blinkit
table: storeops_order_complaints
identifier: store_feed_complaint_info
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: store-partner-backend-service
  pd_service_name: store-partner-backend-service
  description: Feed Store Health Complaint Metrics
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100 
type: sql
sql: >
  SELECT 
    store_id,
    COUNT(DISTINCT order_id) AS count_of_orders_with_complaints,
    COUNT(
      CASE 
        WHEN product_id != 0 
          AND complaint_type NOT IN ('order_not_delivered') 
          THEN product_id 
      END
    ) AS count_of_items_with_complaints
  FROM 
      {{.table}}
  WHERE 
      store_id IN ({{.merchant_ids}})
      AND complaint_start_time >= {{.start_time_in_mili}}
      AND complaint_end_time <= {{.end_time_in_mili}}
  GROUP BY store_id
filters:
  - merchant_ids
  - start_time_in_mili
  - end_time_in_mili