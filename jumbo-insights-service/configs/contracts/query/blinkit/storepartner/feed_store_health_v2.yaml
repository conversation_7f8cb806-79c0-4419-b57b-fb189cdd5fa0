query_backend: pinot
tenant: Blinkit
table: fact_order_details_v6
identifier: feed_store_health_metrics_prod_v2
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: store-partner-backend-service
  pd_service_name: store-partner-backend-service
  description: Feed Store Health Metrics
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
  SELECT 
    order_date, 
    merchant_id, 
    COALESCE(SUM(CASE WHEN current_status = 'DELIVERED' THEN 1 ELSE 0 END), 0.0) AS delivered_orders,
    COALESCE(SUM(CASE WHEN current_status = 'CANCELLED' THEN 1 ELSE 0 END), 0.0) AS cancelled_orders,
    COALESCE(SUM(CASE WHEN order_billed_timestamp > 0 THEN 1 ELSE 0 END), 0.0) AS billed_orders,
    COALESCE(SUM(CASE WHEN sm_fill_rate = 0 AND current_status != 'APPROVED' THEN 1 ELSE 0 END), 0.0) AS pna_order_count,
    CASE 
      WHEN COUNT(id) > 0 THEN 
        100 * COALESCE(SUM(CASE WHEN (picker_assigned_timestamp - picker_assignment_queued_time) <= 10000 THEN 1 ELSE 0 END), 0.0) / COUNT(id)
      ELSE 0 
    END AS c2a,
    COALESCE(SUM(CASE 
      WHEN delivery_timestamp - checkout_timestamp > 0 
          AND delivery_timestamp - checkout_timestamp <= {{.time_threshold_ms}} 
          AND approved_timestamp > 0 
      THEN 1 ELSE 0 
    END), 0.0) AS under_x_seconds_orders_count,
    CASE 
      WHEN COALESCE(SUM(CASE  
        WHEN current_status = 'DELIVERED' 
            AND order_billed_timestamp != -9223372036854775808 
            AND picker_assignment_queued_time != -9223372036854775808 
        THEN 1  
        ELSE 0 
      END), 0.0) > 0 
      THEN 
        COALESCE(SUM(CASE 
          WHEN current_status = 'DELIVERED' 
              AND order_billed_timestamp != -9223372036854775808 
              AND picker_assignment_queued_time != -9223372036854775808 
          THEN order_billed_timestamp - picker_assignment_queued_time 
          ELSE 0 
        END), 0.0) / 
        SUM(CASE  
          WHEN current_status = 'DELIVERED' 
              AND order_billed_timestamp != -9223372036854775808 
              AND picker_assignment_queued_time != -9223372036854775808 
          THEN 1  
          ELSE 0 
        END)
      ELSE 0 
    END AS avg_in_store_time,
    CASE 
      WHEN COALESCE(SUM(CASE WHEN current_status != 'APPROVED' THEN 1 ELSE 0 END), 0) > 0 THEN
        100 * (
          COALESCE(SUM(CASE WHEN current_status != 'APPROVED' THEN 1 ELSE 0 END), 0) -
          COALESCE(SUM(CASE WHEN sm_fill_rate = 0 AND current_status != 'APPROVED' THEN 1 ELSE 0 END), 0)
        ) / 
        COALESCE(SUM(CASE WHEN current_status != 'APPROVED' THEN 1 ELSE 0 END), 1)
      ELSE 0
    END AS sm_fill_rate
  FROM {{.table}} 
  WHERE 
    order_date = DATETIMECONVERT(
      FromEpochSeconds(now()/1000), 
      '1:MILLISECONDS:EPOCH', 
      '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', 
      '1:DAYS'
    )
    AND org_channel_id IN ('1', '2')
    AND city_name NOT IN ('Not in service area')
    AND merchant_id IN ({{.frontend_merchant_ids}})
  GROUP BY order_date, merchant_id
  ORDER BY merchant_id, order_date
filters:
  - frontend_merchant_ids
  - time_threshold_ms

