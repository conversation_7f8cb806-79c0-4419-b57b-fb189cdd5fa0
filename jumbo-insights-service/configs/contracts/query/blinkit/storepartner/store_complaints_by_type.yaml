query_backend: pinot
tenant: Blinkit
table: storeops_order_complaints_v2
identifier: store_feed_complaint_info_by_type
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: store-partner-backend-service
  pd_service_name: store-partner-backend-service
  description: Store Complaint Metrics By Complaint Type
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100 
type: sql
sql: >
  SELECT 
    store_id,
    complaint_type,
    COUNT(DISTINCT order_id) AS count_of_orders_with_complaints_by_type,
    SUM(CASE WHEN product_id != 0 THEN 1 ELSE 0 END) AS count_of_items_with_complaints_by_type
  FROM 
      {{.table}}
  WHERE 
      store_id IN ({{.outlet_id}})
      AND complaint_start_time >= {{.start_time_in_mili}}
      AND complaint_end_time <= {{.end_time_in_mili}}
  GROUP BY complaint_type, store_id
filters:
  - outlet_id
  - start_time_in_mili
  - end_time_in_mili