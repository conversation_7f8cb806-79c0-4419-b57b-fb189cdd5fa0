query_backend: pinot
tenant: Blinkit
table: bistro_storeops_order_metrics
identifier: bistro_delayed_orders
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Bistro Delay Order Data
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT 
    id AS order_id,
    (NOW() / 1000) - ((approved_timestamp / 1000) + (instore_eta * 60)) AS delay_seconds
  FROM {{.table}}
  WHERE 
    merchant_id = {{.merchant_id}}
    AND order_date = {{.order_date}}
    AND current_status = 'APPROVED'
    AND (NOW() / 1000) > ((approved_timestamp / 1000) + (instore_eta * 60) + {{.delay_threshold}});
filters:
  - merchant_id
  - order_date
  - delay_threshold
