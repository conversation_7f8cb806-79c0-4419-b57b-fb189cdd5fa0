query_backend: pinot
tenant: Blinkit
table: storeops_bistro_order_feedback
identifier: station_product_ratings
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Station Product Ratings
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SET useMultistageEngine=true;
  WITH StationOrderAverageRatings AS (
    SELECT
        ofb.order_id,
        MAX(ofb.event_timestamp) as order_timestamp
    FROM
        {{.table}} ofb
    JOIN
        bistro_product_station_mapping_v2 bpm ON ofb.product_id = bpm.product_id
    WHERE
        ofb.merchant_id = {{.merchant_id}}
        AND bpm.station_id = {{.station_id}}
        AND bpm.store_id = {{.store_id}}
    GROUP BY
        ofb.order_id
  ),
  RankedOrders AS (
    SELECT
        soar.order_id,
        soar.order_timestamp,
        ROW_NUMBER() OVER (ORDER BY soar.order_timestamp DESC) AS rn
    FROM
        StationOrderAverageRatings soar
  ),
  LastNOrders AS (
    SELECT
        order_id
    FROM
        RankedOrders
    WHERE
        rn <= {{.orders_count}}
  ),
  ProductRatings AS (
    SELECT
        ofb.order_id,
        ofb.product_id,
        AVG(ofb.rating) AS avg_product_rating
    FROM
        {{.table}} ofb
    WHERE
        ofb.order_id IN (SELECT order_id FROM LastNOrders)
    GROUP BY
        ofb.order_id, ofb.product_id
  ),
  ProductRatingCounts AS (
    SELECT
        pr.product_id,
        COUNT(*) AS rating_count
    FROM
        ProductRatings pr
    GROUP BY
        pr.product_id
  )
  SELECT
    pr.product_id,
    AVG(pr.avg_product_rating) AS average_rating,
    prc.rating_count
  FROM
    ProductRatings pr
  JOIN
    ProductRatingCounts prc ON pr.product_id = prc.product_id
  GROUP BY
    pr.product_id, prc.rating_count;
filters:
  - merchant_id
  - station_id
  - orders_count
  - store_id
