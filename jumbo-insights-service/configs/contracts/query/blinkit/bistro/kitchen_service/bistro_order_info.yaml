query_backend: pinot
tenant: Blinkit
table: bistro_storeops_order_metrics
identifier: bistro_order_info
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Bistro Order Data
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT
    id,
    merchant_id,
    order_date,
    order_hour
  FROM {{.table}}
  WHERE 
    order_date = {{.order_date}}
    AND order_hour IN ({{.order_hour_list}})
    AND merchant_id IN ({{.merchant_ids}})
filters:
  - merchant_ids
  - order_hour_list
  - order_date
