query_backend: pinot
tenant: Blinkit
table: bistro_storeops_order_metrics
identifier: bistro_average_time_metrics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Bistro Average Time Metrics
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT 
    AVG((order_billed_timestamp - approved_timestamp) / 1000) AS avg_kpt,
    AVG((order_enroute_timestamp - order_billed_timestamp) / 1000) AS avg_rhs
  FROM {{.table}}
  WHERE 
    merchant_id = {{.merchant_id}}
    AND order_date = {{.order_date}}
    AND current_status IN ({{.status}})
filters:
  - merchant_id
  - order_date
  - status
