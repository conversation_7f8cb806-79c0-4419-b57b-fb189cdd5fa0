query_backend: pinot
tenant: Blinkit
table: storeops_order_complaints_v2
identifier: bistro_complaint_metrics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Bistro Complaint Metrics
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT
    complaint_type,
    COUNT(*) AS complaint_count
  FROM
    {{.table}}
  WHERE
    store_id = {{.store_id}}
    AND created_at >= {{.start_timestamp}} AND created_at <= {{.end_timestamp}}
  GROUP BY
    complaint_type
filters:
  - store_id
  - start_timestamp
  - end_timestamp
