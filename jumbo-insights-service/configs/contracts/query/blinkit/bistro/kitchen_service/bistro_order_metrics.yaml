query_backend: pinot
tenant: Blinkit
table: bistro_storeops_order_metrics
identifier: bistro_order_metrics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Bistro Order Metrics
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT
      COUNT(*) AS total_orders,
      100 - (100 * SUM(CASE WHEN current_status NOT IN ('APPROVED', 'CANCELLED', 'DELIVERY_FAILED') AND fill_rate < 100 THEN 1 ELSE 0 END) / COUNT(*)) AS fill_rate,
      SUM(CASE WHEN current_status IN ('CANCELLED', 'DELIVERY_FAILED') THEN 1 ELSE 0 END) AS cancelled_orders,
      SUM(CASE WHEN current_status NOT IN ('APPROVED', 'CANCELLED', 'DELIVERY_FAILED') AND fill_rate < 100 THEN 1 ELSE 0 END) AS pna_orders,
      100 * SUM(CASE WHEN checkout_timestamp > 0 AND delivery_timestamp > 0 AND (delivery_timestamp - checkout_timestamp) <= 900000 AND approved_timestamp > 0 THEN 1 ELSE 0 END) / SUM(CASE WHEN current_status IN ('DELIVERED') THEN 1 ELSE 0 END) AS percentage_u15m_orders
  FROM
      {{.table}}
  WHERE
      merchant_id = {{.merchant_id}}
      AND insert_timestamp >= {{.start_timestamp}}
      AND insert_timestamp < {{.end_timestamp}}
filters:
  - merchant_id
  - start_timestamp
  - end_timestamp
