query_backend: pinot
tenant: Blinkit
table: storeops_bistro_order_feedback
identifier: station_product_ratings_by_date
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Station Product Ratings By Date
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SET useMultistageEngine=true;
  SELECT
    ofb.product_id AS product_id,
    AVG(ofb.rating) AS average_rating,
    COUNT(ofb.rating) AS rating_count
  FROM
    {{.table}} ofb
  JOIN
    bistro_product_station_mapping_v2 bpm ON ofb.product_id = bpm.product_id
  WHERE
    ofb.order_id IN (
        SELECT id
        FROM bistro_storeops_order_metrics
        WHERE insert_timestamp >= {{.start_timestamp}} AND insert_timestamp <= {{.end_timestamp}}
          AND merchant_id = {{.merchant_id}}
    )
    AND ofb.merchant_id = {{.merchant_id}}
    AND bpm.station_id = {{.station_id}}
    AND bpm.store_id = {{.store_id}}
  GROUP BY
    ofb.product_id;
filters:
  - merchant_id
  - station_id
  - store_id
  - start_timestamp
  - end_timestamp
