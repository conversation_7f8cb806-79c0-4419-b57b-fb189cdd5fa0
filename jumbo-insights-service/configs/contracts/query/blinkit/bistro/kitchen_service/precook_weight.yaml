query_backend: pinot
tenant: Blinkit
table: bistro_pre_cook_bucket_weights_store
identifier: bistro_store_slot_weights
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Preprep Slot Weights
caching_ttl: 7200
refresh_interval: 3600
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT
    hr,
    outlet_id,
    slot,
    weights
  FROM {{.table}}
  WHERE 
    outlet_id IN ({{.outlet_id_list}})
    AND slot IN  ({{.slot_list}})
filters:
  - outlet_id_list
  - slot_list
