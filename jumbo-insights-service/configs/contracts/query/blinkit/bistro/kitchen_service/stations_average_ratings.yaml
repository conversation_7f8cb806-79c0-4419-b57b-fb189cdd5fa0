query_backend: pinot
tenant: Blinkit
table: storeops_bistro_order_feedback
identifier: station_average_rating
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Stations Average Ratings
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SET useMultistageEngine=true;
  WITH StationOrderAverageRatings AS (
    SELECT
      ofb.order_id,
      AVG(ofb.rating) AS avg_order_rating,
      bpm.station_id,
      MAX(ofb.event_timestamp) AS order_timestamp
    FROM
      {{.table}} ofb
    JOIN
      bistro_product_station_mapping_v2 bpm ON ofb.product_id = bpm.product_id
    WHERE
      ofb.merchant_id = {{.merchant_id}}
      AND bpm.station_id IN ({{.station_ids}})
      AND bpm.store_id = {{.store_id}}
    GROUP BY
      ofb.order_id, bpm.station_id
  ),
  RankedOrders AS (
    SELECT
      soar.station_id,
      soar.avg_order_rating,
      soar.order_timestamp,
      ROW_NUMBER() OVER (PARTITION BY soar.station_id ORDER BY soar.order_timestamp DESC) AS rn
    FROM
      StationOrderAverageRatings soar
  ),
  LastNOrders AS (
    SELECT
      station_id,
      avg_order_rating
    FROM
      RankedOrders
    WHERE
      rn <= {{.orders_count}}
  )
  SELECT
    station_id,
    AVG(avg_order_rating) AS average_rating
  FROM
    LastNOrders
  GROUP BY
    station_id;
filters:
  - merchant_id
  - station_ids
  - orders_count
  - store_id
