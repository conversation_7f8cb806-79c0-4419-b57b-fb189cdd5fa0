query_backend: pinot
tenant: Blinkit
table: storeops_order_complaints_v2
identifier: bistro_order_complaints
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Bistro Order Complaints
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT
    store_id, 
    complaint_id, 
    order_id, 
    product_id,
    complaint_type, 
    complaint_end_time, 
    delivery_fe_name, 
    delivery_fe_employee_id, 
    product_name, 
    comment
  FROM
    {{.table}}
  WHERE
    store_id = {{.store_id}}
    AND tenant = 'BISTRO'
    AND complaint_end_time >= {{.start_timestamp}} AND complaint_end_time <= {{.end_timestamp}}
  LIMIT 1000
filters:
  - store_id
  - start_timestamp
  - end_timestamp
