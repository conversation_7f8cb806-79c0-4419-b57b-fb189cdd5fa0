query_backend: pinot
tenant: Blinkit
table: storeops_order_complaints_v2
identifier: station_complaint_products
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Station Complaint Products
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SET useMultistageEngine=true;
  SELECT
    bpsm.product_id,
    soc.complaint_type,
    COUNT(*) AS complaint_count
  FROM
    {{.table}} soc
  JOIN
    bistro_product_station_mapping_v2 bpsm ON soc.product_id = bpsm.product_id
  WHERE
    soc.store_id = {{.store_id}}
    AND bpsm.store_id = {{.store_id}}
    AND bpsm.station_id = {{.station_id}}
    AND created_at >= {{.start_timestamp}} AND created_at <= {{.end_timestamp}}
    AND soc.complaint_type IN ({{.complaint_types}})
  GROUP BY
    bpsm.product_id, soc.complaint_type
filters:
  - store_id
  - station_id
  - start_timestamp
  - end_timestamp
  - complaint_types
