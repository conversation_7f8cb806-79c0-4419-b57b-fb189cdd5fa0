query_backend: pinot
tenant: Blinkit
table: storeops_bistro_order_feedback
identifier: bistro_lowest_rated_products
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Bistro Lowest Rated Products
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SET useMultistageEngine=true;
  SELECT
    product_id,
    AVG(rating) AS avg_rating,
    COUNT(rating) AS rating_count
  FROM {{.table}}
  WHERE
    order_id IN (
        SELECT id
        FROM bistro_storeops_order_metrics
        WHERE insert_timestamp >= {{.start_timestamp}} AND insert_timestamp <= {{.end_timestamp}}
          AND merchant_id = {{.merchant_id}}
    )
    AND merchant_id = {{.merchant_id}}
  GROUP BY product_id
  ORDER BY avg_rating ASC
filters:
  - merchant_id
  - start_timestamp
  - end_timestamp
