query_backend: pinot
tenant: Blinkit
table: bistro_sub_order_item_prep_details
identifier: stations_metrics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Stations Metrics
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT
      station_id,
      AVG(prep_start_timestamp - GREATEST(order_created_at, defer_end_timestamp)) AS avg_wait_time,
      AVG(prep_end_timestamp - prep_start_timestamp) AS avg_prep_time,
      (COUNT(DISTINCT CASE WHEN (prep_start_timestamp - GREATEST(order_created_at, defer_end_timestamp)) - wait_time >= {{.wait_time_threshold}} THEN order_id ELSE NULL END) * 100.0 / COUNT(DISTINCT order_id)) AS percent_order_breaching_wait_time,
      (COUNT(DISTINCT CASE WHEN (prep_end_timestamp - prep_start_timestamp) - kpt >= {{.prep_time_threshold}} THEN order_id ELSE NULL END) * 100.0 / COUNT(DISTINCT order_id)) AS percent_order_breaching_prep_time
  FROM
      {{.table}}
  WHERE
      order_created_at >= {{.start_timestamp}} AND order_created_at <= {{.end_timestamp}}
      AND merchant_id = {{.merchant_id}}
  GROUP BY
      station_id
  LIMIT 100
filters:
  - merchant_id
  - start_timestamp
  - end_timestamp
  - wait_time_threshold
  - prep_time_threshold
