query_backend: pinot
tenant: Blinkit
table: bistro_preprep_projection_v1
identifier: bistro_preprep_projection
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Preprep Daily Projection Data
caching_ttl: 7200
refresh_interval: 3600
sla: 100
rate_limit: 10
type: sql
sql: >
  SELECT
    outlet_id,
    sub_pid,
    pre_prep_qty,
    prep_hour,
    dish_pid
  FROM {{.table}}
  WHERE 
    outlet_id IN ({{.outlet_id_list}})
    AND pre_prep_qty > {{.pre_prep_threshold}}
filters:
  - outlet_id_list
  - pre_prep_threshold
