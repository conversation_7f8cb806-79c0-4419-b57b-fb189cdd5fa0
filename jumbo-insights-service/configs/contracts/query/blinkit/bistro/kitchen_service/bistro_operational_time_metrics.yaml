query_backend: pinot
tenant: Blinkit
table: bistro_storeops_order_metrics
identifier: bistro_operational_time_metrics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: kitchen-service
  pd_service_name: kitchen-service
  description: Bistro Operational Time Metrics
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT 
    AVG((order_billed_timestamp - approved_timestamp) / 1000) AS avg_kpt,
    AVG((order_enroute_timestamp - order_billed_timestamp) / 1000) AS avg_rhs,
    AVG((order_prep_start_timestamp - approved_timestamp) / 1000) AS avg_wait_time,
    AVG((order_prep_end_timestamp - order_prep_start_timestamp) / 1000) AS avg_prep_time,
    AVG((order_billed_timestamp - order_prep_end_timestamp) / 1000) AS avg_assembly_time,
    100 * SUM(CASE WHEN (order_billed_timestamp - rider_assigned_timestamp) >= 0 THEN 1 ELSE 0 END) 
        / COUNT(id) AS percent_direct_handover,
    100 * SUM(CASE WHEN ((order_prep_end_timestamp - approved_timestamp) / 1000 - (instore_eta - assembly_time) * 60) > {{.wait_prep_threshold}} THEN 1 ELSE 0 END) 
        / COUNT(id) AS percent_order_breaching_wait_prep_time,
    100 * SUM(CASE WHEN ((order_billed_timestamp - order_prep_end_timestamp) / 1000 - (assembly_time * 60)) > {{.assembly_threshold}} THEN 1 ELSE 0 END) 
        / COUNT(id) AS percent_order_breaching_assembly_time
  FROM {{.table}}
  WHERE 
    merchant_id = {{.merchant_id}}
    AND insert_timestamp >= {{.start_timestamp}} AND insert_timestamp <= {{.end_timestamp}}
    AND current_status IN ({{.status}})
filters:
  - merchant_id
  - status
  - wait_prep_threshold
  - assembly_threshold
  - start_timestamp
  - end_timestamp
