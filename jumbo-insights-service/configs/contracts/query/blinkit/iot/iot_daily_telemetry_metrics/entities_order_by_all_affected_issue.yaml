query_backend: pinot
tenant: Blinkit
table: iot_daily_telemetry_metrics
identifier: blinkit_iot_entities_order_by_all_affected_issue
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: iot-central-service
  pd_service_name: iot-central-service
  description: IOT Dashboard Summary
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
  SELECT 
  entity_id
  FROM iot_daily_telemetry_metrics
  WHERE "date" = {{.date}}
  GROUP BY entity_id
  ORDER BY 
  SUM(hooter_on_windows) FILTER (WHERE entity_type='OUTLET_ID')
  + SUM(room_off_windows) FILTER (WHERE entity_type='STORE_FROZEN_ROOM') 
  + SUM(door_open_windows) FILTER (WHERE entity_type='STORE_FROZEN_ROOM') 
  + SUM(temp_breach_windows) FILTER (WHERE entity_type='STORE_FROZEN_ROOM') DESC
  LIMIT 10
filters:
  - date