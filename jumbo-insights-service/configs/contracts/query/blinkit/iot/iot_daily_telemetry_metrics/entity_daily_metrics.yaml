query_backend: pinot
tenant: Blinkit
table: iot_daily_telemetry_metrics
identifier: blinkit_iot_entity_daily_metrics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: iot-central-service
  pd_service_name: iot-central-service
  description: IOT Dashboard Summary
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
  SELECT 
  entity_id,
  "date",
  SUM(hooter_on_windows) FILTER (WHERE entity_type = 'OUTLET_ID') AS hooter_on_windows,
  SUM(room_off_windows) FILTER (WHERE entity_type = 'STORE_FROZEN_ROOM') AS room_off_windows,
  SUM(door_open_windows) FILTER (WHERE entity_type = 'STORE_FROZEN_ROOM') AS door_open_windows,
  SUM(temp_breach_windows) FILTER (WHERE entity_type = 'STORE_FROZEN_ROOM') AS temp_breach_windows,
  SUM(fault_count) FILTER (WHERE entity_type = 'STORE_FROZEN_ROOM') AS fault_count,
  SUM(
    hooter_on_windows * (entity_type = 'OUTLET_ID') +
    room_off_windows * (entity_type = 'STORE_FROZEN_ROOM') +
    door_open_windows * (entity_type = 'STORE_FROZEN_ROOM') +
    temp_breach_windows * (entity_type = 'STORE_FROZEN_ROOM') +
    fault_count * (entity_type = 'STORE_FROZEN_ROOM')
  ) AS total_issues
  FROM iot_daily_telemetry_metrics
  WHERE "date" >= {{.date}}
  AND entity_id IN ({{.entity_ids}})
  GROUP BY entity_id, "date"
  ORDER BY total_issues DESC
filters:
  - date
  - entity_ids