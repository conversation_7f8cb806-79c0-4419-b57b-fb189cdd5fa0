query_backend: pinot
tenant: Blinkit
table: iot_daily_telemetry_metrics
identifier: blinkit_iot_city_daily_metrics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: iot-central-service
  pd_service_name: iot-central-service
  description: IOT Dashboard Summary
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
  SELECT 
  city_id,
  "date",
  COUNT(DISTINCT entity_id) FILTER (
    WHERE entity_type = 'OUTLET_ID' AND hooter_on_windows > 0
  ) AS hooter_on_entity_count,
  COUNT(DISTINCT entity_id) FILTER (
    WHERE entity_type = 'STORE_FROZEN_ROOM' AND room_off_windows > 0
  ) AS room_off_entity_count,
  COUNT(DISTINCT entity_id) FILTER (
    WHERE entity_type = 'STORE_FROZEN_ROOM' AND door_open_windows > 0
  ) AS door_open_entity_count,
  COUNT(DISTINCT entity_id) FILTER (
    WHERE entity_type = 'STORE_FROZEN_ROOM' AND temp_breach_windows > 0
  ) AS temp_breach_entity_count,
  COUNT(DISTINCT entity_id) FILTER (
    WHERE 
      (entity_type = 'OUTLET_ID' AND hooter_on_windows > 0) OR
      (entity_type = 'STORE_FROZEN_ROOM' AND (
        room_off_windows > 0 OR 
        door_open_windows > 0 OR 
        temp_breach_windows > 0
      ))
  ) AS total_entities_with_any_issue
  FROM iot_daily_telemetry_metrics
  WHERE "date" >= {{.date}}
  AND city_id IN ({{.city_ids}})
  GROUP BY city_id, "date"
  ORDER BY "date" DESC
filters:
  - date
  - city_ids