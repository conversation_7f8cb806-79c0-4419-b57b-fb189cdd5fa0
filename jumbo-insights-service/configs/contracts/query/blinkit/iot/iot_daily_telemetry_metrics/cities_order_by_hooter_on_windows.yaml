query_backend: pinot
tenant: Blinkit
table: iot_daily_telemetry_metrics
identifier: blinkit_iot_cities_order_by_hooter_on_windows
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: iot-central-service
  pd_service_name: iot-central-service
  description: IOT Dashboard Summary
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
  SELECT 
  city_id
  FROM iot_daily_telemetry_metrics
  WHERE "date" = {{.date}} AND (
    (entity_type = 'OUTLET_ID' AND hooter_on_windows > 0)
  )
  GROUP BY city_id
  ORDER BY COUNT(DISTINCT entity_id) DESC
filters:
  - date