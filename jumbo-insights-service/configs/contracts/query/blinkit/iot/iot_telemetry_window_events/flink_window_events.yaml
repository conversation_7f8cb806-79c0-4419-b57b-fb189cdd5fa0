query_backend: pinot
tenant: Blinkit
table: iot_telemetry_window_event
identifier: blinkit_iot_flink_window_events
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: iot-central-service
  pd_service_name: iot-central-service
  description: IOT Dashboard Summary
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
  SELECT 
  external_device_id,
  entity_id,
  COUNT(*),
  SUM(duration)
  FROM iot_telemetry_window_events
  WHERE "tenant" = {{.tenant}}
  AND "param_key" IN ({{.param_keys}})
  AND "entity_type" IN ({{.entity_types}})
  AND "end_timestamp" >= {{.start_time}}
  GROUP BY external_device_id,entity_id
filters:
  - tenant
  - param_keys
  - entity_types
  - start_time