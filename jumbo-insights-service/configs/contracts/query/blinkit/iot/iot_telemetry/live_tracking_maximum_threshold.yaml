query_backend: pinot
tenant: Blinkit
table: iot_telemetry
identifier: blinkit_iot_live_tracking_maximum_threshold
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: iot-central-service
  pd_service_name: iot-central-service
  description: IOT Dashboard Summary
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
  SELECT entity_id,
  SUM(CASE WHEN CAST(param_value AS DOUBLE) >= {{.check_value}} THEN 1 ELSE 0 END) AS count,
  COUNT(*) AS total_count
  FROM {{.table}} 
  WHERE "tenant" = {{.tenant}}
  AND "param_key" IN ({{.param_keys}})
  AND "entity_type" IN ({{.entity_types}})
  AND "device_type" IN ({{.device_types}})
  AND "event_timestamp" >= {{.start_time}}
  AND param_value != ''
  GROUP BY entity_id
filters:
  - check_value
  - tenant
  - param_keys
  - entity_types
  - device_types
  - start_time