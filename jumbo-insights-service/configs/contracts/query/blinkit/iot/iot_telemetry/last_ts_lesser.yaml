query_backend: pinot
tenant: Blinkit
table: iot_telemetry
identifier: blinkit_iot_last_ts_lesser
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: iot-central-service
  pd_service_name: iot-central-service
  description: IOT Dashboard Summary
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
  SELECT 
  entity_id,
  MAX(event_timestamp) AS max_event_timestamp
  FROM iot_telemetry
  WHERE "tenant" = {{.tenant}}
  AND "param_key" IN ({{.param_keys}})
  AND param_value != ''
  AND CAST(param_value AS DOUBLE) <= {{.param_value}}
  AND "entity_type" IN ({{.entity_types}})
  AND "device_type" IN ({{.device_types}})
  AND "event_timestamp" >= {{.start_time}}
  GROUP BY entity_id
filters:
  - tenant
  - param_keys
  - param_value
  - entity_types
  - device_types
  - start_time
