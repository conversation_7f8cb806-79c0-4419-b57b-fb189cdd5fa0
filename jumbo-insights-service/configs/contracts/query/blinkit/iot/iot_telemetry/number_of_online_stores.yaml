query_backend: pinot
tenant: Blinkit
table: iot_telemetry
identifier: blinkit_iot_online_stores_count
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: iot-central-service
  pd_service_name: iot-central-service
  description: IOT Dashboard Summary
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
  SELECT COUNT(DISTINCT "entity_id") 
  FROM {{.table}} 
  WHERE "tenant" = {{.tenant}}
  AND "device_type" IN ({{.device_types}})
  AND "entity_type" IN ({{.entity_types}})
  AND "event_timestamp"  >= {{.start_time}}
filters:
  - tenant
  - device_types
  - start_time
  - entity_types
