query_backend: pinot
tenant: Blinkit
table: iot_telemetry
identifier: blinkit_iot_online_stores
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: iot-central-service
  pd_service_name: iot-central-service
  description: IOT Dashboard Summary
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
  SELECT DISTINCT "entity_id"
  FROM {{.table}} 
  WHERE "tenant" = {{.tenant}}
  AND "event_timestamp"  >= {{.start_time}}
filters:
  - tenant
  - start_time
