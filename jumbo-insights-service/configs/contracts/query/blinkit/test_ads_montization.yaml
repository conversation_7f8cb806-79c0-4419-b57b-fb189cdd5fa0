# can be pinot/trino
query_backend: pinot
tenant: Blinkit
table: ads_monetization_v2
identifier: test_ads_monetization_v2
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: dataplatform
  pd_service_name: data_platform
  description: test query for blinkit pinot cluster
# TTL for cache, Time in seconds
caching_ttl: 3600 # one hour
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 160

# contract type
type: sql
sql: >
  SELECT COUNT(DISTINCT(ads_type))
  FROM {{.table}}
  WHERE ads_type = {{.ads_type}}

filters:
  - ads_type
