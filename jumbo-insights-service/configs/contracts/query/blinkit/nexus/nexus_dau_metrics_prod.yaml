query_backend: pinot
tenant: Blinkit
table: merchant_daily_active_users_v2
identifier: blinkit_nexus_dau_metrics_prod
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Cluster Feed Metrics
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100 
type: sql
sql: >
  SELECT 
    city,
    DATETIMECONVERT(
        window_start + 19800000, 
        '1:MILLISECONDS:EPOCH', 
        '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', 
        '1:DAYS'
    ) AS order_date,
    LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
    MAX(window_end) AS max_window_end,
    app,
    merchant_id
  FROM 
      {{.table}}
  WHERE 
      (
          (dt = DATETIMECONVERT(FromEpochSeconds(now()/1000), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') AND window_end <= FromEpochSeconds(now()/1000))
      )
      AND merchant_id IN ({{.merchant_ids}})
  GROUP BY 
      app, 
      city, 
      merchant_id, 
      DATETIMECONVERT(
          window_start + 19800000, 
          '1:MILLISECONDS:EPOCH', 
          '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', 
          '1:DAYS'
      )
filters:
  - merchant_ids
