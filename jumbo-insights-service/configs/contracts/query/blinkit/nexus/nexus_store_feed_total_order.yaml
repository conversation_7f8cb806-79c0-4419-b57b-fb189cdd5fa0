query_backend: pinot
tenant: Blinkit
table: storeops_order_metrics_v2
identifier: blinkit_store_feed_total_order
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: total order
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
  SELECT 
        merchant_id,
        SUM(CASE WHEN current_status = 'DELIVERED' THEN 1 ELSE 0 END) AS total_orders
  FROM 
      {{.table}}
  WHERE 
      merchant_id IN ({{.frontend_merchant_ids}})
      AND insert_timestamp >= {{.start_date}}
      AND insert_timestamp < {{.end_date}}
  GROUP BY merchant_id
filters:
  - frontend_merchant_ids
  - start_date 
  - end_date
