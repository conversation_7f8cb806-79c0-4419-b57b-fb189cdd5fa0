query_backend: pinot
tenant: Blinkit
table: storeops_order_metrics_v2
identifier: blinkit_nexus_picker_assignment_metrics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Picker Assignment Metrics
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT 
    SUM(
      CASE 
        WHEN (picker_assignment_queued_time IS NOT NULL AND picker_assignment_queued_time > 0) 
        THEN 1 
        ELSE 0 
      END
    ) AS ready_to_assign_orders,
    SUM(
      CASE 
        WHEN (picker_assignment_queued_time IS NULL OR picker_assignment_queued_time <= 0) 
        THEN 1 
        ELSE 0 
      END
    ) AS not_ready_to_assign_orders
  FROM {{.table}}
  WHERE merchant_id IN ({{.merchant_ids}}) 
    AND insert_timestamp >= {{.start_time}} 
    AND insert_timestamp <= {{.end_time}}
    AND picker_employee_id IS NULL 
    AND current_status = 'APPROVED'
filters:
  - merchant_ids
  - start_time
  - end_time
