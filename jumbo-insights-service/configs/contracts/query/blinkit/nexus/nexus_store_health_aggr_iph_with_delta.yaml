query_backend: pinot
tenant: Blinkit
table: storeops_activity_daily_metrics_v1
identifier: nexus_store_health_storeops_activity_interval_iph_with_delta
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Store Health IPH aggregated for weekly and monthly metrics
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT 
    frontend_merchant_id,
    CASE 
        WHEN dt >= {{.interval_start_date}} AND dt < {{.interval_end_date}} THEN {{.interval_start_date}}
        WHEN dt >= {{.delta_start_date}} AND dt < {{.delta_end_date}} THEN {{.delta_start_date}}
    END AS interval_start_day,
    SUM(packaged_putaway + milk_putaway) / (SUM(putter_active_time) * 1.0 / 3600.0) AS items_putaway_per_hour
  FROM 
      {{.table}}
  WHERE 
      (
          (dt >= {{.interval_start_date}} AND dt < {{.interval_end_date}}) OR 
          (dt >= {{.delta_start_date}} AND dt < {{.delta_end_date}})
      )
      AND frontend_merchant_id IN ({{.frontend_merchant_ids}})
  GROUP BY 
      frontend_merchant_id, interval_start_day
  ORDER BY 
      frontend_merchant_id, interval_start_day ASC
filters:
  - frontend_merchant_ids
  - interval_start_date
  - interval_end_date
  - delta_start_date
  - delta_end_date
