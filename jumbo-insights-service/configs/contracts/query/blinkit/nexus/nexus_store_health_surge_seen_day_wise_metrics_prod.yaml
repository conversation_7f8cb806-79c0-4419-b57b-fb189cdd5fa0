query_backend: pinot
tenant: Blinkit
table: ticktock_serviceability_daily_surge_info
identifier: nexus_store_health_surge_seen_day_wise_metrics_prod
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Surge Seen Info Daywise
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT 
    frontend_merchant_id,   
    DATETIMECONVERT(
        window_start_epoch,
        '1:MILLISECONDS:EPOCH',
        '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)',
        '1:DAYS'
    ) AS dt,
    LASTWITHTIME(total_carts, window_end_epoch, 'LONG') AS total_cart_instances,
    LASTWITHTIME(total_surge_carts, window_end_epoch, 'LONG') AS surge_cart_instances,
    LASTWITHTIME(rider_surge_carts, window_end_epoch, 'LONG') AS rider_surge_cart_instances,
    LASTWITHTIME(picker_surge_carts, window_end_epoch, 'LONG') AS picker_surge_cart_instances,
    LASTWITHTIME(rain_surge_carts, window_end_epoch, 'LONG') AS rain_surge_cart_instances
  FROM 
    {{.table}}
  WHERE 
    (
      (
        (DATETIMECONVERT(window_start_epoch, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)','1:DAYS') = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000  - 86400*0 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND window_end_epoch <= FromEpochSeconds({{.provided_time}}/1000 - 86400*0))
      )
      OR (
        (DATETIMECONVERT(window_start_epoch, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)','1:DAYS') = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000  - 86400*7 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND window_end_epoch <= FromEpochSeconds({{.provided_time}}/1000 - 86400*7))
      )
      OR (
        (DATETIMECONVERT(window_start_epoch, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)','1:DAYS') = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000  - 86400*14 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND window_end_epoch <= FromEpochSeconds({{.provided_time}}/1000 - 86400*14))
      )
      OR (
        (DATETIMECONVERT(window_start_epoch, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)','1:DAYS') = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000  - 86400*21 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND window_end_epoch <= FromEpochSeconds({{.provided_time}}/1000 - 86400*21))
      )
      OR (
          (DATETIMECONVERT(window_start_epoch, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)','1:DAYS') = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000  - 86400*28 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND window_end_epoch <= FromEpochSeconds({{.provided_time}}/1000 - 86400*28))
      )
    )
    AND frontend_merchant_id IN ({{.frontend_merchant_ids}})
  GROUP BY frontend_merchant_id, dt 
  ORDER BY frontend_merchant_id, dt
filters:
  - frontend_merchant_ids
  - provided_time
