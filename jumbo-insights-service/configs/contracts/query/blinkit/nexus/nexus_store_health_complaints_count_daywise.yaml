query_backend: pinot
tenant: Blinkit
table: storeops_order_complaints_v2
identifier: nexus_store_health_complaints_count_daywise
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Store Health Items and Orders Complaint Count
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT
    store_id,
    DATETIMECONVERT(
        complaint_start_time,
        '1:MILLISECONDS:EPOCH',
        '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)',
        '1:DAYS'
    ) AS dt,
    COUNT(DISTINCT order_id) AS count_of_orders_with_complaints,
    COUNT(CASE 
        WHEN product_id != 0 
          AND complaint_type NOT IN ('order_not_delivered') 
        THEN product_id 
    END) AS count_of_items_with_complaints
  FROM
      {{.table}}
  WHERE
      (
          (
              (DATETIMECONVERT(complaint_start_time, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)','1:DAYS') = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000  - 86400*0 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND complaint_start_time <= FromEpochSeconds({{.provided_time}}/1000 - 86400*0))
          )
          OR (
              (DATETIMECONVERT(complaint_start_time, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)','1:DAYS') = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000  - 86400*7 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND complaint_start_time <= FromEpochSeconds({{.provided_time}}/1000 - 86400*7))
          )
          OR (
              (DATETIMECONVERT(complaint_start_time, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)','1:DAYS') = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000  - 86400*14 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND complaint_start_time <= FromEpochSeconds({{.provided_time}}/1000 - 86400*14))
          )
          OR (
              (DATETIMECONVERT(complaint_start_time, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)','1:DAYS') = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000  - 86400*21 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND complaint_start_time <= FromEpochSeconds({{.provided_time}}/1000 - 86400*21))
          )
          OR (
              (DATETIMECONVERT(complaint_start_time, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)','1:DAYS') = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000  - 86400*28 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND complaint_start_time <= FromEpochSeconds({{.provided_time}}/1000 - 86400*28))
          )
      )
      AND store_id IN ({{.store_ids}})
  GROUP BY
      store_id,
      dt
  ORDER BY
      store_id,
      dt
filters:
  - store_ids
  - provided_time
