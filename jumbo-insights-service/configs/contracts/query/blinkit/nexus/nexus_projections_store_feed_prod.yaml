query_backend: pinot
tenant: Blinkit
table: projection_metrics_v2
identifier: blinkit_nexus_projection_prod
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Store Projection Data
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100 
type: sql
sql: >
  SELECT 
        dt,   
        event_names,
        jsonExtractScalar(event_names, '$', 'STRING') AS event_names_str,
        outlet_id,
        proj_gmv,
        proj_carts
  FROM 
      {{.table}}
  WHERE 
    outlet_id IN ({{.outlet_ids}})
    AND dt >= {{.start_date}}
    AND dt <= {{.end_date}}
  ORDER BY outlet_id, dt
filters:
  - outlet_ids
  - start_date 
  - end_date 
