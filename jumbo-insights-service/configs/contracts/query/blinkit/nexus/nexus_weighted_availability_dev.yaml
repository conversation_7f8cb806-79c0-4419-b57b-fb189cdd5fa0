query_backend: pinot
tenant: Blinkit
table: store_hourly_weighted_availability
identifier: blinkit_weighted_availability_dev
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Weighted Availability
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100 
type: sql
sql: >
  SELECT outlet_id,weighted_availability
  FROM {{.table}} WHERE outlet_id
  IN ({{.outlet_ids}})
filters:
  - outlet_ids
