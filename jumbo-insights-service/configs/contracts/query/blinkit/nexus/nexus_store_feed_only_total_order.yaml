query_backend: pinot
tenant: Blinkit
table: storeops_order_metrics_v2
identifier: blinkit_store_feed_only_total_order
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: total order v2 
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100 
type: sql
sql: >
  SELECT 
        merchant_id,
        count(*) as total_orders
  FROM 
      {{.table}}
  WHERE 
      merchant_id IN ({{.frontend_merchant_ids}})
      AND insert_timestamp >= {{.start_date}}
      AND insert_timestamp < {{.end_date}}
      AND approved_timestamp > 0
  GROUP BY merchant_id
filters:
  - frontend_merchant_ids
  - start_date 
  - end_date
