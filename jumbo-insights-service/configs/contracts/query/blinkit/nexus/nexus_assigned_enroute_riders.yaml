query_backend: pinot
tenant: Blinkit
table: storeops_order_metrics_v2
identifier: blinkit_nexus_assigned_riders
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Assigned Riders
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT 
    SUM(
      CASE 
        WHEN current_status = 'ENROUTE' 
        THEN 1 
        ELSE 0 
      END
    ) AS enroute_riders,
    SUM(
      CASE 
        WHEN delivery_fe_id IS NOT NULL 
          AND current_status = 'BILLED' 
          AND update_timestamp < {{.riders_assigned_to_enroute_time}} 
        THEN 1 
        ELSE 0 
      END
    ) AS riders_assigned_not_enroute
  FROM {{.table}}
  WHERE merchant_id IN ({{.merchant_ids}})
    AND insert_timestamp >= {{.start_time}} 
    AND insert_timestamp <= {{.end_time}}
filters:
  - merchant_ids
  - start_time
  - end_time
  - riders_assigned_to_enroute_time
