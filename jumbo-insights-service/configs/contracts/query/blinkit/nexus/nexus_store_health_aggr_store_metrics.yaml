query_backend: pinot
tenant: Blinkit
table: nexus_store_health_daily_aggr_metrics
identifier: nexus_store_health_aggr_store_metrics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Store Health aggregated store metricsfor weekly and monthly view
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT 
    outlet_id,

    {{.interval_start_date}} AS interval_start_date,
    {{.delta_start_date}} AS delta_start_date,

    -- Metrics
    (SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN c2a_within_10_sec_orders ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN sonar_orders ELSE 0 END), 0)) * 100 AS c2a,

    (SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN direct_handover_orders ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN sonar_orders ELSE 0 END), 0)) * 100 AS dh,

    SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN picking_time_sec ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN total_items_quantity_ordered ELSE 0 END), 0) AS ppi,

    (1 - (SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN picker_pna_orders ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN sonar_orders ELSE 0 END), 0))) * 100 AS fill_rate,

    (SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN surge_carts ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN carts ELSE 0 END), 0)) * 100 AS over_all_surgeseen,

    SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN rider_surge_carts ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN carts ELSE 0 END), 0) * 100 AS rider_surgeseen,

    SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN rain_surge_carts ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN carts ELSE 0 END), 0) * 100 AS rain_surgeseen,

    SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN picker_surge_carts ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN carts ELSE 0 END), 0) * 100 AS picker_surgeseen,
    
    SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN item_complaints ELSE 0 END)  AS items_complaints_count,
    SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN order_with_complaints ELSE 0 END)  AS order_complaints_count,
    
     SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN order_with_complaints ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN delivered_orders ELSE 0 END), 0) * 100 AS complaint_percentage,
    
    (SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN (packaged_qty + perishables_others) ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.interval_start_date}} AND date_ <= {{.interval_end_date}} THEN putter_active_time_minutes ELSE 0 END), 0)) * 60 AS items_putaway_per_hour,


    -- Deltas
    (SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN c2a_within_10_sec_orders ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN sonar_orders ELSE 0 END), 0)) * 100 AS delta_c2a,

    SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN direct_handover_orders ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN sonar_orders ELSE 0 END), 0) * 100 AS delta_dh,

    SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN picking_time_sec ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN total_items_quantity_ordered ELSE 0 END), 0) AS delta_ppi,

    (1 - (SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN picker_pna_orders ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN sonar_orders ELSE 0 END), 0))) * 100 AS delta_fill_rate,

    SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN surge_carts ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN carts ELSE 0 END), 0) * 100 AS delta_over_all_surgeseen,

    SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN rider_surge_carts ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN carts ELSE 0 END), 0) * 100 AS delta_rider_surgeseen,

    SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN rain_surge_carts ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN carts ELSE 0 END), 0) * 100 AS delta_rain_surgeseen,

    SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN picker_surge_carts ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN carts ELSE 0 END), 0) * 100 AS delta_picker_surgeseen,
    
    SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN item_complaints ELSE 0 END)  AS delta_items_complaints_count,

    SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN order_with_complaints ELSE 0 END)  AS delta_order_complaints_count,
    
    SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN order_with_complaints ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN delivered_orders ELSE 0 END), 0) * 100 AS delta_complaint_percentage,
    
    (SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN (packaged_qty + perishables_others) ELSE 0 END) 
    / NULLIF(SUM(CASE WHEN date_ >= {{.delta_start_date}} AND date_ <= {{.delta_end_date}} THEN putter_active_time_minutes ELSE 0 END), 0)) * 60 AS delta_items_putaway_per_hour

  FROM 
    {{.table}}
  WHERE 
      outlet_id IN ({{.merchant_ids}})
      AND date_ BETWEEN {{.delta_start_date}} AND {{.interval_end_date}}
  GROUP BY outlet_id
  ORDER BY outlet_id, interval_start_date ASC
filters:
  - merchant_ids
  - interval_start_date
  - interval_end_date
  - delta_start_date
  - delta_end_date
