query_backend: pinot
tenant: Blinkit
table: fact_order_details_v6
identifier: blinkit_nexus_store_fill_rate_ppi
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Store Fill Rate PPI Metrics
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100 
type: sql
sql: >
  SELECT order_date, merchant_id, 
         100 * (SUM(CASE WHEN current_status != 'APPROVED' THEN 1 ELSE 0 END) - 
         (SUM(CASE WHEN (sm_fill_rate = 0 AND current_status != 'APPROVED') THEN 1 ELSE 0 END))) / 
         (SUM(CASE WHEN current_status != 'APPROVED' THEN 1 ELSE 0 END)) AS sm_fill_rate,
         SUM((CASE WHEN (pick_completion_time < 0 OR picking_start_time < 0) THEN 0 ELSE pick_completion_time - picking_start_time END)/1000) / SUM(((total_items_quantity - jsonextractscalar(paas_metrics, '$.quantity', 'INT', 0)) + jsonextractscalar(paas_metrics, '$.sku', 'INT', 0))) as ppi_in_seconds
  FROM {{.table}} 
  WHERE order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS')
  AND org_channel_id IN ('1', '2')
  AND city_name NOT IN ('Not in service area')
  AND merchant_id IN ({{.merchant_ids}})
  GROUP BY order_date, merchant_id
  ORDER BY merchant_id, order_date      
filters:
  - merchant_ids
