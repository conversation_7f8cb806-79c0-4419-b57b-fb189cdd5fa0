query_backend: pinot
tenant: Blinkit
table: nexus_outlet_agg_employees_active_time_v1
identifier: blinkit_nexus_agg_employees_active_time
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Employees Instore Time
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT 
    outlet_id,
    snapshot_date_ist,
    snapshot_hour_ist,
    total_active_hours
  FROM 
    {{.table}}
  WHERE 
    outlet_id IN ({{.merchant_ids}})
    AND snapshot_date_ist >= {{.dt}}
filters:
  - merchant_ids
  - dt