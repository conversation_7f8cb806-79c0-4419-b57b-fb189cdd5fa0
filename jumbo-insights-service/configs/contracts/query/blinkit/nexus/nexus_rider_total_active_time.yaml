query_backend: pinot
tenant: Blinkit
table: rider_login_time_metrics_v2
identifier: blinkit_nexus_rider_active_time
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Riders Total Active Time
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT "date", 
         frontend_merchant_id, 
         frontend_merchant_name, 
         hour_,
         SUM(total_active_mins) AS rider_total_active_mins
  FROM {{.table}}
  WHERE frontend_merchant_id IN ({{.merchant_ids}})
    AND "date" IN ({{.dts}})
  GROUP BY "date", frontend_merchant_id, frontend_merchant_name, hour_
  ORDER BY hour_
filters:
  - merchant_ids
  - dts
