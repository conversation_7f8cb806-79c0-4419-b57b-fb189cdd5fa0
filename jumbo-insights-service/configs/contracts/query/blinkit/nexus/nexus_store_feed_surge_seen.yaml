query_backend: pinot
tenant: Blinkit
table: ticktock_serviceability_daily_surge_info
identifier: blinkit_store_feed_surge_seen_info
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Surge Seen Info
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100 
type: sql
sql: >
  SELECT 
        frontend_merchant_id,   
        ToDateTime(window_start_epoch, 'yyyy-MM-dd', 'Asia/Kolkata') AS dt,
        LASTWITHTIME(total_carts, window_end_epoch, 'LONG') AS total_cart_instances,
        LASTWITHTIME(total_surge_carts, window_end_epoch, 'LONG') AS surge_cart_instances,
        LASTWITHTIME(rider_surge_carts, window_end_epoch, 'LONG') AS rider_surge_cart_instances,
        LASTWITHTIME(picker_surge_carts, window_end_epoch, 'LONG') AS picker_surge_cart_instances,
        LASTWITHTIME(rain_surge_carts, window_end_epoch, 'LONG') AS rain_surge_cart_instances
  FROM 
      {{.table}}
  WHERE 
      window_start_epoch >= (ToEpochSeconds(DATETRUNC('DAY', DATEADD('DAY', 0, NOW()))) - 19800) * 1000
      AND frontend_merchant_id IN ({{.frontend_merchant_ids}})
  GROUP BY frontend_merchant_id, dt 
  ORDER BY frontend_merchant_id, dt
filters:
  - frontend_merchant_ids
