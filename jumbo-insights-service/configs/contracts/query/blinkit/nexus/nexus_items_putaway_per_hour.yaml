query_backend: pinot
tenant: Blinkit
table: storeops_activity_metrics
identifier: blinkit_average_putaway_per_hour
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Average number of items put away per hour
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT dt,frontend_merchant_id,
         (SUM(packaged_putaway+milk_putaway) / (SUM(putter_active_time)/3600)) AS items_putaway_per_hour
  FROM {{.table}} WHERE dt IN ({{.dts}})
    AND frontend_merchant_id IN ({{.frontend_merchant_id}})
  GROUP BY dt, frontend_merchant_id
  ORDER BY frontend_merchant_id, dt
filters:
  - frontend_merchant_id
  - dts
