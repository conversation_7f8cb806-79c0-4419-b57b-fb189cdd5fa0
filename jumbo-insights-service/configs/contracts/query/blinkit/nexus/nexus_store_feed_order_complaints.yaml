query_backend: pinot
tenant: Blinkit
table: storeops_order_complaints_v2
identifier: blinkit_store_feed_total_order_complaints
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: total orders complaints 
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100 
type: sql
sql: >
  SELECT 
        store_id,
        COUNT(DISTINCT order_id) AS total_order_with_complaints, 
        MIN(complaint_start_time) AS complaint_start_time,
        MAX(complaint_end_time) AS complaint_end_time
  FROM 
      {{.table}}
  WHERE 
      store_id IN ({{.outlet_ids}})
      AND complaint_start_time >= {{.start_date}}
      AND complaint_end_time <= {{.end_date}}
  GROUP BY store_id
filters:
  - outlet_ids
  - start_date 
  - end_date
