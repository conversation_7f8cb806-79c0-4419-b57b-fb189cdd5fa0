query_backend: pinot
tenant: Blinkit
table: merchant_outlet_facility_mapping_v2
identifier: blinkit_store_outlet_ids
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Store OutletIds 
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100 
type: sql
sql: >
  SELECT
    facility_id,
    frontend_merchant_id,
    backend_merchant_id, 
    pos_outlet_id,
    pos_outlet_name
  FROM 
    {{.table}}  
  WHERE 
    is_active IN ({{.active}})
filters:
  - active