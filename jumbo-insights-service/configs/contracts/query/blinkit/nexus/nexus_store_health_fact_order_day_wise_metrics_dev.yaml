query_backend: pinot
tenant: Blinkit
table: fact_order_details_v6
identifier: blinkit_nexus_store_health_fact_order_day_wise_metrics_dev
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Store Health Fact Order Day Wise Metrics Dev
caching_ttl: 300
refresh_interval: 60
sla: 250
rate_limit: 100 
type: sql
sql: >
  SELECT 
    order_date, 
    merchant_id, 
    COUNT(id) AS total_orders,
    SUM(CASE WHEN current_status = 'DELIVERED' THEN 1 ELSE 0 END) AS delivered_orders,
    SUM(CASE WHEN current_status = 'CANCELLED' THEN 1 ELSE 0 END) AS cancelled_orders,
    SUM(CASE WHEN order_billed_timestamp > 0 THEN 1 ELSE 0 END) AS billed_orders,
    SUM(CASE WHEN (order_billed_timestamp - insert_timestamp) <= {{.time_threshold_ms}} AND approved_timestamp > 0 THEN 1 ELSE 0 END) AS instore_time_under_x_seconds_orders_count,
    SUM(
        CASE 
            WHEN current_status = 'DELIVERED' 
            THEN order_billed_timestamp - picker_assignment_queued_time 
            ELSE 0 
        END
    ) / NULLIF(
        SUM(CASE WHEN current_status = 'DELIVERED' THEN 1 ELSE 0 END), 0
    ) AS avg_in_store_time,
    100 * (
        SUM(CASE WHEN current_status != 'APPROVED' THEN 1 ELSE 0 END) - 
        SUM(CASE WHEN (sm_fill_rate = 0 AND current_status != 'APPROVED') THEN 1 ELSE 0 END)
    ) / NULLIF(
        SUM(CASE WHEN current_status != 'APPROVED' THEN 1 ELSE 0 END), 0
    ) AS sm_fill_rate,
    100 * SUM(
        CASE 
            WHEN (order_billed_timestamp - rider_assigned_timestamp) >= 0 
            THEN 1 
            ELSE 0 
        END
    ) / NULLIF(COUNT(id), 0) AS percentage_order_assigned_before_billing,
    100 * SUM(
        CASE 
            WHEN current_status = 'CANCELLED' 
            THEN 1 
            ELSE 0 
        END
    ) / NULLIF(COUNT(id), 0) AS cancellation_percentage,
    100 * SUM(
        CASE 
            WHEN (picker_assigned_timestamp - picker_assignment_queued_time) <= 10000 
            THEN 1 
            ELSE 0 
        END
    ) / NULLIF(COUNT(id), 0) AS percentage_checkout_to_picker_assigned,
    COUNT(DISTINCT customer_id) AS transacting_users_count,
    SUM(
        CASE 
            WHEN (pick_completion_time < 0 OR picking_start_time < 0) 
            THEN 0 
            ELSE pick_completion_time - picking_start_time 
        END
    ) / 1000 / NULLIF(
        SUM(
            (total_items_quantity - jsonextractscalar(paas_metrics, '$.quantity', 'INT', 0)) + 
            jsonextractscalar(paas_metrics, '$.sku', 'INT', 0)
        ), 
        0
    ) AS ppi_in_seconds
  FROM 
      {{.table}} 
  WHERE 
      (
        (order_date = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000 -  86400*0  ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
        OR
        (order_date = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000  - 86400*1 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
        OR
        (order_date = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000  - 86400*2 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
        OR
        (order_date = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000  - 86400*3 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
        OR
        (order_date = DATETIMECONVERT(FromEpochSeconds({{.provided_time}}/1000  - 86400*4 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
      )
      AND org_channel_id IN ('1', '2')
      AND city_name NOT IN ('Not in service area')
      AND merchant_id IN ({{.merchant_ids}})
  GROUP BY 
      order_date, 
      merchant_id
  ORDER BY 
      merchant_id, 
      order_date
filters:
  - merchant_ids
  - provided_time
  - time_threshold_ms
