query_backend: pinot
tenant: Blinkit
table: order_complaint_metrics_daily_aggr_v1
identifier: nexus_store_health_aggr_complaint_metrics_with_delta
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: Store Health Items and Orders Complaint Count aggregated for weekly and monthly
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100
type: sql
sql: >
  SELECT 
      outlet_id,
      {{.interval_start_date}} AS interval_start_date,
      {{.delta_start_date}} AS delta_start_date,
      complaint_type,
      SUM(CASE WHEN dt >= {{.interval_start_date}} AND dt <= {{.interval_end_date}} 
               THEN item_complaints ELSE 0 END) AS sum_item_complaints,
      SUM(CASE WHEN dt >= {{.interval_start_date}} AND dt <= {{.interval_end_date}} 
               THEN orders_with_complaints ELSE 0 END) AS sum_orders_with_complaints,
      SUM(CASE WHEN dt >= {{.delta_start_date}} AND dt <= {{.delta_end_date}} 
               THEN item_complaints ELSE 0 END) AS delta_sum_item_complaints,
      SUM(CASE WHEN dt >= {{.delta_start_date}} AND dt <= {{.delta_end_date}} 
               THEN orders_with_complaints ELSE 0 END) AS delta_sum_orders_with_complaints
  FROM 
      {{.table}}
  WHERE  
      dt BETWEEN {{.delta_start_date}} AND {{.interval_end_date}}
      AND outlet_id IN ({{.merchant_ids}})
  GROUP BY 
      outlet_id, complaint_type
  ORDER BY 
      outlet_id, interval_start_date ASC
filters:
  - merchant_ids
  - interval_start_date
  - interval_end_date
  - delta_start_date
  - delta_end_date
