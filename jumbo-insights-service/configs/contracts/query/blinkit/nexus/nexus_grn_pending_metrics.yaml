query_backend: pinot
tenant: Blinkit
table: consignment_item_pending_grn_events_v2
identifier: blinkit_pending_grn
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: nexus-backend-service
  pd_service_name: nexus-backend-service
  description: GRN Pending
caching_ttl: 300
refresh_interval: 60
sla: 200
rate_limit: 100 
type: sql
sql: >
  SELECT
    site_id,
    SUM(
      CASE 
        WHEN grn_ts IS NOT NULL THEN 0
        ELSE GREATEST(dispatch_quantity - putaway_quantity, 0)
      END
    ) AS pending_grn
  FROM 
    {{.table}}  
  WHERE 
    site_id IN ({{.outlet_ids}})
    AND dispatch_ts is not NULL
    AND unloading_started_ts is not NULL
    AND dispatch_ts >= {{.dispatch_date}}
    AND label = 'OVERALL'
  GROUP BY 
    site_id
filters:
  - outlet_ids
  - dispatch_date
