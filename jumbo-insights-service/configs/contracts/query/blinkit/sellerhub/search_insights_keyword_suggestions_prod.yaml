query_backend: pinot
tenant: Blinkit
table: sellerhub_keywords_rank_v1
identifier: blinkit_sellerhub_keyword_suggestions_prod
audit:
  author_email: rahul.k<PERSON><EMAIL>
  team_email: <EMAIL>
  service: seller-central
  pd_service_name: seller-central
  description: Search Insights Keyword Suggestions
caching_ttl: 1800
refresh_interval: 900
sla: 50
rate_limit: 100
type: open_sql
sql: >
  SELECT 
    keyword
  FROM 
    {{.table}}
  WHERE 
    TEXT_MATCH(keyword, '/.*{{.input_word}}.*/')
    ORDER BY rank ASC
filters:
  - input_word