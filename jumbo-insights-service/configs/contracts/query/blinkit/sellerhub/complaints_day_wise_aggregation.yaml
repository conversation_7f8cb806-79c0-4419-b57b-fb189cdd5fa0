query_backend: pinot
tenant: Blinkit
table: blinkit_order_complaints
identifier: blinkit_sellerhub_complaints_day_wise
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: seller-central
  pd_service_name: seller-central
  description: Product Complaints and Aggregated Data on Order Date
caching_ttl: 900
refresh_interval: 450
sla: 100
rate_limit: 100
type: sql
sql: >
  SELECT
    DATETIMECONVERT(
        order_date,
        '1:SECONDS:EPOCH',
        '1:DAYS:SIMPLE_DATE_FORMAT:dd-MM-yyyy',
        '1:DAYS',
        'Asia/Kolkata'
    ) AS order_date,
    COUNT(1) AS complaint_count,
    SUM(CASE WHEN complaint_type IN ({{.return_complaint_type_list}}) THEN 1 ELSE 0 END) as return_count
  FROM blinkit_order_complaints
  WHERE order_date BETWEEN {{.order_start_date}} AND {{.order_end_date}} 
  AND product_id IN ({{.product_id_list}})
  AND (LOOKUP('merchant_outlet_facility_mapping_v2', 'pos_outlet_city_name', 'frontend_merchant_id', fe_merchant_id) IN ({{.city_name_list}}) OR ({{.city_name_fallback}}=1))
  GROUP BY order_date
  ORDER BY order_date
filters:
  - return_complaint_type_list
  - order_start_date
  - order_end_date
  - product_id_list
  - city_name_list
  - city_name_fallback
