query_backend: pinot
tenant: Blinkit
table: sellerhub_keywords_rank_v1
identifier: blinkit_sellerhub_keyword_default_suggestions_dev
audit:
  author_email: rahul.k<PERSON><EMAIL>
  team_email: <EMAIL>
  service: seller-central
  pd_service_name: seller-central
  description: Search Insights default top keywords
caching_ttl: 7200
refresh_interval: 3600
sla: 50
rate_limit: 100
type: sql
sql: >
  SELECT 
    keyword
    FROM 
        {{.table}}
    ORDER BY rank ASC
filters: []