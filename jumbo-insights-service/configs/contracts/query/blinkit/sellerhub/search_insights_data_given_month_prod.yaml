query_backend: pinot
tenant: Blinkit
table: sellerhub_agg_monthly_keyword_searches_v1
identifier: blinkit_sellerhub_search_insights_monthly_data_prod
audit:
  author_email: rahul.k<PERSON><EMAIL>
  team_email: <EMAIL>
  service: seller-central
  pd_service_name: seller-central
  description: Search Insights data in a given month
caching_ttl: 900
refresh_interval: 800
sla: 100
rate_limit: 100
type: sql
sql: >
  SELECT keyword, city_id, city_name, searched, 
    CASE
      WHEN searched = 0 THEN 0
      ELSE COALESCE(atcs/searched, 0)
    END AS conversion_rate, search_trend, most_bought_products
    FROM {{.table}} 
    WHERE keyword IN ({{.keyword_list}}) AND (city_id in ({{.city_id_list}}) OR {{.filter_fallback}} = 1) AND month_start_date = {{.input_date}}
    AND (
      CASE  
        WHEN searched = 0 THEN 0
        ELSE COALESCE(atcs/searched, 0)
      END BETWEEN {{.conversion_rate_lower_limit}} and {{.conversion_rate_upper_limit}}
    )
filters:
  - keyword_list
  - city_id_list
  - filter_fallback
  - input_date
  - conversion_rate_lower_limit
  - conversion_rate_upper_limit