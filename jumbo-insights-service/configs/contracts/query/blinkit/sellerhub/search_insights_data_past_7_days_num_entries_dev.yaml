query_backend: pinot
tenant: Blinkit
table: sellerhub_agg_t_7_keyword_searches_v3
identifier: blinkit_sellerhub_search_insights_past_7_days_data_num_entries_dev
audit:
  author_email: rahul.<PERSON><PERSON><PERSON><PERSON>@grofers.com
  team_email: <EMAIL>
  service: seller-central
  pd_service_name: seller-central
  description: Number of entries in Search Insights data for past 7 days
caching_ttl: 900
refresh_interval: 450
sla: 100
rate_limit: 100
type: sql
sql: >
  SELECT COUNT(*) AS num_entries
    FROM {{.table}} 
    WHERE keyword IN ({{.keyword_list}}) AND (city_id in ({{.city_id_list}}) OR {{.filter_fallback}} = 1) AND at_date_ist = {{.input_date}}
    AND (
      CASE  
        WHEN searched = 0 THEN 0
        ELSE COALESCE(atcs/searched, 0)
      END BETWEEN {{.conversion_rate_lower_limit}} and {{.conversion_rate_upper_limit}}
    )
filters:
  - keyword_list
  - city_id_list
  - filter_fallback
  - input_date
  - conversion_rate_lower_limit
  - conversion_rate_upper_limit