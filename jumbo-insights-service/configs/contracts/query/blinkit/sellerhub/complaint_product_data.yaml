query_backend: pinot
tenant: Blinkit
table: blinkit_order_complaints
identifier: blinkit_sellerhub_product_complaint
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: seller-central
  pd_service_name: seller-central
  description: Product Complaints Data
caching_ttl: 900
refresh_interval: 450
sla: 100
rate_limit: 100
type: sql
sql: >
  SELECT complaint_id, complaint_type, cart_id, suborder_id, order_id
  FROM blinkit_order_complaints
  WHERE order_date BETWEEN {{.order_start_date}} AND {{.order_end_date}}
  AND product_id IN ({{.product_id_list}})
  AND (complaint_type in ({{.complaint_type_list}}) OR {{.complaint_type_list_fallback}}=1)
  AND (LOOKUP('merchant_outlet_facility_mapping_v2', 'pos_outlet_city_name', 'frontend_merchant_id', fe_merchant_id) IN ({{.city_name_list}}) OR ({{.city_name_fallback}}=1))
filters:
  - order_start_date
  - order_end_date
  - product_id_list
  - complaint_type_list
  - complaint_type_list_fallback
  - city_name_list
  - city_name_fallback
