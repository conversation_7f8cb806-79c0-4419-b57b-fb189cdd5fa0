query_backend: pinot
tenant: Blinkit
table: blinkit_order_complaints
identifier: blinkit_sellerhub_agg_product_complaint_type_wise_complaints
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: seller-central
  pd_service_name: seller-central
  description: Product Complaints and Aggregated Data on Complaint Type
caching_ttl: 900
refresh_interval: 450
sla: 100
rate_limit: 100
type: sql
sql: >
  SELECT
    product_id,
  	complaint_type,
    COUNT(1) as complaint_count
    FROM blinkit_order_complaints
    WHERE order_date BETWEEN {{.order_start_date}} AND {{.order_end_date}}
    AND product_id IN ({{.product_id_list}})
    AND (LOOKUP('merchant_outlet_facility_mapping_v2', 'pos_outlet_city_name', 'frontend_merchant_id', fe_merchant_id) IN ({{.city_name_list}}) OR ({{.city_name_fallback}}=1))
    GROUP BY product_id, complaint_type
filters:
  - order_start_date
  - order_end_date
  - product_id_list
  - city_name_list
  - city_name_fallback
