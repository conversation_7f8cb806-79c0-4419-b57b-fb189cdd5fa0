# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: weather_graphcast_prediction
identifier: prediction_data_for_cities
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: weather-service
  pd_service_name: z-weather-service
  description: query to get weather prediction data for cities

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    city_name_manual,
    rain_class,
    rain_in_mm,
    prediction_for_datetime,
    current_datetime
  FROM weather_graphcast_prediction
  WHERE city_name_manual != 'Nil'
  ORDER BY city_name_manual, prediction_for_datetime

filters: []
