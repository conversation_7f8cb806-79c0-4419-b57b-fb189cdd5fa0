query_backend: trino
tenant: Zomato
catalog: zomato
schema: insights_etls
table: weather_device_enriched_data
identifier: weather_device_data_download
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: weather_service
  pd_service_name: weather_service
  description: Weather Device Data download

# Time in second
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 60000
# Request per second
rate_limit: 100
# contract type
type: sql
# presign time hours
presign_time_hours: 24
sql: > 
      SELECT 
      city_name,
      locality_name,
      device_date_time,
      CASE 
            WHEN rainfall_intensity_mm = -111.00 THEN 'NULL' 
            ELSE CAST(CAST(rainfall_intensity_mm AS DECIMAL(18, 2)) AS VARCHAR(20))
      END as rain_intensity,
      CASE 
            WHEN rainfall_accumulation_mm = -111.00 THEN 'NULL' 
            ELSE CAST(CAST(rainfall_accumulation_mm AS DECIMAL(18, 2)) AS VARCHAR(20))
      END as rain_accumulation,
      CASE 
            WHEN current_humidity_perc = -111.00 THEN 'NULL'
            ELSE CAST(CAST(current_humidity_perc AS DECIMAL(18, 2)) AS VARCHAR(20))
      END as humidity,
      CASE 
            WHEN current_temperature_deg_c = -111.00 THEN 'NULL'
            ELSE CAST(CAST(current_temperature_deg_c AS DECIMAL(18, 2)) AS VARCHAR(20))
      END as temperature,
      CASE 
            WHEN wind_direction_deg = -111.00 THEN 'NULL'
            ELSE CAST(CAST(wind_direction_deg AS DECIMAL(18, 2)) AS VARCHAR(20))
      END as wind_direction,
      CASE 
            WHEN wind_speed_ms = -111.00 THEN 'NULL'
            ELSE CAST(CAST(wind_speed_ms AS DECIMAL(18, 2)) AS VARCHAR(20))
      END as wind_speed
      FROM {{.table}}
      where dt >= {{.start_dt}} and dt <= {{.end_dt}}
      AND device_serial_number = (
        select min(device_serial_number) 
        FROM {{.table}}
        where dt >= {{.start_dt}} and dt <= {{.end_dt}}
        and ({{.locality_id}} = 0
              OR locality_id = {{.locality_id}} )
        AND ({{.start_time}} = 0
            OR report_time > {{.start_time}} )
        AND ({{.end_time}} = 0
            OR report_time < {{.end_time}} )
      )
      AND ({{.locality_id}} = 0
      OR locality_id = {{.locality_id}} )
      AND ({{.start_time}} = 0
      OR report_time > {{.start_time}} )
      AND ({{.end_time}} = 0
      OR report_time < {{.end_time}} )
      ORDER BY report_time DESC

filters:
  - start_dt
  - end_dt
  - locality_id
  - start_time
  - end_time