# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: weather_graphcast_prediction
identifier: distinct_prediction_datetimes_non_city
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: weather-service
  pd_service_name: z-weather-service
  description: query to get distinct prediction datetimes for non-city (latitude/longitude) data

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT DISTINCT prediction_for_datetime
  FROM weather_graphcast_prediction
  WHERE city_name_manual = 'Nil'
    AND locality_id = -111
  ORDER BY prediction_for_datetime

filters: []
