# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: weather_graphcast_prediction
identifier: prediction_data_bounding_box
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: weather-service
  pd_service_name: z-weather-service
  description: query to get weather prediction data for a bounding box defined by southwest and northeast coordinates

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    CASE
      WHEN {{.col_id}} = 1 THEN rain_in_mm
      WHEN {{.col_id}} = 2 THEN temperature
      WHEN {{.col_id}} = 3 THEN mean_sea_level_pressure
      WHEN {{.col_id}} = 4 THEN ten_m_u_component_of_wind
      WHEN {{.col_id}} = 5 THEN ten_m_v_component_of_wind 
      WHEN {{.col_id}} = 6 THEN ten_m_magnitude_of_wind
      ELSE -111
    END AS weather_variable,
    latitude,
    longitude
  FROM weather_graphcast_prediction
  WHERE latitude BETWEEN {{.SW_lat}} AND {{.NE_lat}}
    AND (
      CASE
        WHEN {{.SW_lon}} <= {{.NE_lon}} THEN longitude >= {{.SW_lon}} AND longitude <= {{.NE_lon}}
        ELSE longitude >= {{.SW_lon}} OR longitude <= {{.NE_lon}}
      END
    )
    AND city_name_manual = 'Nil'
    AND locality_id = -111
    AND prediction_for_datetime = {{.prediction_for_datetime}}
  ORDER BY 
    latitude,
    CASE
      WHEN {{.SW_lon}} <= {{.NE_lon}} THEN longitude
      ELSE CASE WHEN longitude < {{.SW_lon}} THEN longitude + 360 ELSE longitude END
    END
    
filters:
  - col_id
  - SW_lat
  - NE_lat
  - SW_lon
  - NE_lon
  - prediction_for_datetime
