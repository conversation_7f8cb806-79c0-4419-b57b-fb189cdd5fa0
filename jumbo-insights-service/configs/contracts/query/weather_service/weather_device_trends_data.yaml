query_backend: pinot
tenant: Zomato
table: weather_station_events
identifier: weather_device_trends_data
audit:
  author_email: nishan<PERSON>.<EMAIL>
  team_email: <EMAIL>
  service: weather-service
  pd_service_name: z_weather_service
  description: Weather Device Trends Data
# Time in seconds
caching_ttl: 3600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 900
# Time in ms
sla: 100
# Request per second
rate_limit: 300
# contract type
type: sql
sql: > 
      SELECT 
      HOUR(("time" + 19800) * 1000) as report_hour,
      SUM(case WHEN current_temperature_deg_c != -111 THEN current_temperature_deg_c ELSE 0 END) / SUM(case WHEN current_temperature_deg_c != -111 THEN 1 ELSE 0 END) AS current_temperature,
      SUM(case WHEN rainfall_intensity_mm != -111 THEN rainfall_intensity_mm ELSE 0 END) / SUM(case WHEN rainfall_intensity_mm != -111 THEN 1 ELSE 0 END) AS rain_intensity,
      SUM(case WHEN wind_direction_deg != -111 THEN wind_direction_deg ELSE 0 END) / SUM(case WHEN wind_direction_deg != -111 THEN 1 ELSE 0 END) AS wind_degree,
      SUM(case WHEN wind_speed_ms != -111 THEN wind_speed_ms ELSE 0 END) / SUM(case WHEN wind_speed_ms != -111 THEN 1 ELSE 0 END) AS wind_speed,
      SUM(case WHEN current_humidity_perc != -111 THEN current_humidity_perc ELSE 0 END) / SUM(case WHEN current_humidity_perc != -111 THEN 1 ELSE 0 END) AS humidity,
      SUM(CASE WHEN rainfall_intensity_mm != -111 THEN rainfall_intensity_mm ELSE 0 END) AS rain_precipitation
        FROM {{.table}}
      WHERE device_serial_number IN ({{.device_ids}})
        AND "time" BETWEEN {{.start_time}} AND {{.end_time}}
        GROUP BY report_hour

filters:
  - device_ids
  - start_time
  - end_time

