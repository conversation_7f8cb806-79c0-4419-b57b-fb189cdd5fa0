query_backend: pinot
tenant: Zomato
table: weather_graphcast_prediction
identifier: prediction_variable_min_max
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: weather-service
  pd_service_name: z-weather-service
  description: Query to get the minimum and maximum values of the provided weather variable from prediction data

caching_ttl: 1200
refresh_interval: 300
sla: 100
rate_limit: 200
type: open_sql
sql: >
  SELECT
    MIN({{.col_name}}) AS min_variable_value,
    MAX({{.col_name}}) AS max_variable_value
  FROM weather_graphcast_prediction
  WHERE city_name_manual = 'Nil'
    AND locality_id = -111
  GROUP BY 'constant'

filters:
  - col_name
