query_backend: pinot
tenant: Zomato
table: weather_station_events
identifier: weather_device_pings_timestamps
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: weather-service
  pd_service_name: z-weather-service
  description: Weather Device Ping Timestamps
# Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 300
# contract type
type: sql
sql: > 
      SELECT
        DATETIMECONVERT(report_time, '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd''T''HH:mm:ss''Z''', '1:SECONDS:EPOCH', '1:SECONDS') AS device_time_epoch
      FROM weather_station_events
      WHERE device_serial_number = {{ .device_id }}
        AND device_time_epoch BETWEEN {{ .start_time }} AND {{ .end_time }}
        AND rainfall_intensity_mm >= 0
      GROUP by device_time_epoch
      ORDER BY device_time_epoch

filters:
  - device_id
  - start_time
  - end_time

