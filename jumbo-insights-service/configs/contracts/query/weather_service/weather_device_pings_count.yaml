query_backend: pinot
tenant: Zomato
table: weather_station_events
identifier: weather_device_pings_count
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: weather-service
  pd_service_name: z-weather-service
  description: Weather Device Pings Count
# Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 300
# contract type
type: sql
sql: > 
      SELECT device_serial_number,
            COUNT(DISTINCT report_time)
      FROM weather_station_events
      WHERE device_serial_number IN ({{ .device_ids }})
        AND "time" BETWEEN {{ .start_time }} AND {{ .end_time }}
        AND rainfall_intensity_mm >= 0
      GROUP BY 1

filters:
  - device_ids
  - start_time
  - end_time

