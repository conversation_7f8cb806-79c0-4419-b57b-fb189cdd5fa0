query_backend: pinot
tenant: Zomato
table: dineout_res_stats
identifier: dineout_stats
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: zomato-pay-service
  pd_service_name: zomato-pay-service
  description: stats for dineout merchant dashboard

# Time in second
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 160
# Dimensions
columns:
  - name: res_id
  - name: ULV_users
    func: sum
    source_column: ulv_users
  - name: visits
    func: sum
    source_column: visits
  - name: total_transacting_users
    func: sum
    source_column: total_transacting_users
  - name: total_txns
    func: sum
    source_column: total_txns
  - name: new_transacting_users
    func: sum
    source_column: new_transacting_users
  - name: new_user_txns
    func: sum
    source_column: new_user_txns
# Group by columns
aggregations:
  - res_id
filters:
  - res_id
  - agg_window