# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: restaurant_status_active_log
identifier: res_status_change_log
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: zomato_web
  pd_service_name: merchant_platform
  description: Restaurant status changelog
# Time in second
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 2
# contract type
type: sql
sql: >
  SELECT res_id, event_name, ToDateTime("time"*1000, 'yyyy-MM-dd HH:mm:ss', 'Asia/Kolkata') as changed_date, user_id, 
  reason, reason_id, source, origin
  FROM {{.table}}
  WHERE "time" BETWEEN {{.from_time}} AND {{.to_time}}
    AND event_name IN ({{.event_name_array}})
    AND res_id IN ({{.res_id}})
filters:
  - res_id
  - event_name_array
  - from_time
  - to_time
