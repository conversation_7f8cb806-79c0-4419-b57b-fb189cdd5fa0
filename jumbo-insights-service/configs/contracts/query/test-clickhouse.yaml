# can be pinot/trino
query_backend: clickhouse
tenant: Zomato
table: chat_backend_service
identifier: test-clickhouse
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: chat_backend_service
  pd_service_name: chat_backend_service
  description: clickhouse-integration-test

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: > 
  select
      toStartOfMinute(ts) as mn,
      count() as cnt
  from
     {{.table}}
  where
      msg like {{.message1}}
      AND msg LIKE {{.message2}}
      AND ts BETWEEN FROM_UNIXTIME({{.ts_from}}) AND FROM_UNIXTIME({{.ts_to}})
  group by
      mn
  order by
      mn
filters:
  - ts_from
  - ts_to
  - message1
  - message2
