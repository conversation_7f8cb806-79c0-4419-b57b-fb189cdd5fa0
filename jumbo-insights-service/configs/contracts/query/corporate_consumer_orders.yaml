 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: corporate_consumer_orders
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: corporate-funds-service
  pd_service_name: corporate-funds-service
  description: Total Sales and delivered orders for Corporate Consumers
# TTL for cache, Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT consumer_order_user_profile_selected_primary_id,
  SUM(consumer_order_total_cost) as total_sales,
  COUNT(consumer_order_id) as delivered_orders
  FROM composite_order_events
  WHERE consumer_order_created_at BETWEEN {{.start_time}} AND {{.end_time}}
  AND consumer_order_user_profile_selected_primary_id IN ({{.consumer_order_user_profile_selected_primary_id}})
  AND consumer_order_state IN ({{.consumer_order_state}})
  GROUP BY consumer_order_user_profile_selected_primary_id
filters:
  - consumer_order_user_profile_selected_primary_id
  - consumer_order_state
  - start_time
  - end_time
