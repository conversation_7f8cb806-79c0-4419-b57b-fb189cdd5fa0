query_backend: pinot
tenant: Zomato
table: dining_daily_stats
identifier: dining_aggregated_engagement_insights
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: dining-service
  pd_service_name: dining-service
  description: stats for dineout merchant dashboard

# Time in second
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# Dimensions
type: sql
sql: >
  SELECT dt,
  SUM(impression_sessions) as impression_sessions,
  SUM(impression_users) as impression_users,
  SUM(impression_new_user_sessions) as impression_new_user_sessions,
  SUM(impression_new_users) as impression_new_users,
  SUM(impression_repeat_user_sessions) as impression_repeat_user_sessions,
  SUM(impression_repeat_users) as impression_repeat_users,
  SUM(click_sessions) as click_sessions,
  SUM(click_users) as click_users,
  SUM(click_new_user_sessions) as click_new_user_sessions,
  <PERSON>UM(click_new_users) as click_new_users,
  <PERSON>UM(click_repeat_user_sessions) as click_repeat_user_sessions,
  <PERSON><PERSON>(click_repeat_users) as click_repeat_users,
  <PERSON>UM(ulvs) as ulvs,
  SUM(ulv_users) as ulv_users,
  SUM(new_user_ulvs) as new_user_ulvs,
  SUM(new_ulv_users) as new_ulv_users,
  SUM(repeat_ulvs) as repeat_ulvs,
  SUM(repeat_ulv_users) as repeat_ulv_users
  FROM {{.table}}
  WHERE res_id IN ({{.res_id}})
  AND dt IN ({{.dt}})
  AND agg_window = {{.agg_window}}
  GROUP BY 1

filters:
- res_id
- agg_window
- dt

