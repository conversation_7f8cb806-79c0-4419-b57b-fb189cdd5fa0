query_backend: pinot
tenant: Zomato
table: dining_daily_stats_v3
identifier: dining_res_aggregated_insights
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: dining-service
  pd_service_name: dining-service
  description: stats for dining merchant dashboard

# Time in second
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# Dimensions
type: sql
sql: >
  SELECT res_id,
  SUM(gross_revenue) AS gross_revenue,
  SUM(net_revenue) AS net_revenue,
  SUM(net_revenue_walk_in) AS net_revenue_walk_in,
  SUM(net_revenue_tr) AS net_revenue_tr,
  SUM(gross_revenue_new_users) AS gross_revenue_new_users,
  SUM(gross_revenue_repeat_users) AS gross_revenue_repeat_users,
  SUM(total_txns) AS total_txns,
  SUM(txn_new_users) AS txn_new_users,
  SUM(txn_repeat_users) AS txn_repeat_users,
  SUM(txns_walk_in) AS txns_walk_in,
  SUM(txns_tr) AS txns_tr,
  SUM(total_bookings) AS total_bookings,
  SUM(total_free_bookings) AS total_free_bookings,
  SUM(total_paid_bookings) AS total_paid_bookings,
  SUM(total_covers_sold) AS total_covers_sold,
  SUM(tr_gross_revenue) AS tr_gross_revenue,
  SUM(cofunding_burn) AS cofunding_burn,
  SUM(dining_delights_burn) AS dining_delights_burn,
  SUM(bank_discount_burn) AS bank_discount_burn,
  SUM(bank_discount_merchant_burn) AS bank_discount_merchant_burn,
  SUM(ulvs) AS ulvs,
  SUM(search_led_ulvs) AS search_led_ulvs,
  SUM(menu_opens) AS menu_opens,
  MAX(menu_age) AS menu_age,
  SUM(photo_opens) AS photo_opens,
  SUM(calls_requested) AS calls_requested,
  SUM(res_card_impressions) AS res_card_impressions,
  SUM(ctr*res_card_impressions)/100 as res_card_taps,
  SUM(vp_visits_delivered) AS vp_visits_delivered,
  SUM(ip_impressions_delivered) AS ip_impressions_delivered,
  SUM(vp_ad_spend) AS vp_ad_spend,
  SUM(ip_ad_spend) AS ip_ad_spend,
  SUM(vp_users_session_level) AS vp_users_session_level,
  SUM(ip_users_session_level) AS ip_users_session_level,
  SUM(inventory_sl) AS inventory_sl,
  SUM(tr_sl) AS tr_sl,
  SUM(calls_received) AS calls_received,
  SUM(calls_answered) AS calls_answered,
  SUM(calls_missed) AS calls_missed,
  AVG(call_duration) AS avg_call_duration_seconds,
  AVG(pick_up_time) AS avg_call_pick_up_time_seconds
  FROM {{.table}}
  WHERE res_id IN ({{.res_id}})
  AND dt >= {{.start_date}}
  AND dt <= {{.end_date}}
  GROUP BY 1

filters:
  - res_id
  - start_date
  - end_date
