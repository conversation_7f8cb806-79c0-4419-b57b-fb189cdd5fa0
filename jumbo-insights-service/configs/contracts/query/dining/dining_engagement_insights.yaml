query_backend: pinot
tenant: Zomato
table: dining_daily_stats
identifier: dining_engagement_insights
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: dining-service
  pd_service_name: dining-service
  description: stats for dineout merchant dashboard

# Time in second
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# Dimensions
type: sql
sql: >
  SELECT *
  FROM {{.table}}
  WHERE res_id IN ({{.res_id}})
  AND dt IN ({{.dt}})
  AND agg_window = {{.agg_window}}

filters:
- res_id
- agg_window
- dt

