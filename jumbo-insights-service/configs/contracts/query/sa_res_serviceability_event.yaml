# can be pinot/trino
query_backend: trino
tenant: Zomato
catalog: hive
schema: jumbo2
table: res_serviceability_events
identifier: sa_res_events_logs
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: serviceability-aggregator
  pd_service_name: serviceability-aggregator-service
  description: serviceability debugger dashboard

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT timestamp,
  admin_active_flag_off_709,
  automated_lp_city_kill_status,
  city_id,
  delivery_status_flag_off_710,
  entity_type,
  locality_id,
  menu_unavailability,
  merchant_status,
  o2_city_killed_101,
  restaurant_logs_timing_active_status,
  restaurant_non_logs_timing_active_status,
  restaurant_temporarily_closed_711,
  sa_serviceable,
  serviceable,
  shutdown
  FROM {{.table}}
  WHERE dt={{.date}}
  AND entity_id ={{.res_id}}
  AND timestamp > {{.start_timestamp}}
  AND timestamp < {{.end_timestamp}}
filters:
  - date
  - res_id
  - start_timestamp
  - end_timestamp
