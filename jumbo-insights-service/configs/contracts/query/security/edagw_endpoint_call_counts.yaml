# can be pinot/trino
query_backend: clickhouse
tenant: Zomato
table: ed_api_gateway_service
identifier: security_edagw_endpoint_call_counts
audit:
  author_email: kr.<PERSON><PERSON><PERSON><PERSON>@zomato.com
  team_email: <EMAIL>
  service: z-ed-api-gateway
  pd_service_name: z-ed-api-gateway
  description: Find the daily endpoint call counts for a given endpoint

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: > 
  SELECT
    schema_violation['message.log.route'] AS route,
    schema_violation['message.log.method'] AS method,
    COUNT(*) AS count
  FROM cluster('logs_cluster','prod', 'ed_api_gateway_service')
  WHERE
    schema_violation['message.log.level'] = {{.level}}
    AND mapContains(schema_violation, 'message.log.route') = 1
    AND ts BETWEEN FROM_UNIXTIME({{.ts_from}}) AND FROM_UNIXTIME({{.ts_to}})
  GROUP BY
    schema_violation['message.log.route'],
    schema_violation['message.log.method']
filters:
  - ts_from
  - ts_to
  - level
