# can be pinot/trino
query_backend: clickhouse
tenant: Zomato
table: zomato_api_gateway
identifier: security_zomato_api_gateway_endpoint_call_counts
audit:
  author_email: kr.<PERSON><PERSON><PERSON><PERSON>@zomato.com
  team_email: <EMAIL>
  service: zomato_api-gateway
  pd_service_name: zomato_api-gateway
  description: Find the daily endpoint call counts for a given endpoint

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: > 
  SELECT
    schema_violation['message.log.route'] AS route,
    schema_violation['message.log.method'] AS method,
    COUNT(*) AS count
  FROM cluster('logs_cluster','prod', 'zomato_api_gateway')
  WHERE
    schema_violation['message.log.level'] = {{.level}}
    AND mapContains(schema_violation, 'message.log.route') = 1
    AND ts BETWEEN FROM_UNIXTIME({{.ts_from}}) AND FROM_UNIXTIME({{.ts_to}})
  GROUP BY
    schema_violation['message.log.route'],
    schema_violation['message.log.method']
filters:
  - ts_from
  - ts_to
  - level
