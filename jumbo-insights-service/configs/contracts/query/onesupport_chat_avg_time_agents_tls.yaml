# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: zomato_chat_agent_metrics
identifier: chat_avg_time_agents_tls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: chat-v2-backend-service
  pd_service_name: chat-backend-v2-service
  description: Avg metric value for agents of tls

# Time in second
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 200
# Request per second
rate_limit: 1000

# contract type
type: sql
sql: >
  SELECT agent_id, AVG(CAST(event_value AS INT)) as value
  FROM {{.table}}
  WHERE event_name = {{.event_name}}
  AND tl_id IN ({{.tl_ids}})
  AND "time" BETWEEN {{.start_time}} AND {{.end_time}}
  GROUP BY agent_id
filters:
  - event_name
  - tl_ids
  - start_time
  - end_time
