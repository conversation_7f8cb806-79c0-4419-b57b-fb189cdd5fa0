 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: logistics_zone_metrics
identifier: logistics_zone_metrics_realtime
audit:
  author_email: k<PERSON>hn<PERSON>.<EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Get logistics zone level realtime metrics for fleet coach app
# TTL for cache, Time in seconds
caching_ttl: 1000
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT 
    dt, 
    time_interval, 
    SUM(active_riders) AS active_riders, 
    MAX(salience_loss) AS salience_loss,
    SUM(slot_bookings) AS slot_bookings, 
    SUM(slot_capacity) slot_capacity, 
    SUM(slot_compliance) AS slot_compliance
  FROM {{.table}}
  WHERE 
    dt = {{.dt}}
    AND time_interval IN ({{.time_interval}})
    AND zone_id = {{.zone_id}}
    AND carrier_type IN ({{.carrier_type}})
  GROUP BY dt, time_interval, zone_id
filters:
  - dt
  - time_interval
  - zone_id
  - carrier_type
