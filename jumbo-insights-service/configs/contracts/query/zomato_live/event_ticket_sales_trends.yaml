# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: zlive_order_changelog
identifier: ticket_sales_trend
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: zomaland-php
  pd_service_name: zomaland
  description: ticket sales and gmv trends for zlive event merchant dashboard

# Time in second
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 200
# Request per second
rate_limit: 100

# contract type
type: sql
sql: >
  SELECT
      CASE
      WHEN {{.agg_level}} = 1
        THEN concat(
          year(FromDateTime(order_created_at, 'yyyy-MM-dd''T''HH:mm:ssZ')),
          LPAD(MONTH(FromDateTime(order_created_at, 'yyyy-MM-dd''T''HH:mm:ssZ')), 2, '0'),
          '-'
        )
      WHEN {{.agg_level}} = 2
        THEN concat(
          year(FromDateTime(order_created_at, 'yyyy-MM-dd''T''HH:mm:ssZ')),
          LPAD(week(FromDateTime(order_created_at, 'yyyy-MM-dd''T''HH:mm:ssZ')), 2, '0'),
          '-'
        )
      WHEN {{.agg_level}} = 3
        THEN concat(
          year(FromDateTime(order_created_at, 'yyyy-MM-dd''T''HH:mm:ssZ')),
            concat(
              LPAD(MONTH(FromDateTime(order_created_at, 'yyyy-MM-dd''T''HH:mm:ssZ')), 2, '0'),
              LPAD(DAY(FromDateTime(order_created_at, 'yyyy-MM-dd''T''HH:mm:ssZ')), 2, '0'),
              ''
            ),
          ''
        )
      ELSE ''     
      END as agg_label,
  sum(ticket_quantity - (case when cancelled_ticket_quantity < 0 then 0 else cancelled_ticket_quantity end)) AS tickets,
  sum(ticket_total_amount - total_ticket_refunded_amount) AS GMV
  FROM {{.table}}
  WHERE order_status in ('ORDER_STATUS_COMPLETED', 'ORDER_STATUS_CANCELLED')
  AND payment_status in ('PAYMENT_STATUS_SUCCESS', 'PAYMENT_STATUS_REFUNDED')
  AND zlive_event_id  IN ({{.zlive_event_id}})
  AND FromDateTime(order_created_at, 'yyyy-MM-dd''T''HH:mm:ssZ') >= {{.start_time}}
  AND FromDateTime(order_created_at, 'yyyy-MM-dd''T''HH:mm:ssZ') <= {{.end_time}}
  AND user_id != '318766414'
  AND order_source_type != 'ORDER_SOURCE_TYPE_COMPLIMENTARY'
  AND ticket_id != -1
  GROUP BY 1
filters:
  - zlive_event_id
  - start_time
  - end_time
  - agg_level
