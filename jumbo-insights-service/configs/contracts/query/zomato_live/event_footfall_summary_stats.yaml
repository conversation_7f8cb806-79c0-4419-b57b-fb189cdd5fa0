# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: zlive_order_changelog
identifier: zlive_footfall_summary
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: zomaland-php
  pd_service_name: zomaland
  description: overall ticket sales & gmv stats for zlive event merchant dashboard

# Time in second
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 200
# Request per second
rate_limit: 100

# contract type
type: sql
sql: >
  SELECT ticket_id,
  sum(ticket_quantity - (case when cancelled_ticket_quantity < 0 then 0 else cancelled_ticket_quantity end)) AS count,
  sum(redeemed_quantity) AS redeemed_count
  FROM {{.table}}
  WHERE order_status = 'ORDER_STATUS_COMPLETED'
  AND payment_status = 'PAYMENT_STATUS_SUCCESS'
  AND zlive_event_id  IN ({{.zlive_event_id}})
  AND ticket_id != -1
  GROUP BY 1
filters:
  - zlive_event_id
