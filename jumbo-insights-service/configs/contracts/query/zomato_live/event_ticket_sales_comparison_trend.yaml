# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: zlive_order_changelog
identifier: events_ticket_sales_comparison_trends
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: zomaland-php
  pd_service_name: zomaland
  description: comparison trends of tickets sales on different dates between events for zlive event merchant dashboard

# Time in second
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 200
# Request per second
rate_limit: 100

# contract type
type: sql
sql: >
  SELECT
  concat(
    year(FromDateTime(order_created_at, 'yyyy-MM-dd''T''HH:mm:ssZ')),
      concat(
        LPAD(MONTH(FromDateTime(order_created_at, 'yyyy-MM-dd''T''HH:mm:ssZ')), 2, '0'),
        LPAD(DAY(FromDateTime(order_created_at, 'yyyy-MM-dd''T''HH:mm:ssZ')), 2, '0'),
        ''
      ),
    ''
  ) as date_string,
  zlive_event_id as event_id,
  sum(ticket_quantity - (case when cancelled_ticket_quantity < 0 then 0 else cancelled_ticket_quantity end)) AS tickets
  FROM {{.table}}
  WHERE order_status = 'ORDER_STATUS_COMPLETED'
  AND payment_status = 'PAYMENT_STATUS_SUCCESS'
  AND zlive_event_id  IN ({{.zlive_event_id}})
  AND FromDateTime(order_created_at, 'yyyy-MM-dd''T''HH:mm:ssZ') >= {{.start_time}}
  AND FromDateTime(order_created_at, 'yyyy-MM-dd''T''HH:mm:ssZ') <= {{.end_time}}
  AND order_source_type != 'ORDER_SOURCE_TYPE_COMPLIMENTARY'
  AND user_id != '318766414'
  AND ticket_id != -1
  GROUP BY 1, 2
filters:
  - zlive_event_id
  - start_time
  - end_time
