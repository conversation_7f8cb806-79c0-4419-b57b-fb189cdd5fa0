# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: zlive_order_changelog
identifier: zlive_total_tickets_sales_and_gmv
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: zomaland-php
  pd_service_name: zomaland
  description: stats for zlive event merchant dashboard

# Time in second
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 200
# Request per second
rate_limit: 100

# contract type
type: sql
sql: >
  SELECT sum(ticket_quantity - (case when cancelled_ticket_quantity < 0 then 0 else cancelled_ticket_quantity end)) AS tickets,
  sum(ticket_total_amount - total_ticket_refunded_amount) AS GMV,
  sum(ticket_total_amount - total_ticket_refunded_amount - tax_amount * (case when ticket_quantity < 0 then 0 else ((ticket_quantity - cancelled_ticket_quantity) / ticket_quantity) end)) AS Net_gmv
  FROM {{.table}}
  WHERE order_status in ('ORDER_STATUS_COMPLETED', 'ORDER_STATUS_CANCELLED')
  AND payment_status in ('PAYMENT_STATUS_SUCCESS', 'PAYMENT_STATUS_REFUNDED')
  AND zlive_event_id  IN ({{.zlive_event_id}})
  AND ticket_id != -1
filters:
  - zlive_event_id
