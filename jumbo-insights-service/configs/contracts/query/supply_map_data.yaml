# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: trend_stats
identifier: ztrends_supply_map_data
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Order volume for each price range bucket
# TTL for cache, Time in seconds
caching_ttl: 3600 # one hour
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 160
# Dimensions
columns:
  - name: item_entity_type
  - name: item_entity_id
  - name: location_entity_id
  - name: location_entity_type
  - name: item_name
  - name: res_volume
    func: sum
    source_column: res_volume
# Group by columns
aggregations:
  - item_entity_type
  - item_entity_id
  - item_name
  - location_entity_id
  - location_entity_type

filters:
  - item_entity_type
  - item_entity_id
  - location_entity_type
  - location_entity_id
  - country_id
  - state_id
  - city_id
