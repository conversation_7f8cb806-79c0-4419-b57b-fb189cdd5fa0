# can be pinot/trino
query_backend: trino
tenant: Zomato
catalog: hive
schema: jumbo_derived
table: vendor_payouts
identifier: rider_weekly_payout_metrics_for_vendors
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: driver-service-grpc
  pd_service_name: driver-service-grpc
  description: driver service
# Time in second
# caching not implemented in async querier
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 60000 # one minute
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  Select 
  WEEK(date_parse(payout_date, '%Y-%m-%dT%H:%i:%sZ'))-1 as Week,
  vp.driver_id as "FEID",
  coalesce(rdas.driver_category,'zomato') as "Driver Category",
  vp.city_name as "City",
  net_payout as "Gross Earnings",
  delivery_charge as "Total Delivery Charges with taxes",
  availability_fee as "Availability Fees",
  deductions as "Deduction Amount",
  pocket_withdrawals as "Pocket Withdrawal",
  (dp_cash_settlement + cash_recovery_from_vendor) as "Excess Cash",
  dp_cash_settlement as "DP Cash Settlement",
  cash_recovery_from_vendor as "Cash Recovery From Vendor",
  deduction_recovery_from_vendor as "Deduction Recovery From Vendor",
  previous_week_settlement_adjustment as "Settlement Adjustment",
  rider_payout as "DP Payout"
  FROM jumbo_derived.vendor_payouts as vp
  left join jumbo_derived.rider_daily_accounting_summary as rdas on rdas.dt = vp.dt and rdas.driver_id = vp.driver_id
  WHERE vp.dt = {{.date}}
  AND vendor_id = {{.vendor_id}}
filters:
  - vendor_id
  - date
