# can be pinot/trino
query_backend: trino
tenant: Zomato
catalog: zomato
schema: rbac_logistics_etls
table: city_delivery_drivers_master
identifier: rider_daily_metric_for_vendors
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: driver-service-grpc
  pd_service_name: driver-service-grpc
  description: driver service
# Time in second
# caching not implemented in async querier
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 60000 # one minute
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  Select
      date_format(date_parse(ddm.dt, '%Y%m%d'), '%Y/%m/%d') as "Account Date", 
      ddm.vendor_name as "Vendor",
      ddm.driver_id as "FEID",
      dp_name as "Delivery Partner Name",
      runnr_city as "City", 
      zone_name as "Zone", 
      lifecycle_status as "Status",
      login_hours as "Total Login Hours",
      drop_tps as "Total Orders",
      CASE WHEN booked_gigs != 0
      THEN CONCAT(CAST((compliant_gigs * 100.0 / booked_gigs) AS VARCHAR), '%')
      END AS "Gigs Compliance",
      cancellations as "Cancellations",
      denials as "Denials",
      breached_orders_10_min as "Delays",
      pocket_withdrawal as "Pocket Withdrawal",
      joining_bonus_reversal as "JB Reversal",
      cod_orders as "Total COD Orders",
      excess_cash as "Excess Cash",
      CONCAT(CAST(hotspot_compliance_score AS VARCHAR), '%') as "Hotspot Compliance Score",
      surge_orders as "Surge Orders",
      ROUND(support_fees + slot_cancellation_fee + manual_deductions + joining_bonus_reversal, 2) as "Deductions",
      ROUND(ldrp_amount + manual_additions + base_pay_amount + bonus_pay_amount + pickup_drop_incentive_amount + calls_per_order_incentive_amount + daily_milestone_amount + weekly_milestone_amount + distance_amount + hourly_pay + joining_bonus + ming_amount + multi_pickup_pay + order_rating_incentive_amount + reservationpay_amount + surge_pay_amount + trip_min_guarantee + wait_time_pay + weekly_ming_amount + offer_incentive + referral_bonus + support_fees + slot_cancellation_fee + manual_deductions + joining_bonus_reversal, 2) as "Net Earning",
      vehicle_number as "Vehicle Number"
  FROM rbac_logistics_etls.city_delivery_drivers_master as ddm
  left join jumbo_derived.driver_vehicle_mapping
  as dvm on dvm.dt = ddm.dt and dvm.driver_id = ddm.delivery_driver_id
  WHERE ddm.dt = {{.date}}
  AND ddm.vendor_id = {{.vendor_id}}
filters:
  - vendor_id
  - date
