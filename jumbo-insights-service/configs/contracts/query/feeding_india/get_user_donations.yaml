# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: feeding_india_donation_stats
identifier: feeding_india_user_donation_amount
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: feeding_india
  pd_service_name: feeding-india-pager
  description: user's total donation donated to Feeding India
# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 200
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT feeding_india_donation as donation_amount
  FROM feeding_india_donation_stats 
  WHERE user_id = {{.user_id}} 
filters:
  - user_id
