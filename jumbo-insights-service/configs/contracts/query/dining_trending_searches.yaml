# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: dining_trending_searches
identifier: dining_trending_searches
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: dining-service
  pd_service_name: dining-service
  description: Get dining trending searches for a location
# TTL for cache, Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 120
# Request per second
rate_limit: 800
# contract type
type: sql
sql: >
  SELECT entity_id, entity_name, entity_type, rank, searches, trending_until_hour, image_url
  FROM {{.table}}
  WHERE location_id = ({{.location_id}})
  AND location_type = ({{.location_type}})
  AND hour_agg = ({{.hour_agg}})
filters:
  - location_id
  - location_type
  - hour_agg
