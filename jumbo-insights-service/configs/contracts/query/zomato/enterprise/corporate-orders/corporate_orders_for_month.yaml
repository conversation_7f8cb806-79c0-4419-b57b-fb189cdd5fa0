# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: zfe_limit_transaction_events
identifier: zfe_orders_insights_datewise
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: corporate-funds-service
  pd_service_name: corporate-funds-service
  description: Limit orders for month

# Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT COUNT(DISTINCT order_id) AS order_count, 
    SUM(CASE WHEN transaction_type = 'TRANSACTION_TYPE_DEBIT' THEN amount ELSE 0 END)- SUM(CASE WHEN transaction_type = 'TRANSACTION_TYPE_REFUND' THEN amount ELSE 0 END)
    as net_amount,
    COUNT(DISTINCT employee_id) AS employees_who_placed_order
  FROM zfe_limit_transaction_events 
  WHERE company_id = {{.company_id}} 
    AND "timestamp" BETWEEN {{.start_date}} AND {{.end_date}} 

filters:
  - company_id
  - start_date
  - end_date