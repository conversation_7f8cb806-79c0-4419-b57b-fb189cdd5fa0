query_backend: trino
tenant: Zomato
catalog: hive
schema: jumbo2
table: email_tracking
identifier: email_tracking_events
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: email_service
  pd_service_name: email-service
  description: Email tracking events

# Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql

sql: >
  SELECT time, event_name, message_id, client_id, from_email, to_email, to_domain, subject,
         tenant, type, category, provider, url_fragment, error_code, error_message, tags, template_id, dt
  FROM {{.table}}
  WHERE
    dt >= {{.from_date}} AND dt <= {{.to_date}}
    AND time >= {{.from_time}} AND time <= {{.to_time}}
    AND case when {{.hashed_to_email_id}} != '' then to_email = {{.hashed_to_email_id}} else true end
    AND case when {{.from_email}} != '' then from_email = {{.from_email}} else true end
    AND case when {{.client}} != '%%' then client_id LIKE {{.client}} else true end
    AND case when {{.template_id}} != '' then template_id = {{.template_id}} else true end
    AND case when {{.message_id}} != '' then message_id = {{.message_id}} else true end
    AND case when {{.subject}} != '%%' then subject LIKE {{.subject}} else true end
    AND case when CARDINALITY( ARRAY[{{.event_name}}] ) = 1 and ARRAY[{{.event_name}}][1] = '' then true else event_name IN ({{.event_name}}) end
    AND case when CARDINALITY( ARRAY[{{.category}}] ) = 1 and ARRAY[{{.category}}][1] = '' then true else category IN ({{.category}}) end
    AND case when CARDINALITY( ARRAY[{{.type}}] ) = 1 and ARRAY[{{.type}}][1] = '' then true else type IN ({{.type}}) end
    AND case when CARDINALITY( ARRAY[{{.tenant}}] ) = 1 and ARRAY[{{.tenant}}][1] = '' then true else tenant IN ({{.tenant}}) end

filters:
  - hashed_to_email_id
  - from_date
  - to_date
  - from_time
  - to_time
  - from_email
  - client
  - template_id
  - message_id
  - subject
  - event_name
  - category
  - type
  - tenant
