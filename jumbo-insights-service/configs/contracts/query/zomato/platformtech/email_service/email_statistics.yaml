query_backend: trino
tenant: Zomato
catalog: zomato
schema: jumbo2
table: email_tracking
identifier: email_statistics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: email_service
  pd_service_name: email-service
  description: Email statistics related events

# Time in seconds
caching_ttl: 600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql

sql: >
  SELECT
  COUNT(
    DISTINCT IF(
      event_name = 'EMAIL_DELIVERED',
      message_id,
      NULL
    )
  ) AS total_unique_delivered_email_count,
  COUNT(
    DISTINCT IF(
      event_name = 'EMAIL_SENT',
      message_id,
      NULL
    )
  ) AS total_unique_email_count,
  COUNT(
    DISTINCT IF(
      event_name = 'EMAIL_DELIVERED',
      to_email,
      NULL
    )
  ) AS unique_users_reached
  FROM {{.table}}
  WHERE
    dt >= {{.from_date}} AND dt <= {{.to_date}}
    AND time >= {{.from_time}} AND time <= {{.to_time}}

filters:
  - from_date
  - to_date
  - from_time
  - to_time
