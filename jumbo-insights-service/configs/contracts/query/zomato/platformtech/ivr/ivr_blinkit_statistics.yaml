query_backend: trino
tenant: Zomato
catalog: zomato
schema: blinkit_jumbo2
table: telecom_ivr_tracking
identifier: blinkit_ivr_statistics_events
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: telecom_service
  pd_service_name: telecom-service
  description: Telecom blinkit ivr statistics related events

# Time in seconds
caching_ttl: 600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql

sql: >
  SELECT
  COUNT(
    DISTINCT IF(
      event_name = 'IVR_REQUEST_SENT',
      request_id,
      NULL
    )
  ) AS total_ivr_requested_count,
  COUNT(
    DISTINCT IF(
      event_name IN ('busy', 'BUSY', 'no-answer', 'NOANSWER', 'COMPLETED', 'completed'),
      request_id,
      NULL
    )
  ) AS total_ivr_successful_count,
  COUNT(
    DISTINCT IF(
      event_name IN ('COMPLETED', 'completed'),
      receiver_sha256,
      NULL
    )
  ) AS unique_users_reached
  FROM {{.table}}
  WHERE
  dt >= {{.from_date}} AND dt <= {{.to_date}}
  AND timestamp >= {{.from_time}} AND timestamp <= {{.to_time}}

filters:
  - from_date
  - to_date
  - from_time
  - to_time
