query_backend: trino
tenant: Zomato
catalog: zomato
schema: jumbo2
table: telecom_ivr_tracking
identifier: ivr_tracking_events
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: telecom_service
  pd_service_name: telecom-service
  description: Telecom ivr tracking events

# Time in seconds
caching_ttl: 600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql

sql: >
  SELECT timestamp, request_id, service_type, event_name, provider_call_id, caller, receiver_sha256, provider, recording_duration,
       start_time, end_time, duration, ringing_time, keypress, error_message, tracking_metadata, tenant, context_id, dt,
       receiver_number_details.isd_code as receiver_isd_code, receiver_number_details.sha256_number_without_isd as receiver_number_without_isd_sha256,
       receiver_number_details.sha256_number_with_isd as receiver_number_with_isd_sha256
  FROM {{.table}}
  WHERE
  dt >= {{.from_date}} AND dt <= {{.to_date}}
  AND timestamp >= {{.from_time}} AND timestamp <= {{.to_time}}
  AND case when {{.request_id}} != '' then request_id = {{.request_id}} else true end
  AND case when {{.provider_call_id}} != '' then provider_call_id = {{.provider_call_id}} else true end
  AND case when {{.context_id}} != '' then context_id = {{.context_id}} else true end
  AND case when {{.caller}} != '' then caller = {{.caller}} else true end
  AND case when {{.receiver_sha256}} != '' then receiver_sha256 = {{.receiver_sha256}} else true end
  AND case when {{.receiver_number_with_isd_sha256}} != '' then coalesce(receiver_number_details.sha256_number_with_isd = {{.receiver_number_with_isd_sha256}}, false) else true end
  AND case when {{.receiver_number_without_isd_sha256}} != '' then coalesce(receiver_number_details.sha256_number_without_isd = {{.receiver_number_without_isd_sha256}}, false) else true end
  AND case when {{.error_message}} != '' then error_message = {{.error_message}} else true end
  AND case when CARDINALITY(ARRAY[{{.event_name}}]) = 1 and ARRAY[{{.event_name}}][1] = '' then true else event_name IN ({{.event_name}}) end
  AND case when CARDINALITY(ARRAY[{{.tenant}}]) = 1 and ARRAY[{{.tenant}}][1] = '' then true else tenant IN ({{.tenant}}) end
  AND case when CARDINALITY(ARRAY[{{.service_type}}]) = 1 and ARRAY[{{.service_type}}][1] = '' then true else service_type IN ({{.service_type}}) end
  AND case when CARDINALITY(ARRAY[{{.provider}}]) = 1 and ARRAY[{{.provider}}][1] = '' then true else provider IN ({{.provider}}) end

filters:
  - from_date
  - to_date
  - from_time
  - to_time
  - request_id
  - service_type
  - event_name
  - provider_call_id
  - context_id
  - caller
  - receiver_sha256
  - receiver_number_with_isd_sha256
  - receiver_number_without_isd_sha256
  - provider
  - tenant
  - error_message
