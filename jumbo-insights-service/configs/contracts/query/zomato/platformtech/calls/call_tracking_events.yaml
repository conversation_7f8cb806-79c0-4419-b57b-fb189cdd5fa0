query_backend: trino
tenant: Zomato
catalog: zomato
schema: jumbo2
table: telecom_call_tracking
identifier: call_tracking_events
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: telecom_service
  pd_service_name: telecom-service
  description: Telecom call tracking events

# Time in seconds
caching_ttl: 600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql

sql: >
  SELECT timestamp, connection_id, event_name, call_type, provider, isd_code, a_party_numbers, 
       b_party_numbers, a_pins, b_pins, duration, country_id, order_id, caller_type, receiver_type, caller_id, receiver_id, 
       context_id, tenant, client, meta_data, call_direction, virtual_number, virtual_number_id, 
       alternate_virtual_number, error_message, error_code, call_status, caller_phone_number_sha256, 
       receiver_phone_number_sha256, call_duration, on_call_duration, call_sid, end_time, disconnected_by, dt,
       caller_number_details.isd_code AS caller_isd_code, caller_number_details.sha256_number_without_isd AS caller_number_without_isd_sha256, 
       receiver_number_details.isd_code AS receiver_isd_code, receiver_number_details.sha256_number_without_isd AS receiver_number_without_isd_sha256,
       json_format(CAST(a_party_number_details AS JSON)) AS a_party_number_details_json, json_format(CAST(b_party_number_details AS JSON)) AS b_party_number_details_json
  FROM {{.table}}
  WHERE
  dt >= {{.from_date}} AND dt <= {{.to_date}}
  AND timestamp >= {{.from_time}} AND timestamp <= {{.to_time}}
  AND case when {{.isd_code}} != 0 then isd_code = {{.isd_code}} else true end
  AND case when {{.connection_id}} != '' then connection_id = {{.connection_id}} else true end
  AND 
    (
      case when {{.a_party_numbers}} != '' then CONTAINS(a_party_numbers, {{.a_party_numbers}}) else true end
      OR case when {{.b_party_numbers}} != '' then CONTAINS(b_party_numbers, {{.b_party_numbers}}) else true end
      OR case when {{.caller_phone_number_sha256}} != '' then caller_phone_number_sha256 = {{.caller_phone_number_sha256}} else true end
      OR case when {{.receiver_phone_number_sha256}} != '' then receiver_phone_number_sha256 = {{.receiver_phone_number_sha256}} else true end
    )
  AND
    (
      case when {{.a_party_number_without_isd}} != '' then coalesce(any_match(a_party_number_details, x -> x.sha256_number_without_isd = {{.a_party_number_without_isd}}), false) else true end
      OR case when {{.b_party_number_without_isd}} != '' then coalesce(any_match(b_party_number_details, x -> x.sha256_number_without_isd = {{.b_party_number_without_isd}}), false) else true end
      OR case when {{.caller_number_without_isd}} != '' then coalesce(caller_number_details.sha256_number_without_isd = {{.caller_number_without_isd}}, false) else true end
      OR case when {{.receiver_number_without_isd}} != '' then coalesce(receiver_number_details.sha256_number_without_isd = {{.receiver_number_without_isd}}, false) else true end
    )
  AND
    (
      case when {{.a_party_number_with_isd}} != '' then coalesce(any_match(a_party_number_details, x -> x.sha256_number_with_isd = {{.a_party_number_with_isd}}), false) else true end
      OR case when {{.b_party_number_with_isd}} != '' then coalesce(any_match(b_party_number_details, x -> x.sha256_number_with_isd = {{.b_party_number_with_isd}}), false) else true end
      OR case when {{.caller_number_with_isd}} != '' then coalesce(caller_number_details.sha256_number_with_isd = {{.caller_number_with_isd}}, false) else true end
      OR case when {{.receiver_number_with_isd}} != '' then coalesce(receiver_number_details.sha256_number_with_isd = {{.receiver_number_with_isd}}, false) else true end
    )
  AND case when {{.order_id}} != 0 then order_id = {{.order_id}} else true end
  AND case when {{.caller_id}} != '' then caller_id = {{.caller_id}} else true end
  AND case when {{.receiver_id}} != '' then receiver_id = {{.receiver_id}} else true end
  AND case when {{.context_id}} != '' then context_id = {{.context_id}} else true end
  AND case when {{.call_sid}} != '' then call_sid = {{.call_sid}} else true end
  AND case when {{.virtual_number}} != '' then virtual_number = {{.virtual_number}} else true end
  AND case when {{.alternate_virtual_number}} != '' then alternate_virtual_number = {{.alternate_virtual_number}} else true end
  AND case when {{.error_message}} != '' then error_message = {{.error_message}} else true end
  AND case when {{.error_code}} != '' then error_code = {{.error_code}} else true end
  AND case when CARDINALITY( ARRAY[{{.tenant}}] ) = 1 and ARRAY[{{.tenant}}][1] = '' then true else tenant IN ({{.tenant}}) end
  AND case when CARDINALITY( ARRAY[{{.client}}] ) = 1 and ARRAY[{{.client}}][1] = '' then true else client IN ({{.client}}) end
  AND case when CARDINALITY( ARRAY[{{.call_status}}] ) = 1 and ARRAY[{{.call_status}}][1] = '' then true else call_status IN ({{.call_status}}) end
  AND case when CARDINALITY( ARRAY[{{.event_name}}] ) = 1 and ARRAY[{{.event_name}}][1] = '' then true else event_name IN ({{.event_name}}) end
  AND case when CARDINALITY( ARRAY[{{.call_type}}] ) = 1 and ARRAY[{{.call_type}}][1] = '' then true else call_type IN ({{.call_type}}) end
  AND case when CARDINALITY( ARRAY[{{.provider}}] ) = 1 and ARRAY[{{.provider}}][1] = '' then true else provider IN ({{.provider}}) end
  AND case when CARDINALITY( ARRAY[{{.caller_type}}] ) = 1 and ARRAY[{{.caller_type}}][1] = '' then true else caller_type IN ({{.caller_type}}) end
  AND case when CARDINALITY( ARRAY[{{.receiver_type}}] ) = 1 and ARRAY[{{.receiver_type}}][1] = '' then true else receiver_type IN ({{.receiver_type}}) end
  AND case when CARDINALITY( ARRAY[{{.call_direction}}] ) = 1 and ARRAY[{{.call_direction}}][1] = '' then true else call_direction IN ({{.call_direction}}) end

filters:
  - from_date
  - to_date
  - from_time
  - to_time
  - connection_id
  - event_name
  - call_type
  - provider
  - isd_code
  - a_party_numbers
  - b_party_numbers
  - order_id
  - caller_type
  - receiver_type
  - caller_id
  - receiver_id
  - context_id
  - call_sid
  - tenant
  - call_direction
  - virtual_number
  - alternate_virtual_number
  - error_message
  - error_code
  - call_status
  - caller_phone_number_sha256
  - receiver_phone_number_sha256
  - client
  - a_party_number_without_isd
  - b_party_number_without_isd
  - caller_number_without_isd
  - receiver_number_without_isd
  - a_party_number_with_isd
  - b_party_number_with_isd
  - caller_number_with_isd
  - receiver_number_with_isd
