query_backend: trino
tenant: Zomato
catalog: zomato
schema: jumbo2
table: telecom_call_tracking
identifier: call_statistics_events
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: telecom_service
  pd_service_name: telecom-service
  description: Telecom call statistics related events for dualleg and nummasking

# Time in seconds
caching_ttl: 600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql

sql: >
  SELECT
  COUNT(
    DISTINCT IF(
      event_name = 'EVENT_NAME_CALLBACK_RECEIVED_FROM_PROVIDER'
      AND call_type = 'CALL_TYPE_NUMBER_MASKING_ALLOCATE'
      AND call_status IN ('CALL_STATUS_COMPLETED', 'CALL_STATUS_NO_ANSWER', 'CALL_STATUS_CANCELED', 'CALL_STATUS_BUSY'),
      call_sid,
      NULL
    )
  ) AS total_nummasking_successful_count,
  COUNT(
    DISTINCT IF(
      event_name = 'EVENT_NAME_CALLBACK_RECEIVED_FROM_PROVIDER'
      AND call_type = 'CALL_TYPE_NUMBER_MASKING_ALLOCATE',
      call_sid,
      NULL
    )
  ) AS total_nummasking_calls_count,
  COUNT(
    DISTINCT IF(
      event_name = 'EVENT_NAME_CALLBACK_RECEIVED_FROM_PROVIDER'
      AND call_type = 'CALL_TYPE_DUAL_LEG'
      AND call_status IN ('CALL_STATUS_COMPLETED', 'CALL_STATUS_NO_ANSWER', 'CALL_STATUS_CANCELED', 'CALL_STATUS_BUSY'),
      call_sid,
      NULL
    )
  ) AS total_dualleg_successful_count,
  COUNT(
    DISTINCT IF(
      event_name = 'EVENT_NAME_CALLBACK_RECEIVED_FROM_PROVIDER'
      AND call_type = 'CALL_TYPE_DUAL_LEG',
      call_sid,
      NULL
    )
  ) AS total_dualleg_calls_count,
  COUNT(
    DISTINCT IF(
      event_name = 'EVENT_NAME_CALLBACK_RECEIVED_FROM_PROVIDER'
      AND call_status IN ('CALL_STATUS_COMPLETED'),
      receiver_phone_number_sha256,
      NULL
    )
  ) AS unique_users_reached
  FROM {{.table}}
  WHERE
  dt >= {{.from_date}} AND dt <= {{.to_date}}
  AND timestamp >= {{.from_time}} AND timestamp <= {{.to_time}}

filters:
  - from_date
  - to_date
  - from_time
  - to_time
