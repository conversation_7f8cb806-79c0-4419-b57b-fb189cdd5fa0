query_backend: trino
tenant: Zomato
catalog: zomato
schema: blinkit_jumbo2
table: sms_tracking
identifier: sms_blinkit_statistics_events
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: sms_service
  pd_service_name: sms-service-v2
  description: SMS Blinkit statistics events

# Time in seconds
caching_ttl: 600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql

sql: >
  SELECT
  COUNT(
    IF(
      event_name = 'SMS_CALLBACK_FROM_PROVIDER'
      AND provider_status = 'DELIVERY_STATUS_SUCCESS',
      provider_reference_id,
      NULL
    )
  ) AS total_delivered_sms_count,
  COUNT(
    IF(
      event_name = 'SMS_SENT_OVER_PROVIDER',
      provider_reference_id,
      NULL
    )
  ) AS total_sms_count,
  COUNT(DISTINCT 
    IF(
      event_name = 'SMS_CALLBACK_FROM_PROVIDER'
      AND provider_status = 'DELIVERY_STATUS_SUCCESS',
      phone_number_hash,
      NULL
    )
  ) AS unique_users_reached
  FROM {{.table}}
  WHERE
    dt >= {{.from_date}} AND dt <= {{.to_date}}
    AND time >= {{.from_time}} AND time <= {{.to_time}}

filters:
  - from_date
  - to_date
  - from_time
  - to_time
