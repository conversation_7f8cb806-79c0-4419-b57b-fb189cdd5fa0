query_backend: trino
tenant: Zomato
catalog: hive
schema: jumbo2
table: sms_tracking
identifier: sms_tracking_events
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: sms_service
  pd_service_name: sms-service-v2
  description: SMS tracking events

# Time in seconds
caching_ttl: 600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql

sql: >
  SELECT time, event_name, service, tenant, message_id, message_type, dlt_message_id, dlt_message_type, template_usecase, 
       message_length, phone_number_hash, country_id, provider, provider_status, provider_reference_id, 
       provider_account_id, error_code, error_message, tags, unicode_character_count, dt
  FROM {{.table}}
  WHERE
    dt >= {{.from_date}} AND dt <= {{.to_date}}
    AND time >= {{.from_time}} AND time <= {{.to_time}}
    AND case when {{.message_id}} != '' then message_id = {{.message_id}} else true end
    AND case when {{.dlt_message_id}} != '' then dlt_message_id = {{.dlt_message_id}} else true end
    AND case when {{.template_usecase}} != '' then template_usecase = {{.template_usecase}} else true end
    AND case when {{.phone_number_hash}} != '' then phone_number_hash = {{.phone_number_hash}} else true end
    AND case when {{.country_id}} != 0 then country_id = {{.country_id}} else true end
    AND case when {{.provider_reference_id}} != '' then provider_reference_id = {{.provider_reference_id}} else true end
    AND case when {{.provider_account_id}} != '' then provider_account_id = {{.provider_account_id}} else true end
    AND case when {{.error_code}} != '' then error_code = {{.error_code}} else true end
    AND case when {{.error_message}} != '' then error_message = {{.error_message}} else true end
    AND case when CARDINALITY( ARRAY[{{.event_name}}] ) = 1 and ARRAY[{{.event_name}}][1] = '' then true else event_name IN ({{.event_name}}) end
    AND case when CARDINALITY( ARRAY[{{.tenant}}] ) = 1 and ARRAY[{{.tenant}}][1] = '' then true else tenant IN ({{.tenant}}) end
    AND case when CARDINALITY( ARRAY[{{.message_type}}] ) = 1 and ARRAY[{{.message_type}}][1] = '' then true else message_type IN ({{.message_type}}) end
    AND case when CARDINALITY( ARRAY[{{.provider}}] ) = 1 and ARRAY[{{.provider}}][1] = '' then true else provider IN ({{.provider}}) end
    AND case when CARDINALITY( ARRAY[{{.provider_status}}] ) = 1 and ARRAY[{{.provider_status}}][1] = '' then true else provider_status IN ({{.provider_status}}) end

filters:
  - from_date
  - to_date
  - from_time
  - to_time
  - event_name
  - message_id
  - dlt_message_id
  - template_usecase
  - phone_number_hash
  - country_id 
  - provider
  - provider_reference_id
  - provider_account_id
  - error_code
  - error_message
  - tenant
  - message_type
  - provider_status
