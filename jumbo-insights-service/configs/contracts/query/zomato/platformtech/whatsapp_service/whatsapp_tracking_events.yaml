query_backend: trino
tenant: Zomato
catalog: zomato
schema: jumbo2
table: whatsapp_tracking
identifier: whatsapp_tracking_events
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: whatsapp_service
  pd_service_name: whatsapp-service
  description: WhatsApp tracking events

# Time in seconds
caching_ttl: 600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql

sql: >
  SELECT
    client,
    conversation_id,
    dt,
    event_name,
    failure_reason,
    message_category,
    message_id,
    metadata,
    provider,
    provider_status_description,
    provider_timestamp,
    template_category,
    template_usecase,
    tenant,
    timestamp,
    unique_id,
    user_id,
    hashed_phone_number_details.isd_code AS isd_code,
    hashed_phone_number_details.sha256_hashed_number_without_isd AS hashed_phone_number_without_isd,
    hashed_phone_number_details.sha256_hashed_number_with_isd AS hashed_phone_number_with_isd
  FROM
    {{.table}}
  WHERE
    dt >= {{.from_date}} AND dt <= {{.to_date}}
    AND case when {{.client}} != '' then client = {{.client}} else true end
    AND case when {{.conversation_id}} != '' then conversation_id = {{.conversation_id}} else true end
    AND case when CARDINALITY( ARRAY[{{.event_name}}] ) = 1 and ARRAY[{{.event_name}}][1] = '' then true else event_name IN ({{.event_name}}) end
    AND case when {{.failure_reason}} != '%%' then failure_reason LIKE {{.failure_reason}} else true end
    AND case when CARDINALITY( ARRAY[{{.isd_code}}] ) = 1 and ARRAY[{{.isd_code}}][1] = '' then true else hashed_phone_number_details.isd_code IN ({{.isd_code}}) end
    AND case when {{.hashed_phone_number_without_isd}} != '' then hashed_phone_number_details.sha256_hashed_number_without_isd = {{.hashed_phone_number_without_isd}} else true end
    AND case when {{.hashed_phone_number_with_isd}} != '' then hashed_phone_number_details.sha256_hashed_number_with_isd = {{.hashed_phone_number_with_isd}} else true end
    AND case when CARDINALITY( ARRAY[{{.message_category}}] ) = 1 and ARRAY[{{.message_category}}][1] = '' then true else message_category IN ({{.message_category}}) end
    AND case when {{.message_id}} != '' then message_id = {{.message_id}} else true end
    AND case when CARDINALITY( ARRAY[{{.provider}}] ) = 1 and ARRAY[{{.provider}}][1] = '' then true else provider IN ({{.provider}}) end
    AND case when {{.provider_status_description}} != '%%' then provider_status_description LIKE {{.provider_status_description}} else true end
    AND case when {{.provider_timestamp}} != 0 then provider_timestamp = {{.provider_timestamp}} else true end
    AND case when CARDINALITY( ARRAY[{{.template_category}}] ) = 1 and ARRAY[{{.template_category}}][1] = '' then true else template_category IN ({{.template_category}}) end
    AND case when {{.template_usecase}} != '%%' then template_usecase LIKE {{.template_usecase}} else true end
    AND case when CARDINALITY( ARRAY[{{.tenant}}] ) = 1 and ARRAY[{{.tenant}}][1] = '' then true else tenant IN ({{.tenant}}) end
    AND timestamp >= {{.from_time}} AND timestamp <= {{.to_time}}
    AND case when {{.unique_id}} != '' then unique_id = {{.unique_id}} else true end
    AND case when {{.user_id}} != '' then user_id = {{.user_id}} else true end

filters:
  - from_date
  - to_date
  - client
  - conversation_id
  - event_name
  - failure_reason
  - message_category
  - message_id
  - provider
  - provider_status_description
  - provider_timestamp
  - template_category
  - template_usecase
  - tenant
  - from_time
  - to_time
  - unique_id
  - user_id
  - isd_code
  - hashed_phone_number_without_isd
  - hashed_phone_number_with_isd
