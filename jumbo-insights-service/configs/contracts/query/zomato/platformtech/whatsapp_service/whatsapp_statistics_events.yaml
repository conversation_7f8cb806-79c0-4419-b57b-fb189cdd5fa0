query_backend: trino
tenant: Zomato
catalog: hive
schema: jumbo2
table: whatsapp_tracking
identifier: whatsapp_statistics_events
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: whatsapp_service
  pd_service_name: whatsapp-service
  description: WhatsApp statistics events

# Time in seconds
caching_ttl: 600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql

sql: >
  SELECT
  COUNT(DISTINCT
    IF(
      event_name = 'DELIVERY_REPORT_MESSAGE_DELIVERED',
      message_id,
      NULL
    )
  ) AS total_delivered_whatsapp_count,
  COUNT(DISTINCT
    IF(
      event_name = 'SEND_MESSAGE_REQUEST_SENT_TO_PROVIDER',
      message_id,
      NULL
    )
  ) AS total_whatsapp_count,
  COUNT(DISTINCT 
    IF(
      event_name = 'DELIVERY_REPORT_MESSAGE_DELIVERED',
      hashed_phone_number_details.sha256_hashed_number_with_isd,
      NULL
    )
  ) AS unique_users_reached
  FROM {{.table}}
  WHERE
    dt >= {{.from_date}} AND dt <= {{.to_date}}
    AND timestamp >= {{.from_time}} AND timestamp <= {{.to_time}}

filters:
  - from_date
  - to_date
  - from_time
  - to_time
