 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: platform_experiments_l0_metrics
identifier: cohort_filter_list
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: experimentation-service
  pd_service_name: experimentation-service
  description: Get the list of filters for cohort analysis
# TTL for cache, Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: open_sql
sql: >
  SELECT DISTINCT
    {{.column}}
  FROM
    {{.table}}
  WHERE
    dt BETWEEN '{{.start_date}}' AND '{{.end_date}}'
    AND experiment_id = '{{.experiment_id}}'
    {{.filter_conditions}}
filters:
  - experiment_id
  - start_date
  - end_date
  - column
  - filter_conditions
