# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: platform_experiments_l0_metrics_hourly
identifier: experiment_distinct_dimensions_hourly
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: experimentation-service
  pd_service_name: experimentation-service
  description: Fetch distinct dimension type and values for hourly experiment metrics 

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT DISTINCT
      dimension_type,
      dimension_value
  FROM {{.table}}
  WHERE dt BETWEEN {{.start_dt}} AND {{.end_dt}}
  AND (
      (dt = {{.start_dt}} AND hr >= {{.start_hr}})
      OR (dt > {{.start_dt}} AND dt < {{.end_dt}})
      OR (dt = {{.end_dt}} AND hr <= {{.end_hr}})
  )
  AND (CASE WHEN 'all' IN ({{.source}}) THEN 1=1 ELSE source IN ({{.source}}) END)
  AND (CASE WHEN -2 IN ({{.city_id}}) THEN 1=1 ELSE city_id IN ({{.city_id}}) END)
  AND experiment_id IN ({{.experiment_id}})
  AND experiment_bucket_id IN ({{.experiment_bucket_id}})

  
filters:
  - start_dt
  - end_dt
  - start_hr
  - end_hr
  - source
  - city_id
  - experiment_id
  - experiment_bucket_id