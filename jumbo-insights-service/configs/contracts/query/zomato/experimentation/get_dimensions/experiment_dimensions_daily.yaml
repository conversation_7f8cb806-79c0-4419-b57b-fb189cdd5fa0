# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: platform_experiments_l0_metrics_daily
identifier: experiment_distinct_dimensions_daily
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: experimentation-service
  pd_service_name: experimentation-service
  description: Fetch distinct dimension type and values for daily experiment metrics 

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT DISTINCT
      dimension_type,
      dimension_value
  FROM {{.table}}
  WHERE dt BETWEEN {{.start_dt}} AND {{.end_dt}}
  AND (CASE WHEN 'all' IN ({{.source}}) THEN 1=1 ELSE source IN ({{.source}}) END)
  AND (CASE WHEN -2 IN ({{.city_id}}) THEN 1=1 ELSE city_id IN ({{.city_id}}) END)
  AND experiment_id IN ({{.experiment_id}})
  AND experiment_bucket_id IN ({{.experiment_bucket_id}})
  
filters:
  - start_dt
  - end_dt
  - source
  - city_id
  - experiment_id
  - experiment_bucket_id