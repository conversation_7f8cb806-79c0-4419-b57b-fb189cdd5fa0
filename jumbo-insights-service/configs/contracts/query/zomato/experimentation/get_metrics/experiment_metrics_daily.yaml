# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: platform_experiments_l0_metrics_daily
identifier: experiment_platform_metrics_daily
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: experimentation-service
  pd_service_name: experimentation-service
  description: Fetch platform experiment metrics daily level

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
      dt,
      experiment_id,
      experiment_bucket_id,
      CASE 
          WHEN 'all' IN ({{.source}}) 
              THEN 'ALL' 
          ELSE source
      END AS source_str,
      CASE 
          WHEN -2 IN ({{.city_id}}) 
              THEN 'ALL' 
          ELSE CAST(city_id AS STRING) 
      END AS city_id_str,
      dimension_type,
      dimension_value,
      SUM(users) as users,
      SUM(total_orders) AS total_orders,
      SUM(total_net_margin) AS total_net_margin,
      SUM(total_gmv_upa) AS total_gmv_upa,
      SUM(total_nov) AS total_nov,
      SUM(total_dc_realized) AS total_dc_realized,
      SUM(total_logs_cost) AS total_logs_cost,
      SUM(total_refund) AS total_refund,
      SUM(total_zvd) AS total_zvd,
      SUM(total_voucher_discount) AS total_voucher_discount,
      SUM(total_commission) AS total_commission,
      SUM(total_drop_distance) AS total_drop_distance,
      SUM(app_opens) AS app_opens,
      SUM(menu_sessions) AS menu_sessions,
      SUM(cart_sessions) AS cart_sessions,
      SUM(order_sessions) AS order_sessions,
      SUM(foc_ad_orders) AS foc_ad_orders,
      SUM(ad_orders) AS ad_orders,
      SUM(ad_commission) AS ad_commission,
      SUM(ad_gmv_upa) AS ad_gmv_upa,
      SUM(ad_dc_realized) AS ad_dc_realized,
      SUM(ad_logs_cost) AS ad_logs_cost,
      SUM(ad_refund) AS ad_refund,
      SUM(ad_zvd) AS ad_zvd,
      SUM(ad_net_margin) AS ad_net_margin,
      SUM(ad_drop_distance) AS ad_drop_distance,
      SUM(ads_rev) AS ads_rev,
      SUM(cg_ads_rev) AS cg_ads_rev,
      SUM(tg_ads_rev) AS tg_ads_rev,
      SUM(ad_clicks) AS ad_clicks,
      SUM(ad_impressions) AS ad_impressions,
      SUM(ads_cv) AS ads_cv,
      SUM(adt) AS adt,
      SUM(ddt) AS ddt,
      SUM(busy_minutes) AS busy_minutes,
      SUM(nkam_orders) AS nkam_orders,
      SUM(rkam_orders) AS rkam_orders,
      SUM(ckam_orders) AS ckam_orders,
      SUM(lp_orders) AS lp_orders,
      SUM(delivery_charge) AS delivery_charge,
      SUM(dc_discount) AS dc_discount,
      SUM(n2r_orders) AS n2r_orders,

      -- funnel metrics
      SUM(menu_sessions) * 1.00 / SUM(app_opens) AS a2m,
      SUM(cart_sessions) * 1.00 / SUM(menu_sessions) AS m2c,
      SUM(order_sessions) * 1.00 / SUM(cart_sessions) AS c2o,
      SUM(order_sessions) * 1.00 / SUM(app_opens) AS otr,
      SUM(order_sessions) * 1.00 / SUM(menu_sessions) AS m2o,

      -- unit economic metrics
      SUM(total_gmv_upa) * 1.00 / SUM(app_opens) as gmvao,
      SUM(total_gmv_upa) * 1.00 / SUM(total_orders) as aov,
      -- asv
      SUM(total_commission) * 1.00 / SUM(total_orders) as acv,
      SUM(total_dc_realized) / SUM(total_orders) as dc_realized_per_order,
      -- commission
      SUM(ads_rev) / SUM(total_orders) as ads_rev_per_order,
      SUM(total_logs_cost) / SUM(total_orders) as logs_cost_per_order,
      SUM(total_zvd) / SUM(total_orders) as zvdo,
      SUM(total_refund) / SUM(total_orders) as refund_per_order,
      SUM(total_net_margin) / SUM(total_orders) as net_margin_per_order,

      -- logs metrics
      SUM(busy_minutes) / SUM(total_orders) as btpo,
      SUM(adt) / SUM(total_orders) as adt_per_order,
      SUM(ddt) / SUM(total_orders) as ddt_per_order

  FROM {{.table}}
  WHERE dt BETWEEN {{.start_dt}} AND {{.end_dt}}
  AND (CASE WHEN 'all' IN ({{.source}}) THEN 1=1 ELSE source IN ({{.source}}) END)
  AND (CASE WHEN -2 IN ({{.city_id}}) THEN 1=1 ELSE city_id IN ({{.city_id}}) END)
  AND experiment_id IN ({{.experiment_id}})
  AND experiment_bucket_id IN ({{.experiment_bucket_id}})
  AND (CASE WHEN 'all' IN ({{.dimension_type}}) THEN 1=1 ELSE dimension_type IN ({{.dimension_type}}) END)
  AND (CASE WHEN 'all' IN ({{.dimension_value}}) THEN 1=1 ELSE dimension_value IN ({{.dimension_value}}) END)
  GROUP BY 1, 2, 3, 4, 5, 6, 7
  ORDER BY 1
  
filters:
  - start_dt
  - end_dt
  - source
  - city_id
  - experiment_id
  - experiment_bucket_id
  - dimension_type
  - dimension_value