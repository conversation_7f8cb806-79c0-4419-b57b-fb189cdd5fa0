# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: mealtime_ads_daily_stats
identifier: reporting_mealtime_trends_ads
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ads-service
  pd_service_name: ads-service
  description: Mealtime Ads Metrics

# Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 1000
# contract type
type: sql
sql: >
  SELECT
    (CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('graph') THEN hour ELSE 0 END) as hour,
    SUM(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('graph') THEN ads_revenue ELSE 0.00 END) as ad_sales,
    SUM(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('graph') THEN clicks ELSE 0 END) as ad_menu_visits,
    SUM(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('graph') THEN ad_orders ELSE 0 END) as ad_orders,
    SUM(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('graph') THEN impressions ELSE 0 END) as ad_impressions,
    SUM(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('graph') THEN ads_spend ELSE 0.00 END) as ad_spend
  FROM {{.table}}
  WHERE "timestamp" BETWEEN {{.start_time}} and {{.end_time}}
    AND res_id IN ({{.res_id}})
    AND product_id IN ({{.product_id}})
    AND product_type in ({{.product_type}})
    AND (ad_group_id IN ({{.ad_group_id}}) OR (CASE WHEN '-1' IN ({{.ad_group_id}}) then 1 else 0 end) = 1)
  GROUP BY hour

filters:
  - start_time
  - end_time
  - res_id
  - product_id
  - ad_group_id
  - aggregation_type
  - sub_aggregation_type
  - product_type