query_backend: pinot
tenant: Zomato
table: ba_hub_ads_analytics
identifier: overall_ad_group
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ads-service
  pd_service_name: ads-service
  description: Overall Ads trends

# Time in seconds
caching_ttl: 1200

# Time in seconds; 0 to disable fetch from cache
refresh_interval: 300

# Time in milliseconds
sla: 100

# Request per second
rate_limit: 200

# contract type
type: sql

# SQL query block using '>'
sql: >
  SELECT 
    ad_group_id as agg_value,
    SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN total_sales
            ELSE 0.00
        END
    ) AS total_sales,
    SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN total_sales
            ELSE 0.00
        END
    ) AS ad_group_level_total_sales,
    SUM(
        CASE
            WHEN ad_group_id != 'OVERALL' THEN ads_cv
            ELSE 0.00
        END
    ) AS sales_from_ads,
    SUM(
        CASE
            WHEN ad_group_id != 'OVERALL' THEN billed_revenue
            ELSE 0.00
        END
    ) AS ad_spend,
    SUM(
        CASE
            WHEN ad_group_id != 'OVERALL' THEN daily_campaign_budget
            ELSE 0.00
        END
    ) AS daily_campaign_budget,
    SUM(
        CASE
            WHEN ad_group_id != 'OVERALL' THEN ad_orders
            ELSE 0
        END
    ) AS ad_orders,
    SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN total_orders
            ELSE 0
        END
    ) AS total_orders,
    SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN total_orders
            ELSE 0
        END
    ) AS ad_group_level_total_orders,
    SUM(
        CASE
            WHEN ad_group_id != 'OVERALL' THEN ad_menu_opens
            ELSE 0
        END
    ) AS ad_menu_visits,
    SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN menu_opens
            ELSE 0
        END
    ) AS menu_opens,
    SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN menu_opens
            ELSE 0
        END
    ) AS ad_group_level_total_menu_visits,
    SUM(
        CASE
            WHEN ad_group_id != 'OVERALL' THEN delivered_ad_impression
            ELSE 0
        END
    ) AS ad_impressions,
    SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN total_impressions
            ELSE 0
        END
    ) AS total_impressions,
    SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN total_impressions
            ELSE 0
        END
    ) AS ad_group_level_total_impressions,
    SUM(
        CASE
            WHEN ad_group_id != 'OVERALL' THEN new_users_acquired
            ELSE 0
        END
    ) AS new_users_from_ads
  FROM ba_hub_ads_analytics 
  WHERE "timestamp" BETWEEN  {{.start_time}}  
                      AND  {{.end_time}} 
    AND res_id in ({{.res_id}})
    AND product_id in ({{.product_id}}) 
    AND (
        ad_group_id IN ({{.ad_group_id}})
        OR ad_group_id = 'OVERALL'
        OR (CASE WHEN 'NOT_OVERALL' IN ({{.ad_group_id}}) THEN 1 ELSE 0 END) = 1
    )
  GROUP BY agg_value

filters:
  - start_time
  - end_time
  - product_id
  - res_id
  - ad_group_id
