# can be pinot/trino
query_backend: trino
tenant: Zomato
catalog: hive
schema: insights_etls
table: merchant_dashboard_mealtime_ads_analytics
identifier: daily_mealtime_reporting_ads
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ads-service
  pd_service_name: ads-service
  description: Daily Mealtime Ads Metrics

# Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  WITH segmentations AS (
    SELECT DISTINCT try_cast(external_campaign_id AS VARCHAR) AS campaign_id,
      CASE 
        WHEN targeting IS NULL OR targeting = '' THEN 'NA'
        ELSE targeting 
      END AS targeting,
      CASE 
        WHEN segments IS NULL OR segments = '' THEN 'NA'
        ELSE segments
      END AS segments
    FROM ads_etls.campaign_targeting_segments
    WHERE dt = (SELECT MAX(dt) FROM ads_etls."campaign_targeting_segments$partitions")
  ),
  dates as (
    SELECT DISTINCT try_cast(campaign_id as varchar) as campaign_id,
    cast(campaign_start_date as varchar) as start_date,
    cast(campaign_end_date as varchar) as end_date
    from ads_etls.campaign_performance
    where dt >= '20240101'
  ),
  campaign_keyword as (
    SELECT DISTINCT try_cast(external_campaign_id as varchar) as campaign_id,
    coalesce(serving_metadata.keywords[1], 'NA') AS BoS_keyword
    FROM mongo_ads.campaigns
    WHERE dt >= '20240101'
    and product_id = 107
  )
  SELECT 
    (CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN dt ELSE 'NA' END) AS "Date",
    (CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN res_id ELSE 0 END) AS "Res Id",
    (CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN ad_group_id ELSE 'NA' END) AS "Ad Group",
    (CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN product_type ELSE 'NA' END) AS "Product Type",
    (CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN campaign_id ELSE 'NA' END) AS "Campaign Id",
    COALESCE(MAX(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN targeting ELSE 'NA' END), 'NA') AS "Targeting",
    COALESCE(MAX(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN segments ELSE 'NA' END), 'NA') AS "Segments",
    COALESCE(MAX(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN BoS_keyword ELSE 'NA' END), 'NA') AS "BOS Keyword",
    COALESCE(MAX(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN start_date ELSE 'NA' END), 'NA') AS "Start Date",
    COALESCE(MAX(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN end_date ELSE 'NA' END), 'NA') AS "End Date",
    (CASE 
      WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN 
        CASE 
          WHEN mealtime = 'breakfast' THEN 'Breakfast'
          WHEN mealtime = 'lunch' THEN 'Lunch'
          WHEN mealtime = 'dinner' THEN 'Dinner'
          WHEN mealtime = 'late_night' THEN 'Late Night'
          ELSE 'NA' 
        END 
      ELSE 'NA' 
    END) AS Mealtime,
    SUM(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN impressions ELSE 0 END) AS "Ad Impressions",
    SUM(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN clicks ELSE 0 END) AS "Ad Clicks",
    SUM(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN ad_orders ELSE 0 END) AS "Ad Orders",
    ROUND(COALESCE(SUM(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN TRY_CAST(ad_orders AS REAL) END) / NULLIF(SUM(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN TRY_CAST(clicks AS REAL) END), 0), 0) * 100.00, 2) AS "Ads M2O(%)",
    ROUND(COALESCE(SUM(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN TRY_CAST(clicks AS REAL) END) / NULLIF(SUM(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN TRY_CAST(impressions AS REAL) END), 0), 0) * 100.00, 2) AS "Ads CTR(%)",
    ROUND(COALESCE(SUM(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN TRY_CAST(ad_orders AS REAL) END) / NULLIF(SUM(CASE WHEN {{.aggregation_type}} IN ('Mealtime') AND {{.sub_aggregation_type}} IN ('report') THEN TRY_CAST(impressions AS REAL) END), 0), 0) * 100.00, 2) AS "Ads OTR(%)"
  FROM {{.table}} 
  LEFT JOIN segmentations USING(campaign_id) 
  LEFT JOIN dates USING(campaign_id)
  LEFT JOIN campaign_keyword USING(campaign_id)
  WHERE "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
    AND res_id IN ({{.res_id}})
    AND product_id IN ({{.product_id}})
    AND product_type IN ({{.product_type}})
    AND (ad_group_id IN ({{.ad_group_id}}) OR (CASE WHEN '-1' IN ({{.ad_group_id}}) THEN 1 ELSE 0 END) = 1)
    AND dt >= {{.start_date}}
    AND dt <= {{.end_date}}
  GROUP BY 1, 2, 3, 4, 5, 11
  ORDER BY 1, 2, 3, 4, 5, 11

filters:
  - start_time
  - end_time
  - start_date
  - end_date
  - res_id
  - product_id
  - ad_group_id
  - aggregation_type
  - sub_aggregation_type
  - product_type