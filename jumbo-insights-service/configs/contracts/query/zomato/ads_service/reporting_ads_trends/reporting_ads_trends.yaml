# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_ads_analytics
identifier: reporting_ads_trends
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ads-service
  pd_service_name: ads-service
  description: Overall Ads trends 

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
  CASE 
      WHEN {{.agg_level_enum}} IN (1, 4) THEN CONCAT(CONCAT('year', ist_year, '_'), 
                                                     CASE 
                                                         WHEN ist_month < 10 THEN CONCAT('0', ist_month, '')
                                                         ELSE ist_month
                                                     END, '_')
      WHEN {{.agg_level_enum}} IN (2, 3) THEN CONCAT('year', ist_year, '_')
      ELSE 'NULL'     
  END AS parent_aggregation_type,
  CASE 
      WHEN {{.agg_level_enum}} IN (1, 4) THEN CONCAT('day', 
                                                     CASE 
                                                         WHEN ist_day < 10 THEN CONCAT('0', ist_day, '')
                                                         ELSE ist_day
                                                     END, '_')
      WHEN {{.agg_level_enum}} = 2 THEN CONCAT('week',
                                               CASE 
                                                   WHEN ist_isoweek < 10 THEN CONCAT('0', ist_isoweek, '')
                                                   ELSE ist_isoweek
                                               END, '_') 
      WHEN {{.agg_level_enum}} = 3 THEN CONCAT('month', 
                                               CASE 
                                                   WHEN ist_month < 10 THEN CONCAT('0', ist_month, '')
                                                   ELSE ist_month
                                               END, '_')
      ELSE 'NULL'
  END AS aggregation_type,
  CASE 
      WHEN {{.agg_level_enum}} IN (1, 4) THEN CONCAT(CASE
                                                         WHEN ist_day < 10 THEN CONCAT('0', ist_day, '')
                                                         ELSE ist_day 
                                                     END,  
                                                     CASE 
                                                        WHEN ist_month = 1 THEN 'Jan'
                                                        WHEN ist_month = 2 THEN 'Feb'
                                                        WHEN ist_month = 3 THEN 'Mar'
                                                        WHEN ist_month = 4 THEN 'Apr'
                                                        WHEN ist_month = 5 THEN 'May'
                                                        WHEN ist_month = 6 THEN 'Jun'
                                                        WHEN ist_month = 7 THEN 'Jul'
                                                        WHEN ist_month = 8 THEN 'Aug'
                                                        WHEN ist_month = 9 THEN 'Sep'
                                                        WHEN ist_month = 10 THEN 'Oct'
                                                        WHEN ist_month = 11 THEN 'Nov'
                                                        WHEN ist_month = 12 THEN 'Dec'
                                                        ELSE 'NULL'
                                                     END, ' ')
      WHEN {{.agg_level_enum}} = 2 THEN CONCAT('Week', 
                                               CASE
                                                   WHEN ist_isoweek < 10 THEN CONCAT('0', ist_isoweek, '')
                                                   ELSE ist_isoweek 
                                               END, ' ')
      WHEN {{.agg_level_enum}} = 3 and ist_month = 1 THEN CONCAT('Jan', ist_year, ' ')
      WHEN {{.agg_level_enum}} = 3 and ist_month = 2 THEN CONCAT('Feb', ist_year, ' ')
      WHEN {{.agg_level_enum}} = 3 and ist_month = 3 THEN CONCAT('Mar', ist_year, ' ')     
      WHEN {{.agg_level_enum}} = 3 and ist_month = 4 THEN CONCAT('Apr', ist_year, ' ')     
      WHEN {{.agg_level_enum}} = 3 and ist_month = 5 THEN CONCAT('May', ist_year, ' ')     
      WHEN {{.agg_level_enum}} = 3 and ist_month = 6 THEN CONCAT('Jun', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 3 and ist_month = 7 THEN CONCAT('Jul', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 3 and ist_month = 8 THEN CONCAT('Aug', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 3 and ist_month = 9 THEN CONCAT('Sep', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 3 and ist_month = 10 THEN CONCAT('Oct', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 3 and ist_month = 11 THEN CONCAT('Nov', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 3 and ist_month = 12 THEN CONCAT('Dec', ist_year, ' ')  
      ELSE 'NULL'     
  END AS aggregation_type_string,
  CASE 
      WHEN {{.agg_level_enum}} IN (1, 4) THEN dt
      WHEN {{.agg_level_enum}} = 2 THEN CONCAT(ist_year, 
                                               CASE 
                                                   WHEN ist_isoweek < 10 THEN CONCAT('0', ist_isoweek, '')
                                                   ELSE ist_isoweek
                                               END, '_')      
      WHEN {{.agg_level_enum}} = 3 THEN CONCAT(ist_year, 
                                               CASE 
                                                   WHEN ist_month < 10 THEN CONCAT('0', ist_month, '')
                                                   ELSE ist_month
                                               END, '_')

      
      ELSE 'NULL'
  END AS agg_value,
  CASE
      WHEN {{.agg_level_enum}} = 1 THEN {{.no_of_days}}
      WHEN {{.agg_level_enum}} = 2 AND ist_day_of_week <= {{.no_of_days}} THEN {{.no_of_days}}
      WHEN {{.agg_level_enum}} = 3 AND ist_day <= {{.no_of_days}} THEN {{.no_of_days}}
      WHEN {{.agg_level_enum}} = 4 AND ist_day_of_week = {{.no_of_days}} THEN {{.no_of_days}}
      ELSE 0
  END AS no_of_days,
  
  SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN total_sales
            ELSE 0
        END
     ) AS total_sales,
  SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN total_sales
            ELSE 0
        END
     ) AS ad_group_level_total_sales,
  SUM(
        CASE
            WHEN ad_group_id != 'OVERALL' THEN ads_cv
            ELSE 0
        END
     ) AS sales_from_ads,

  SUM(
        CASE
            WHEN ad_group_id != 'OVERALL' THEN billed_revenue
            ELSE 0
        END
     ) AS ad_spend,  

  SUM(
        CASE
            WHEN ad_group_id != 'OVERALL' THEN daily_campaign_budget
            ELSE 0
        END
     ) AS daily_campaign_budget,  

  SUM(
        CASE
            WHEN ad_group_id != 'OVERALL' THEN ad_orders
            ELSE 0
        END
     ) AS ad_orders,  
  SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN total_orders
            ELSE 0
        END
     ) AS total_orders,
  SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN total_orders
            ELSE 0
        END
     ) AS ad_group_level_total_orders,

  SUM(
        CASE
            WHEN ad_group_id != 'OVERALL' THEN ad_menu_opens
            ELSE 0
        END
     ) AS ad_menu_visits,
  SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN menu_opens
            ELSE 0
        END
     ) AS menu_opens,
  SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN menu_opens
            ELSE 0
        END
     ) AS ad_group_level_total_menu_visits,     

  SUM(
        CASE
            WHEN ad_group_id != 'OVERALL' THEN delivered_ad_impression
            ELSE 0
        END
     ) AS ad_impressions,     
  SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN total_impressions
            ELSE 0
        END
     ) AS total_impressions,
  SUM(
        CASE
            WHEN ad_group_id = 'OVERALL' THEN total_impressions
            ELSE 0
        END
     ) AS ad_group_level_total_impressions,
  SUM(
        CASE
            WHEN ad_group_id != 'OVERALL' THEN new_users_acquired
            ELSE 0
        END
     ) AS new_users_from_ads
  
  FROM {{.table}} 
  WHERE "timestamp" BETWEEN {{.start_time}} 
                        AND {{.end_time}}
  AND res_id in ({{.res_id}})
  AND no_of_days = {{.no_of_days}}
  AND product_id in ({{.product_id}})
  AND product_type in ({{.product_type}})
  AND (
        ad_group_id in ({{.ad_group_id}}) OR 
        ad_group_id = 'OVERALL' OR
        (CASE WHEN 'NOT_OVERALL' IN ({{.ad_group_id}}) then 1 else 0 end) = 1 
      )
  GROUP BY parent_aggregation_type, aggregation_type, aggregation_type_string, no_of_days, agg_value

filters:
  - start_time
  - end_time
  - product_id
  - res_id
  - agg_level_enum
  - no_of_days
  - ad_group_id
  - product_type