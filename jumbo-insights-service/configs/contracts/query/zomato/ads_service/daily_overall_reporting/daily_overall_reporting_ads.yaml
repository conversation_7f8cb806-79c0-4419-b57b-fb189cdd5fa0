# can be pinot/trino
query_backend: trino
tenant: Zomato
catalog: hive
schema: insights_etls
table: ba_hub_ads_analytics
identifier: daily_overall_reporting_ads
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ads-service
  pd_service_name: ads-service
  description: Daily Overall Ads Metrics

# Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  WITH segmentations AS (
    SELECT DISTINCT try_cast(external_campaign_id AS VARCHAR) AS campaign_id,
      CASE 
        WHEN targeting IS NULL OR targeting = '' THEN 'NA'
        ELSE targeting 
      END AS targeting,
      CASE 
        WHEN segments IS NULL OR segments = '' THEN 'NA'
        ELSE segments
      END AS segments
    FROM ads_etls.campaign_targeting_segments
    WHERE dt = (SELECT MAX(dt) FROM ads_etls."campaign_targeting_segments$partitions")
  ),
  dates as (
    SELECT DISTINCT try_cast(campaign_id as varchar) as campaign_id,
    cast(campaign_start_date as varchar) as start_date,
    cast(campaign_end_date as varchar) as end_date
    from ads_etls.campaign_performance
    where dt >= '20240101'
  ),
  campaign_keyword as (
    SELECT DISTINCT try_cast(external_campaign_id as varchar) as campaign_id,
    coalesce(serving_metadata.keywords[1], 'NA') AS BoS_keyword
    FROM mongo_ads.campaigns
    WHERE dt >= '20240101'
    and product_id = 107
  )
  SELECT 
    (CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN dt ELSE 'NA' END) AS "Date",
    (CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN res_id ELSE 0 END) AS "Res ID",
    (CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN ad_group_id ELSE 'NA' END) AS "Ad Group",
    (CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN city ELSE 'NA' END) AS "City",
    (CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN area ELSE 'NA' END) AS "Subzone",
    (CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN product_type ELSE 'NA' END) AS "Product Type",
    (CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN campaign_id ELSE 'NA' END) AS "Campaign Id",
    COALESCE(MAX(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN targeting ELSE 'NA' END), 'NA') AS "Targeting",
    COALESCE(MAX(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN segments ELSE 'NA' END), 'NA') AS "Segments",
    COALESCE(MAX(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN start_date ELSE 'NA' END), 'NA') AS "Start Date",
    COALESCE(MAX(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN end_date ELSE 'NA' END), 'NA') AS "End Date",
    COALESCE(MAX(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN BoS_keyword ELSE 'NA' END), 'NA') AS "BOS Keyword",
    MAX(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN cpx ELSE 0.0 END) AS "CPX",
    SUM(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN delivered_ad_impression ELSE 0 END) AS "Ad Impressions",
    SUM(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN ad_menu_opens ELSE 0 END) AS "Ad Clicks",
    SUM(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN ad_orders ELSE 0 END) AS "Ad Orders",
    SUM(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN ad_cart_built ELSE 0 END) AS "Ad Carts",
    ROUND(SUM(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN billed_revenue ELSE 0.0 END),2) AS "Ad Spend (Rs)",
    ROUND(SUM(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN ads_cv ELSE 0.0 END),2) AS "Ad Sales (Rs)",
    ROUND(SUM(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN total_impressions ELSE 0 END),2) AS "Total Impressions",
    ROUND(SUM(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN menu_opens ELSE 0 END),2) AS "Total Clicks",
    SUM(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN total_orders ELSE 0 END) AS "Total Orders",
    ROUND(SUM(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN daily_campaign_budget ELSE 0.0 END),2) AS "Daily Booked Budget (Rs)",
    ROUND(coalesce(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(ad_orders AS real) END) / nullif(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(ad_menu_opens AS real) END), 0), 0) * 100.00,2) AS "Ads M2O (%)",
    ROUND(coalesce(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(ad_menu_opens AS real) END) / nullif(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(delivered_ad_impression AS real) END), 0), 0) * 100.00,2) AS "Ads CTR (%)",
    ROUND(coalesce(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(ad_orders AS real) END) / nullif(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(delivered_ad_impression AS real) END), 0), 0) * 100.00,2) AS "Ads OTR (%)",
    ROUND(coalesce(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(ads_cv AS real) END) / nullif(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(billed_revenue AS real) END), 0), 0),2) AS "ROI",
    ROUND(coalesce(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(billed_revenue AS real) END) / nullif(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(daily_campaign_budget AS real) END), 0), 0) * 100.00,2) AS "Delivery (%)",
    ROUND(coalesce(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(total_orders AS real) END) / nullif(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(menu_opens AS real) END), 0), 0) * 100.00,2) AS "Overall M2O (%)",
    ROUND(coalesce(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(menu_opens AS real) END) / nullif(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(total_impressions AS real) END), 0), 0) * 100.00,2) AS "Overall CTR (%)",
    ROUND(coalesce(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(total_orders AS real) END) / nullif(sum(CASE WHEN {{.aggregation_type}} IN ('OVERALL') AND {{.sub_aggregation_type}} IN ('report') THEN try_cast(total_impressions AS real) END), 0), 0) * 100.00,2) AS "Overall OTR (%)"
  FROM {{.table}} 
  LEFT JOIN segmentations USING(campaign_id) 
  LEFT JOIN dates USING(campaign_id)
  LEFT JOIN campaign_keyword USING(campaign_id)
  WHERE "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
    AND res_id IN ({{.res_id}})
    AND product_id IN ({{.product_id}})
    AND product_type IN ({{.product_type}})
    AND (ad_group_id IN ({{.ad_group_id}}) OR (CASE WHEN '-1' IN ({{.ad_group_id}}) THEN 1 ELSE 0 END) = 1)
    AND dt >= {{.start_date}}
    AND dt <= {{.end_date}}
  GROUP BY 1, 2, 3, 4, 5, 6, 7
  ORDER BY 1, 2, 3, 4, 5, 6, 7

filters:
  - start_time
  - end_time
  - start_date
  - end_date
  - res_id
  - product_id
  - ad_group_id
  - aggregation_type
  - sub_aggregation_type
  - product_type