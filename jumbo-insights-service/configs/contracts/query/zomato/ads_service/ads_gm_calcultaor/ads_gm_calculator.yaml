# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: grow_maxx_calculator
identifier: grow_maxx_calculator
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ads-service
  pd_service_name: ads-service
  description: Calculator for Grow Maxx

# Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
      month(FromDateTime(dt,'yyyyMMdd')) AS month_num,
      res_id,
      sum(lzero_share * growth) as lzero_factor,
      max(billed_amount) as billed_amount,
      max(ads_cv) as ads_cv,
      max(weekday_cv) as weekday_cv,
      max(weekend_cv) as weekend_cv
  FROM {{.table}}
  WHERE dt = {{.start_date}}
    AND res_id IN ({{.res_id}})
  GROUP BY month_num, res_id
filters:
  - start_date
  - res_id