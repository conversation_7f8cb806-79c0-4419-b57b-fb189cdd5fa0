# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: user_ads_daily_stats
identifier: reporting_user_trends_ads
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ads-service
  pd_service_name: ads-service
  description: User Ads Metrics

# Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    (CASE 
      WHEN {{.aggregation_type}} IN ('NRL') AND {{.sub_aggregation_type}} IN ('graph') THEN nrl 
      WHEN {{.aggregation_type}} IN ('veg_non_veg') AND {{.sub_aggregation_type}} IN ('graph') THEN veg_type
      WHEN {{.aggregation_type}} IN ('ps_segment') AND {{.sub_aggregation_type}} IN ('graph') THEN ps_segment
      ELSE 'NA'
    END) as user_type,
    sum(CASE 
      WHEN {{.aggregation_type}} in ('NRL','veg_non_veg','ps_segment') AND {{.sub_aggregation_type}} IN ('graph') THEN ads_revenue 
      ELSE 0.00 
    end) as ad_sales,
    sum(CASE 
      WHEN {{.aggregation_type}} in ('NRL','veg_non_veg','ps_segment') AND {{.sub_aggregation_type}} IN ('graph') THEN ads_spend 
      ELSE 0.00 
    end) as ad_spend,
    sum(CASE 
      WHEN {{.aggregation_type}} in ('NRL','veg_non_veg','ps_segment') AND {{.sub_aggregation_type}} IN ('graph') THEN clicks 
      ELSE 0 
    end) as ad_menu_visits,
    sum(CASE 
      WHEN {{.aggregation_type}} in ('NRL','veg_non_veg','ps_segment') AND {{.sub_aggregation_type}} IN ('graph') THEN ad_orders 
      ELSE 0 
    end) as ad_orders,
    sum(CASE 
      WHEN {{.aggregation_type}} in ('NRL','veg_non_veg','ps_segment') AND {{.sub_aggregation_type}} IN ('graph') THEN impressions 
      ELSE 0 
    end) as ad_impressions
  FROM {{.table}} 
  WHERE "timestamp" BETWEEN {{.start_time}} and {{.end_time}}
    AND res_id IN ({{.res_id}})
    AND product_id IN ({{.product_id}})
    AND product_type in ({{.product_type}})
    AND (ad_group_id IN ({{.ad_group_id}}) OR (CASE WHEN '-1' IN ({{.ad_group_id}}) then 1 else 0 end) = 1)
  GROUP BY user_type

filters:
  - start_time
  - end_time
  - res_id
  - product_id
  - ad_group_id
  - aggregation_type
  - sub_aggregation_type
  - product_type