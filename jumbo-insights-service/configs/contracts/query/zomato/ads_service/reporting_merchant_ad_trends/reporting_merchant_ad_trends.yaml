query_backend: pinot
tenant: Zomato
table: ba_hub_ads_analytics
identifier: reporting_merchant_ad_trends
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ads-service
  pd_service_name: ads-service
  description: Overall Ads trends

caching_ttl: 1200

refresh_interval: 300

sla: 100

rate_limit: 200

type: sql

sql: >
  SELECT res_id, campaign_id,
    SUM(CASE WHEN campaign_id = 'OVERALL' THEN total_sales ELSE 0 END) AS total_sales,
    SUM(CASE WHEN campaign_id = 'OVERALL' THEN total_orders ELSE 0 END) AS total_orders,
    SUM(CASE WHEN campaign_id = 'OVERALL' THEN menu_opens ELSE 0 END) AS total_menu_opens,
    SUM(CASE WHEN campaign_id = 'OVERALL' THEN total_impressions ELSE 0 END) AS total_impressions,
    SUM(CASE WHEN campaign_id != 'OVERALL' THEN ad_menu_opens ELSE 0 END) AS ad_menu_visits,
    SUM(CASE WHEN campaign_id != 'OVERALL' THEN ad_orders ELSE 0 END) AS ad_orders,
    SUM(CASE WHEN campaign_id != 'OVERALL' THEN daily_campaign_budget ELSE 0 END) AS total_campaign_budget,
    SUM(CASE WHEN campaign_id != 'OVERALL' THEN new_users_acquired ELSE 0 END) AS new_users_from_ads,
    SUM(CASE WHEN campaign_id != 'OVERALL' THEN delivered_ad_impression ELSE 0 END) AS ad_impressions,
    SUM(CASE WHEN campaign_id != 'OVERALL' THEN billed_revenue ELSE 0 END) AS ad_spend,
    SUM(CASE WHEN campaign_id != 'OVERALL' THEN ads_cv ELSE 0 END) AS sales_from_ads
  FROM ba_hub_ads_analytics
  WHERE res_id IN ({{.res_id}})
    AND (
      campaign_id IN ({{.campaign_id}}) OR 
      campaign_id = 'OVERALL'
    )
  GROUP BY res_id, campaign_id
filters:
  - res_id
  - campaign_id