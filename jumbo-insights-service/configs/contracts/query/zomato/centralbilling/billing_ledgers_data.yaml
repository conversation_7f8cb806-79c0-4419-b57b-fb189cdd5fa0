query_backend: trino
tenant: Zomato
catalog: zomato
schema: insights_etls
table: cbilling_ledgers_pe
identifier: billing_ledgers_data
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: cbilling-service
  pd_service_name: cbilling
  description: Get Ledgers data for merchants
# Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 60000
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT * FROM {{.table}} where business_id = {{.business_id}}
  and dt >= {{.start_dt}} and dt <= {{.end_dt}} and payout_entity_ref_id = {{.payout_entity_ref_id}}

filters:
  - start_dt
  - end_dt
  - business_id
  - payout_entity_ref_id
