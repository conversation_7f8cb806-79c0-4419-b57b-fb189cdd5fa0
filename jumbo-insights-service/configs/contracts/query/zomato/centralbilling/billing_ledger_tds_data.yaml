query_backend: trino
tenant: Zomato
catalog: hive
schema: insights_etls
table: cbilling_ledgers_pe
identifier: billing_ledgers_tds
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: cbilling-service
  pd_service_name: cbilling
  description: Get Ledgers data for tds gmv deduction job
# Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 60000
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT * FROM {{.table}} where business_id in ({{.business_id}})
  and payout_entity_ref_id in ({{.entity_id}})
  and (to_entity_type ='pe' or from_entity_type ='pe')
  and dt >= {{.start_dt}} and dt <= {{.end_dt}}

filters:
  - business_id
  - entity_id
  - start_dt
  - end_dt