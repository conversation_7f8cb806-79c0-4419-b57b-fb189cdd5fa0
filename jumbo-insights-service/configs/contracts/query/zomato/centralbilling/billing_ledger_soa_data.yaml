query_backend: trino
tenant: Zomato
catalog: zomato
schema: insights_etls
table: cbilling_ledgers_pe
identifier: billing_ledgers_soa
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: cbilling-service
  pd_service_name: cbilling
  description: Get Ledgers data for SOA
# Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 60000
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT * FROM {{.table}} where business_id = {{.business_id}}
  and payout_entity_ref_id = {{.entity_id}}
  and payout_ref_id in ({{.payout_ref_id}})
  and (to_entity_type ='pe' or from_entity_type ='pe')
  and dt >= {{.start_dt}} and dt <= {{.end_dt}}

filters:
  - business_id
  - entity_id
  - start_dt
  - end_dt
  - payout_ref_id