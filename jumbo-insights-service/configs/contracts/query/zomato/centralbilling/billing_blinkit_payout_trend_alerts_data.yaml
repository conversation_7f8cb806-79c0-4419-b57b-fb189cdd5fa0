query_backend: trino
tenant: Zomato
catalog: hive
schema: insights_etls
table: cbilling_blinkit_payout_trend_alert_table
identifier: billing_blinkit_payout_trend_alerts
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: cbilling-service
  pd_service_name: cbilling
  description: Get entity level payout trend alerts for blinkit
# Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 60000
# Request per second
rate_limit: 2
# contract type
type: sql
sql: >
  SELECT * FROM {{.table}} where 
  dt = {{.payout_dt}} and alert_type in ({{.alert_type}})

filters:
  - alert_type
  - payout_dt