query_backend: trino
tenant: Zomato
table: user_dsz_mapping
identifier: audience_creation_trino_v0
catalog: zomato
schema: jumbo_derived
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: flywheel-ads-service
  pd_service_name: z-flywheel-ads-service
  description: Get audience list of users cohorts of the as per the parameters
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 10000 # 10s
# Request per second
rate_limit: 10 
# contract type
type: sql
sql: >
  SELECT distinct udm.user_id as users
  FROM {{.table}} udm
  JOIN jumbo_derived.res_cuisine_dsz_mapping rcdm ON rcdm.dsz_id = udm.dsz_id
  AND (try_cast(rcdm.brand_id AS bigint) IN ({{.entity_id}}))
  AND rcdm.dt IN
    (SELECT max(dt)
    FROM jumbo_derived.res_cuisine_dsz_mapping
    WHERE dt >= date_format(date(date_parse({{.end_dt}}, '%Y%m%d')) - interval '5' DAY, '%Y%m%d') )
  JOIN jumbo_derived.user_platform_properties upp ON cast(upp.user_id AS varchar) = udm.user_id
  JOIN jumbo_derived.o2_user_res_data_mapping urm ON urm.ao_userid = udm.user_id
  AND cast(rcdm.res_id AS varchar) = urm.menu_resid
  JOIN jumbo_derived.user_properties_cuisine_level ucm ON cast(ucm.user_id AS varchar) = udm.user_id
  WHERE upp.aov > {{.aov_lower}}
    AND upp.aov < {{.aov_higher}}
    AND upp.affluence_cohort IN ({{.affluence_cohort}})
    AND upp.gold_flag IN ({{.gold_flag}})
    AND upp.ps_segment IN ({{.ps_segment}})
    AND upp.total_platform_orders_120_days >= {{.total_platform_orders_120_days}}
    AND upp.total_platform_orders_180_days >= {{.total_platform_orders_180_days}}
    AND upp.total_platform_orders_30_days >= {{.total_platform_orders_30_days}}
    AND upp.total_platform_orders_60_days >= {{.total_platform_orders_60_days}}
    AND upp.total_platform_orders_90_days >= {{.total_platform_orders_90_days}}
    AND urm.aov >= {{.brand_aov_lower}}
    AND urm.aov <= {{.brand_aov_higher}}
    AND urm.total_orders <= {{.total_brand_orders_higher}}
    AND urm.total_orders >= {{.total_brand_orders_lower}}
    AND urm.order_cohort IN ({{.order_cohort}})
    AND urm.menu_cohort IN ({{.menu_cohort}})
    AND (CASE
            WHEN {{.cuisine}} = 'All' THEN 1 = 1
            ELSE cuisine_name IN ({{.cuisine}})
        END)
    AND try_cast(ucm.city_id AS bigint) >= ({{.city_id}})
    AND urm.source IN ({{.source}})
    AND ucm.orders_within_30_days >= {{.orders_within_30_days_lower}}
    AND ucm.orders_within_30_days <= {{.orders_within_30_days_higher}}
    AND ucm.orders_within_60_days >= {{.orders_within_60_days_lower}}
    AND ucm.orders_within_60_days <= {{.orders_within_60_days_higher}}
    AND ucm.orders_within_90_days >= {{.orders_within_90_days_lower}}
    AND ucm.orders_within_90_days <= {{.orders_within_90_days_higher}}
    AND ucm.orders_within_120_days >= {{.orders_within_120_days_lower}}
    AND ucm.orders_within_120_days <= {{.orders_within_120_days_higher}}
    AND ucm.orders_within_180_days >= {{.orders_within_180_days_lower}}
    AND ucm.orders_within_180_days <= {{.orders_within_180_days_higher}}
    AND udm.dt BETWEEN date_format(date(date_parse({{.end_dt}}, '%Y%m%d')) - interval '60' DAY, '%Y%m%d') AND {{.end_dt}}
filters:
  - entity_id
  - end_dt
  - aov_lower
  - aov_higher
  - affluence_cohort
  - gold_flag
  - ps_segment
  - total_platform_orders_30_days
  - total_platform_orders_60_days
  - total_platform_orders_90_days
  - total_platform_orders_120_days
  - total_platform_orders_180_days
  - brand_aov_lower
  - brand_aov_higher
  - total_brand_orders_higher
  - total_brand_orders_lower
  - order_cohort
  - menu_cohort
  - cuisine
  - city_id
  - source
  - orders_within_30_days_lower
  - orders_within_30_days_higher
  - orders_within_60_days_lower
  - orders_within_60_days_higher
  - orders_within_90_days_lower
  - orders_within_90_days_higher
  - orders_within_120_days_lower
  - orders_within_120_days_higher
  - orders_within_180_days_lower
  - orders_within_180_days_higher
