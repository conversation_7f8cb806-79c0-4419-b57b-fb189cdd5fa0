query_backend: pinot
tenant: Zomato
table: flywheel_reporting_insights
identifier: meta_reporting_insights_v0
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: flywheel-ads-service
  pd_service_name: z-flywheel-ads-service
  description: Get meta ads performance metrics at aggregated and breakdown levels
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: open_sql
sql: >
  SELECT 
    {{.select_columns}},
    SUM(impressions) AS impressions,
    SUM(spend) AS spend,
    SUM(link_clicks) AS link_clicks,
    SUM(menu_opens) AS menu_opens,
    SUM(purchase_value) AS purchase_value
  FROM {{.table}}
  WHERE 
    campaign_id IN ({{.campaign_ids}})
    AND dt BETWEEN {{.start_date}} AND {{.end_date}}
    AND breakdown_type = {{.breakdown_type}}
    AND level = {{.level}}
  GROUP BY 
    {{.select_columns}}
  ORDER BY 
    {{.order_by_columns}}
filters:
  - select_columns
  - campaign_ids
  - start_date
  - end_date
  - breakdown_type
  - level
  - order_by_columns
