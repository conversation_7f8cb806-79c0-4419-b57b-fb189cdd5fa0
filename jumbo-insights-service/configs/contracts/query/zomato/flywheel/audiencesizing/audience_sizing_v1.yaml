query_backend: trino
tenant: Zomato
table: user_dsz_mapping
identifier: audience_sizing_trino_v1
catalog: zomato
schema: jumbo_derived
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: flywheel-ads-service
  pd_service_name: z-flywheel-ads-service
  description: Get count of users cohorts of the as per the parameters
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 10000 # 10s
# Request per second
rate_limit: 10 
# contract type
type: sql
sql: >
  SELECT count(*) from (
    SELECT udm.user_id
    FROM {{.table}} udm 
    JOIN jumbo_derived.res_cuisine_dsz_mapping rcdm ON rcdm.dsz_id = udm.dsz_id
    AND ( CASE
              WHEN {{.brand_id}} <> -1 THEN try_cast(rcdm.brand_id AS bigint) IN ({{.brand_id}})
              ELSE (CASE
                        WHEN {{.brand_id}} = -1 THEN try_cast(rcdm.res_id AS bigint) IN ({{.res_id}})
                    END)
          END )
    AND rcdm.dt IN
      ( SELECT max(dt)
      FROM jumbo_derived.res_cuisine_dsz_mapping
      WHERE dt >= date_format(date(date_parse({{.end_dt}}, '%Y%m%d')) - interval '15' DAY, '%Y%m%d')
      AND dsz_id IS NOT NULL )

    AND (CASE
          WHEN {{.city_filter_type}} = 'specific' THEN try_cast(rcdm.city_id AS bigint) IN ({{.city_id}})
          WHEN {{.city_filter_type}} = 'exclude' THEN NOT try_cast(rcdm.city_id AS bigint) IN ({{.city_id}})
          ELSE try_cast(rcdm.city_id AS bigint) >= 1
      END ) 
    LEFT JOIN jumbo_derived.user_platform_properties upp ON cast(upp.user_id AS varchar) = udm.user_id
    LEFT JOIN flywheel_etls.o2_user_res_data_mapping urm ON urm.ao_userid = udm.user_id
    AND (CASE
            WHEN {{.brand_id}} <> -1 THEN try_cast(urm.brand_id AS bigint) IN ({{.brand_id}})
            ELSE (CASE
                      WHEN {{.brand_id}} = -1 THEN try_cast(urm.menu_resid AS bigint) IN ({{.res_id}})
                  END)
        END)
    LEFT JOIN jumbo_derived.user_properties_cuisine_level ucm ON cast(ucm.user_id AS varchar) = udm.user_id 
    LEFT JOIN jumbo_derived.user_base_mapping ub on ub.user_id = udm.user_id
    WHERE ( CASE
                WHEN {{.order_cohort}} = 'all' THEN TRUE
                ELSE CASE
                        WHEN {{.oc_mode}} = 'exclude' THEN NOT regexp_like(lower(COALESCE(urm.order_cohort, 'null')), {{.order_cohort}})
                        WHEN {{.oc_mode}} = 'include' THEN regexp_like(lower(COALESCE(urm.order_cohort, 'null')), {{.order_cohort}})
                        ELSE TRUE

                    END
            END )
      AND ( CASE
                WHEN {{.menu_cohort}} = 'all' THEN TRUE
                ELSE CASE
                        WHEN {{.mc_mode}} = 'exclude' THEN NOT regexp_like(lower(COALESCE(urm.menu_cohort, 'null')), {{.menu_cohort}})
                        WHEN {{.mc_mode}} = 'include' THEN regexp_like(lower(COALESCE(urm.menu_cohort, 'null')), {{.menu_cohort}})
                        ELSE TRUE 

                    END
            END )
      AND ( CASE
                WHEN {{.cuisine}} = 'all' THEN TRUE
                ELSE CASE
                        WHEN {{.c_mode}} = 'exclude' THEN NOT regexp_like(lower(ucm.cuisine_name), {{.cuisine}})
                        WHEN {{.c_mode}} = 'include' THEN regexp_like(lower(ucm.cuisine_name), {{.cuisine}})
                        ELSE TRUE

                    END
            END )
      AND (CASE
              WHEN {{.city_filter_type}} = 'specific' THEN try_cast(ucm.city_id AS bigint) IN ({{.city_id}})
              WHEN {{.city_filter_type}} = 'exclude' THEN NOT try_cast(ucm.city_id AS bigint) IN ({{.city_id}})
              ELSE try_cast(ucm.city_id AS bigint) >= 1
          END)
      AND udm.dt BETWEEN date_format(date(date_parse({{.end_dt}}, '%Y%m%d')) - interval '180' DAY, '%Y%m%d') AND {{.end_dt}}
      
      AND CASE WHEN (upp.affluence_cohort is null or upp.affluence_cohort = '') then 'null' ELSE upp.affluence_cohort END in ({{.affluence_cohort}})
      AND CASE WHEN (cast( upp.gold_flag as varchar) is null or cast(upp.gold_flag as varchar) = '') then 'null' ELSE cast(upp.gold_flag as varchar) END in ({{.gold_flag}})
      AND CASE WHEN (upp.ps_segment is null or upp.ps_segment = '') then 'null' ELSE upp.ps_segment END in ({{.ps_segment}})
      AND CASE WHEN (ub.source is null or ub.source  = '') then 'null' ELSE ub.source END in ({{.source}})

      AND COALESCE(upp.aov, 0) >= {{.aov_lower}}
      AND COALESCE(upp.aov, 10000000000000) <= {{.aov_higher}}

      AND COALESCE(upp.total_platform_orders_30_days, 0) >= {{.total_platform_orders_30_days}}
      AND COALESCE(upp.total_platform_orders_60_days, 0) >= {{.total_platform_orders_60_days}}
      AND COALESCE(upp.total_platform_orders_90_days, 0) >= {{.total_platform_orders_90_days}}
      AND COALESCE(upp.total_platform_orders_120_days, 0 ) >= {{.total_platform_orders_120_days}}
      AND COALESCE(upp.total_platform_orders_180_days, 0) >= {{.total_platform_orders_180_days}}

      AND coalesce(ucm.cuisine_aov, 0) >= {{.cuisine_aov_lower}} 
      AND COALESCE(urm.total_orders, 0) >= {{.total_brand_orders_lower}} AND COALESCE(urm.total_orders, 10000000000000) <= {{.total_brand_orders_higher}}
      GROUP BY 1
      HAVING ( 
          sum(COALESCE(ucm.orders_within_30_days, 0)) >= {{.orders_within_30_days_lower}}
        AND sum(COALESCE(ucm.orders_within_60_days, 0)) >= {{.orders_within_60_days_lower}} 
        AND sum(COALESCE(ucm.orders_within_90_days, 0)) >= {{.orders_within_90_days_lower}}
        AND sum(COALESCE(ucm.orders_within_120_days, 0)) >= {{.orders_within_120_days_lower}} 
        AND sum(COALESCE(ucm.orders_within_180_days, 0)) >= {{.orders_within_180_days_lower}}
      ) 
    )
filters:
  - end_dt
  - aov_lower
  - aov_higher
  - affluence_cohort
  - gold_flag
  - ps_segment
  - brand_id
  - res_id
  - order_cohort
  - oc_mode
  - menu_cohort
  - mc_mode
  - cuisine
  - c_mode
  - city_filter_type
  - city_id
  - total_platform_orders_120_days
  - total_platform_orders_180_days
  - total_platform_orders_30_days
  - total_platform_orders_60_days
  - total_platform_orders_90_days
  - cuisine_aov_lower
  - total_brand_orders_lower
  - total_brand_orders_higher
  - source
  - orders_within_30_days_lower
  - orders_within_60_days_lower
  - orders_within_90_days_lower
  - orders_within_120_days_lower
  - orders_within_180_days_lower
