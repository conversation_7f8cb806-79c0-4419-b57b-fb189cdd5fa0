query_backend: trino
tenant: Zomato
table: z_flywheel_audience
identifier: audience_sizing_trino_v2
catalog: zomato
schema: flywheel_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: flywheel-ads-service
  pd_service_name: z-flywheel-ads-service
  description: Get audience list of users cohorts of the as per the parameters
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 10000 # 10s
# Request per second
rate_limit: 10 
# contract type
type: sql
sql: >
  SELECT 
  DISTINCT user_id,
           source,
           sha256_email,
           adid_idfa,
           mobile,
           user_dsz_list
  FROM {{.table}}
  WHERE ( CASE
                        WHEN {{.oc_mode}} = 'exclude' THEN lower(COALESCE(CASE WHEN try_cast(menu_resid AS bigint) IN ({{.res_id}}) THEN order_cohort END, 'null')) NOT IN ({{.order_cohort}})
                        WHEN {{.oc_mode}} = 'include' THEN lower(COALESCE(CASE WHEN try_cast(menu_resid AS bigint) IN ({{.res_id}}) THEN order_cohort END, 'null')) IN ({{.order_cohort}})
                        ELSE TRUE 
            END )
      AND ( CASE
                        WHEN {{.mc_mode}} = 'exclude' THEN lower(COALESCE(CASE WHEN try_cast(menu_resid AS bigint) IN ({{.res_id}}) THEN menu_cohort END, 'null')) NOT IN ({{.menu_cohort}})
                        WHEN {{.mc_mode}} = 'include' THEN lower(COALESCE(CASE WHEN try_cast(menu_resid AS bigint) IN ({{.res_id}}) THEN menu_cohort END, 'null')) IN ({{.menu_cohort}})
                        ELSE TRUE 
            END )
      AND (CASE
              WHEN {{.city_filter_type}} = 'specific' THEN try_cast(cityid AS bigint) IN ({{.city_id}})
              WHEN {{.city_filter_type}} = 'exclude' THEN NOT try_cast(cityid AS bigint) IN ({{.city_id}})
              ELSE try_cast(cityid AS bigint) >= 1
          END)
      AND CASE WHEN (affluence_cohort is null or affluence_cohort = '') then 'null' ELSE affluence_cohort END in ({{.affluence_cohort}})
      AND CASE WHEN (cast( gold_flag as varchar) is null or cast(gold_flag as varchar) = '') then 'null' ELSE cast(gold_flag as varchar) END in ({{.gold_flag}})
      AND CASE WHEN (ps_segment is null or ps_segment = '') then 'null' ELSE ps_segment END in ({{.ps_segment}})
      AND CASE WHEN (source is null or source  = '') then 'null' ELSE source END in ({{.source}})
      AND CASE WHEN (current_hml_tag is null or current_hml_tag  = '') then 'null' ELSE current_hml_tag END in ({{.current_hml_tag}})
      AND (CASE 
                        WHEN {{.veg_mode}} = 'all' THEN TRUE
                        WHEN {{.veg_mode}} = 'gt' then coalesce(veg_score, -1) >= {{.veg_score}}
                        WHEN {{.veg_mode}} = 'lt' then coalesce(veg_score, 101) <= {{.veg_score}}
                        ELSE TRUE
            END )
  
      AND COALESCE(user_aov, 0) >= {{.aov_lower}}
      AND COALESCE(user_aov, 10000000000000) <= {{.aov_higher}}
  
      AND COALESCE(total_platform_orders_30_days, 0) >= {{.total_platform_orders_30_days}}
      AND COALESCE(total_platform_orders_60_days, 0) >= {{.total_platform_orders_60_days}}
      AND COALESCE(total_platform_orders_90_days, 0) >= {{.total_platform_orders_90_days}}
      AND COALESCE(total_platform_orders_120_days, 0 ) >= {{.total_platform_orders_120_days}}
      AND COALESCE(total_platform_orders_180_days, 0) >= {{.total_platform_orders_180_days}}
      AND (CASE WHEN {{.cc_mode}}= 'include' then
                 CASE WHEN {{.cc}} = 'cc1' THEN
                  (   COALESCE(cc1_orders_within_30_days, 0) >= {{.orders_within_30_days_lower}}
                      AND COALESCE(cc1_orders_within_60_days, 0) >= {{.orders_within_60_days_lower}} 
                      AND COALESCE(cc1_orders_within_90_days, 0) >= {{.orders_within_90_days_lower}}
                      AND COALESCE(cc1_orders_within_120_days, 0) >= {{.orders_within_120_days_lower}} 
                      AND COALESCE(cc1_orders_within_180_days, 0) >= {{.orders_within_180_days_lower}} 
                      AND coalesce(cc1_aov, 0) >= {{.cuisine_aov_lower}} )
                   WHEN {{.cc}} = 'cc2' THEN
                  (   COALESCE(cc2_orders_within_30_days, 0) >= {{.orders_within_30_days_lower}}
                      AND COALESCE(cc2_orders_within_60_days, 0) >= {{.orders_within_60_days_lower}} 
                      AND COALESCE(cc2_orders_within_90_days, 0) >= {{.orders_within_90_days_lower}}
                      AND COALESCE(cc2_orders_within_120_days, 0) >= {{.orders_within_120_days_lower}} 
                      AND COALESCE(cc2_orders_within_180_days, 0) >= {{.orders_within_180_days_lower}} 
                      AND coalesce(cc2_aov, 0) >= {{.cuisine_aov_lower}} )
                   WHEN {{.cc}} = 'cc3' THEN
                  (   COALESCE(cc3_orders_within_30_days, 0) >= {{.orders_within_30_days_lower}}
                      AND COALESCE(cc3_orders_within_60_days, 0) >= {{.orders_within_60_days_lower}} 
                      AND COALESCE(cc3_orders_within_90_days, 0) >= {{.orders_within_90_days_lower}}
                      AND COALESCE(cc3_orders_within_120_days, 0) >= {{.orders_within_120_days_lower}} 
                      AND COALESCE(cc3_orders_within_180_days, 0) >= {{.orders_within_180_days_lower}} 
                      AND coalesce(cc3_aov, 0) >= {{.cuisine_aov_lower}} )
                   WHEN {{.cc}} = 'cc4' THEN
                  (   COALESCE(cc4_orders_within_30_days, 0) >= {{.orders_within_30_days_lower}}
                      AND COALESCE(cc4_orders_within_60_days, 0) >= {{.orders_within_60_days_lower}} 
                      AND COALESCE(cc4_orders_within_90_days, 0) >= {{.orders_within_90_days_lower}}
                      AND COALESCE(cc4_orders_within_120_days, 0) >= {{.orders_within_120_days_lower}} 
                      AND COALESCE(cc4_orders_within_180_days, 0) >= {{.orders_within_180_days_lower}} 
                      AND coalesce(cc4_aov, 0) >= {{.cuisine_aov_lower}} )
                   WHEN {{.cc}} = 'cc5' THEN
                  (   COALESCE(cc5_orders_within_30_days, 0) >= {{.orders_within_30_days_lower}}
                      AND COALESCE(cc5_orders_within_60_days, 0) >= {{.orders_within_60_days_lower}} 
                      AND COALESCE(cc5_orders_within_90_days, 0) >= {{.orders_within_90_days_lower}}
                      AND COALESCE(cc5_orders_within_120_days, 0) >= {{.orders_within_120_days_lower}} 
                      AND COALESCE(cc5_orders_within_180_days, 0) >= {{.orders_within_180_days_lower}} 
                      AND coalesce(cc5_aov, 0) >= {{.cuisine_aov_lower}} )
                   WHEN {{.cc}} = 'cc6' THEN
                  (   COALESCE(cc6_orders_within_30_days, 0) >= {{.orders_within_30_days_lower}}
                      AND COALESCE(cc6_orders_within_60_days, 0) >= {{.orders_within_60_days_lower}} 
                      AND COALESCE(cc6_orders_within_90_days, 0) >= {{.orders_within_90_days_lower}}
                      AND COALESCE(cc6_orders_within_120_days, 0) >= {{.orders_within_120_days_lower}} 
                      AND COALESCE(cc6_orders_within_180_days, 0) >= {{.orders_within_180_days_lower}} 
                      AND coalesce(cc6_aov, 0) >= {{.cuisine_aov_lower}} )
                  WHEN {{.cc}} = 'cc7' THEN
                  (   COALESCE(cc7_orders_within_30_days, 0) >= {{.orders_within_30_days_lower}}
                      AND COALESCE(cc7_orders_within_60_days, 0) >= {{.orders_within_60_days_lower}} 
                      AND COALESCE(cc7_orders_within_90_days, 0) >= {{.orders_within_90_days_lower}}
                      AND COALESCE(cc7_orders_within_120_days, 0) >= {{.orders_within_120_days_lower}} 
                      AND COALESCE(cc7_orders_within_180_days, 0) >= {{.orders_within_180_days_lower}} 
                      AND coalesce(cc7_aov, 0) >= {{.cuisine_aov_lower}} )
                  WHEN {{.cc}} = 'cc8' THEN
                  (   COALESCE(cc8_orders_within_30_days, 0) >= {{.orders_within_30_days_lower}}
                      AND COALESCE(cc8_orders_within_60_days, 0) >= {{.orders_within_60_days_lower}} 
                      AND COALESCE(cc8_orders_within_90_days, 0) >= {{.orders_within_90_days_lower}}
                      AND COALESCE(cc8_orders_within_120_days, 0) >= {{.orders_within_120_days_lower}} 
                      AND COALESCE(cc8_orders_within_180_days, 0) >= {{.orders_within_180_days_lower}} 
                      AND coalesce(cc8_aov, 0) >= {{.cuisine_aov_lower}} )
                  END
          else true end)


filters:
  - aov_lower
  - aov_higher
  - affluence_cohort
  - gold_flag
  - ps_segment
  - current_hml_tag
  - order_cohort
  - oc_mode
  - menu_cohort
  - mc_mode
  - veg_mode
  - veg_score
  - cc
  - cc_mode
  - city_filter_type
  - res_id
  - city_id
  - total_platform_orders_120_days
  - total_platform_orders_180_days
  - total_platform_orders_30_days
  - total_platform_orders_60_days
  - total_platform_orders_90_days
  - cuisine_aov_lower
  - source
  - orders_within_30_days_lower
  - orders_within_60_days_lower
  - orders_within_90_days_lower
  - orders_within_120_days_lower
  - orders_within_180_days_lower
