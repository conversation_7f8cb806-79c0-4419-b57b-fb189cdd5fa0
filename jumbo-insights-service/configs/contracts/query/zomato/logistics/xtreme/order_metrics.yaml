# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: xtreme_order_metrics
identifier: xtreme_order_metrics_aggregated
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-merchant-service
  pd_service_name: logistics-merchant-service
  description: Xtreme order metrics aggregated
# TTL for cache, Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT 
  COUNT(*) AS total_orders,
  SUM(CASE WHEN order_status = 'COMPLETE' THEN 1 ELSE 0 END) AS completed_orders,
  SUM(CASE WHEN order_status = 'CANCELLED' THEN 1 ELSE 0 END) AS cancelled_orders,
  SUM(CASE WHEN order_status = 'SHIPPED' THEN 1 ELSE 0 END) AS shipped_orders,
  SUM(CASE WHEN order_status = 'RETURN' THEN 1 ELSE 0 END) AS return_orders,
  SUM(CASE WHEN order_status = 'DRIVER_ASSIGNED' THEN 1 ELSE 0 END) AS pickup_awaited,
  SUM(CASE WHEN amount_to_be_collected = 0 THEN 1 ELSE 0 END) AS prepaid_orders,
  SUM(CASE WHEN amount_to_be_collected <> 0 THEN 1 ELSE 0 END) AS cod_orders,  
  SUM(CASE WHEN order_status = 'COMPLETE' AND total_distance <= 3 THEN 1 ELSE 0 END) AS completed_orders_0_3kms,
  SUM(CASE WHEN order_status = 'COMPLETE' AND total_distance > 3 AND total_distance <= 5 THEN 1 ELSE 0 END) AS completed_orders_3_5kms,
  SUM(CASE WHEN order_status = 'COMPLETE' AND total_distance > 5 THEN 1 ELSE 0 END) AS completed_orders_gt5kms,
  SUM(CASE WHEN order_status = 'COMPLETE' AND total_distance <= 3 THEN delivery_time ELSE 0 END) AS total_delivery_time_0_3kms,
  SUM(CASE WHEN order_status = 'COMPLETE' AND total_distance > 3 AND total_distance <= 5 THEN delivery_time ELSE 0 END) AS total_delivery_time_3_5kms,
  SUM(CASE WHEN order_status = 'COMPLETE' AND total_distance > 5 THEN delivery_time ELSE 0 END) AS total_delivery_time_gt5kms,
  SUM(CASE WHEN order_status = 'COMPLETE' THEN total_distance ELSE 0 END) AS completed_orders_total_distance,
  SUM(CASE WHEN order_status = 'COMPLETE' THEN delivery_time ELSE 0 END) AS completed_orders_total_delivery_time
  FROM {{.table}}
  WHERE DATETIMECONVERT(
         "created_at", 
         '1:SECONDS:EPOCH', 
         '1:DAYS:SIMPLE_DATE_FORMAT:yyyyMMdd', 
         '1:DAYS'
       ) >= {{.start_date}} AND DATETIMECONVERT(
         "created_at", 
         '1:SECONDS:EPOCH', 
         '1:DAYS:SIMPLE_DATE_FORMAT:yyyyMMdd', 
         '1:DAYS'
       ) <= {{.end_date}} AND source_id = {{.source_id}}
filters:
  - start_date
  - end_date
  - source_id