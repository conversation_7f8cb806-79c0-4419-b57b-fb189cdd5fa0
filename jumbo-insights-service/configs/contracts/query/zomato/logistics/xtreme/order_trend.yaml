# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: xtreme_order_metrics
identifier: xtreme_order_trend
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-merchant-service
  pd_service_name: logistics-merchant-service
  description: Xtreme order trend
# TTL for cache, Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql

# aggregation_level = 1 hourly
# aggregation_level = 2 daily
# aggregation_level = 3 weekly
# aggregation_level = 4 monthly

# when aggregation_level = 1, pass start_date = end_date
sql: >
  SELECT 
  CASE 
    WHEN {{.aggregation_level}} = '1' THEN HOUR(created_at * 1000, 'Asia/Kolkata')
    WHEN {{.aggregation_level}} = '2' THEN DATETIMECONVERT(
         "created_at", 
         '1:SECONDS:EPOCH', 
         '1:DAYS:SIMPLE_DATE_FORMAT:yyyyMMdd', 
         '1:DAYS'
       )
    WHEN {{.aggregation_level}} = '3' THEN concat(year(created_at * 1000, 'Asia/Kolkata'), weekOfYear(created_at * 1000, 'Asia/Kolkata'), '-')
    WHEN {{.aggregation_level}} = '4' THEN concat(year(created_at * 1000, 'Asia/Kolkata'), monthOfYear(created_at * 1000, 'Asia/Kolkata'), '-')
  ELSE ''
  END AS time_unit_label,
  COUNT(*) AS total_orders,
  SUM(CASE WHEN order_status = {{.order_status}} THEN 1 ELSE 0 END) AS total_orders_with_status
  FROM {{.table}}
  WHERE DATETIMECONVERT(
         "created_at", 
         '1:SECONDS:EPOCH', 
         '1:DAYS:SIMPLE_DATE_FORMAT:yyyyMMdd', 
         '1:DAYS'
       ) >= {{.start_date}} AND DATETIMECONVERT(
         "created_at", 
         '1:SECONDS:EPOCH', 
         '1:DAYS:SIMPLE_DATE_FORMAT:yyyyMMdd', 
         '1:DAYS'
       ) <= {{.end_date}} AND source_id = {{.source_id}}
  GROUP BY 1
  ORDER BY 1
filters:
  - start_date
  - end_date
  - source_id
  - order_status
  - aggregation_level