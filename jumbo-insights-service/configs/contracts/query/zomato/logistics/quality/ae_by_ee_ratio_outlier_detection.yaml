query_backend: pinot
tenant: zanalytics
table: logs_accounting_trip_ledgers
identifier: ae_by_ee_ratio_outlier_detection
audit:
  author_email: r<PERSON><PERSON><PERSON><PERSON><PERSON>@zomato.com
  team_email: <EMAIL>
  service: logistics-quality-service
  pd_service_name: logistics-quality-service
  description: Detect outliers in the AE by EE ratio

# Time in second
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# Dimensions
type: sql
sql: >
  SET useMultistageEngine=true;
  WITH base AS (
    SELECT
      driver_id,
      ref_id AS trip_id,
      internal_order_ids,
      roundDecimal(CAST(actual_amount AS DOUBLE) / CAST(estimated_amount AS DOUBLE), 2) AS ratio
    FROM 
      {{.table}}
    WHERE
      ref_type = 'Trip'
      AND business = 'zomato'
      AND actual_amount > 0
      AND estimated_amount > 0
      AND "timestamp" >= (cast(now() as INTEGER) - {{.lookback_window_ms}})/1000
      AND ARRAY_LENGTH(internal_order_ids) = 1
  ),
  perc AS (
    SELECT
      PERCENTILETDIGEST(ratio, 99) AS ratio_99
    FROM
      base
  )
  SELECT
    driver_id,
    arrayElementAtString(internal_order_ids, 1) AS order_id,
    trip_id AS trip_id,
    ratio AS ae_by_ee_ratio
  FROM
    base b
    JOIN perc p
      ON b.ratio >= p.ratio_99
  WHERE 
    b.ratio >= 2;

filters:
  - lookback_window_ms
