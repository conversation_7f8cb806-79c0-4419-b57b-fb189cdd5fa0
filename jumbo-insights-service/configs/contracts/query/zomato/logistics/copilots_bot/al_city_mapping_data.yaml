query_backend: pinot
tenant: Zomato
table: al_city_mapping
identifier: al_city_mapping_data
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ai-copilots-demo
  pd_service_name: ai-copilots-demo
  description: Get AL city mapping
# TTL for cache, Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 120
# Request per second
rate_limit: 800
# contract type
type: sql
sql: >
  SELECT al_email_id, city_id, city_names
  FROM {{.table}}
  WHERE al_email_id = ({{.al_email_id}})
filters:
  - al_email_id