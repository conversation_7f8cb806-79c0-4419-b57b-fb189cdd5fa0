query_backend: pinot
tenant: Zomato
table: fc_zone_mapping
identifier: fc_zone_mapping_data
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ai-copilots-demo
  pd_service_name: ai-copilots-demo
  description: Get FC Zone mapping
# TTL for cache, Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 120
# Request per second
rate_limit: 800
# contract type
type: sql
sql: >
  SELECT fleet_coach_email_id, zone_id, city_name, city_names
  FROM {{.table}}
  WHERE fleet_coach_email_id = ({{.fleet_coach_email_id}})
filters:
  - fleet_coach_email_id