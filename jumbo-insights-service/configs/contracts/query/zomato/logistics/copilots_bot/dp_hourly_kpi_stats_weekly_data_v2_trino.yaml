query_backend: trino
tenant: Zomato
table: dp_hourly_kpi_stats_weekly_v2
identifier: dp_hourly_kpi_stats_weekly_data_v2_trino
catalog: hive
schema: insights_etls
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ai-copilots-demo
  pd_service_name: ai-copilots-demo
  description: Get rider level cost data across different hours for all cities with zone level data
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: open_sql
sql: >
  SELECT {{.select_columns}}
  FROM {{.table}}
  WHERE {{.where_conditions}}
  GROUP BY {{.group_by_columns}}
  HAVING {{.having_columns}}
  ORDER BY {{.order_by_columns}}
filters:
  - select_columns
  - where_conditions
  - group_by_columns
  - having_columns
  - order_by_columns
