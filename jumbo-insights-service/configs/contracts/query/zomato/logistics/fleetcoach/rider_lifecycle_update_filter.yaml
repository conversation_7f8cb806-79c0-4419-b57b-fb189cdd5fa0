# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: rider_daily_metrics
identifier: rider_lifecycle_update_filter
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Filter riders based on updation in lifecycle status
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT delivery_driver_id
  FROM {{.table}}
  WHERE
    lifecycle_status = {{.lifecycle_status}}
    AND lifecycle_status_last_updated > {{.start_date}}
    AND dt = {{.end_date}}
    AND delivery_driver_id IN ({{.delivery_driver_ids}})
filters:
  - start_date
  - end_date
  - delivery_driver_ids
  - lifecycle_status
