 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: fleet_coach_performance_metrics
identifier: fc_coverage_channels
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Get fleetcoach coverage channels
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
    dt, 
    week, 
    SUM(calls)*100.0 / (SUM(mapped_dp) + SUM(unmapped_dp)) AS calls_pct,
    SUM(meets)*100.0 / (SUM(mapped_dp) + SUM(unmapped_dp)) AS meets_pct,
    SUM(positive_feedback_pct)*100.0 / (SUM(mapped_dp) + SUM(unmapped_dp)) AS positive_feedback_pct
  FROM {{.table}}
  WHERE 
    dt IN ({{.dates}})
    AND fleet_coach_id = ({{.fleet_coach_id}})
  GROUP BY 1,2
  ORDER BY dt DESC
filters:
  - dates
  - fleet_coach_id
