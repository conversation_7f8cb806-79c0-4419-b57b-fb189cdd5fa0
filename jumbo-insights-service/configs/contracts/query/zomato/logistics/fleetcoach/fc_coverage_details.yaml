 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: fleet_coach_performance_metrics
identifier: fc_coverage_details
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Get fleetcoach coverage details
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT 
    dt, 
    week, 
    CASE
      WHEN driver_category = 'others' THEN 'Others' 
      WHEN driver_category = 'Ultra_Marathoner' THEN 'Ultra marathoner'
      WHEN driver_category = 'All' THEN 'All DPs'
      ELSE driver_category
    END as driver_category,
    pct_coverage
  FROM {{.table}} 
  WHERE 
    dt IN ({{.dates}})
    AND fleet_coach_id = ({{.fleet_coach_id}})
  ORDER BY dt DESC
filters:
  - dates
  - fleet_coach_id
