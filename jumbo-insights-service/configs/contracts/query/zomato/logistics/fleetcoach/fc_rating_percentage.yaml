query_backend: pinot
tenant: Zomato
table: fleet_coach_rating
identifier: fc_rating_percentage
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: catalyst-service
  description: Get rating percentage of fleetcoach for every tag
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql

# SQL query to be executed
sql: >
  select fleet_coach_id, tag_id, tag_name, rating_percentage, dt
  from {{.table}}
  where fleet_coach_id = {{.fleet_coach_id}}
  and dt >= {{.start_date}}
  and dt <= {{.end_date}}
  order by dt desc, tag_id
filters:
  - fleet_coach_id
  - start_date
  - end_date
