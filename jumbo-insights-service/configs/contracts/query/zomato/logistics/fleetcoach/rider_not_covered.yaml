# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: rider_daily_metrics
identifier: rider_not_covered
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Filter riders based coverage
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT delivery_driver_id
  FROM {{.table}}
  WHERE 
    fleet_coach_id = ({{.fleet_coach_id}})
    AND delivery_driver_id IN ({{.delivery_driver_ids}})
    AND dt = ({{.end_date}})
    AND last_met < ({{.start_date}})
    AND last_call_connected < ({{.start_date}})
filters:
  - start_date
  - end_date
  - fleet_coach_id
  - delivery_driver_ids
