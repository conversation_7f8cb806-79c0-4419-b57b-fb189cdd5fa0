 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: rider_daily_metrics
identifier: rider_meeting_met_or_not_met
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Filter riders based on interactions with fleetcoach
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT delivery_driver_id
  FROM {{.table}}
  WHERE fleet_coach_id = ({{.fleet_coach_id}})
  AND delivery_driver_id IN ({{.delivery_driver_ids}})
  AND ( 
    CASE
      WHEN 1 = {{.last_met_condition}} THEN last_met < {{.past_days_to_consider}}   
      ELSE last_met >= {{.past_days_to_consider}}  
    END
  )
  AND dt = ({{.date}})
filters:
  - date
  - fleet_coach_id
  - delivery_driver_ids
  - past_days_to_consider
  - last_met_condition
