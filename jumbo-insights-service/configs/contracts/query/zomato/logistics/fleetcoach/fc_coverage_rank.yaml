# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: fleet_coach_performance_metrics
identifier: fc_coverage_rank
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Get fleetcoach coverage rank
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT 
    (COUNT(*) + 1) as rank
    FROM {{.table}}
    WHERE
      dt = {{.dt}}
      AND driver_category = 'All'
      AND (
          CASE
            WHEN {{.city_condition}} = 2 THEN city_id = {{.city_id}}
            WHEN {{.city_condition}} = 3 THEN city_tier = {{.city_tier}}
            ELSE true
          END
        )
      AND (
          CASE
              WHEN city_tier = 'Top7' THEN mapped_dp >= 600
              WHEN city_tier = 'Top_8-15' THEN mapped_dp >= 300
              WHEN city_tier = 'Top_16-30' THEN mapped_dp >= 300
              WHEN city_tier = 'Bharat' THEN mapped_dp >= 200
              ELSE mapped_dp >= 100
          END
      )
      AND (pct_coverage > {{.metric_value}} OR (pct_coverage = {{.metric_value}} AND mapped_dp > {{.mapped_dp}}))
filters:
  - dt
  - metric_value
  - city_condition
  - city_tier
  - city_id
  - mapped_dp
