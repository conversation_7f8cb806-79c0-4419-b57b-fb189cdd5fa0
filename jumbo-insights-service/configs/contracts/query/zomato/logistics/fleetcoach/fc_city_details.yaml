# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: fleet_coach_performance_metrics
identifier: fc_city_details
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Get fleetcoach city details
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT 
    city_id, 
    city_tier,
    city_name,
    mapped_dp,
    pct_coverage,
    change_churn_2w,
    change_churn_2w_new
    FROM {{.table}}
    WHERE
      dt = {{.dt}}
      AND fleet_coach_id = {{.fleet_coach_id}}
      AND driver_category = 'All'
filters:
  - dt
  - fleet_coach_id
