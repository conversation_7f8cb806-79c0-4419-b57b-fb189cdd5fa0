query_backend: trino
tenant: Zomato
schema: rbac_logistics_etls
table: city_delivery_drivers_master
identifier: delivery_driver_master
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: driver-service-worker
  pd_service_name: driver-service-worker
  description: Get the driver ids to find cohort of drivers who are eligible for stress nudge
# Time in second
# caching not implemented in async querier
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 60000 # one minute
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT DISTINCT
    delivery_driver_id
  FROM
    rbac_logistics_etls.city_delivery_drivers_master
  WHERE
  dt BETWEEN date_format(CURRENT_DATE - INTERVAL {{.past_x_days}} DAY, '%Y%m%d')
         AND date_format(CURRENT_DATE - INTERVAL '1' DAY, '%Y%m%d')
    AND zone_id = {{.zone_id}}
    AND is_slot_dp = 1
    AND lifecycle_status = 'active'
  GROUP BY
    delivery_driver_id
  HAVING
    SUM(CAST(drop_tps AS INTEGER)) >= {{.min_order_completed}}
    
filters:
  - past_x_days
  - zone_id
  - min_order_completed
