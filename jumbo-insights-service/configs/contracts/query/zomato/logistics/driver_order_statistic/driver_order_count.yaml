query_backend: pinot
tenant: Zomato
table: logistic_orders_stats
identifier: driver_order_count
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-api-gateway
  pd_service_name: logistics-api-gateway-service
  description: Driver's order count
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT 
    COUNT(*) AS order_count
  FROM {{.table}}
  WHERE
  dt <= {{.end_date}}
  AND dt >= {{.start_date}}
  AND source_id = {{.source_id}}
  AND driver_id = {{.driver_id}}
filters:
  - start_date
  - end_date
  - driver_id
  - source_id