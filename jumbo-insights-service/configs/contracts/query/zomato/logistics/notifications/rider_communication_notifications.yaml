query_backend: trino
tenant: Zomato
schema: rbac_logistics_etls
table: city_delivery_drivers_master
identifier: delivery_partner_notification_dashboard_query
audit:
  author_email: girish.b<PERSON><PERSON><PERSON>@zomato.com
  team_email: <EMAIL>
  service: driver-service-grpc
  pd_service_name: driver-service-grpc
  description: Get data for drivers to send notifications
# Time in second
# caching not implemented in async querier
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 60000 # one minute
# Request per second
rate_limit: 10
# contract type
type: open_sql
sql: >
  SELECT {{.select_clause}}
  FROM rbac_logistics_etls.city_delivery_drivers_master
  WHERE {{.where_conditions}}
  LIMIT {{.limit_clause}}
filters:
  - where_conditions
  - select_clause
  - limit_clause
