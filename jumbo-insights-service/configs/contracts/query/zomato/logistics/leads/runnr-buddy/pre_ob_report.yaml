# can be pinot/trino
query_backend: trino
tenant: Zomato
catalog: hive
table: insights_etls.rbr_pre_ob
identifier: runnr-buddy-leads-pre-ob-report
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: lead-management-service
  pd_service_name: lead-management-service
  description: Get runnr buddy leads pre-ob report
# Time in second
# caching not implemented in async querier
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 60000 # one minute
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT 
    user_id,
    lead_id,
    phone_number_encrypted as referred_phone_number,
    referred_date,
    referred_name,
    city_name,
    status,
    lead_ageing,
    app_download_flag,
    carrier_submitted,
    city_submitted,
    zone_submitted,
    aadhaar_submitted,
    pan_submitted,
    selfie_submitted,
    payment_details_submitted,
    asset_delivery_address,
    calling_priority,
    onboarding_app_type
  FROM {{.table}}
  WHERE user_id IN ({{.user_ids}})
    AND referred_dt >= {{.start_date}}
    AND referred_dt <= {{.end_date}}
  ORDER BY referred_dt DESC
filters:
  - user_ids
  - start_date
  - end_date
decryption:
  - sql_column: referred_phone_number
    source_column: dynamodb.prod_lead_management_service
