query_backend: pinot
tenant: Zomato
table: insights_etls.rbr_pre_ob
identifier: runnr-buddy-leads-pre-ob-lead-list
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: lead-management-service
  pd_service_name: lead-management-service
  description: Get runnr buddy leads List pre-ob report (Pinot version)
caching_ttl: 7200  # Time in seconds
refresh_interval: 3600  # Time in seconds
sla: 60000  # in milliseconds
rate_limit: 10
type: sql
sql: >
  SELECT 
    user_id,
    lead_id,
    referred_date,
    referred_name,
    city,
    lead_status,
    lead_open_time,
    app_download_status,
    aadhaar_submitted,
    pan_submitted,
    ob_done
  FROM {{.table}}
  WHERE user_id IN ({{.user_ids}})
    AND referred_dt >= {{.start_date}}
    AND referred_dt <= {{.end_date}}
  ORDER BY referred_dt DESC
filters:
  - user_ids
  - start_date
  - end_date

