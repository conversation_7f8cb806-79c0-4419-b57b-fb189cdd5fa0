# can be pinot/trino
query_backend: trino
tenant: Zomato
catalog: zomato
table: insights_etls.rbr_post_ob
identifier: runnr-buddy-leads-post-ob-report
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: lead-management-service
  pd_service_name: lead-management-service
  description: Get runnr buddy leads post-ob report
# Time in second
# caching not implemented in async querier
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 60000 # one minute
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
    user_id,
    lead_id,
    "driver id" as driver_id,
    driver_name,
    phone_number_encrypted as driver_phone_number,
    city,
    referred_date,
    doj,
    source,
    carrier,
    lifecycle_status,
    qc_status,
    qc_created_at,
    qc_updated_at,
    qc_rej_reason,
    bgv_status,
    bgv_passed_at,
    "nominee filled" as nominee_filled,
    "training completed" as training_completed,
    first_active_at,
    first_order,
    last_order,
    zsp_moving_date,
    bfleet_moving_date,
    first_gig,
    latest_gig,
    "7d_orders",
    "14d_orders",
    "30d_orders",
    "56d_orders",
    "0d_orders",
    "2d_orders",
    payment_type
  FROM {{.table}}
  WHERE user_id IN ({{.user_ids}})
    AND dt >= {{.start_date}}
    AND dt <= {{.end_date}}
  ORDER BY dt DESC
filters:
  - user_ids
  - start_date
  - end_date
decryption:
  - sql_column: driver_phone_number
    source_column: dynamodb.prod_lead_management_service

