query_backend: pinot
tenant: Zomato
table: zomato_polls_tracking
identifier: zomato_polls_tracking_fetch_user_response
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: zomato polls user first response 
# TTL for cache, Time in seconds
caching_ttl: 1200 # 20 minutes
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT poll_response as response_name 
  FROM zomato_polls_tracking 
  WHERE poll_id = {{.poll_id}} 
  AND location = {{.location}} 
  AND user_id = {{.user_id}} 
filters:
  - poll_id
  - location
  - user_id
