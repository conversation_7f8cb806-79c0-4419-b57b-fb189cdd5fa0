# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: order_events_metrics
identifier: order_events_metrics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: aggregator
  pd_service_name: data_platform
  description: One liner desc of the query
# TTL for cache, Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 120
# Request per second
rate_limit: 20
# Dimensions
columns:
  - name: eventName
  - name: vendorId
  - name: cityId
  - name: is_delayed
  - name: avg_duration_in_mins
    func: avg
    source_column: duration_in_mins
  - name: total_orders
    func: count
    source_column: orderId
# Group by columns
aggregations:
  - eventName
  - vendorId
  - cityId
  - is_delayed
filters:
  - processingTime
  - eventName
  - vendorId
  - duration_in_mins
