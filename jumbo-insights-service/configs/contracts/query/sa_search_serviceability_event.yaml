# can be pinot/trino
query_backend: trino
tenant: Zomato
catalog: zomato
schema: jumbo2
table: search_serviceability_aggregator_logs
identifier: sa_multi_res_logs
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: serviceability-aggregator
  pd_service_name: serviceability-aggregator-service
  description: serviceability debugger dashboard

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: > 
      SELECT city_id,
       delivery_cell_id,
       event_name,
       latitude,
       longitude,
       time,
       array_join(transform(top_n_complete_response_new, x -> json_format(CAST(x AS JSON))), ', ') AS top_n_complete_response_new,
       array_join(user_subscription_type,',') AS user_subscription_type,
       user_id,
       session_id
      FROM {{.table}}
      WHERE dt={{.date}}
      AND ((user_id={{.user_id}})
      OR ({{.user_id}} = 'all'))
      AND ((session_id = {{.session_id}})
      OR ({{.session_id}} = 'all'))
      AND (({{.start_time}} = 0)
      OR (time >= {{.start_time}}))
      AND (({{.end_time}} = 0)
      OR (time <= {{.end_time}}))
      AND ((event_name={{.event_name}})
      OR ({{.event_name}} = 'all'))
filters:
  - date
  - user_id
  - session_id
  - start_time
  - end_time
  - event_name
