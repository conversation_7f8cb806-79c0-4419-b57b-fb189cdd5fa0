query_backend: clickhouse
tenant: Zomato
table: code_insights_service
identifier: athena_clickhouse
audit:
  author_email: mura<PERSON>.<EMAIL>
  team_email: <EMAIL>
  service: code-insights-service
  pd_service_name: z-code-insights-service
  description: athena_clickhouse

# Time in second
caching_ttl: 7200 # 2 hours
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 10000
# Request per second
rate_limit: 1
# contract type
# todo: move message.log.caller, and message.log.level out of schema_violation
type: sql
sql: > 
  select
      container.image.name as image_name,
      schema_violation['message.log.caller'] as caller,
      count(*) as count
  from
     cluster('logs_cluster',{{.cluster_env}}, {{.target_table}})
  where
      schema_violation['message.log.level'] LIKE {{.level}}
      AND toDateTime(ts) BETWEEN FROM_UNIXTIME({{.ts_from}}) AND FROM_UNIXTIME({{.ts_to}})
      AND container.labels.environment = {{.environment}}
      GROUP BY 1, 2
      LIMIT 1000

table_names:
  - target_table

filters:
  - ts_from
  - ts_to
  - level
  - environment
  - cluster_env
