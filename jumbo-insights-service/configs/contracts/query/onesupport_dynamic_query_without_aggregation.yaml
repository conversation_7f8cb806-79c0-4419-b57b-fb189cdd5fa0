query_backend: clickhouse
tenant: nugget-analytics
table: one_support_events
identifier: onesupport_dynamic_query_without_aggregation
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: chat-v2-backend-service
  pd_service_name: chat-backend-v2-service
  description: Get data for one support events
# TTL for cache, Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 200
# Request per second
rate_limit: 100
# contract type
type: open_sql
sql: >
  SELECT {{.select_columns}}
  FROM {{.dynamic_table_name}}
  WHERE {{.where_conditions}}
  {{.group_by_statement}}
  {{.having_statement}}
  {{.order_by_statement}}
filters:
  - select_columns
  - dynamic_table_name
  - where_conditions
  - group_by_statement
  - having_statement
  - order_by_statement