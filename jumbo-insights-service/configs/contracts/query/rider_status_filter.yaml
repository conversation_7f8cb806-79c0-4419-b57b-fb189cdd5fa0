 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: rider_hourly_metrics
identifier: rider_status_filter
audit:
  author_email: jain.saura<PERSON><EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Filter riders based on their order status
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT DISTINCT(delivery_driver_id), driver_status
  FROM {{.table}}
  WHERE driver_status IN ({{.driver_status}})
  AND delivery_driver_id IN ({{.delivery_driver_ids}})
  AND dt = ({{.date}})
  AND hour = ({{.hour}})
filters:
  - date
  - driver_status
  - delivery_driver_ids
  - hour
