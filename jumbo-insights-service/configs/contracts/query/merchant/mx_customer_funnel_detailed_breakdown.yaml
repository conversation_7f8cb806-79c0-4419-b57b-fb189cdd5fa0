# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: d1_customer_funnel_detailed
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: BA Hub stats

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT res_id, SUM(total_items) as total_menu_items, SUM(items_with_image) as items_with_menu_image, 
  SUM(items_with_description) as items_with_menu_description, SUM(packaging_charges) as total_packaging_charges,
  SUM(bill_subtotal) as total_bill_subtotal, SUM(discount_orders) as discount_orders, SUM(total_orders) as total_orders,
  SUM(menu_opens) as menu_opens, SUM(cart_builts) as cart_created
  FROM {{.table}}
  WHER<PERSON> "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
  AND res_id IN ({{.res_id}})
  GROUP BY res_id
filters:
  - res_id
  - start_time
  - end_time
