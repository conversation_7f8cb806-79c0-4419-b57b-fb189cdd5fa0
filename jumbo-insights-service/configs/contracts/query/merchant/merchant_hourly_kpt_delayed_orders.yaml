# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: hourly_kpt_delayed_orders
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Get hourly kpt delayed orders at res level
# TTL for cache, Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 120
# Request per second
rate_limit: 20
# Dimensions
columns:
  - name: merchant_order_created_ist_hour
  - name: total_orders
    func: count
    source_column: "1"
# Group by columns
aggregations:
  - merchant_order_created_ist_hour
filters:
  - merchant_order_res_id
  - logistics_partner_id
  - merchant_order_kpt_delay_secs
