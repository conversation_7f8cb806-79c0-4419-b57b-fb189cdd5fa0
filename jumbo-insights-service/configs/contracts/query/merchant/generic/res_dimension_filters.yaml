# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: restaurant_dimension_mappings
identifier: res_dimension_filters
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query to filter out required entities based on input filter values

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
  CASE
      WHEN {{.col_id}} = 1 THEN res_id
      WHEN {{.col_id}} = 2 THEN chain_id
      WHEN {{.col_id}} = 3 THEN brand_id
      WHEN {{.col_id}} = 4 THEN subzone_id
      WHEN {{.col_id}} = 5 THEN zone_id 
      WHEN {{.col_id}} = 6 THEN city_id
      WHEN {{.col_id}} = 7 THEN merchant_id
      WHEN {{.col_id}} = 8 THEN bucket_id
      ELSE 'NA'
  END AS entity_id,
  CASE
      WHEN {{.col_id}} = 1 THEN res_name
      WHEN {{.col_id}} = 2 THEN chain_name
      WHEN {{.col_id}} = 3 THEN brand_name
      WHEN {{.col_id}} = 4 THEN CONCAT(subzone_name, zone_name, ', ')
      WHEN {{.col_id}} = 5 THEN zone_name
      WHEN {{.col_id}} = 6 THEN city_name
      WHEN {{.col_id}} = 7 THEN merchant_name
      WHEN {{.col_id}} = 8 THEN bucket_name
      ELSE 'NA'
  END AS entity_name,
  COUNT(*) AS entity_count
  FROM restaurant_dimension_mappings 
  WHERE ( 
          CASE WHEN {{.filter_res_ids}} = 1 THEN res_id IN ({{.res_id_array}}) ELSE TRUE END
        ) AND ( 
          CASE WHEN {{.filter_chain_ids}} = 1 THEN chain_id IN ({{.chain_id_array}}) ELSE TRUE END
        ) AND (
          CASE WHEN {{.filter_brand_ids}} = 1 THEN brand_id IN ({{.brand_id_array}}) ELSE TRUE END
        ) AND (
          CASE WHEN {{.filter_merchant_ids}} = 1 THEN merchant_id IN ({{.merchant_id_array}}) ELSE TRUE END
        ) AND (
          CASE WHEN {{.filter_city_ids}} = 1 THEN city_id IN ({{.city_id_array}}) ELSE TRUE END
        ) AND (
          CASE WHEN {{.filter_zone_ids}} = 1 THEN zone_id IN ({{.zone_id_array}}) ELSE TRUE END
        ) AND (
          CASE WHEN {{.filter_subzone_ids}} = 1 THEN subzone_id IN ({{.subzone_id_array}}) ELSE TRUE END
        ) AND (
          CASE WHEN {{.filter_bucket_ids}} = 1 THEN bucket_id IN ({{.bucket_id_array}}) ELSE TRUE END
        )

  GROUP BY 1,2
    
filters:
  - col_id
  - filter_res_ids
  - res_id_array
  - filter_chain_ids
  - chain_id_array
  - filter_brand_ids
  - brand_id_array
  - filter_merchant_ids
  - merchant_id_array
  - filter_city_ids
  - city_id_array
  - filter_zone_ids
  - zone_id_array
  - filter_subzone_ids
  - subzone_id_array
  - filter_bucket_ids
  - bucket_id_array
