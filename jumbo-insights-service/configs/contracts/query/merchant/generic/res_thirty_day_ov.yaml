# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: res_thirty_day_ov
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query to get last 30 day orders and sales of a res

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
  consumer_order_res_id AS res_id,
  COUNT(consumer_order_id) AS total_orders, 
  SUM(
    CASE
        WHEN merchant_order_state = 'COMPLETED' THEN 1
        ELSE 0
    END
  ) AS delivered_orders,
  SUM(merchant_order_total_cost) AS total_sales

  FROM composite_order_events
  WHERE consumer_order_res_id in ({{.res_ids}})
  AND merchant_order_created_ist_dt > {{.start_date_in_yyyy_mm_dd}}
  AND consumer_order_delivery_mode = 'DELIVERY'

  GROUP BY 1
    
filters:
  - res_ids
  - start_date_in_yyyy_mm_dd