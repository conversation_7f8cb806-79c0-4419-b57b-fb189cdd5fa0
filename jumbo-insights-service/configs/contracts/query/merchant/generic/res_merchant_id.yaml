# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: restaurant_dimension_mappings
identifier: res_merchant_id
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query merchant_id of restaurant. data is t-1 days stale

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    res_id, 
    res_name, 
    merchant_id, 
    merchant_name,
    chain_id,
    chain_name,
    supply_brand_id,
    supply_brand_name,
    subzone_id,
    subzone_name,
    zone_id,
    zone_name,
    city_id,
    city_name,    
    city_cluster
  FROM {{.table}}
  WHERE res_id IN ({{.res_id_array}})
    
filters:
  - res_id_array