# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: mx_res_stats
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query to get res level stats for given date range

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    res_id,
    SUM(total_orders) AS total_orders,
    SUM(delivered_orders) AS delivered_orders

  FROM {{.table}}
  WHERE "timestamp" BETWEEN {{.start_timestamp}} AND {{.end_timestamp}}
  AND res_id in ({{.res_ids}})

  GROUP BY res_id
    
filters:
  - res_ids
  - start_timestamp
  - end_timestamp