# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: composite_order_events_last_updated
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query to get last updated timestamp of composite-order-events tbl

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 20
# contract type
type: sql
sql: >
  SELECT
    FLOOR(MAX(ingestion_time)/1000000000) AS ingestion_time
  FROM composite_order_events
  WHERE merchant_order_created_at > now()/1000 - 86400
  AND consumer_order_delivery_mode IN ('DELIVERY')



