# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: res_unbundling_stats
identifier: unbundling_stats
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query to display stats while upgrading/downgrading via unbundling distance module

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
  data_type,
  distance_bucket, bucket_type,
  delivered_orders AS orders,
  commission_charged*100.0/commissionable_amount AS service_fee,
  your_earnings

  FROM res_unbundling_stats
  WHERE res_id = {{.res_id}}

filters:
  - res_id
