# can be pinot/trino
query_backend: trino
tenant: Zomato
catalog: hive
schema: mx_etls
table: mx_order_history
identifier: mx_order_history_download_v4
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchant-api-gateway
  pd_service_name: merchant_platform
  description: Merchant order history download V4 with ordering
# Time in ms
sla: 60000
# Request per second
rate_limit: 10
caching_ttl: 300
refresh_interval: 150
# contract type
type: sql
sql: >
  SELECT res_id AS "Restaurant ID",
        res_name AS "Restaurant name",
        subzone AS "Subzone",
        city AS "City",
        tab_id AS "Order ID",
        order_placed_at AS "Order Placed At",
        status AS "Order Status",
        order_type AS "Delivery",
        display_drop_distance AS "Distance",
        item_names AS "Items in order",
        instructions AS "Instructions",
        discount_construct AS "Discount construct",
        CAST(bill_subtotal AS DOUBLE) AS "Bill subtotal",
        CAST(packaging_charges AS DOUBLE) AS "Packaging charges",
        CAST(mvd AS DOUBLE) AS "Restaurant discount (Promo)",
        CAST(IF(CAST(merchant_salt_discount AS DOUBLE) > 0, merchant_salt_discount, salt_discount) AS DOUBLE) AS "Restaurant discount (Flat offs, Freebies & others)",
        CAST(merchant_gold_discount AS DOUBLE) AS "Gold discount",
        CAST(merchant_brand_pack_discount AS DOUBLE) AS "Brand pack discount",
        CAST(merchant_total_amount AS DOUBLE) AS "Total",
        rating as "Rating",
        review_text AS "Review",
        reject_message_name AS "Cancellation / Rejection reason",
        compensation_amount AS "Restaurant compensation (Cancellation)",
        penalty_amount AS "Restaurant penalty (Rejection)",
        kpt AS "KPT duration (minutes)",
        rider_wait_time AS "Rider wait time (minutes)",
        for_status AS "Order Ready Marked",
        complaint_reason AS "Customer complaint tag",
        customer_id AS "Customer ID"
  FROM {{.table}}
  WHERE res_id IN ({{.res_id_array}})
    AND dt >= {{.start_date}}
    AND dt <= {{.end_date}}
  ORDER BY res_id,created_at DESC

filters:
  - res_id_array
  - start_date
  - end_date
decryption:
  - sql_column: Customer Phone number
    source_column: zomato4.number_verification
