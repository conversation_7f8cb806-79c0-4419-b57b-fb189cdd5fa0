# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: uptime_pack_bhds
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: calculation of uptime percentage for res_id BHDS

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    CASE
      WHEN {{.agg_enum}} = 1 THEN DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:HH',
        '1:HOURS'
      )
      WHEN {{.agg_enum}} = 2 THEN DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:dd',
        '1:DAYS'
      )
      WHEN {{.agg_enum}} = 3 THEN DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:EEE',
        '1:DAYS'
      )
      WHEN {{.agg_enum}} = 4 THEN  DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:yyyy-MM',
        '1:DAYS'
      )
      ELSE 'agg'
    END AS agg_level_str,
    res_id as resid,
    SUM(uptime_visible_sessions)*100.00/SUM(uptime_logs_sessions) AS uptime,
    min("timestamp") AS agg_level
  FROM 
    ba_hub_daily_stats
  WHERE
    res_id in ({{.res_ids}})
    AND "timestamp" BETWEEN {{.start_timestamp_millis}} AND {{.end_timestamp_millis}} 
  GROUP BY agg_level_str,  res_id

filters:
  - agg_enum
  - res_ids
  - start_timestamp_millis
  - end_timestamp_millis
  
