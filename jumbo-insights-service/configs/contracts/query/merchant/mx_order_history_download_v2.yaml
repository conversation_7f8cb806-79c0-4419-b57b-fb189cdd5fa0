# can be pinot/trino
query_backend: trino
tenant: Zomato
catalog: hive
schema: jumbo_derived
table: mx_order_history_v2
identifier: mx_order_history_download_v2
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchant-api-gateway
  pd_service_name: merchant_platform
  description: Merchant order history download V2
# Time in ms
sla: 60000
# Request per second
rate_limit: 10
caching_ttl: 300
refresh_interval: 150
# contract type
type: sql
sql: >
  SELECT res_id AS "Restaurant ID",
        res_name AS "Restaurant name",
        subzone AS "Subzone",
        city AS "City",
        tab_id AS "Order ID",
        order_placed_at AS "Order Placed At",
        status AS "Order Status",
        order_type AS "Delivery",
        item_names AS "Items in order",
        instructions AS "Instructions",
        promo_code AS "Discount construct",
        bill_subtotal AS "Bill subtotal",
        packaging_charges AS "Packaging charges",
        mvd AS "Restaurant discount (Promo)",
        salt_discount AS "Restaurant discount (Flat offs, Freebies & others)",
        gold_discount AS "Gold discount",
        merchant_total_amount AS "Total",
        rating as "Rating",
        review_text AS "Review",
        reject_message_name AS "Cancellation / Rejection reason",
        compensation_amount AS "Restaurant compensation (Cancellation)",
        penalty_amount AS "Restaurant penalty (Rejection)",
        kpt AS "KPT duration (minutes)",
        rider_wait_time AS "Rider wait time (minutes)",
        for_status AS "Order Ready Marked",
        complaint_reason AS "Customer complaint tag",
        customer_id AS "Customer ID"
  FROM {{.table}}
  WHERE res_id IN ({{.res_id_array}})
    AND dt >= {{.start_date}}
    AND dt <= {{.end_date}}

filters:
  - res_id_array
  - start_date
  - end_date
decryption:
  - sql_column: Customer Phone number
    source_column: zomato4.number_verification
