# can be pinot/trino
query_backend: trino
tenant: Zomato
catalog: zomato
schema: jumbo_derived
table: mx_order_history
identifier: mx_order_history_download
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchant-api-gateway
  pd_service_name: merchant_platform
  description: Merchant order history download
# Time in ms
sla: 60000
# Request per second
rate_limit: 10
caching_ttl: 300
refresh_interval: 150
# contract type
type: sql
sql: >
  SELECT res_id AS "Restaurant ID",
        res_name AS "Restaurant Name",
        city AS "City",
        tab_id AS "Order ID",
        order_placed_at AS "Order Placed At",
        status AS "Order Status",
        order_type AS "Order Type",
        payment_method AS "Payment Method",
        total_amount AS "Bill Amount",
        rejection_details AS "Rejection Details",
        for_status AS "Order Ready Marked",
        rating AS "Rating",
        customer_id AS "Customer ID",
        customer_phone_number AS "Customer Phone number"
  FROM {{.table}}
  WHERE res_id IN ({{.res_id_array}})
    AND dt >= {{.start_date}}
    AND dt <= {{.end_date}}

filters:
  - res_id_array
  - start_date
  - end_date
decryption:
  - sql_column: Customer Phone number
    source_column: zomato4.number_verification
