query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: gm_portfolio_detailed
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: BA detailed portfolio over different time periods(quarterly, weekly, monthly, daily) - Multiple metrics for BA between start time and end time for a bunch of resIds

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    CASE 
    WHEN {{.agg_level_enum}} = 1
         THEN concat('year', ist_year, '_') 
    WHEN {{.agg_level_enum}} = 2
         THEN concat('year', ist_year, '_') 
    WHEN {{.agg_level_enum}} = 3
         THEN concat('year', ist_year, '_')  
    WHEN {{.agg_level_enum}} = 4
         THEN concat(concat('year', ist_year, '_'), 
                     CASE WHEN ist_month < 10 
                               THEN concat('0', ist_month, '')
                          ELSE ist_month
                     END,
                     '_')
    ELSE 'NULL'     
    END as parent_aggregation_type,

    CASE 
    WHEN {{.agg_level_enum}} = 1
         THEN concat('quarter', ist_quarter, '_') 
    WHEN {{.agg_level_enum}} = 2
         THEN concat('month', 
                     CASE WHEN ist_month < 10 
                               THEN concat('0', ist_month, '')
                          ELSE ist_month
                     END,
                     '_')
    WHEN {{.agg_level_enum}} = 3
         THEN concat('week',
                     CASE WHEN ist_isoweek < 10 
                               THEN concat('0', ist_isoweek, '')
                          ELSE ist_isoweek
                     END,     
                     '_') 
    WHEN {{.agg_level_enum}} = 4
         THEN concat('day', 
                     CASE WHEN ist_day < 10 
                               THEN concat('0', ist_day, '')
                          ELSE ist_day
                     END,
                     '_')
    ELSE 'NULL'
    END as aggregation_type,

    CASE 
    WHEN {{.agg_level_enum}} = 1 and ist_quarter = 1
         THEN 'Jan - Mar'
    WHEN {{.agg_level_enum}} = 1 and ist_quarter = 2
         THEN 'Apr - Jun'
    WHEN {{.agg_level_enum}} = 1 and ist_quarter = 3
         THEN 'Jul - Sep'
    WHEN {{.agg_level_enum}} = 1 and ist_quarter = 4
         THEN 'Oct - Dec'     
    WHEN {{.agg_level_enum}} = 2 and ist_month = 1
         THEN concat('Jan', ist_year, ' ')
    WHEN {{.agg_level_enum}} = 2 and ist_month = 2
         THEN concat('Feb', ist_year, ' ')
    WHEN {{.agg_level_enum}} = 2 and ist_month = 3
         THEN concat('Mar', ist_year, ' ')     
    WHEN {{.agg_level_enum}} = 2 and ist_month = 4
         THEN concat('Apr', ist_year, ' ')     
    WHEN {{.agg_level_enum}} = 2 and ist_month = 5
         THEN concat('May', ist_year, ' ')     
    WHEN {{.agg_level_enum}} = 2 and ist_month = 6
         THEN concat('Jun', ist_year, ' ')  
    WHEN {{.agg_level_enum}} = 2 and ist_month = 7
         THEN concat('Jul', ist_year, ' ')  
    WHEN {{.agg_level_enum}} = 2 and ist_month = 8
         THEN concat('Aug', ist_year, ' ')  
    WHEN {{.agg_level_enum}} = 2 and ist_month = 9
         THEN concat('Sep', ist_year, ' ')  
    WHEN {{.agg_level_enum}} = 2 and ist_month = 10
         THEN concat('Oct', ist_year, ' ')  
    WHEN {{.agg_level_enum}} = 2 and ist_month = 11
         THEN concat('Nov', ist_year, ' ')  
    WHEN {{.agg_level_enum}} = 2 and ist_month = 12
         THEN concat('Dec', ist_year, ' ')  
    WHEN {{.agg_level_enum}} = 3 
         THEN concat('Week', 
                    CASE
                    WHEN ist_isoweek < 10
                         THEN concat('0', ist_isoweek, '')
                    ELSE ist_isoweek 
                    END, 
                     ' ')
    WHEN {{.agg_level_enum}} = 4
         THEN concat(CASE
                     WHEN ist_day < 10
                          THEN concat('0', ist_day, '')
                     ELSE ist_day 
                     END,  
                     CASE 
                     WHEN ist_month = 1
                          THEN 'Jan'
                     WHEN ist_month = 2
                          THEN 'Feb'
                     WHEN ist_month = 3
                          THEN 'Mar'
                     WHEN ist_month = 4
                          THEN 'Apr'
                     WHEN ist_month = 5
                          THEN 'May'
                     WHEN ist_month = 6
                          THEN 'Jun'
                     WHEN ist_month = 7
                          THEN 'Jul'
                     WHEN ist_month = 8
                          THEN 'Aug'
                     WHEN ist_month = 9
                          THEN 'Sep'
                     WHEN ist_month = 10
                          THEN 'Oct'
                     WHEN ist_month = 11
                          THEN 'Nov'
                     WHEN ist_month = 12
                          THEN 'Dec'
                     ELSE 'NULL'
                     END,
                     ' ')
    ELSE 'NULL'     
    END as aggregation_type_string,

    CASE 
    WHEN {{.agg_level_enum}} = 1 AND ist_day_of_quarter <= {{.no_of_days}}
         THEN {{.no_of_days}}
    WHEN {{.agg_level_enum}} = 2 AND ist_day <= {{.no_of_days}}
         THEN {{.no_of_days}}
    WHEN {{.agg_level_enum}} = 3 AND ist_day_of_week <= {{.no_of_days}}
         THEN {{.no_of_days}}
    WHEN {{.agg_level_enum}} = 4
         THEN {{.no_of_days}}
    ELSE 0
    END as no_of_days,
    SUM(gmv) as gov,
    SUM(pnl_table_order_count) as orders,
    SUM(impressions) as impressions,
    SUM(menu_opens) as menu_opens,
    SUM(menu_opens)*100.00
    /SUM(case when impressions = 0
                  then menu_opens
              else impressions end) as i2m,
    SUM(cart_builts) as cart_builts,
    SUM(cart_builts)*100.00
    /SUM(case when cart_builts > menu_opens
                  then cart_builts 
              else menu_opens end) as m2c,
    SUM(pnl_table_order_count)*100.00
    /SUM(case when pnl_table_order_count > cart_builts
                  then pnl_table_order_count 
              else cart_builts end) as c2o,
    SUM(gmv)/SUM(pnl_table_order_count) as aov,
    SUM(commission_revenue) as commission_revenue,
    SUM(commission_revenue)*100.00/SUM(commissionable_value) as tr_percent,
    SUM(commissionable_value)/SUM(pnl_table_order_count) as acv,
    SUM(ad_rev) as ad_rev,
    SUM(ad_rev)*100.00/SUM(commissionable_value) as ad_by_cv,
    SUM(case when ad_rev>0 
                  then 1
            else 0
        end)*100.00/count(res_id) as ad_penetration_percentage,
    SUM(ads_cv)/SUM(billed_revenue) as ad_roi,
    SUM(zvd_final) as promo_cost,
    SUM(zvd_final)/SUM(pnl_table_order_count) as zvdo,
    SUM(zvd_final)/SUM(merchant_voucher_discount) as zvd_by_mvd,
    SUM(mx_attr_refund_amount) + SUM(mx_refund) - SUM(mx_recoup) - SUM(mx_rejection_penalty) as customer_refund_cost,
    (SUM(supply_mx_sent_refund) + SUM(supply_mx_attr_non_refund))*100.00/SUM(total_orders) as orders_requiring_support,
    SUM(ad_rev) + SUM(commission_revenue) + SUM(mx_recoup) + SUM(mx_rejection_penalty) - SUM(zvd_final) - SUM(mx_attr_refund_amount) - SUM(mx_refund) as gm_cm,
    SUM(actual_visibility)*100.00/SUM(expected_visibility) as visibility,
    SUM(composite_mx_lost_sales) as lost_sales,
    SUM(billed_leads)*100.00/SUM(total_target_leads) as ad_delivery_percentage,
    SUM(commission_revenue)/SUM(pnl_table_order_count) as commission_revenue_per_order,
    SUM(ad_rev)/SUM(pnl_table_order_count) as ad_revenue_per_order,
    (SUM(mx_attr_refund_amount) + SUM(mx_refund) - SUM(mx_recoup) - SUM(mx_rejection_penalty))/SUM(pnl_table_order_count) as refund_cost_per_order,
    (
        SUM(ad_rev) + SUM(commission_revenue) + SUM(mx_recoup) + SUM(mx_rejection_penalty)
      - SUM(zvd_final) - SUM(mx_attr_refund_amount) - SUM(mx_refund)
    )/SUM(pnl_table_order_count) as contribution_margin_per_order,
    SUM(sl_pool)*1.0000/SUM(op) as salience_loss,
    (
     sum(ad_rev) + sum(commission_revenue) + sum(mx_recoup) + sum(dc_realized) 
   + sum(mx_rejection_penalty) + sum(mor)
   - sum(zvd_final) - sum(cx_refund) - sum(mx_refund) - sum(logs_cost)
   - sum(transaction_cost) - sum(marketing_cost) - sum(servers_and_tech_cost) - sum(overheads_cost)
   - sum(rider_onboarding_cost)
   - sum(otof_amount)
    ) as net_cm,
    (
     sum(ad_rev) + sum(commission_revenue) + sum(mx_recoup) + sum(dc_realized) 
   + sum(mx_rejection_penalty) + sum(mor)
   - sum(zvd_final) - sum(cx_refund) - sum(mx_refund) - sum(logs_cost)
   - sum(transaction_cost) - sum(marketing_cost) - sum(servers_and_tech_cost) - sum(overheads_cost)
   - sum(rider_onboarding_cost)
   - sum(otof_amount)
    )/sum(pnl_table_order_count) as net_cm_per_order,
    SUM(mor) + SUM(dc_realized) as other_revenue,
    SUM(logs_cost) + (SUM(cx_refund) - SUM(mx_attr_refund_amount)) + SUM(transaction_cost) + sum(marketing_cost) + sum(servers_and_tech_cost) + sum(overheads_cost) + sum(rider_onboarding_cost) as other_costs,
    SUM(bad_orders)*100.00/SUM(total_orders) as bad_orders,
    SUM(user_paid_amount) as user_paid_amount
    FROM {{.table}}
    WHERE "timestamp" 
    BETWEEN {{.start_time}} AND {{.end_time}}
    AND res_id in ({{.res_id}})
    AND no_of_days = {{.no_of_days}}
    AND ist_day_of_quarter > 0
    AND ist_day_of_week > 0
    GROUP BY parent_aggregation_type, aggregation_type, aggregation_type_string, no_of_days
    ORDER BY parent_aggregation_type, aggregation_type, aggregation_type_string
filters:
  - start_time
  - end_time
  - res_id
  - agg_level_enum
  - no_of_days
