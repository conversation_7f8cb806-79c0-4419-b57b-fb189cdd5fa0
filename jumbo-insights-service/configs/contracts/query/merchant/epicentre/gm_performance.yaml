query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: gm_performance
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: BA Monthly Performance - GOV and CM for BA between start time and end time for a bunch of resIds

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
  CONCAT('quarter', ist_quarter, '_') as aggregation_type,
  SUM(gmv) as gov, 
  SUM(ad_rev) + SUM(commission_revenue) + SUM(mx_recoup) + SUM(mx_rejection_penalty) - SUM(zvd_final) - SUM(mx_attr_refund_amount) - SUM(mx_refund) as gm_cm
  FROM {{.table}}
  WHERE "timestamp" 
  BETWEEN {{.start_time}} AND {{.end_time}}
  AND res_id in ({{.res_id}})
  AND ist_day_of_quarter <= {{.ist_day_of_quarter}}
  GROUP BY aggregation_type
filters:
  - start_time
  - end_time
  - res_id
  - ist_day_of_quarter
