query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: gm_portfolio
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: BA Weekly Portfolio - Multiple metrics for BA between start time and end time for a bunch of resIds

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
  CONCAT('week', ist_isoweek, '_') as aggregation_type,
  SUM(gmv) as gov,
  SUM(ad_rev) + SUM(commission_revenue) + SUM(mx_recoup) + SUM(mx_rejection_penalty) - SUM(zvd_final) - SUM(mx_attr_refund_amount) - SUM(mx_refund) as gm_cm,
  SUM(commission_revenue) as commission_revenue,
  <PERSON>UM(ad_rev) as ad_rev,
  SUM(zvd_final) as promo_cost,
  SUM(mx_attr_refund_amount) + SUM(mx_refund) - SUM(mx_recoup) - SUM(mx_rejection_penalty) as refund_cost
  FROM {{.table}}
  WHERE "timestamp" 
  BETWEEN {{.start_time}} AND {{.end_time}}
  AND res_id in ({{.res_id}})
  AND ist_day_of_week <= {{.ist_day_of_week}}
  GROUP BY aggregation_type
filters:
  - start_time
  - end_time
  - res_id
  - ist_day_of_week
