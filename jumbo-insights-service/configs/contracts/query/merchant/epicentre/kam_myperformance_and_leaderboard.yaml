query_backend: pinot
tenant: Zomato
table: gm_daily_stats
identifier: kam_myperformance_and_leaderboard
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: epicentre - my performance and leaderboard widget query

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
  am_id, comparison_logic, 
  am_type AS city_cluster,
  metric AS metric_name, 
  metric_value, baseline_metric_value, baseline_mtd_metric_value, am_rank,
  CASE
      WHEN card_name = 'LEADERBOARD' THEN metric_value
      WHEN card_name = 'MY_PERFORMANCE' THEN metric_value - baseline_mtd_metric_value
      ELSE 0
  END AS growth,
  CASE
      WHEN card_name = 'MY_PERFORMANCE' THEN (((metric_value - baseline_mtd_metric_value)/baseline_mtd_metric_value)) * 100.0
      ELSE 0
  END AS growth_percentage,
  kam_incentive

  FROM {{.table}}
  WHERE dt = 'fixed'
  AND agg_type = 'month'
  AND card_name = {{.card_name}}
  AND metric IN ({{.metric_name}})
  AND CASE
          WHEN card_name = 'LEADERBOARD' THEN secondary_input_key = {{.kam_id}}
          WHEN card_name = 'MY_PERFORMANCE' THEN am_id = {{.kam_id}}
          ELSE false
      END

filters:
  - card_name
  - metric_name
  - kam_id
