query_backend: pinot
tenant: Zomato
table: gm_daily_stats
identifier: gm_leaderboard
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: GM Leaderboard - Top 3 GMs in terms of incremental commission revenue for a city cluster

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
  am_id,
  LASTWITHTIME(am_type, "timestamp", 'STRING') as am_type,
  LASTWITHTIME(am_rank, "timestamp", 'INT') as am_rank_updated,
  LASTWITHTIME(metric_value, "timestamp", 'INT') as metric_value
  FROM {{.table}}
  WHERE "timestamp" >= {{.start_time}} 
  AND agg_type = {{.agg_type}}
  AND metric = {{.metric}}
  AND secondary_input_key = {{.am_id}}
  AND dt = 'fixed' 
  GROUP BY am_id
  ORDER BY am_rank_updated ASC
filters:
  - start_time
  - agg_type
  - metric
  - am_id
  
