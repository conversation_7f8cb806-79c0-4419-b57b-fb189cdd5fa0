query_backend: pinot
tenant: Zomato
table: mx_epicentre_stats
identifier: query_epicentre_leaderboard_detailed
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query for epicentre leaderboard detailed page

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
  entity_id AS kam_id,
  city_cluster,
  city_tier,
  comparison_logic,
  current_rank,
  CASE
      WHEN metric_name = 'bad_orders' THEN current_value
      ELSE (current_value - prev_value)*100.0/prev_value 
  END AS percentage_growth

  FROM {{.table}}
  WHERE query_key = {{.entity_id}}
  AND entity_type = {{.entity_type}}
  AND card_type = {{.card_type}}
  AND agg_type = {{.agg_type}}
  AND metric_name = {{.metric_name}}
  AND city_tier = {{.city_tier}}
filters:
  - entity_id
  - entity_type
  - card_type
  - agg_type
  - metric_name
  - city_tier

  
