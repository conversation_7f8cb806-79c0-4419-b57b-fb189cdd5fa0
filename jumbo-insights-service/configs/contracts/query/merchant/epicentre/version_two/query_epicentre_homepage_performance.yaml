query_backend: pinot
tenant: Zomato
table: mx_epicentre_stats
identifier: query_epicentre_homepage_performance
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query for epicentre homepage your performance

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
  entity_id AS kam_id,
  metric_name AS metric,
  current_value,
  prev_value,
  (current_value - prev_value)*100.0/prev_value AS value_delta,
  current_rank,
  prev_rank,
  CASE
      WHEN prev_rank = 0 THEN 0
      ELSE prev_rank - current_rank 
  END AS rank_delta,
  comparison_logic,
  comparison_logic_start_unixtime,
  comparison_logic_end_unixtime

  FROM {{.table}}
  WHERE query_key = {{.entity_id}}
  AND card_type = {{.card_type}}
  AND agg_type = {{.agg_type}}
  AND entity_type = {{.entity_type}}
filters:
  - entity_id
  - card_type
  - agg_type
  - entity_type

  
