query_backend: pinot
tenant: Zomato
table: mx_epicentre_stats
identifier: query_epicentre_res_card_info
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query for epicentre res cards

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
  entity_id AS res_id,
  metric_name AS metric,
  current_value,
  prev_value,
  (current_value - prev_value)*100.0/prev_value AS value_delta,
  current_rank AS sort_value,
  comparison_logic

  FROM {{.table}}
  WHERE query_key = {{.entity_id}}
  AND card_type = {{.card_type}}
  AND agg_type = {{.agg_type}}
  AND entity_type = {{.entity_type}}
filters:
  - entity_id
  - card_type
  - agg_type
  - entity_type

  
