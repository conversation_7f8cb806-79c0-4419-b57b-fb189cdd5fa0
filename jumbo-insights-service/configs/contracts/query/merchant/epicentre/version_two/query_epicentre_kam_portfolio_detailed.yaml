query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: kam_portfolio_detailed
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: KAM detailed portfolio over different time periods(quarterly, weekly, monthly, daily) - Multiple metrics for KAM between start time and end time for a bunch of resIds

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    CASE 
    WHEN {{.agg_level_enum}} = 1
         THEN concat('year', ist_year, '_') 
    WHEN {{.agg_level_enum}} = 2
         THEN concat('year', ist_year, '_') 
    WHEN {{.agg_level_enum}} = 3
         THEN concat('year', ist_year, '_')  
    WHEN {{.agg_level_enum}} = 4
         THEN concat(concat('year', ist_year, '_'), 
                     CASE WHEN ist_month < 10 
                               THEN concat('0', ist_month, '')
                          ELSE ist_month
                     END,
                     '_')
    ELSE 'NULL'     
    END as parent_aggregation_type,

    CASE 
    WHEN {{.agg_level_enum}} = 1
         THEN concat('quarter', ist_quarter, '_') 
    WHEN {{.agg_level_enum}} = 2
         THEN concat('month', 
                     CASE WHEN ist_month < 10 
                               THEN concat('0', ist_month, '')
                          ELSE ist_month
                     END,
                     '_')
    WHEN {{.agg_level_enum}} = 3
         THEN concat('week',
                     CASE WHEN ist_isoweek < 10 
                               THEN concat('0', ist_isoweek, '')
                          ELSE ist_isoweek
                     END,     
                     '_') 
    WHEN {{.agg_level_enum}} = 4
         THEN concat('day', 
                     CASE WHEN ist_day < 10 
                               THEN concat('0', ist_day, '')
                          ELSE ist_day
                     END,
                     '_')
    ELSE 'NULL'
    END as aggregation_type,

    CASE 
    WHEN {{.agg_level_enum}} = 1 and ist_quarter = 1
         THEN 'Jan - Mar'
    WHEN {{.agg_level_enum}} = 1 and ist_quarter = 2
         THEN 'Apr - Jun'
    WHEN {{.agg_level_enum}} = 1 and ist_quarter = 3
         THEN 'Jul - Sep'
    WHEN {{.agg_level_enum}} = 1 and ist_quarter = 4
         THEN 'Oct - Dec'     
    WHEN {{.agg_level_enum}} = 2 and ist_month = 1
         THEN concat('Jan', ist_year, ' ')
    WHEN {{.agg_level_enum}} = 2 and ist_month = 2
         THEN concat('Feb', ist_year, ' ')
    WHEN {{.agg_level_enum}} = 2 and ist_month = 3
         THEN concat('Mar', ist_year, ' ')     
    WHEN {{.agg_level_enum}} = 2 and ist_month = 4
         THEN concat('Apr', ist_year, ' ')     
    WHEN {{.agg_level_enum}} = 2 and ist_month = 5
         THEN concat('May', ist_year, ' ')     
    WHEN {{.agg_level_enum}} = 2 and ist_month = 6
         THEN concat('Jun', ist_year, ' ')  
    WHEN {{.agg_level_enum}} = 2 and ist_month = 7
         THEN concat('Jul', ist_year, ' ')  
    WHEN {{.agg_level_enum}} = 2 and ist_month = 8
         THEN concat('Aug', ist_year, ' ')  
    WHEN {{.agg_level_enum}} = 2 and ist_month = 9
         THEN concat('Sep', ist_year, ' ')  
    WHEN {{.agg_level_enum}} = 2 and ist_month = 10
         THEN concat('Oct', ist_year, ' ')  
    WHEN {{.agg_level_enum}} = 2 and ist_month = 11
         THEN concat('Nov', ist_year, ' ')  
    WHEN {{.agg_level_enum}} = 2 and ist_month = 12
         THEN concat('Dec', ist_year, ' ')  
    WHEN {{.agg_level_enum}} = 3 
         THEN concat('Week', 
                    CASE
                    WHEN ist_isoweek < 10
                         THEN concat('0', ist_isoweek, '')
                    ELSE ist_isoweek 
                    END, 
                     ' ')
    WHEN {{.agg_level_enum}} = 4
         THEN concat(CASE
                     WHEN ist_day < 10
                          THEN concat('0', ist_day, '')
                     ELSE ist_day 
                     END,  
                     CASE 
                     WHEN ist_month = 1
                          THEN 'Jan'
                     WHEN ist_month = 2
                          THEN 'Feb'
                     WHEN ist_month = 3
                          THEN 'Mar'
                     WHEN ist_month = 4
                          THEN 'Apr'
                     WHEN ist_month = 5
                          THEN 'May'
                     WHEN ist_month = 6
                          THEN 'Jun'
                     WHEN ist_month = 7
                          THEN 'Jul'
                     WHEN ist_month = 8
                          THEN 'Aug'
                     WHEN ist_month = 9
                          THEN 'Sep'
                     WHEN ist_month = 10
                          THEN 'Oct'
                     WHEN ist_month = 11
                          THEN 'Nov'
                     WHEN ist_month = 12
                          THEN 'Dec'
                     ELSE 'NULL'
                     END,
                     ' ')
    ELSE 'NULL'     
    END as aggregation_type_string,

    CASE 
    WHEN {{.agg_level_enum}} = 1 AND ist_day_of_quarter <= {{.no_of_days}}
         THEN {{.no_of_days}}
    WHEN {{.agg_level_enum}} = 2 AND ist_day <= {{.no_of_days}}
         THEN {{.no_of_days}}
    WHEN {{.agg_level_enum}} = 3 AND ist_day_of_week <= {{.no_of_days}}
         THEN {{.no_of_days}}
    WHEN {{.agg_level_enum}} = 4
         THEN {{.no_of_days}}
    ELSE 0
    END as no_of_days,

    SUM(gmv) as gov,
    SUM(delivered_orders) as delivered_orders,
    SUM(pnl_table_order_count) as orders,
    SUM(total_orders) as placed_orders,
    SUM(order_cancellation) as cancelled_orders,
    SUM(gmv)/SUM(delivered_orders) as aov,

    AVG(menu_score) as menu_score,
    SUM(items_with_image)*100.00/SUM(total_items) as items_with_image,
    SUM(items_with_description)*100.00/SUM(total_items) as items_with_description,
    SUM(total_catalogue_with_serving_info)*100.00/SUM(total_items) as items_with_serving_info,

    SUM(menu_opens) as menu_opens,
    SUM(impressions) as impressions,
    SUM(menu_opens)*100.00
    /SUM(case when impressions = 0
                  then menu_opens
              else impressions end) as i2m,
    SUM(cart_builts) as cart_builts,
    SUM(cart_builts)*100.00
    /SUM(case when cart_builts > menu_opens
                  then cart_builts 
              else menu_opens end) as m2c,
    SUM(pnl_table_order_count)*100.00
    /SUM(case when pnl_table_order_count > cart_builts
                  then pnl_table_order_count 
              else cart_builts end) as c2o,
    SUM(pnl_table_order_count)*100.00
    /SUM(case when cart_builts > menu_opens
                  then cart_builts 
              else menu_opens end) as m2o,

    avg(res_delivery_rating_till_date) as average_rating,
    SUM(kitchent_prep_time)*1.00/SUM(kitchent_prep_time_not_null) as kpt_mins,
    SUM(actual_visibility)*100.00/SUM(expected_visibility) as visibility,
    (SUM(expected_visibility) - SUM(actual_visibility))/3600 as offline_time_mins,

    SUM(case when ad_rev>0 
                  then 1
            else 0
        end)*100.00/count(res_id) as ad_penetration_percentage,
    SUM(delivered_ad_impression) as ad_impression,
    SUM(ad_menu_opens)*100.00/SUM(delivered_ad_impression) as ad_ctr,
    SUM(new_users_acquired)*100.00/SUM(new_users) as new_users_from_ads,
    SUM(ad_menu_opens) as ad_mo,
    SUM(ad_orders) as ad_orders,
    SUM(ad_rev) as ad_revenue,
    SUM(billed_revenue)*100.00/SUM(commissionable_value) as ad_by_cv,
    SUM(ads_cv) as sales_from_ads,
    SUM(ads_cv)/SUM(billed_revenue) as ad_roi,
    SUM(billed_revenue) as ad_spend,
    SUM(billed_leads)*100.00/SUM(total_target_leads) as ad_delivery_percentage,

    SUM(packaging_charges)/SUM(pnl_table_order_count) as packaging_charge_per_order,
    SUM(discount)/SUM(discount_orders) as discount_per_order,
    SUM(composite_mx_offer_orders)*100.00/SUM(delivered_orders) as discount_order_percentage,
    SUM(composite_mx_offer_sales) as sales_from_offers,
    SUM(composite_discount_applied_amount) as discount_given,
    SUM(composite_discount_applied_amount)*100.00/SUM(composite_mx_offer_sales) as effective_discount,

    SUM(commission_revenue) as commission_revenue,
    SUM(commission_revenue)*100.00/SUM(commissionable_value) as tr_percentage,
    SUM(commissionable_value) as cv,
    SUM(commissionable_value)/SUM(pnl_table_order_count) as acv,
    SUM(zvd_final) as promo_cost,

    SUM(merchant_voucher_discount)/SUM(mvd_orders) as mvdo,
    SUM(zvd_final)/SUM(pnl_table_order_count) as zvdo,
    SUM(zvd_final)/SUM(merchant_voucher_discount) as zvd_by_mvd,
    SUM(mx_attr_refund_amount) + SUM(mx_refund) - SUM(mx_recoup) - SUM(mx_rejection_penalty) as customer_refund_cost,
    SUM(dst_complaint_orders)*100.00/SUM(pnl_table_order_count) as orders_requiring_support,

    SUM(ad_rev) + SUM(commission_revenue) + SUM(mx_recoup) + SUM(mx_rejection_penalty) - SUM(zvd_final) 
      - SUM(mx_attr_refund_amount) - SUM(mx_refund) as gm_cm,

    SUM(commission_revenue)/SUM(pnl_table_order_count) as commission_revenue_per_order,
    SUM(ad_rev)/SUM(pnl_table_order_count) as ad_revenue_per_order,

    (SUM(mx_attr_refund_amount) + SUM(mx_refund) - SUM(mx_recoup) - SUM(mx_rejection_penalty))
      /SUM(pnl_table_order_count) as refund_cost_per_order,
      
    (
      SUM(ad_rev) + SUM(commission_revenue) + SUM(mx_recoup) + SUM(mx_rejection_penalty)
      - SUM(zvd_final) - SUM(mx_attr_refund_amount) - SUM(mx_refund)
    )/SUM(pnl_table_order_count) as contribution_margin_per_order,

    SUM(sl_pool)*1.0000/SUM(op) as salience_loss,

    (
      SUM(ad_rev) + SUM(commission_revenue) + SUM(mx_recoup) + SUM(dc_realized) 
      + SUM(mx_rejection_penalty) + SUM(mor)
      - SUM(zvd_final) - SUM(cx_refund) - SUM(mx_refund) - SUM(logs_cost)
      - SUM(transaction_cost) - SUM(marketing_cost) - SUM(servers_and_tech_cost) - SUM(overheads_cost)
      - SUM(rider_onboarding_cost)
      - SUM(otof_amount)
    ) as net_cm,

    (
      SUM(ad_rev) + SUM(commission_revenue) + SUM(mx_recoup) + SUM(dc_realized) 
      + SUM(mx_rejection_penalty) + SUM(mor)
      - SUM(zvd_final) - SUM(cx_refund) - SUM(mx_refund) - SUM(logs_cost)
      - SUM(transaction_cost) - SUM(marketing_cost) - SUM(servers_and_tech_cost) - SUM(overheads_cost)
      - SUM(rider_onboarding_cost)
      - SUM(otof_amount)
    )/SUM(pnl_table_order_count) as net_cm_per_order,

    SUM(mor) + SUM(dc_realized) as other_revenue,

    SUM(logs_cost) + (SUM(cx_refund) - SUM(mx_attr_refund_amount)) 
    + SUM(transaction_cost) + SUM(marketing_cost) 
    + SUM(servers_and_tech_cost) + SUM(overheads_cost) + SUM(rider_onboarding_cost) as other_costs,

    SUM(bad_orders)*100.00/SUM(total_orders) as bad_orders_percentage,
    (SUM(supply_mx_sent_refund) + SUM(supply_mx_attr_non_refund))*100.00/SUM(total_orders) as complaint_orders_percentage,
    SUM(rejected_orders)*100.00/SUM(total_orders) as rejected_orders_percentage,
    SUM(kpt_delayed_orders)*100.00/SUM(total_orders) as kpt_delayed_orders_percentage,
    SUM(low_rated_orders)*100.00/SUM(total_orders) as poor_rated_orders_percentage,
    SUM(composite_mx_lost_sales) as lost_sales,
    SUM(supply_mx_sent_refund) + SUM(supply_mx_attr_non_refund) as complaint_orders,
    SUM(supply_mx_sent_refund_pp) + SUM(supply_mx_attr_non_refund_pp) AS poor_packaging_share,
    SUM(supply_mx_sent_refund_pq) + SUM(supply_mx_attr_non_refund_pq) AS poor_quality_share,
    SUM(supply_mx_sent_refund_wo) + SUM(supply_mx_attr_non_refund_wo) AS wrong_item_share,
    SUM(supply_mx_sent_refund_mi) + SUM(supply_mx_attr_non_refund_mi) AS missing_item_share,
    SUM(dst_out_of_stock) AS ioos_share,
    SUM(composite_mx_rejected_orders)*100.0/SUM(total_orders) AS rejections_share,
    SUM(composite_mx_rejected_orders + timed_out_orders) as rejected_and_timedout_orders,
    SUM(mx_nearing_closing_time) as nearing_closing_time,
    SUM(mx_kitchen_is_full) as kitchen_full,
    SUM(mx_restaurant_is_closed) as outlet_closed,
    SUM(timed_out_orders) as timedout_orders,
    SUM(kpt_delayed_orders) as kpt_delayed_orders,
    SUM(kpt_delay_10_to_20_minutes) as kpt_delay_10_to_20_minutes_orders,
    SUM(kpt_delay_20_to_30_minutes) as kpt_delay_20_to_30_minutes_orders,
    SUM(kpt_delay_30_plus_minutes) as kpt_delay_30_plus_minutes_orders,
    SUM(low_rated_orders) as poor_rated_orders,
    SUM(rated_1_orders) as rated_1_orders,
    SUM(rated_2_orders) as rated_2_orders,
    MIN("timestamp") as min_time,
    MAX("timestamp") as max_time,
    SUM(user_paid_amount) as user_paid_amount

    FROM {{.table}}
    WHERE "timestamp" 
    BETWEEN {{.start_timestamp}} AND {{.end_timestamp}}
    AND res_id in ({{.res_id}})
    AND no_of_days = {{.no_of_days}}
    AND ist_day_of_quarter > 0
    AND ist_day_of_week > 0
    GROUP BY parent_aggregation_type, aggregation_type, aggregation_type_string, no_of_days
    ORDER BY parent_aggregation_type, aggregation_type, aggregation_type_string
filters:
  - start_timestamp
  - end_timestamp
  - res_id
  - agg_level_enum
  - no_of_days
