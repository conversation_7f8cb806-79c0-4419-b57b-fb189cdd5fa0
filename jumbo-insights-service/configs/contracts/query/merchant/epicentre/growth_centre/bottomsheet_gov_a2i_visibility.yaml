query_backend: pinot
tenant: Zomato
table: ba_hub_growth_recommendations
identifier: bottomsheet_gov_a2i_visibility
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: EPICENTRE - growth centre - bottomsheet data for a2i visibility

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT 
  metric_display_string AS metric_display_name,
  CONCAT('bottomsheet', metric_name, '_') AS unique_card_identifier,
  metric_name, metric_res, metric_similar_res,
  breakfast, lunch, evening AS snacks, dinner, late_night

  FROM {{.table}}
  WHERE res_id in ({{.res_ids}})
  AND framework_type = 'GOV'
  AND card_identifier = 'A2I'
  AND metric_name = 'visibility'

filters:
  - res_ids
