query_backend: pinot
tenant: Zomato
table: ba_hub_growth_recommendations
identifier: mx_gc_agg_pot_gov_cm
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: EPICENTRE - aggregated GOV and CM for all res mapped to the KAM

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT 
  framework_type,
  (SUM(metric_potential_growth)+SUM(last_30_day_gmv))*100.0/SUM(last_30_day_gmv) - 100.0 AS potential_growth

  FROM {{.table}}
  WHERE res_id in ({{.res_ids}})
  AND card_type = 'HOMEPAGE'
  GROUP BY framework_type
filters:
  - res_ids
