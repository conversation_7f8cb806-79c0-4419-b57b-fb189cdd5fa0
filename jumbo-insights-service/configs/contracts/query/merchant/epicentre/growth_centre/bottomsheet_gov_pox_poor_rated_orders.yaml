query_backend: pinot
tenant: Zomato
table: ba_hub_growth_recommendations
identifier: bottomsheet_gov_pox_poor_rated_orders
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: EPICENTRE - growth centre - bottomsheet data for gov reduce complaints

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT 
  metric_display_string AS metric_display_name,
  CONCAT('bottomsheet', metric_name, '_') AS unique_card_identifier,
  metric_name, metric_res, metric_similar_res,
  rated_orders, reviews AS orders_with_reviews, responses AS orders_with_responses,
  rated_1_orders AS one_stars, 
  rated_2_orders AS two_stars, 
  rated_3_orders AS three_stars, 
  rated_4_orders AS four_stars, 
  rated_5_orders AS five_stars

  FROM {{.table}}
  WHERE res_id in ({{.res_ids}})
  AND framework_type = 'GOV'
  AND card_identifier = 'POX'
  AND metric_name = 'poor_rated_orders'

filters:
  - res_ids