query_backend: pinot
tenant: Zomato
table: ba_hub_growth_recommendations
identifier: bottomsheet_cm_ads_cpc
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: EPICENTRE - growth centre - bottomsheet_gov_a2i_upsell_cpc

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT 
  metric_display_string AS metric_display_name, 
  metric_name, res_id,
  
  CONCAT('bottomsheet', metric_name, '_') AS unique_card_identifier,
  
  upsell_cpc AS current_cpc, 
  upsell_clicks AS current_clicks,  
  upsell_cpc * upsell_clicks AS current_budget, 

  current_budget AS booked_revenue, 
  
  FLOOR(current_clicks) as clicks, 
  current_cpc as cpc, 
  current_ads_by_cv AS ads_by_cv,
  roi, 
  current_delivery AS delivery,
  
  FLOOR(lxc_available_ad_clicks) AS available_clicks, 
  lxc_booked_clicks AS booked_clicks,
  lxc_delivery as delivery_percentage,
  lxc_saturation AS saturation_score,

  CASE
      WHEN metric_display_string IN ('Enable ads') THEN 1
      WHEN metric_display_string IN ('Upsell ads clicks & CPC', 'Upsell ads clicks', 'Upsell ads CPC') THEN 2
      ELSE 0
  END AS metric_display_name_id

  FROM {{.table}}
  WHERE res_id in ({{.res_ids}})
  AND framework_type = 'GOV'
  AND card_identifier = 'A2I'
  AND metric_name = 'ads_cpc'

filters:
  - res_ids
