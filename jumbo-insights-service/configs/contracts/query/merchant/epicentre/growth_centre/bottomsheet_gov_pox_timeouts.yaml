query_backend: pinot
tenant: Zomato
table: ba_hub_growth_recommendations
identifier: bottomsheet_gov_pox_timeouts
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: EPICENTRE - growth centre - bottomsheet data for gov pox rejections and timeouts

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT 
  metric_display_string AS metric_display_name,
  CONCAT('bottomsheet', metric_name, '_') AS unique_card_identifier,
  metric_name, metric_res, metric_similar_res,
  mx_not_operational_today + mx_restaurant_is_closed + mx_restaurant_has_not_opened_yet AS outlet_closed,
  mx_kitchen_is_full AS kitchen_full,
  mx_nearing_closing_time AS nearing_closing_time,
  mx_item_out_of_stock As item_out_of_stock,
  0 as timeouts,
  mx_no_delivery_boys + mx_out_of_subzone_area  + mx_no_answer + mx_content_issue + mx_moq_issue + mx_wrong_res_address + mx_device_issue AS others 

  FROM {{.table}}
  WHERE res_id in ({{.res_ids}})
  AND framework_type = 'GOV'
  AND card_identifier = 'POX'
  AND metric_name = 'rejections_and_timed_out_orders'

filters:
  - res_ids