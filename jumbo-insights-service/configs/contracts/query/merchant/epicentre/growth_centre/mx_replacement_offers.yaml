query_backend: pinot
tenant: Zomato
table: win_cx_from_other_res
identifier: mx_replacement_offers
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: EPICENTRE - growth centre - mx replacement offers data

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT 
  offer_id, 
  res_id,
  customer_segment,
  active_flag,
  max_value,
  max_dish
  orders,
  unique_users,
  total_cost,
  returning_users,
  sales_returning,
  start_date,
  end_date

  FROM {{.table}}
  WHERE res_id in ({{.res_ids}})

filters:
  - res_ids
