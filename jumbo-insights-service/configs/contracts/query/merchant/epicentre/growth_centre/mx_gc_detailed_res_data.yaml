query_backend: pinot
tenant: Zomato
table: ba_hub_growth_recommendations
identifier: mx_gc_detailed_res_data
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: EPICENTRE - aggregated GOV and CM for all res mapped to the KAM

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT 
  res_id, 
  metric_display_string AS metric_display_name,
  metric_name, metric_res, metric_similar_res,
  metric_potential_growth, 
  card_identifier AS unique_card_identifier, 
  potential_growth,
  card_value_res, card_value_similar_res

  FROM {{.table}}
  WHERE res_id in ({{.res_ids}})
  AND framework_type = {{.tab_key}}
  AND card_identifier IN ({{.card_identifier}})
  
filters:
  - res_ids
  - tab_key
  - card_identifier
