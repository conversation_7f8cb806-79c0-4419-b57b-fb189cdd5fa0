query_backend: pinot
tenant: Zomato
table: ba_hub_growth_recommendations
identifier: mx_gc_all_res_tags
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: EPICENTRE - aggregated GOV and CM for all res mapped to the KAM

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT 
  metric_display_string AS metric_display_name,
  res_id, metric_name, metric_potential_growth,
  CASE
      WHEN metric_display_string = 'No Recommendation' THEN 'REMOVE'
      WHEN metric_name IN ('poor_rated_orders', 'rejections_and_timed_out_orders', 'kpt_10_delayed_orders', 'complaint_orders') AND metric_res > metric_similar_res THEN 'KEEP'
      WHEN metric_potential_growth > 0 THEN 'KEEP'
      ELSE 'REMOVE'
  END AS keep_or_remove_row

  FROM {{.table}}
  WHERE res_id in ({{.res_ids}})
  AND framework_type = {{.tab_key}}
  AND metric_name in ({{.tag_keys}})
  AND keep_or_remove_row = 'KEEP'
  AND card_identifier NOT IN ('I_SM', 'I_I2M', 'I_M2C', 'I_C2O', 'I_POX', 'I_A2I', 'I_CM', 'I_GFM', 'I_RM', 'I_AM', 'I_PM')
filters:
  - res_ids
  - tab_key
  - tag_keys
  
