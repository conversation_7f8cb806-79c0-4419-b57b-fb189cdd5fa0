query_backend: pinot
tenant: Zomato
table: ba_hub_growth_recommendations
identifier: bottomsheet_gov_pox_complaints
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: EPICENTRE - growth centre - bottomsheet data for gov reduce complaints

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT 
  metric_display_string AS metric_display_name,
  CONCAT('bottomsheet', metric_name, '_') AS unique_card_identifier,
  metric_name, metric_res, metric_similar_res,
  missing_items, poor_quality, wrong_items, poor_package, order_delayed, poor_quantity, out_of_stock

  FROM {{.table}}
  WHERE res_id in ({{.res_ids}})
  AND framework_type = 'GOV'
  AND card_identifier = 'POX'
  AND metric_name = 'complaint_orders'

filters:
  - res_ids