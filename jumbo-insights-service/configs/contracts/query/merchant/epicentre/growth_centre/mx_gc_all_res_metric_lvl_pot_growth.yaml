query_backend: pinot
tenant: Zomato
table: ba_hub_growth_recommendations
identifier: mx_gc_all_metric_lvl_pot_growth
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: EPICENTRE - aggregated GOV and CM for all res mapped to the KAM

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT 
  res_id,
  SUM(metric_potential_growth) AS agg_potential_growth,
  LASTWITHTIME(last_30_day_gmv, "timestamp", 'DOUBLE') AS current_metric

  FROM {{.table}}
  WHERE res_id in ({{.res_ids}})
  AND framework_type = {{.tab_key}}
  AND metric_name in ({{.metric_name}})
  GROUP BY res_id
filters:
  - res_ids
  - tab_key
  - metric_name
  
