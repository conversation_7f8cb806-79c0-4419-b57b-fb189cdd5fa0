query_backend: pinot
tenant: Zomato
table: ba_hub_growth_recommendations
identifier: bottomsheet_promos_all
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: EPICENTRE - growth centre - all promo bottomsheets

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT 
  metric_display_string AS metric_display_name,
  metric_name, res_id,

  CONCAT('bottomsheet', metric_name, '_') AS unique_card_identifier,
  metric_res, metric_similar_res,

  CASE
      WHEN framework_type = 'GOV' AND base_code_increment_tag = '1' THEN 'Create New Base Promo'
      WHEN framework_type = 'GOV' AND base_code_increment_tag = '2' THEN 'Right Shift Active Base Promo'
      WHEN framework_type = 'CM' AND base_code_increment_tag = '3' THEN 'Left Shift Active Base Promo'
      WHEN framework_type = 'CM' AND base_code_increment_tag = '5' THEN 'Remove Active Base Promo'
      ELSE 'NA'
  END AS header_base_code,
  CASE
      WHEN framework_type = 'GOV' AND flat_code_increment_tag = '1' THEN 'Create New Flat Promo'
      WHEN framework_type = 'GOV' AND flat_code_increment_tag = '2' THEN 'Right Shift Active Flat Promo'
      WHEN framework_type = 'CM' AND flat_code_increment_tag = '1' THEN 'Create New Flat Code'
      WHEN framework_type = 'CM' AND flat_code_increment_tag = '4' THEN 'Increase Mov Of Active Flat Promo'
      ELSE 'NA'
  END AS header_flat_code,
  
  CASE
      WHEN current_base_code IN ('No Base Code Running', 'EMPTY_STR') THEN 'NA'
      ELSE current_base_code
  END AS active_base_code, 
  CASE
      WHEN current_base_code_mov IN ('No Base Code Running', 'EMPTY_STR') THEN 'NA'
      ELSE current_base_code_mov
  END AS active_base_code_mov, 

  CASE
      WHEN base_code_recommendation IN ('No Action', 'EMPTY_STR', 'None', '0') THEN 'NA'
      WHEN base_code_recommendation = 'Remove Code' THEN base_code_recommendation
      ELSE SUBSTR(base_code_recommendation, 0, -6)
  END AS recommended_base_code, 
  CASE
      WHEN base_code_recommendation IN ('No Action', 'EMPTY_STR', 'None', '0') THEN 'NA'
      WHEN base_code_recommendation = 'Remove Code' THEN base_code_recommendation
      ELSE SUBSTR(base_code_recommendation, -3, -1)
  END AS recommended_base_code_mov,
  
  CASE
      WHEN current_flat_code IN ('No Flat code Running', 'EMPTY_STR') THEN 'NA'
      ELSE current_flat_code
  END AS active_flat_code, 
  CASE
      WHEN current_flat_code_mov IN ('No Flat code Running', 'EMPTY_STR') THEN 'NA'
      ELSE current_flat_code_mov
  END AS active_flat_code_mov, 

  CASE
      WHEN flat_code_recommendation IN ('None', 'EMPTY_STR', 'No Action', '0') THEN 'NA'
      WHEN flat_code_recommendation = 'Remove Code' THEN flat_code_recommendation
      ELSE SUBSTR(flat_code_recommendation, 0, -6)
  END AS recommended_flat_code, 
  CASE
      WHEN flat_code_recommendation IN ('None', 'EMPTY_STR', 'No Action', '0') THEN 'NA'
      WHEN flat_code_recommendation = 'Remove Code' THEN flat_code_recommendation
      ELSE SUBSTR(flat_code_recommendation, -3, -1)
  END AS recommended_flat_code_mov  

  FROM {{.table}}
  WHERE res_id in ({{.res_ids}})
  AND framework_type IN ('CM', 'GOV')
  AND card_identifier IN ('C2O', 'PROMO_OPTIMIZATION')
  AND metric_name IN ('r2r_la_zvdpo', 'n2r_um_zvdpo', 'n2r_mm_zvdpo', 'r2r_um_zvdpo', 'n2r_la_zvdpo', 'r2r_mm_zvdpo')
  AND framework_type = {{.tab_key}}

filters:
  - res_ids
  - tab_key
