query_backend: pinot
tenant: Zomato
table: ba_hub_growth_recommendations
identifier: bottomsheet_m2c_item_level_data
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: EPICENTRE - growth centre - bottomsheet data for gov m2c item data

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT 
  metric_display_string AS metric_display_name,
  CONCAT('bottomsheet', metric_name, '_') AS unique_card_identifier, metric_display_string AS item, metric_res AS value

  FROM insights_etls.ba_hub_growth_recommendations
  WHERE metric_name in ('distinct_transacting_items_items',
                        'image_coverage_items',
                        'description_coverage_items',
                        'items_with_combo_items',
                        'item_recommendation_items')
  AND res_id = {{.res_ids}}

filters:
  - res_ids