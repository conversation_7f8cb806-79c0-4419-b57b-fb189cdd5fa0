query_backend: pinot
tenant: Zomato
table: ba_hub_growth_recommendations
identifier: bottomsheet_cm_refund
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: EPICENTRE - growth centre - bottomsheet_cm_refund

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT 
  metric_display_string AS metric_display_name,
  metric_name, res_id,
  CONCAT('bottomsheet', metric_name, '_') AS unique_card_identifier,
  metric_res, metric_similar_res,
  complaints_recieved AS complaints_received, 
  complaints_acknowledged,
  actual_ack_pc

  FROM {{.table}}
  WHERE res_id in ({{.res_ids}})
  AND framework_type = 'CM'
  AND card_identifier = 'REFUND_COST'
  AND metric_name IN ('refund_cpo')

filters:
  - res_ids
