query_backend: pinot
tenant: Zomato
table: ba_hub_growth_recommendations
identifier: bottomsheet_cm_commissions
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: EPICENTRE - growth centre - bottomsheet_cm_commissions

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT 
  metric_display_string AS metric_display_name,
  metric_name, res_id,
  
  CONCAT('bottomsheet', metric_name, '_') AS unique_card_identifier,
  metric_res, metric_similar_res,
  
  zpp_current_tr, zpp_increase_tr, zpp_new_tr, 
  zpp_status, zpp_last_updated, zpp_logs,
  zpp_avg_distance, 
  logs_cpo,
  zpp_below_5km_ov_perc,
  zpp_between_5_8km_ov_perc,
  zpp_above_8km_ov_perc,
  zpp_pg_percentage AS pg_percentage,
  
  CASE
      WHEN metric_display_string IN ('Upgrade to ZPP Premium') THEN 1
      WHEN metric_display_string IN ('Increase commissions') THEN 2
      ELSE 0
  END AS metric_display_name_id
  
  FROM {{.table}}
  WHERE res_id in ({{.res_ids}})
  AND framework_type = 'CM'
  AND card_identifier = 'COMMISSION_REVENUE'
  AND metric_name = 'take_rate'

filters:
  - res_ids
