# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: res_events_metrics
identifier: cx_funnel
audit:
  author_email: inti.<PERSON><PERSON><PERSON>@zomato.com
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Customer funnel overview
# Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 160
# Dimensions
columns:
  - name: res_id
  - name: menu_opens
    func: sum
    source_column: menu_viewed_count
  - name: cart_builds
    func: sum
    source_column: cart_loaded_count
  - name: order_makes
    func: sum
    source_column: order_placed_count
# Group by columns
aggregations:
  - res_id
filters:
  - res_id
  - metric_name
  - window_start_time 
  - window_end_time