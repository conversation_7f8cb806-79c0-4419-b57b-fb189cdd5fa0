query_backend: trino
tenant: Zomato
catalog: hive
schema: insights_etls
table: res_status_logs_daily_sync
identifier: mx_res_status_logs
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchant-api-gateway
  pd_service_name: merchant_platform
  description: Restaurant status logs
# Time in ms
sla: 60000
# Request per second
rate_limit: 10
caching_ttl: 300
refresh_interval: 150
# contract type
type: sql
sql: >
  SELECT res_id,
        action,
        added_by,
        added_on,
        reason,
        reason_id,
        source,
        time
  FROM {{.table}}
  WHERE res_id IN ({{.res_id_array}})
  AND action IN ({{.action_type_array}})
  ORDER BY time DESC

filters:
  - res_id_array
  - action_type_array
