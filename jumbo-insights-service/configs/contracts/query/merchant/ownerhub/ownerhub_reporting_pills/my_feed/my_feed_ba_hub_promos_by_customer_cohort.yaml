# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: my_feed_ba_hub_promos_by_customer_cohort
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub reporting page - promo breakups

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    SUM(composite_mx_offer_sales) AS bhpa_gross_sales, 
    SUM(composite_mx_offer_orders) AS bhpa_gross_orders,
    SUM(new_orders_from_offers) AS bhpa_summary_new_users,
    SUM(new_orders_from_offers) AS bhpa_summary_repeat_users,  
    SUM(lapsed_orders_from_offers) AS bhpa_summary_lapsed_users,              
    SUM(la_orders_from_offers) AS bhpa_summary_la_users,   
    SUM(mm_orders_from_offers) AS bhpa_summary_mm_users,   
    SUM(um_orders_from_offers) AS bhpa_summary_um_users
  FROM ba_hub_daily_stats
  WHERE res_id in (
    {{.res_ids}}
  )
  AND "timestamp" BETWEEN {{.start_timestamp_millis}} AND {{.end_timestamp_millis}} 

filters:
  - res_ids
  - start_timestamp_millis
  - end_timestamp_millis
  
