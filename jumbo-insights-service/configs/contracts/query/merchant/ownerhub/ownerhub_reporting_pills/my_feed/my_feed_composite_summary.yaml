# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: my_feed_composite_summary
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub my-feed page live sales tracking widgets

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    MAX(merchant_order_created_at) AS agg_level,
    SUM(
      CASE 
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN merchant_order_total_cost
        ELSE 0
      END
    ) AS coe_summary_total_sales,
    SUM(
      CASE 
        WHEN (
          merchant_order_state in ({{.completed_order_states}})
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN merchant_order_total_cost
        ELSE 0
      END
    ) AS coe_summary_quick_total_sales,
    SUM(
      CASE
        WHEN merchant_order_state in ({{.all_order_states}}) THEN 1
        ELSE 0
      END
    ) AS coe_summary_total_orders,
    SUM(
      CASE
        WHEN (
          merchant_order_state in ({{.all_order_states}})
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN 1
        ELSE 0
      END
    ) AS coe_summary_quick_total_orders,
    SUM(
      CASE
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN 1
        ELSE 0
      END
    ) AS coe_summary_delivered_orders,
    SUM(
      CASE
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN 1
        ELSE 0
      END
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.all_order_states}}) THEN 1
        ELSE 0
      END
    ) AS coe_summary_delivered_orders_percentage, 
    SUM(
      CASE
        WHEN (
          merchant_order_state in ({{.all_order_states}})
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN 1
        ELSE 0
      END
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.all_order_states}}) THEN 1
        ELSE 0
      END
    ) AS coe_summary_quick_orders_percentage,      
    SUM(
      CASE
        WHEN (
          merchant_order_state in ({{.all_order_states}})
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN merchant_order_total_cost
        ELSE 0
      END
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.all_order_states}}) THEN merchant_order_total_cost
        ELSE 0
      END
    ) AS coe_summary_quick_sales_percentage,
    SUM(
      CASE 
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN merchant_order_total_cost
        ELSE 0
      END
    )/SUM(
      CASE
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN 1
        ELSE 0
      END
    ) AS coe_summary_avg_order_value,
    SUM(
      CASE
          WHEN consumer_order_rating in ({{.low_rating_id}}) THEN 1
          ELSE 0
      END
    ) AS coe_summary_poor_rated_orders,
    SUM(
      CASE
          WHEN consumer_order_rating in ({{.low_rating_id}}) THEN 1
          ELSE 0
      END
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN 1
        ELSE 0
      END
    ) AS coe_summary_poor_rated_orders_percentage,    
    SUM(
      CASE
          WHEN (
            merchant_order_state IN ({{.mx_reject_order_state}}) 
            AND merchant_order_reject_reason_id in ({{.mx_reject_reason_id}}) 
          ) THEN 1
          ELSE 0
      END
    ) AS coe_summary_rejected_orders,
    SUM(
      CASE
          WHEN (
            merchant_order_state IN ({{.mx_reject_order_state}}) 
            AND merchant_order_reject_reason_id in ({{.mx_reject_reason_id}}) 
          ) THEN merchant_order_total_cost
          ELSE 0
      END
    ) AS coe_summary_rejected_sales,
    SUM(
      CASE
          WHEN (
            merchant_order_state IN ({{.mx_reject_order_state}}) 
            AND merchant_order_reject_reason_id in ({{.mx_reject_reason_id}}) 
          ) THEN merchant_order_total_cost
          ELSE 0
      END
    )*100.0/SUM(
      CASE
          WHEN merchant_order_state IN ({{.all_order_states}}) 
            THEN merchant_order_total_cost
          ELSE 0
      END
    ) AS coe_summary_rejected_sales_percentage,
    SUM(
      CASE
          WHEN (
            merchant_order_state IN ({{.mx_reject_order_state}}) 
            AND merchant_order_reject_reason_id in ({{.mx_reject_reason_id}}) 
          ) THEN 1
          ELSE 0
      END
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.all_order_states}}) THEN 1
        ELSE 0
      END
    ) AS coe_summary_rejected_orders_percentage,    
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time <= 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time <= 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time        
        WHEN (
          logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN logistics_order_state_history_picked_time - merchant_order_state_history_accepted_time - 180
        ELSE 0
      END
     )/SUM(
      CASE
        WHEN (
          logistics_order_state_history_arrived_time > 0
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN 1
        ELSE 0
      END
    ) AS coe_summary_avg_kpt,
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time <= 120
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND merchant_order_state_history_accepted_time > 0
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time <= 120
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND merchant_order_state_history_accepted_time > 0
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time        
        WHEN (
          logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_accepted_time > 0
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN logistics_order_state_history_picked_time - merchant_order_state_history_accepted_time - 120
        ELSE 0
      END
     )/SUM(
      CASE
        WHEN (
          logistics_order_state_history_arrived_time > 0
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN 1
        ELSE 0
      END
    ) AS coe_summary_quick_avg_kpt,    
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time <= 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
        ) THEN 1
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time <= 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
        ) THEN 1        
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time <= 180
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND logistics_order_state_history_picked_time > 0
        ) THEN 1
        ELSE 0
      END 
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.all_order_states}}) THEN 1
        ELSE 0
      END
    ) AS coe_summary_for_accuracy_percentage,
    SUM(
      CASE
        WHEN merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time > 180
            AND logistics_order_state_history_picked_time > 0
              AND merchant_order_state_history_ready_time > 0
            THEN 1
        WHEN merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time > 180
            THEN 1
        ELSE 0
      END
    ) AS coe_summary_rider_handshake_delayed_orders,
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time > 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
        ) THEN logistics_order_state_history_picked_time - merchant_order_state_history_ready_time
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time > 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
        ) THEN logistics_order_state_history_picked_time - merchant_order_state_history_ready_time        
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time > 180
          AND logistics_order_state_history_picked_time > 0
          AND logistics_order_state_history_arrived_time > 0
        ) THEN logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time
        ELSE 0
      END
    )/SUM(
      CASE
        WHEN (
          logistics_order_state_history_arrived_time > 0
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
        ) THEN 1
        ELSE 0
      END
    ) AS coe_summary_avg_rider_handover_time,    
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time > 180
        ) THEN 1
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time > 180
        ) THEN 1
        ELSE 0
      END
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.all_order_states}}) THEN 1
        ELSE 0
      END
    ) AS coe_summary_rider_handshake_delayed_orders_percentage,
    SUM(
      CASE
          WHEN merchant_order_kpt_delay_secs >= 600 THEN 1
          ELSE 0
      END
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN 1
        ELSE 0
      END
    ) AS coe_summary_kpt_delayed_orders_percentage,
    SUM(
      CASE
          WHEN (
            merchant_order_kpt_delay_secs >= 60
            AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
          ) THEN 1
          ELSE 0
      END
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN 1
        ELSE 0
      END
    ) AS coe_summary_quick_kpt_delayed_orders_percentage,
    SUM(
      CASE
          WHEN merchant_order_kpt_delay_secs >= 600 THEN merchant_order_kpt_delay_secs
          ELSE 0
      END
    )/SUM(
      CASE
          WHEN merchant_order_kpt_delay_secs >= 600 THEN 1
          ELSE 0
      END
    ) AS coe_summary_orders_avg_delay,
    SUM(
      CASE
          WHEN (
            merchant_order_kpt_delay_secs >= 60
            AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
          ) THEN merchant_order_kpt_delay_secs
          ELSE 0
      END
    )/SUM(
      CASE
          WHEN (
            merchant_order_kpt_delay_secs >= 60 
            AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
          ) THEN 1
          ELSE 0
      END
    ) AS coe_summary_quick_orders_avg_delay,
    SUM(
      CASE
          WHEN merchant_order_kpt_delay_secs >= 600 THEN 1
          ELSE 0
      END
    ) AS coe_summary_kpt_delayed_orders
  FROM composite_order_events
  WHERE "consumer_order_created_at" BETWEEN {{.start_timestamp}} AND {{.end_timestamp}} 
  AND consumer_order_created_ist_hour <= {{.data_bw_zero_to_x_hrs}}
  AND consumer_order_delivery_mode IN ('DELIVERY', 'delivery')
  AND merchant_order_res_id in ({{.res_ids}})

filters:
  - completed_order_states
  - all_order_states
  - res_ids
  - start_timestamp
  - end_timestamp
  - data_bw_zero_to_x_hrs
  - low_rating_id
  - mx_reject_order_state
  - mx_reject_reason_id