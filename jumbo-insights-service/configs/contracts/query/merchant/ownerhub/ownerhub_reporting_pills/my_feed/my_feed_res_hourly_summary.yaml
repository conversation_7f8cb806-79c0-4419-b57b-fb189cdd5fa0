# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: restaurant_hourly_stats
identifier: my_feed_res_hourly_summary
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub reporting page - funnel conversion | a2i, m2c, c2o metrics

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    SUM(menu_impressions) AS rhs_summary_impressions,
    SUM(menu_opens)*100.0/SUM(menu_impressions) AS rhs_summary_i2m,
    SUM(menu_opens) AS rhs_summary_menu_opens,
    SUM(cart_created) AS rhs_summary_cart_builds,
    SUM(cart_created)*100.0/SUM(menu_opens) AS rhs_summary_m2c,    
    SUM(order_placed) AS rhs_summary_order_placed,
    SUM(orders_created) AS rhs_summary_orders_created,
    SUM(orders_completed) AS rhs_summary_orders_completed,    
    SUM(orders_created)*100.0/SUM(cart_created) AS rhs_summary_c2o,
    SUM(orders_created)*100.0/SUM(menu_opens) AS rhs_summary_m2o,
    SUM(orders_completed)*100.0/SUM(orders_created) AS rhs_summary_delivered_orders_percentage,
    SUM(search_impressions) AS rhs_summary_search_impressions,
    SUM(search_menu_opens) AS rhs_summary_search_menu_opens,
    SUM(recommended_for_you_impressions) AS rhs_summary_recommended_for_you_impressions,
    SUM(recommended_for_you_menu_opens) AS rhs_summary_recommended_for_you_menu_opens,
    SUM(dish_or_cuisine_search_impressions) AS rhs_summary_dish_or_cuisine_search_impressions,
    SUM(dish_or_cuisine_menu_opens) AS rhs_summary_dish_or_cuisine_search_menu_opens,
    SUM(home_page_impressions) AS rhs_summary_home_page_impressions,
    SUM(home_page_menu_opens) AS rhs_summary_home_page_menu_opens,
    SUM(offers_page_impressions) AS rhs_summary_offers_page_impressions,
    SUM(offers_page_menu_opens) AS rhs_summary_offers_page_menu_opens,
    SUM(campaign_impressions) AS rhs_summary_campaign_impressions,
    SUM(campaign_menu_opens) AS rhs_summary_campaign_menu_opens,
    SUM(other_impressions) AS rhs_summary_other_impressions,
    SUM(other_menu_opens) AS rhs_summary_other_menu_opens,
    SUM(new_user_impressions) AS rhs_summary_new_user_impressions,
    SUM(new_user_menu_opens) AS rhs_summary_new_user_menu_opens,
    SUM(lapsed_user_impressions) AS rhs_summary_lapsed_user_impressions,
    SUM(lapsed_menu_opens) AS rhs_summary_lapsed_user_menu_opens,
    SUM(repeat_user_impressions) AS rhs_summary_repeat_user_impressions,
    SUM(repeat_user_menu_opens) AS rhs_summary_repeat_user_menu_opens,
    SUM(la_user_impressions) AS rhs_summary_la_user_impressions,
    SUM(la_user_menu_opens) AS rhs_summary_la_user_menu_opens,
    SUM(mm_user_impressions) AS rhs_summary_mm_user_impressions,
    SUM(mm_menu_opens) AS rhs_summary_mm_user_menu_opens,
    SUM(um_user_impressions) AS rhs_summary_um_user_impressions,
    SUM(um_user_menu_opens) AS rhs_summary_um_user_menu_opens,

    SUM(offline_hours) AS rhs_summary_offline_seconds,
    SUM(actual_online_hours)*100.0/SUM(expected_online_hours) AS rhs_summary_online_percentage,
    100.0 - SUM(actual_online_hours)*100.0/SUM(expected_online_hours) AS rhs_summary_offline_percentage
  FROM restaurant_hourly_stats
  WHERE res_id in (
    {{.res_ids}}
  )
  AND "timestamp" BETWEEN {{.start_timestamp_millis}} AND {{.end_timestamp_millis}} 

filters:
  - res_ids
  - start_timestamp_millis
  - end_timestamp_millis
  
