# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: my_feed_ba_hub_daily_by_period
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub reporting page - funnel conversion | a2i, m2c, c2o metrics

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    CASE
      WHEN {{.agg_enum}} = 0 THEN DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:dd tz(Asia/Kolkata)',
        '1:DAYS'
      )
      WHEN {{.agg_enum}} IN (2, 3) THEN dt
      WHEN {{.agg_enum}} = 5 THEN  DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:yyyy-MM tz(Asia/Kolkata)',
        '1:DAYS'
      )
      WHEN {{.agg_enum}} = 7 THEN DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:EEE tz(Asia/Kolkata)',
        '1:DAYS'
      )      
      WHEN {{.agg_enum}} = 8 THEN DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:e tz(Asia/Kolkata)',
        '1:DAYS'
      )            
      ELSE 'agg'
    END AS agg_level_str,
    MAX("timestamp"/1000) AS agg_level,

    SUM(delivered_merchant_received_amount) AS total_sales,
    SUM(total_orders) AS total_orders,
    SUM(delivered_orders) AS delivered_orders,
    SUM(delivered_merchant_received_amount)/SUM(delivered_orders) AS avg_order_value,
    SUM(low_rated_orders) AS poor_rated_orders,
    SUM(composite_mx_rejected_orders) AS rejected_orders,

    SUM(impressions) AS impressions,
    SUM(menu_opens) AS menu_opens,
    SUM(menu_opens)*100.0/SUM(impressions) AS i2m,
    SUM(cart_builts) AS cart_builts,
    SUM(cart_builts)*100.0/SUM(menu_opens) AS m2c,
    SUM(total_orders) AS orders_placed,
    SUM(total_orders)*100.0/SUM(cart_builts) AS c2o,
    SUM(total_orders)*100.0/SUM(menu_opens) AS m2o,
    SUM(delivered_orders)*100.0/SUM(total_orders) AS delivered_orders_percentage,

    SUM(composite_mx_rejected_orders)*100.00/SUM(total_orders) AS rejected_orders_percentage,
    SUM(composite_mx_lost_sales) AS rejected_sales,
    (
      SUM(supply_mx_sent_refund) 
    + SUM(supply_mx_attr_non_refund)
    ) AS complaint_orders,
    (
      SUM(supply_mx_sent_refund) 
    + SUM(supply_mx_attr_non_refund)
    )*100.00/SUM(total_orders) AS complaint_orders_percentage,
    SUM(supply_mx_sent_refund)*100.0/SUM(total_orders) AS complaint_orders_refunded_percentage,
    SUM(low_rated_orders)*100.0/SUM(total_orders) AS poor_rated_orders_percentage,
    SUM(actual_visibility)*100.0/SUM(expected_visibility) AS online_percentage,
    SUM(last_week_estimated_lost_sales) AS estimated_lost_sales,

    SUM(mx_kitchen_is_full) AS rejected_kitchen_is_full_orders,
    SUM(mx_item_out_of_stock) AS rejected_mx_item_out_of_stock_orders,
    SUM(mx_restaurant_is_closed) AS rejected_mx_outlet_is_closed_orders,

    (
      SUM(composite_mx_rejected_orders)
    - SUM(mx_kitchen_is_full)
    - SUM(mx_item_out_of_stock)
    - SUM(mx_restaurant_is_closed)
    ) AS rejected_others,

    SUM(supply_pp_ors) AS poor_packing_orders,
    SUM(supply_wo_ors) AS wrong_order_orders,
    SUM(supply_pq_ors) AS poor_quality_orders,
    SUM(supply_mi_ors) AS missing_items_orders,
    (
      SUM(supply_mx_sent_refund) 
    + SUM(supply_mx_attr_non_refund)
    - SUM(supply_pp_ors)
    - SUM(supply_wo_ors)
    - SUM(supply_pq_ors)
    - SUM(supply_mi_ors)
    ) AS other_complaint_orders,

    SUM(supply_mx_sent_refund_pp) AS refunded_poor_packing_percentage,
    SUM(supply_mx_sent_refund_wo) AS refunded_wrong_order_percentage,
    SUM(supply_mx_sent_refund_pq) AS refunded_poor_quality_percentage,
    SUM(supply_mx_sent_refund_mi) AS refunded_missing_items_percentage,
    (
      SUM(supply_mx_sent_refund) 
    - SUM(supply_mx_sent_refund_pp)
    - SUM(supply_mx_sent_refund_pq)
    - SUM(supply_mx_sent_refund_wo)
    - SUM(supply_mx_sent_refund_mi)
    ) AS refunded_other_complaint_orders,   
    SUM(supply_refunded_ors) AS refunded_complaint_orders,
    
    SUM(resolved_ors) AS resolved_complaint_orders,
    SUM(winback_ors) AS winback_complaint_orders,

    SUM(kitchent_prep_time)*1.00/SUM(kitchent_prep_time_not_null) AS avg_kpt,
    SUM(kpt_delayed_orders)*100.0/SUM(delivered_orders) AS kpt_delayed_orders_percentage,
    SUM(for_accurate_orders)*100.0/SUM(total_orders) AS for_accuracy_percentage,
    (
      SUM(total_orders) 
    - SUM(for_marked_orders)
    )*100.0/SUM(total_orders) AS not_for_marked_orders_percentage,
    SUM(
      CASE
        WHEN handover_time_breach_orders > 0 THEN handover_time_breach_orders
        ELSE 0
      END
    )*100.0/SUM(total_orders) AS rider_handshake_delayed_orders_percentage,

      SUM(new_users) 
    + SUM(repeat_users) 
    + SUM(lapsed_users) AS total_users,
    SUM(new_users) AS new_users,
    SUM(repeat_users) AS repeat_users,
    SUM(lapsed_users) AS lapsed_users,

    SUM(um_orders) AS um_orders,
    SUM(mm_orders) AS mm_orders,
    SUM(la_orders) AS la_orders,
    
    SUM(composite_mx_offer_sales) + SUM(composite_discount_applied_amount) AS sales_from_offers,
    (
        SUM(composite_mx_offer_sales) 
      + SUM(composite_discount_applied_amount)
    )*100.0/(
        SUM(merchant_received_amount) 
      + SUM(composite_discount_applied_amount)
    ) AS sales_from_offers_perc_share,
    SUM(composite_discount_applied_amount) AS discount_given,
    SUM(composite_discount_applied_amount)/SUM(delivered_orders) AS discount_given_per_order,
    SUM(composite_mx_offer_orders) AS orders_from_offers,
    SUM(composite_mx_offer_orders)*100.0/SUM(delivered_orders) AS orders_from_offers_perc_share,
    SUM(composite_discount_applied_amount)*100/(
        SUM(composite_mx_offer_sales) 
      + SUM(composite_discount_applied_amount)
    ) AS effective_discount,

    SUM(delivered_ad_impression) AS ads_impressions,
    SUM(delivered_ad_impression)*100.0/SUM(impressions) AS ads_impressions_perc_share,
    SUM(ads_cv) AS sales_from_ads,
    SUM(ads_cv)*100.0/SUM(commissionable_value) AS sales_from_ads_perc_share,
    SUM(ad_orders) AS ad_orders,
    SUM(ad_orders)*100.0/SUM(total_orders) AS ad_orders_perc_share,    
    SUM(billed_revenue) AS ads_spend,
    SUM(ad_menu_opens) AS ad_clicks,
    SUM(ad_orders)*100.0/SUM(delivered_ad_impression) AS ad_ctr,
    SUM(ad_menu_opens)*100.0/SUM(delivered_ad_impression) AS ad_m2o,
    SUM(billed_revenue)/SUM(ad_menu_opens) AS avg_cost_per_click,
    (SUM(ads_cv) - SUM(grow_max_ads_cv))/(SUM(billed_revenue) - SUM(grow_max_billed_revenue)) AS ads_roi,

    SUM(total_items) AS total_dishes,
    SUM(quick_total_items) AS quick_total_items,    
    
    MAX(ingestion_time) AS ingestion_time
  FROM ba_hub_daily_stats 
  WHERE res_id in (
    {{.res_ids}}
  )
  AND "timestamp" BETWEEN {{.start_timestamp_millis}} AND {{.end_timestamp_millis}} 
  GROUP BY agg_level_str

filters:
  - agg_enum
  - res_ids
  - start_timestamp_millis
  - end_timestamp_millis
  
