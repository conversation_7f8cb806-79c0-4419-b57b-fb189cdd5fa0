# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: my_feed_composite_by_period
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub my-feed page live sales tracking widgets

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    CASE
      WHEN {{.agg_enum}} = 0 THEN DATETIMECONVERT(
        "merchant_order_created_at",
        '1:SECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:dd tz(Asia/Kolkata)',
        '1:DAYS'
      )    
      WHEN {{.agg_enum}} IN (2, 3) THEN consumer_order_created_ist_dt
      WHEN {{.agg_enum}} = 4 THEN CAST(merchant_order_created_ist_hour AS STRING)
      WHEN {{.agg_enum}} = 5 THEN DATETIMECONVERT(
        "merchant_order_created_at",
        '1:SECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:yyyy-MM tz(Asia/Kolkata)',
        '1:DAYS'
      )
      WHEN {{.agg_enum}} = 6 THEN merchant_order_mealtime
      WHEN {{.agg_enum}} = 7 THEN DATETIMECONVERT(
        "merchant_order_created_at",
        '1:SECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:EEE tz(Asia/Kolkata)',
        '1:DAYS'
      )
      WHEN {{.agg_enum}} = 8 THEN DATETIMECONVERT(
        "merchant_order_created_at",
        '1:SECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:e tz(Asia/Kolkata)',
        '1:DAYS'
      )      
      ELSE 'agg'
    END AS agg_level_str,
    MAX(merchant_order_created_at) AS agg_level,
    SUM(
      CASE 
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN merchant_order_total_cost
        ELSE 0
      END
    ) AS total_sales,
    SUM(
      CASE 
        WHEN (
          merchant_order_state in ({{.completed_order_states}})
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN merchant_order_total_cost
        ELSE 0
      END
    ) AS quick_total_sales,
    SUM(
      CASE
        WHEN merchant_order_state in ({{.all_order_states}}) THEN 1
        ELSE 0
      END
    ) AS total_orders,
    SUM(
      CASE
        WHEN (
          merchant_order_state in ({{.all_order_states}})
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN 1
        ELSE 0
      END
    ) AS quick_total_orders,    
    SUM(
      CASE
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN 1
        ELSE 0
      END
    ) AS delivered_orders,
    SUM(
      CASE 
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN merchant_order_total_cost
        ELSE 0
      END
    )/SUM(
      CASE
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN 1
        ELSE 0
      END
    ) AS avg_order_value,
    SUM(
      CASE
          WHEN consumer_order_rating in ({{.low_rating_id}}) THEN 1
          ELSE 0
      END
    ) AS poor_rated_orders,
    SUM(
      CASE
          WHEN consumer_order_rating in ({{.low_rating_id}}) THEN 1
          ELSE 0
      END
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN 1
        ELSE 0
      END
    ) AS poor_rated_orders_percentage,    
    SUM(
      CASE
          WHEN (
            merchant_order_state IN ({{.mx_reject_order_state}}) 
            AND merchant_order_reject_reason_id in ({{.mx_reject_reason_id}}) 
          ) THEN 1
          ELSE 0
      END
    ) AS rejected_orders,
    SUM(
      CASE
          WHEN (
            merchant_order_state IN ({{.mx_reject_order_state}}) 
            AND merchant_order_reject_reason_id in ({{.mx_reject_reason_id}}) 
          ) THEN merchant_order_total_cost
          ELSE 0
      END
    ) AS rejected_sales,
    SUM(
      CASE
          WHEN (
            merchant_order_state IN ({{.mx_reject_order_state}}) 
            AND merchant_order_reject_reason_id in ({{.mx_reject_reason_id}}) 
          ) THEN merchant_order_total_cost
          ELSE 0
      END
    )*100.0/SUM(
      CASE
          WHEN merchant_order_state IN ({{.all_order_states}}) 
            THEN merchant_order_total_cost
          ELSE 0
      END
    ) AS rejected_sales_percentage,    
    SUM(
      CASE
          WHEN (
            merchant_order_state IN ({{.mx_reject_order_state}}) 
            AND merchant_order_reject_reason_id in ({{.mx_reject_reason_id}}) 
          ) THEN 1
          ELSE 0
      END
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.all_order_states}}) THEN 1
        ELSE 0
      END
    ) AS rejected_orders_percentage,
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time <= 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time <= 180          
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time                        
        WHEN (
          logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN logistics_order_state_history_picked_time - merchant_order_state_history_accepted_time - 180
        ELSE 0
      END
     )/SUM(
      CASE
        WHEN (
          logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN 1
        ELSE 0
      END
    ) AS avg_kpt,
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time <= 120
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time <= 120          
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time                        
        WHEN (
          logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_accepted_time > 0
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN logistics_order_state_history_picked_time - merchant_order_state_history_accepted_time - 120
        ELSE 0
      END
     )/SUM(
      CASE
        WHEN (
          logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN 1
        ELSE 0
      END
    ) AS quick_avg_kpt,    
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time <= 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0          
        ) THEN 1
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time <= 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0          
        ) THEN 1
        ELSE 0
      END 
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.all_order_states}}) THEN 1
        ELSE 0
      END
    ) AS for_accuracy_percentage,
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time > 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0                    
        ) THEN 1
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time > 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0                    
        ) THEN 1
        ELSE 0
      END
    ) AS rider_handshake_delayed_orders,    
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time > 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0                    
        ) THEN 1
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time 
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time > 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0                    
        ) THEN 1
        ELSE 0
      END
    )/SUM(
      CASE
        WHEN merchant_order_state in ({{.all_order_states}}) THEN 1
        ELSE 0
      END
    ) AS rider_handshake_delayed_orders_percentage,
    SUM(
      CASE
          WHEN merchant_order_kpt_delay_secs >= 600 THEN 1
          ELSE 0
      END
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN 1
        ELSE 0
      END
    ) as kpt_delayed_orders_percentage,
    SUM(
      CASE
          WHEN (
            merchant_order_kpt_delay_secs >= 60
            AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
          ) THEN merchant_order_kpt_delay_secs
          ELSE 0
      END
    )/SUM(
      CASE
          WHEN (
            merchant_order_kpt_delay_secs >= 60 
            AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
          ) THEN 1
          ELSE 0
      END
    ) AS quick_orders_avg_delay,
    SUM(
      CASE
          WHEN (
            merchant_order_kpt_delay_secs >= 60
            AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
          ) THEN 1
          ELSE 0
      END
    )*100.0/SUM(
      CASE
        WHEN (
          merchant_order_state in ({{.completed_order_states}})
          AND consumer_order_delivery_speed = 'DELIVERY_SPEED_EXPRESS_FOOD_DELIVERY'
        ) THEN 1
        ELSE 0
      END
    ) AS quick_kpt_delayed_orders_percentage
  FROM composite_order_events
  WHERE "consumer_order_created_at" BETWEEN {{.start_timestamp}} AND {{.end_timestamp}} 
  AND merchant_order_created_ist_hour <= {{.data_bw_zero_to_x_hrs}}
  AND consumer_order_delivery_mode IN ('DELIVERY', 'delivery')
  AND merchant_order_res_id in ({{.res_ids}})
  GROUP BY agg_level_str

filters:
  - agg_enum
  - all_order_states
  - completed_order_states
  - res_ids
  - start_timestamp
  - end_timestamp
  - data_bw_zero_to_x_hrs
  - low_rating_id
  - mx_reject_order_state
  - mx_reject_reason_id