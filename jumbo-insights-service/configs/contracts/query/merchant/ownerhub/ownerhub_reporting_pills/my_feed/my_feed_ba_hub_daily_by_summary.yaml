# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: my_feed_ba_hub_daily_by_summary
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub reporting page - funnel conversion | a2i, m2c, c2o metrics

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    SUM(delivered_merchant_received_amount) AS bhds_summary_total_sales,
    SUM(total_orders) AS bhds_summary_total_orders,
    SUM(delivered_orders) AS bhds_summary_delivered_orders,
    SUM(delivered_merchant_received_amount)/SUM(delivered_orders) AS bhds_summary_avg_order_value,
    SUM(low_rated_orders) AS bhds_summary_poor_rated_orders,
    SUM(composite_mx_rejected_orders) AS bhds_summary_rejected_orders,

    SUM(impressions) AS bhds_summary_impressions,
    SUM(menu_opens) AS bhds_summary_menu_opens,
    SUM(menu_opens)*100.0/SUM(impressions) AS bhds_summary_i2m,
    SUM(cart_builts) AS bhds_summary_cart_builts,
    SUM(cart_builts)*100.0/SUM(menu_opens) AS bhds_summary_m2c,
    SUM(total_orders) AS bhds_summary_orders_placed,
    SUM(total_orders)*100.0/SUM(cart_builts) AS bhds_summary_c2o,
    SUM(total_orders)*100.0/SUM(menu_opens) AS bhds_summary_m2o,
    SUM(delivered_orders)*100.0/SUM(total_orders) AS bhds_summary_delivered_orders_percentage,

    SUM(composite_mx_rejected_orders)*100.00/SUM(total_orders) AS bhds_summary_rejected_orders_percentage,
    SUM(composite_mx_lost_sales) AS bhds_summary_rejected_sales,
    (
      SUM(supply_mx_sent_refund) 
    + SUM(supply_mx_attr_non_refund)
    ) AS bhds_summary_complaint_orders,    
    (
        SUM(supply_mx_sent_refund) 
      + SUM(supply_mx_attr_non_refund)
    )*100.00/SUM(total_orders) AS bhds_summary_complaint_orders_percentage,
    SUM(supply_mx_sent_refund) AS bhds_summary_refunded_complaint_orders,
    SUM(supply_mx_sent_refund)*100.0/SUM(total_orders) AS bhds_summary_refunded_complaint_orders_percentage,
    SUM(resolved_ors) AS bhds_summary_resolved_complaint_orders,
    SUM(resolved_ors)*100.0/SUM(total_orders) AS bhds_summary_resolved_complaint_orders_percentage,
    SUM(winback_ors) AS bhds_summary_winback_complaint_orders,
    SUM(winback_ors)*100.0/SUM(total_orders) AS bhds_summary_winback_complaint_orders_percentage,

    SUM(low_rated_orders)*100.0/SUM(total_orders) AS bhds_summary_poor_rated_orders_percentage,
    SUM(actual_visibility)*100.0/SUM(expected_visibility) AS bhds_summary_online_percentage,
    SUM(last_week_estimated_lost_sales) AS bhds_summary_estimated_lost_sales,

    SUM(mx_kitchen_is_full) AS bhds_summary_rejected_kitchen_is_full_orders,
    SUM(mx_item_out_of_stock) AS bhds_summary_rejected_mx_item_out_of_stock_orders,
    SUM(mx_restaurant_is_closed) AS bhds_summary_rejected_mx_outlet_is_closed_orders,    
    (
        SUM(composite_mx_rejected_orders)
      - SUM(mx_kitchen_is_full)
      - SUM(mx_item_out_of_stock)
      - SUM(mx_restaurant_is_closed)
    ) AS bhds_summary_rejected_others,

    SUM(supply_pp_ors) AS bhds_summary_poor_packing_orders,
    SUM(supply_wo_ors) AS bhds_summary_wrong_order_orders,
    SUM(supply_pq_ors) AS bhds_summary_poor_quality_orders,
    SUM(supply_mi_ors) AS bhds_summary_missing_items_orders,
    (
        SUM(supply_mx_sent_refund) 
      + SUM(supply_mx_attr_non_refund)
      - SUM(supply_pp_ors)
      - SUM(supply_wo_ors)
      - SUM(supply_pq_ors)
      - SUM(supply_mi_ors)
    ) AS bhds_summary_other_complaint_orders,

    SUM(supply_mx_sent_refund_pp) AS bhds_summary_refunded_poor_packing_percentage,
    SUM(supply_mx_sent_refund_wo) AS bhds_summary_refunded_wrong_order_percentage,
    SUM(supply_mx_sent_refund_pq) AS bhds_summary_refunded_poor_quality_percentage,
    SUM(supply_mx_sent_refund_mi) AS bhds_summary_refunded_missing_items_percentage,
    (
        SUM(supply_mx_sent_refund) 
      - SUM(supply_mx_sent_refund_pp)
      - SUM(supply_mx_sent_refund_pq)
      - SUM(supply_mx_sent_refund_wo)
      - SUM(supply_mx_sent_refund_mi)
    ) AS bhds_summary_refunded_other_complaint_orders,    

    SUM(winback_pp) AS bhds_summary_winback_poor_packing_percentage,
    SUM(winback_wo) AS bhds_summary_winback_wrong_order_percentage,
    SUM(winback_pq) AS bhds_summary_winback_poor_quality_percentage,
    SUM(winback_mi) AS bhds_summary_winback_missing_items_percentage,
    (
        SUM(winback_ors) 
      - SUM(winback_pp)
      - SUM(winback_pq)
      - SUM(winback_wo)
      - SUM(winback_mi)
    ) AS bhds_summary_winback_other_complaint_orders,

    SUM(resolved_pp) AS bhds_summary_resolved_poor_packing_percentage,
    SUM(resolved_wo) AS bhds_summary_resolved_wrong_order_percentage,
    SUM(resolved_pq) AS bhds_summary_resolved_poor_quality_percentage,
    SUM(resolved_mi) AS bhds_summary_resolved_missing_items_percentage,
    (
        SUM(resolved_ors) 
      - SUM(resolved_pp)
      - SUM(resolved_wo)
      - SUM(resolved_pq)
      - SUM(resolved_mi)
    ) AS bhds_summary_resolved_other_complaint_orders,  

    SUM(kitchent_prep_time)*1.00/SUM(kitchent_prep_time_not_null) AS bhds_summary_avg_kpt,
    SUM(kpt_delayed_orders)*100.0/SUM(delivered_orders) AS bhds_summary_kpt_delayed_orders_percentage,
    SUM(
      CASE
        WHEN kitchent_prep_time - expected_kpt > 0 
          THEN kitchent_prep_time - expected_kpt
        ELSE 0
      END
    )/SUM(kpt_delayed_orders) AS bhds_summary_orders_avg_delay,
    SUM(for_accurate_orders)*100.0/SUM(total_orders) AS bhds_summary_for_accuracy_percentage,
    (
        SUM(total_orders) 
      - SUM(for_marked_orders)
    )*100.0/SUM(total_orders) AS bhds_summary_not_for_marked_orders_percentage,
    SUM(
      CASE
        WHEN handover_time_breach_orders > 0 THEN handover_time_breach_orders
        ELSE 0
      END
    )*100.0/SUM(total_orders) AS bhds_summary_rider_handshake_delayed_orders_percentage,
    SUM(handover_time_seconds_sum)/(SUM(handover_time_orders)*60) AS bhds_summary_avg_rider_handover_time,
    SUM(handover_time_breach_orders) AS bhds_summary_rider_handshake_delayed_orders,

    (
      SUM(new_users) 
    + SUM(repeat_users) 
    + SUM(lapsed_users) 
    ) AS bhds_summary_total_users,
    SUM(new_users) AS bhds_summary_new_users,
    SUM(repeat_users) AS bhds_summary_repeat_users,
    SUM(lapsed_users) AS bhds_summary_lapsed_users,

    SUM(um_orders) AS bhds_summary_um_orders,
    SUM(mm_orders) AS bhds_summary_mm_orders,
    SUM(la_orders) AS bhds_summary_la_orders,

    SUM(drop_dist_less_than_4_kms_orders) AS bhds_summary_drop_dist_less_than_4_kms_orders,
    SUM(drop_dist_bw_4_and_7_kms_orders) AS bhds_summary_drop_dist_bw_4_and_7_kms_orders,
    SUM(drop_dist_more_than_7_kms_orders) AS bhds_summary_drop_dist_more_than_7_kms_orders,
    
    SUM(composite_mx_offer_sales) + SUM(composite_discount_applied_amount) AS bhds_summary_sales_from_offers,
    (
        SUM(composite_mx_offer_sales) 
      + SUM(composite_discount_applied_amount)
    )*100.0/(
        SUM(delivered_merchant_received_amount) 
      + SUM(composite_discount_applied_amount)
    ) AS bhds_summary_sales_from_offers_perc_share,
    SUM(composite_discount_applied_amount) AS bhds_summary_discount_given,
    SUM(composite_discount_applied_amount)/SUM(delivered_orders) AS bhds_summary_discount_given_per_order,
    SUM(composite_mx_offer_orders) AS bhds_summary_orders_from_offers,
    SUM(composite_mx_offer_orders)*100.0/SUM(delivered_orders) AS bhds_summary_orders_from_offers_perc_share,
    SUM(composite_discount_applied_amount)*100/(
        SUM(composite_mx_offer_sales) 
      + SUM(composite_discount_applied_amount)
    ) AS bhds_summary_effective_discount,

    SUM(promos_offer_sales) AS bhds_summary_promo_discount,
    SUM(salt_offer_sales) AS bhds_summary_dish_discount,
    SUM(bxgy_offer_sales) AS bhds_summary_bxgy_discount,
    SUM(freebie_offer_sales) AS bhds_summary_freebie_discount,
    SUM(gold_discount_offer_sales) AS bhds_summary_gold_discount,
    SUM(winback_offer_sales) AS bhds_summary_winback_discount,

    SUM(delivered_ad_impression) AS bhds_summary_ads_impressions,
    SUM(delivered_ad_impression)*100.0/SUM(impressions) AS bhds_summary_ads_impressions_perc_share,
    SUM(ads_cv) AS bhds_summary_sales_from_ads,
    SUM(ads_cv)*100.0/SUM(commissionable_value) AS bhds_summary_sales_from_ads_perc_share,
    SUM(ad_orders) AS bhds_summary_ad_orders,
    SUM(ad_orders)*100.0/SUM(total_orders) AS bhds_summary_ad_orders_perc_share,
    SUM(billed_revenue) AS bhds_summary_ads_spend,
    SUM(ad_orders)*100.0/SUM(delivered_ad_impression) AS bhds_summary_ad_ctr,
    SUM(ad_orders)*100.0/SUM(ad_menu_opens) AS bhds_summary_ad_m2o,
    SUM(ad_menu_opens) AS bhds_summary_ad_clicks,
    SUM(billed_revenue)/SUM(ad_menu_opens) AS bhds_summary_avg_cost_per_click,
    (SUM(ads_cv) - SUM(grow_max_ads_cv))/(SUM(billed_revenue) - SUM(grow_max_billed_revenue)) AS bhds_summary_ads_roi,

    SUM(new_ad_orders) AS bhds_summary_new_ad_orders,
    SUM(repeat_ad_orders) AS bhds_summary_repeat_ad_orders,
    SUM(lapsed_ad_orders) AS bhds_summary_lapsed_ad_orders, 
    SUM(la_ad_orders) AS bhds_summary_la_ad_orders,
    SUM(mm_ad_orders) AS bhds_summary_mm_ad_orders,
    SUM(um_ad_orders) AS bhds_summary_um_ad_orders,

    SUM(new_ad_impressions) AS bhds_summary_new_ad_impressions,
    SUM(repeat_ad_impressions) AS bhds_summary_repeat_ad_impressions,
    SUM(lapsed_ad_impressions) AS bhds_summary_lapsed_ad_impressions,
    SUM(la_ad_impressions) AS bhds_summary_la_ad_impressions,
    SUM(mm_ad_impressions) AS bhds_summary_mm_ad_impressions,
    SUM(um_ad_impressions) AS bhds_summary_um_ad_impressions,

    SUM(total_items) AS bhds_summary_total_dishes,
    SUM(quick_total_items) AS bhds_summary_quick_total_items,
    SUM(quick_total_items)*100.0/SUM(total_items) AS bhds_summary_quick_items_percentage,

    MAX(ingestion_time) AS bhds_summary_ingestion_time
  FROM ba_hub_daily_stats 
  WHERE res_id in (
    {{.res_ids}}
  )
  AND "timestamp" BETWEEN {{.start_timestamp_millis}} AND {{.end_timestamp_millis}} 

filters:
  - res_ids
  - start_timestamp_millis
  - end_timestamp_millis
  
