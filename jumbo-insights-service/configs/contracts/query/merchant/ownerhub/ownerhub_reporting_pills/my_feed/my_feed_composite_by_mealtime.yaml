# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: my_feed_composite_by_mealtime
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub my-feed page live sales tracking widgets by mealtime

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    SUM(
      CASE
        WHEN (
          merchant_order_state IN ({{.completed_order_states}}) 
          AND consumer_order_mealtime = 'Breakfast'
        ) THEN 1
        ELSE 0
      END
    ) AS coe_mealtime_total_orders_breakfast,
    SUM(
      CASE
        WHEN (
          merchant_order_state IN ({{.completed_order_states}}) 
          AND consumer_order_mealtime = 'Lunch'
        ) THEN 1
        ELSE 0
      END
    ) AS coe_mealtime_total_orders_lunch,
    SUM(
      CASE
        WHEN (
          merchant_order_state IN ({{.completed_order_states}}) 
          AND consumer_order_mealtime = 'Snack'
        ) THEN 1
        ELSE 0
      END
    ) AS coe_mealtime_total_orders_snack,
    SUM(
      CASE
        WHEN (
          merchant_order_state IN ({{.completed_order_states}}) 
          AND consumer_order_mealtime = 'Dinner'
        ) THEN 1
        ELSE 0
      END
    ) AS coe_mealtime_total_orders_dinner,
    SUM(
      CASE
        WHEN (
          merchant_order_state IN ({{.completed_order_states}}) 
          AND consumer_order_mealtime = 'LateNight'
        ) THEN 1
        ELSE 0
      END
    ) AS coe_mealtime_total_orders_latenight,
    SUM(
      CASE
        WHEN (
          consumer_order_mealtime = 'Breakfast' 
          AND merchant_order_kpt_delay_secs >= 600
        ) THEN 1
        ELSE 0
      END
    ) AS coe_mealtime_kpt_delayed_orders_breakfast,
    SUM(
      CASE
        WHEN (
          consumer_order_mealtime = 'Lunch' 
          AND merchant_order_kpt_delay_secs >= 600
        ) THEN 1
        ELSE 0
      END
    ) AS coe_mealtime_kpt_delayed_orders_lunch,
    SUM(
      CASE
        WHEN (
          consumer_order_mealtime = 'Snack' 
          AND merchant_order_kpt_delay_secs >= 600
        ) THEN 1
        ELSE 0
      END
    ) AS coe_mealtime_kpt_delayed_orders_snack,
    SUM(
      CASE
        WHEN (
          consumer_order_mealtime = 'Dinner' 
          AND merchant_order_kpt_delay_secs >= 600
        ) THEN 1
        ELSE 0
      END
    ) AS coe_mealtime_kpt_delayed_orders_dinner,
    SUM(
      CASE
        WHEN (
          consumer_order_mealtime = 'LateNight' 
          AND merchant_order_kpt_delay_secs >= 600
        ) THEN 1
        ELSE 0
      END
    ) AS coe_mealtime_kpt_delayed_orders_latenight,
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time <= 180          
          AND consumer_order_mealtime = 'Breakfast'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time <= 180          
          AND consumer_order_mealtime = 'Breakfast'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time        
        WHEN (
          consumer_order_mealtime = 'Breakfast'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN logistics_order_state_history_picked_time - merchant_order_state_history_accepted_time - 180
        ELSE 0
      END
     )/SUM(
      CASE
        WHEN (
          consumer_order_mealtime = 'Breakfast'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN 1
        ELSE 0
      END
    ) AS coe_mealtime_kpt_breakfast,
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time
          AND consumer_order_mealtime = 'Lunch'
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time <= 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time <= 180          
          AND consumer_order_mealtime = 'Lunch'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time                
        WHEN (
          consumer_order_mealtime = 'Lunch'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN logistics_order_state_history_picked_time - merchant_order_state_history_accepted_time - 180
        ELSE 0
      END
     )/SUM(
      CASE
        WHEN (
          consumer_order_mealtime = 'Lunch'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN 1
        ELSE 0
      END
    ) AS coe_mealtime_kpt_lunch,
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time
          AND consumer_order_mealtime = 'Snack'
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time <= 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time <= 180          
          AND consumer_order_mealtime = 'Snack'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time                
        WHEN (
          consumer_order_mealtime = 'Snack'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN logistics_order_state_history_picked_time - merchant_order_state_history_accepted_time - 180
        ELSE 0
      END
     )/SUM(
      CASE
        WHEN (
          consumer_order_mealtime = 'Snack'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN 1        
        ELSE 0
      END
    ) AS coe_mealtime_kpt_snack,
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time
          AND consumer_order_mealtime = 'Dinner'
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time <= 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time <= 180          
          AND consumer_order_mealtime = 'Dinner'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time                
        WHEN (
          consumer_order_mealtime = 'Dinner'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN logistics_order_state_history_picked_time - merchant_order_state_history_accepted_time - 180
        ELSE 0
      END
     )/SUM(
      CASE
        WHEN (
          consumer_order_mealtime = 'Dinner'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN 1
        ELSE 0
      END
    ) AS coe_mealtime_kpt_dinner,
    SUM(
      CASE
        WHEN (
          merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time
          AND consumer_order_mealtime = 'LateNight'
          AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time <= 180
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time
        WHEN (
          merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time
          AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time <= 180          
          AND consumer_order_mealtime = 'LateNight'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time                
        WHEN (
          consumer_order_mealtime = 'LateNight'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN logistics_order_state_history_picked_time - merchant_order_state_history_accepted_time - 180
        ELSE 0
      END
     )/SUM(
      CASE
        WHEN (
          consumer_order_mealtime = 'LateNight'
          AND logistics_order_state_history_picked_time > 0
          AND merchant_order_state_history_ready_time > 0
          AND logistics_order_state_history_arrived_time > 0
          AND merchant_order_state_history_accepted_time > 0
        ) THEN 1
        ELSE 0
      END
    ) AS coe_mealtime_kpt_latenight
  FROM composite_order_events
  WHERE "consumer_order_created_at" BETWEEN {{.start_timestamp}} AND {{.end_timestamp}} 
  AND consumer_order_delivery_mode IN ('DELIVERY', 'delivery')
  AND merchant_order_res_id in ({{.res_ids}})


filters:
  - all_order_states
  - completed_order_states
  - res_ids
  - start_timestamp
  - end_timestamp
  - mx_reject_order_state
  - mx_reject_reason_id