# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: restaurant_hourly_stats
identifier: my_feed_res_hourly_by_period
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub reporting page - funnel conversion | a2i, m2c, c2o metrics

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    CASE
      WHEN {{.agg_enum}} = 0 THEN DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:dd tz(Asia/Kolkata)',
        '1:DAYS'
      )    
      WHEN {{.agg_enum}} = 4 THEN CAST(hr AS STRING)
      WHEN {{.agg_enum}} IN (3, 2) THEN dt
      WHEN {{.agg_enum}} = 5 THEN DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:yyyy-MM tz(Asia/Kolkata)',
        '1:DAYS'
      )
      WHEN {{.agg_enum}} = 6 AND DATETIMECONVERT(
        "timestamp",
        '1:SECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:HH',
        '1:HOURS'
      ) IN ('07', '08', '09', '10') THEN 'Breakfast'
      WHEN {{.agg_enum}} = 6 AND DATETIMECONVERT(
        "timestamp",
        '1:SECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:HH tz(Asia/Kolkata)',
        '1:HOURS'
      ) IN ('11', '12', '13', '14', '15') THEN 'Lunch'
      WHEN {{.agg_enum}} = 6 AND DATETIMECONVERT(
        "timestamp",
        '1:SECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:HH tz(Asia/Kolkata)',
        '1:HOURS'
      ) IN ('16', '17', '18') THEN 'Snack'
      WHEN {{.agg_enum}} = 6 AND DATETIMECONVERT(
        "timestamp",
        '1:SECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:HH tz(Asia/Kolkata)',
        '1:HOURS'
      ) IN ('19', '20', '21', '22') THEN 'Dinner'
      WHEN {{.agg_enum}} = 6 AND DATETIMECONVERT(
        "timestamp",
        '1:SECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:HH tz(Asia/Kolkata)',
        '1:HOURS'
      ) IN ('23', '00', '01', '02', '03', '04', '05', '06') THEN 'LateNight'
      WHEN {{.agg_enum}} = 7 THEN DATETIMECONVERT(
        "timestamp",
        '1:SECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:EEE tz(Asia/Kolkata)',
        '1:DAYS'
      )          
      WHEN {{.agg_enum}} = 8 THEN DATETIMECONVERT(
        "timestamp",
        '1:SECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:e tz(Asia/Kolkata)',
        '1:DAYS'
      )                
      ELSE 'agg'
    END AS agg_level_str,
    MAX("timestamp"/1000) AS agg_level,
    SUM(menu_impressions) AS impressions,
    SUM(menu_opens)*100.0/SUM(menu_impressions) AS i2m,
    SUM(menu_opens) AS menu_opens,
    SUM(cart_created) AS cart_builds,
    SUM(cart_created)*100.0/SUM(menu_opens) AS m2c,    
    SUM(order_placed) AS order_placed,
    SUM(order_placed)*100.0/SUM(cart_created) AS c2o, 
    SUM(order_placed)*100.0/SUM(menu_opens) AS m2o,

    SUM(search_impressions) AS search_impressions,
    SUM(search_menu_opens) AS search_menu_opens,
    SUM(recommended_for_you_impressions) AS recommended_for_you_impressions,
    SUM(recommended_for_you_menu_opens) AS recommended_for_you_menu_opens,
    SUM(dish_or_cuisine_search_impressions) AS dish_or_cuisine_search_impressions,
    SUM(dish_or_cuisine_search_impressions) AS dish_or_cuisine_search_menu_opens,
    SUM(home_page_impressions) AS home_page_impressions,
    SUM(home_page_menu_opens) AS home_page_menu_opens,
    SUM(offers_page_impressions) AS offers_page_impressions,
    SUM(offers_page_menu_opens) AS offers_page_menu_opens,
    (
        SUM(search_impressions)
      - SUM(recommended_for_you_impressions)
      - SUM(dish_or_cuisine_search_impressions)
      - SUM(home_page_impressions)
      - SUM(offers_page_impressions)
    ) AS other_impressions,
    (
        SUM(search_menu_opens)
      - SUM(recommended_for_you_menu_opens)
      - SUM(dish_or_cuisine_search_impressions)
      - SUM(home_page_menu_opens)
      - SUM(offers_page_menu_opens)
    ) AS other_menu_opens,

    SUM(new_user_impressions) AS new_user_impressions,
    SUM(new_user_menu_opens) AS new_user_menu_opens,
    SUM(lapsed_user_impressions) AS lapsed_user_impressions,
    SUM(lapsed_menu_opens) AS lapsed_user_menu_opens,
    SUM(repeat_user_impressions) AS repeat_user_impressions,
    SUM(repeat_user_menu_opens) AS repeat_user_menu_opens,
    SUM(la_user_impressions) AS la_user_impressions,
    SUM(la_user_menu_opens) AS la_user_menu_opens,
    SUM(mm_user_impressions) AS mm_user_impressions,
    SUM(mm_menu_opens) AS mm_menu_opens,
    SUM(um_user_impressions) AS um_user_impressions,
    SUM(um_user_menu_opens) AS um_user_menu_opens,

    SUM(offline_hours) AS offline_seconds,
    SUM(actual_online_hours)*100.0/SUM(expected_online_hours) AS online_percentage,
    100.0 - SUM(actual_online_hours)*100.0/SUM(expected_online_hours) AS offline_percentage
  FROM restaurant_hourly_stats 
  WHERE res_id in (
    {{.res_ids}}
  )
  AND "timestamp" BETWEEN {{.start_timestamp_millis}} AND {{.end_timestamp_millis}} 
  GROUP BY agg_level_str

filters:
  - agg_enum
  - res_ids
  - start_timestamp_millis
  - end_timestamp_millis
  
