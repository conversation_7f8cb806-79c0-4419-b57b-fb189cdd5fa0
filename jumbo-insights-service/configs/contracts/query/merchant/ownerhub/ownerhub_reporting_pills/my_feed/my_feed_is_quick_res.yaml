# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: restaurant_dimension_mappings
identifier: my_feed_is_quick_res
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub my-feed get if quick pill needs to be shown or not

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT  
    MAX(is_quick_res) AS is_quick_res
  FROM restaurant_dimension_mappings
  WHERE res_id in ({{.res_ids}})

filters:
  - res_ids