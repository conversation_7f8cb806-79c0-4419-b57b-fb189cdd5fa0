# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_ads_analytics
identifier: my_feed_ba_hub_ads_by_product
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub reporting page - ads product type breakup

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    SUM(ad_orders) AS ad_orders,
    SUM(
      CASE
        WHEN product_type IN ('Visit Pack', 'Visit Pack Plus', 'PSP') THEN ad_orders
        ELSE 0
      END
    )*100.0/SUM(ad_orders) AS visit_pack_share,
    SUM(
      CASE
        WHEN product_type = 'Branding on search' THEN ad_orders
        ELSE 0
      END
    )*100.0/SUM(ad_orders) AS bos_share,    
    SUM(
      CASE
        WHEN product_type = 'Video Ads' THEN ad_orders
        ELSE 0
      END
    )*100.0/SUM(ad_orders) AS video_ads_share,
    SUM(
      CASE
        WHEN product_type IN ('Brand Tiles', 'Brand Tiles v2') THEN ad_orders
        ELSE 0
      END
    )*100.0/SUM(ad_orders) AS brand_tiles_share,
    SUM(
      CASE
        WHEN product_type IN ('DOTD') THEN ad_orders
        ELSE 0
      END
    )*100.0/SUM(ad_orders) AS dotd_share,
    SUM(
      CASE
        WHEN product_type IN ('Grow Maxx') THEN ad_orders
        ELSE 0
      END
    )*100.0/SUM(ad_orders) AS grow_maxx_share,
    SUM(
      CASE
        WHEN product_type IN ('DOTD', 'Brand Tiles', 'Brand Tiles v2', 'Video Ads', 'Branding on search', 'Visit Pack', 'Visit Pack Plus', 'PSP', 'Grow Maxx') THEN 0
        ELSE ad_orders
      END
    )*100.0/SUM(ad_orders) AS others_share
  FROM ba_hub_ads_analytics
  WHERE res_id in (
    {{.res_ids}}
  )
  AND "timestamp" BETWEEN {{.start_timestamp_millis}} AND {{.end_timestamp_millis}} 
  AND product_type != 'NA'

filters:
  - res_ids
  - start_timestamp_millis
  - end_timestamp_millis
  
