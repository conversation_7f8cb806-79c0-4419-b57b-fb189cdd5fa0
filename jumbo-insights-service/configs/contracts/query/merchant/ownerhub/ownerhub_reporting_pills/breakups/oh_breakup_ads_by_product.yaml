# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_ads_analytics
identifier: oh_breakup_ads_by_product
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query to power ads breakup view in ownerhub

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
        CASE 
        WHEN substr({{.resolution}}, 0, 1) = '1' then city_id
        WHEN substr({{.resolution}}, 1, 2) = '1' then brand_id
        WHEN substr({{.resolution}}, 2, 3) = '1' then res_id
        when substr({{.resolution}}, 3, 4) = '1' then merchant_id
        when substr({{.resolution}}, 4, 5) = '1' then zone_id
        when substr({{.resolution}}, 5, 6) = '1' then chain_id
        ELSE -999
    END as breakup_id,
    CASE 
        WHEN substr({{.resolution}}, 0, 1) = '1' then city_name
        WHEN substr({{.resolution}}, 1, 2) = '1' then brand_name
        WHEN substr({{.resolution}}, 2, 3) = '1' then res_name
        WHEN substr({{.resolution}}, 3, 4) = '1' then merchant_name
        WHEN substr({{.resolution}}, 5, 6) = '1' then chain_name
        ELSE 'bad_row'
    END as breakup_name,
    CASE
        WHEN substr({{.resolution}}, 2, 3) = '1' THEN CONCAT(subzone_name, city_name, '#')
        ELSE 'Overall'
    END AS subzone_city,    
    SUM(ad_orders) AS ad_orders,
    SUM(
      CASE
        WHEN product_type IN ('Visit Pack', 'Visit Pack Plus', 'PSP') THEN ad_orders
        ELSE 0
      END
    )*100.0/SUM(ad_orders) AS visit_pack_share,
    SUM(
      CASE
        WHEN product_type = 'Branding on search' THEN ad_orders
        ELSE 0
      END
    )*100.0/SUM(ad_orders) AS bos_share,    
    SUM(
      CASE
        WHEN product_type = 'Video Ads' THEN ad_orders
        ELSE 0
      END
    )*100.0/SUM(ad_orders) AS video_ads_share,
    SUM(
      CASE
        WHEN product_type IN ('Brand Tiles', 'Brand Tiles v2') THEN ad_orders
        ELSE 0
      END
    )*100.0/SUM(ad_orders) AS brand_tiles_share,
    SUM(
      CASE
        WHEN product_type IN ('DOTD') THEN ad_orders
        ELSE 0
      END
    )*100.0/SUM(ad_orders) AS dotd_share,
    SUM(
      CASE
        WHEN product_type IN ('Grow Maxx') THEN ad_orders
        ELSE 0
      END
    )*100.0/SUM(ad_orders) AS grow_maxx_share,
    SUM(
      CASE
        WHEN product_type IN ('DOTD', 'Brand Tiles', 'Brand Tiles v2', 'Video Ads', 'Branding on search', 'Visit Pack', 'Visit Pack Plus', 'PSP', 'Grow Maxx') THEN 0
        ELSE ad_orders
      END
    )*100.0/SUM(ad_orders) AS others_share,
    COUNT(DISTINCT res_id) AS res_count
  FROM ba_hub_ads_analytics
  WHERE res_id in (
    {{.res_ids}}
  )
  AND "timestamp" BETWEEN {{.start_timestamp_millis}} AND {{.end_timestamp_millis}} 
  AND product_type != 'NA'
  GROUP BY breakup_id, breakup_name, subzone_city

filters:
  - res_ids
  - start_timestamp_millis
  - end_timestamp_millis
  - resolution
  
