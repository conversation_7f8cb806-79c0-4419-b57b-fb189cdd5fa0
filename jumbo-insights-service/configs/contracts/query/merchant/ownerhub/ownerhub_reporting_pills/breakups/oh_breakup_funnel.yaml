# can be pinot/trino
query_backend: clickhouse
tenant: zanalytics
table: mx_enterprise_stats
identifier: oh_breakup_funnel
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query to power funnel databreakup view in ownerhub

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: > 
  with
  base as (
      SELECT
          CASE 
              WHEN substr({{.resolution}}, 1, 1) = '1' then city_id
              WHEN substr({{.resolution}}, 2, 1) = '1' then brand_id
              WHEN substr({{.resolution}}, 3, 1) = '1' then res_id
              when substr({{.resolution}}, 4, 1) = '1' then merchant_id
              when substr({{.resolution}}, 5, 1) = '1' then zone_id
              ELSE -999
          END as breakup_id,
          CASE 
              WHEN substr({{.resolution}}, 1, 1) = '1' then city_name
              WHEN substr({{.resolution}}, 2, 1) = '1' then brand_name
              WHEN substr({{.resolution}}, 3, 1) = '1' then res_name
              WHEN substr({{.resolution}}, 4, 1) = '1' then merchant_name
              WHEN substr({{.resolution}}, 5, 1) = '1' then zone_name
              ELSE 'bad_row'
          END as breakup_name,
          min("timestamp") as agg_level,
          MAX(
            CASE  
               WHEN substring({{.resolution}}, 3, 1) = 1 then concat(subzone_name,'#',city_name)
               ELSE 'Overall'
            END
          ) as subzone_city,
          sum(
            case
              when {{.funnel_event}} = 1 then impressions 
              when {{.funnel_event}} = 2 then menu_opens
              else 0
            end
          ) as funnel_events,
          sum(
            case
              when {{.funnel_event}} = 1 then search_menu_opens
              when {{.funnel_event}} = 2 then search_menu_opens
              else 0
            end
          ) as search_funnel_events,
          sum(
            case
              when {{.funnel_event}} = 1 then recommended_for_you_menu_opens
              when {{.funnel_event}} = 2 then recommended_for_you_menu_opens
              else 0
            end
          ) as recommended_for_you_funnel_events,
          sum(
            case
              when {{.funnel_event}} = 1 then dish_or_cuisine_menu_opens
              when {{.funnel_event}} = 2 then dish_or_cuisine_menu_opens
            end
          ) as dish_or_cuisine_funnel_events,
          sum(
            case
              when {{.funnel_event}} = 1 then home_page_menu_opens
              when {{.funnel_event}} = 2 then home_page_menu_opens
            end
          ) as home_page_funnel_events,
          sum(
            case
              when {{.funnel_event}} = 1 then offers_page_menu_opens
              when {{.funnel_event}} = 2 then offers_page_menu_opens
            end
          ) as offers_page_funnel_events,
          sum(
            case
              when {{.funnel_event}} = 1 then campaign_menu_opens
              when {{.funnel_event}} = 2 then campaign_menu_opens
            end
          ) as campaign_funnel_events,
          sum(
            case
              when {{.funnel_event}} = 1 then other_menu_opens
              when {{.funnel_event}} = 2 then other_menu_opens
            end
          ) as other_funnel_events,
          count(distinct res_id) as res_count
      FROM s3(
        's3://zanalytics-jumbo/insights_etls.db/mx_enterprise_stats/dt=*/period_type=*/dimension_type=*/*',
        'Parquet'
      )
      WHERE dt between {{.start_dt}} and {{.end_dt}}
      AND period_type = {{.period_type}}
      AND dimension_type = {{.dimension_type}}
      AND res_id in ({{.res_ids}})
      GROUP BY 1, 2

  )  
      
  SELECT 
    breakup_id,
    breakup_name,
    subzone_city,
    agg_level,
    res_count,
    funnel_events,
    search_funnel_events,
    recommended_for_you_funnel_events,
    dish_or_cuisine_funnel_events,
    home_page_funnel_events,
    offers_page_funnel_events,
    campaign_funnel_events,
    other_funnel_events
  FROM base

filters:
  - resolution
  - start_dt
  - end_dt
  - period_type
  - dimension_type
  - funnel_event
  - res_ids
