# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: oh_breakup_complaints
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query to power complaint databreakup view in ownerhub

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    CASE 
        WHEN substr({{.resolution}}, 0, 1) = '1' then city_id
        WHEN substr({{.resolution}}, 1, 2) = '1' then brand_id
        WHEN substr({{.resolution}}, 2, 3) = '1' then res_id
        when substr({{.resolution}}, 3, 4) = '1' then merchant_id
        when substr({{.resolution}}, 4, 5) = '1' then zone_id
        when substr({{.resolution}}, 5, 6) = '1' then chain_id
        ELSE -999
    END as breakup_id,
    CASE 
        WHEN substr({{.resolution}}, 0, 1) = '1' then city_name
        WHEN substr({{.resolution}}, 1, 2) = '1' then brand_name
        WHEN substr({{.resolution}}, 2, 3) = '1' then res_name
        WHEN substr({{.resolution}}, 3, 4) = '1' then merchant_name
        WHEN substr({{.resolution}}, 4, 5) = '1' then zone_name
        when substr({{.resolution}}, 5, 6) = '1' then chain_name
        ELSE 'bad_row'
    END as breakup_name,
    CASE
        WHEN substr({{.resolution}}, 2, 3) = '1' THEN CONCAT(subzone_name, city_name, '#')
        ELSE 'Overall'
    END AS subzone_city,
    MAX("timestamp"/1000) AS agg_level,

    sum(case
        when {{.complaint_type}} = 1 then supply_mx_sent_refund_pp
        when {{.complaint_type}} = 2 then winback_pp
        when {{.complaint_type}} = 3 then resolved_pp
        else supply_pp_ors 
    end) as poor_packaging_orders,

    SUM(case
        when {{.complaint_type}} = 1 then supply_mx_sent_refund_pq
        when {{.complaint_type}} = 2 then winback_pq
        when {{.complaint_type}} = 3 then resolved_pq
        else supply_pq_ors
    end) as poor_quality_orders,

    SUM(case
        when {{.complaint_type}} = 1 then supply_mx_sent_refund_wo
        when {{.complaint_type}} = 2 then winback_wo
        when {{.complaint_type}} = 3 then resolved_wo
        else supply_wo_ors
    end) as wrong_orders,

    SUM(case
        when {{.complaint_type}} = 1 then supply_mx_sent_refund_mi
        when {{.complaint_type}} = 2 then winback_mi
        when {{.complaint_type}} = 3 then resolved_mi
        else supply_mi_ors
    end) as missing_item_orders,

    COUNT(distinct res_id) as res_count
    
  FROM ba_hub_daily_stats 
  WHERE res_id in (
    {{.res_ids}}
  )
  AND "timestamp" BETWEEN {{.start_timestamp_millis}} AND {{.end_timestamp_millis}} 
  GROUP BY breakup_id, breakup_name, subzone_city

filters:
  - resolution
  - res_ids
  - start_timestamp_millis
  - end_timestamp_millis
  - complaint_type
