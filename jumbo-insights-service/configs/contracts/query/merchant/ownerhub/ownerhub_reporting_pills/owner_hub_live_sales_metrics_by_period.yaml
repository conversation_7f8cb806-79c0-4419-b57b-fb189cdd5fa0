# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: owner_hub_live_sales_metrics_by_period
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub my-feed page live sales tracking widgets

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    CASE
      WHEN {{.agg_level_enum}} = 4 THEN CAST(merchant_order_created_ist_hour AS STRING)
      WHEN {{.agg_level_enum}} = 3 THEN CAST(merchant_order_created_ist_dt AS STRING)
      WHEN {{.agg_level_enum}} = 2 THEN CAST(merchant_order_created_ist_dt AS STRING)
      WHEN {{.agg_level_enum}} = 5 THEN CONCAT(
          merchant_order_created_ist_year, merchant_order_created_ist_month, '-'
        )
      ELSE 'agg'
    END AS agg_level_str,
    MAX(merchant_order_created_at) AS agg_level,
    SUM(
      CASE 
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN merchant_order_total_cost
        ELSE 0
      END
    ) AS total_sales,
    SUM(
      CASE
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN 1
        ELSE 0
      END
    ) AS total_orders,
    SUM(
      CASE 
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN merchant_order_total_cost
        ELSE 0
      END
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN 1
        ELSE 0
      END
    ) AS avg_order_value,
    FLOOR(MAX(ingestion_time)/1000000000) AS ingestion_time
  FROM composite_order_events
  WHERE merchant_order_created_at BETWEEN {{.start_timestamp}} 
                                    AND {{.end_timestamp}}
  AND merchant_order_created_ist_hour <= {{.data_bw_zero_to_x_hrs}}
  AND consumer_order_delivery_mode IN ('DELIVERY', 'delivery')
  AND merchant_order_res_id in ({{.res_ids}})
  GROUP BY agg_level_str

filters:
  - agg_level_enum
  - completed_order_states
  - res_ids
  - start_timestamp
  - end_timestamp
  - data_bw_zero_to_x_hrs