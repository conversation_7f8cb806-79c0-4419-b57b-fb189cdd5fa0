# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: owner_hub_metric_overview_summary
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub my performance - t-1 metrics

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    SUM(impressions) AS impressions,
    SUM(menu_opens) AS menu_opens,
    SUM(menu_opens)*100.0/SUM(impressions) AS i2m,
    SUM(cart_builts) AS cart_builts,
    SUM(cart_builts)*100.0/SUM(menu_opens) AS m2c,
    SUM(total_orders) AS orders_placed,
    SUM(total_orders)*100.0/SUM(cart_builts) AS c2o,
    SUM(delivered_orders)*100.0/SUM(total_orders) AS delivered_orders,

    SUM(composite_mx_rejected_orders)*100.00/SUM(total_orders) AS rejected_orders,
    SUM(composite_mx_lost_sales) AS rejected_sales,
    (
      SUM(supply_mx_sent_refund) 
    + SUM(supply_mx_attr_non_refund)
    )*100.00/SUM(total_orders) AS complaint_orders,
    SUM(low_rated_orders)*100.0/SUM(total_orders) AS poor_rated_orders,
    SUM(actual_visibility)*100.0/SUM(expected_visibility) AS avg_online_percentage,
    SUM(last_week_estimated_lost_sales/7) AS estimated_lost_sales,

    SUM(kitchent_prep_time)*1.00/SUM(kitchent_prep_time_not_null) AS avg_kpt,
    SUM(kpt_delayed_orders)*100.0/SUM(delivered_orders) AS kpt_delayed_orders,
    SUM(for_accurate_orders)*100.0/SUM(total_orders) AS for_accuracy,
    (
      SUM(total_orders) 
    - SUM(for_marked_orders)
    )*100.0/SUM(total_orders) AS not_for_marked_orders_perc_share,
    SUM(
      CASE
        WHEN handover_time_breach_orders > 0 THEN handover_time_breach_orders
        ELSE 0
      END
    )*100.0/SUM(total_orders) AS rider_handshake_delayed_orders,

    SUM(unique_user_count) AS total_users,
    SUM(new_users) AS new_users,
    SUM(repeat_users) AS repeat_users,
    SUM(lapsed_users) AS lapsed_users,
    
    SUM(composite_mx_offer_sales) + SUM(composite_discount_applied_amount) AS sales_from_offers,
    (
        SUM(composite_mx_offer_sales) 
      + SUM(composite_discount_applied_amount)
    )*100.0/(
        SUM(merchant_received_amount) 
      + SUM(composite_discount_applied_amount)
    ) AS sales_from_offers_perc_share,
    SUM(composite_discount_applied_amount) AS discount_given,
    SUM(composite_discount_applied_amount)/SUM(delivered_orders) AS discount_given_per_order,
    SUM(composite_mx_offer_orders) AS orders_from_offers,
    SUM(composite_mx_offer_orders)*100.0/SUM(delivered_orders) AS orders_from_offers_perc_share,
    SUM(composite_discount_applied_amount)*100/(
        SUM(composite_mx_offer_sales) 
      + SUM(composite_discount_applied_amount)
    ) AS effective_discount,

    SUM(delivered_ad_impression) AS ads_impressions,
    SUM(delivered_ad_impression)*100.0/SUM(impressions) AS ads_impressions_perc_share,
    SUM(ads_cv) AS sales_from_ads,
    SUM(ads_cv)*100.0/SUM(delivered_merchant_received_amount) AS sales_from_ads_perc_share,
    SUM(billed_revenue) AS ads_spend,
    SUM(billed_revenue)/SUM(ad_menu_opens) AS avg_cost_per_click,
    (SUM(ads_cv) - SUM(grow_max_ads_cv))/(SUM(billed_revenue) - SUM(grow_max_billed_revenue)) AS ads_roi,
    MAX(ingestion_time) AS ingestion_time
  FROM {{.table}}
  WHERE "timestamp" BETWEEN {{.start_timestamp_millis}} 
                        AND {{.end_timestamp_millis}}
  AND res_id in ({{.res_ids}})

filters:
  - start_timestamp_millis
  - end_timestamp_millis
  - res_ids
