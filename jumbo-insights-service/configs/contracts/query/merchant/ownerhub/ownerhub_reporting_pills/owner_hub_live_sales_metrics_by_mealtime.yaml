# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: owner_hub_live_sales_metrics_by_mealtime
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub my-feed page live sales tracking widgets

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    CASE
      WHEN merchant_order_created_ist_hour BETWEEN 7 AND 10 THEN 'breakfast'
      WHEN merchant_order_created_ist_hour BETWEEN 11 AND 15 THEN 'lunch'
      WHEN merchant_order_created_ist_hour BETWEEN 16 AND 18 THEN 'snacks'
      WHEN merchant_order_created_ist_hour BETWEEN 19 AND 22 THEN 'dinner'
      ELSE 'late_night'
    END AS agg_level_str,
    MAX(merchant_order_created_at) AS agg_level,
    SUM(
      CASE 
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN merchant_order_total_cost
        ELSE 0
      END
    ) AS total_sales,
    SUM(
      CASE
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN 1
        ELSE 0
      END
    ) AS total_orders,
    SUM(
      CASE 
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN merchant_order_total_cost
        ELSE 0
      END
    )*100.0/SUM(
      CASE
        WHEN merchant_order_state in ({{.completed_order_states}}) THEN 1
        ELSE 0
      END
    ) AS avg_order_value,
    FLOOR(MAX(ingestion_time)/1000000000) AS ingestion_time
  FROM composite_order_events
  WHERE merchant_order_created_at BETWEEN {{.start_timestamp}} 
                                      AND {{.end_timestamp}}
  AND consumer_order_delivery_mode IN ('DELIVERY', 'delivery')
  AND merchant_order_res_id in ({{.res_ids}})
  GROUP BY agg_level_str

filters:
  - completed_order_states
  - res_ids
  - start_timestamp
  - end_timestamp