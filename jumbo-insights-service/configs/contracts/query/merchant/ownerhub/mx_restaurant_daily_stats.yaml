# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: restaurant_daily_stats
identifier: mx_restaurant_daily_stats
audit:
  author_email: inti.<PERSON><PERSON><PERSON>@zomato.com
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: all d-1 lag metrics
# Time in second
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 160
# Dimensions
columns:
  - name: res_id
  - name: total_orders
    func: sum
    source_column: total_orders
  - name: total_complaints
    func: sum
    source_column: total_complaints
  - name: delivery_rating
    func: max
    source_column: delivery_rating
  - name: total_users
    func: sum
    source_column: total_users
  - name: new_users
    func: sum
    source_column: new_users
# Group by columns
aggregations:
  - res_id
filters:
  - res_id
