# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: mx_rejected_orders
audit:
  author_email: inti.<PERSON><PERSON><PERSON>@zomato.com
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Total Rejected orders and Lost sales

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 160
# Dimensions
columns:
  - name: merchant_order_res_id
  - name: total_lost_sales
    func: sum
    source_column: merchant_order_total_cost
  - name: total_rejected_orders
    func: count
    source_column: consumer_order_id
# Group by columns
aggregations:
  - merchant_order_res_id
filters:
  - merchant_order_res_id
  - merchant_order_state
  - merchant_order_reject_reason_id
