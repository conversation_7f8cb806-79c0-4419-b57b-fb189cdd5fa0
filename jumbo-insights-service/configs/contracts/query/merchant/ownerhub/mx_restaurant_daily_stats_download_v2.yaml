# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: restaurant_daily_stats
identifier: mx_restaurant_daily_stats_download_v2
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: all d-1 lag metrics
# Time in second
caching_ttl: 1500
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT res_id, SUM(total_orders) as total_orders, SUM(total_complaints) as total_complaints,
  MAX(delivery_rating) as delivery_rating,
  SUM(total_users) as total_users,
  SUM(new_users) as new_users,
  CASE
    WHEN {{.category_enum}} = 1 THEN concat(concat(year("timestamp", 'Asia/Kolkata') , month("timestamp", 'Asia/Kolkata'), '_'), day("timestamp", 'Asia/Kolkata'), '_')
    WHEN {{.category_enum}} = 2 THEN concat(year("timestamp", 'Asia/Kolkata') , weekOfYear("timestamp", 'Asia/Kolkata'), '_')
    WHEN {{.category_enum}} = 3 THEN concat(year("timestamp", 'Asia/Kolkata') , month("timestamp", 'Asia/Kolkata'), '_')
    ELSE ''
  END AS group_category
  FROM restaurant_daily_stats
  WHERE "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
  AND group_category != ''
  AND res_id IN ({{.res_id}})
  GROUP BY res_id, group_category
filters:
  - res_id
  - start_time
  - end_time
  - category_enum
