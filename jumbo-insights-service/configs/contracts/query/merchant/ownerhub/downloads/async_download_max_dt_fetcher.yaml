 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: async_download_max_dt_fetcher
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Max Date Checker for reports downloads
# TTL for cache, Time in seconds
caching_ttl: 1500
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 200
# contract type
type: sql
sql: > 
  select 
    max(dt) as max_dt
  from ba_hub_daily_stats 