 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: res_cross_dt_level_reporting_download
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Download reporting app from partnerapp/dashboard
# TTL for cache, Time in seconds
caching_ttl: 1500
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
  CASE
      WHEN {{.category_enum}} = 1 THEN concat(concat(year("timestamp", 'Asia/Kolkata') , month("timestamp", 'Asia/Kolkata'), '_'), day("timestamp", 'Asia/Kolkata'), '_')
      WHEN {{.category_enum}} = 2 THEN concat(
        CASE
            WHEN month("timestamp", 'Asia/Kolkata') = 12 AND weekOfYear("timestamp", 'Asia/Kolkata') = 1 THEN CAST(year("timestamp", 'Asia/Kolkata') + 1 AS INT)
            ELSE year("timestamp", 'Asia/Kolkata')
        END,
        weekOfYear("timestamp", 'Asia/Kolkata'), 
        '_'
      )
      WHEN {{.category_enum}} = 3 THEN concat(year("timestamp", 'Asia/Kolkata') , month("timestamp", 'Asia/Kolkata'), '_')
      ELSE ''
  END AS group_category,
  res_id,
  res_name, 
  subzone_name, 
  city_name,

  sum(delivered_orders) as delivered_orders,
  sum(delivered_merchant_received_amount) as total_sales,
  sum(delivered_merchant_received_amount)/sum(delivered_orders) as average_order_value,
  sum(delivered_merchant_received_amount)*100.0/sum(similar_res_sales) as market_share,

  avg(res_delivery_rating_till_date) as average_rating,
  sum(rated_orders)*1.0 as rated_orders,
  sum(bad_orders) as bad_orders,
  sum(composite_mx_rejected_orders) as rejected_orders,
  sum(kpt_delayed_orders) as kpt_delayed_orders,
  sum(low_rated_orders) as low_rated_orders,
  sum(supply_mx_sent_refund) + sum(supply_mx_attr_non_refund) as customer_complaints,

  sum(supply_mx_sent_refund) as mx_sent_refund_compaints,
  sum(supply_mx_attr_non_refund) as mx_attr_non_refund_complaints,

  sum(supply_mx_sent_refund_pp) + sum(supply_mx_attr_non_refund_pp) as poor_packaging,
  sum(supply_mx_sent_refund_pq) + sum(supply_mx_attr_non_refund_pq) as poor_quality,
  sum(supply_mx_sent_refund_wo) + sum(supply_mx_attr_non_refund_wo) as wrong_orders,
  sum(supply_mx_sent_refund_mi) + sum(supply_mx_attr_non_refund_mi) as missing_items,

  sum(supply_wimo_ors) + sum(supply_fnd_ors) + sum(supply_delay_ors) as self_logs_other_ors,

  sum(composite_mx_lost_sales) as lost_sales,
  sum(actual_visibility)*100.00/sum(expected_visibility) as online_percentage,
  (sum(expected_visibility) - sum(actual_visibility))/3600 as offline_hrs,
  sum(kitchent_prep_time)*1.00/sum(kitchent_prep_time_not_null) as kpt,
  sum(for_accurate_orders)*100.0/sum(total_orders) as for_accuracy,

  sum(impressions) as impressions,
  sum(menu_opens)*100.0/sum(impressions) as impressions_to_menu,
  sum(menu_opens) as menu_opens,
  sum(cart_builts)*100.0/sum(menu_opens) as menu_to_cart,
  sum(cart_builts) as cart_builds,
  sum(total_orders)*100.0/sum(cart_builts) as cart_to_order,
  sum(total_orders) as orders_placed,

  sum(new_users) as new_user_orders,
  sum(repeat_users) as repeat_user_orders,
  sum(lapsed_users) as lapsed_user_orders,
  sum(breakfast_orders) as breakfast_orders,
  sum(lunch_orders) as lunch_orders,
  sum(snacks_orders) as snacks_orders,
  sum(dinner_orders) as dinner_orders,
  sum(late_night_orders) as late_night_orders,

  sum(ads_cv) as sales_from_ads,
  sum(ad_menu_opens)*100.0/sum(delivered_ad_impression) as ads_ctr,
  sum(delivered_ad_impression) as ads_impressions,
  sum(ad_menu_opens) as ads_menu_opens,
  sum(ad_orders) as ads_orders,
  sum(billed_revenue) as ads_spend,
  (sum(ads_cv) - sum(grow_max_ads_cv))/(sum(billed_revenue) - sum(grow_max_billed_revenue)) as ads_roi,

  sum(composite_mx_offer_sales) as orders_with_offers,
  sum(composite_mx_offer_orders) as composite_mx_offer_orders,
  sum(composite_discount_applied_amount) as discount_given,
  sum(composite_discount_applied_amount)*100.0/sum(composite_mx_offer_sales) as effective_discount

  FROM {{.table}} 
  WHERE "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
  AND res_id IN ({{.res_ids}})

  GROUP BY group_category, res_id, res_name, subzone_name, city_name

filters:
  - res_ids
  - category_enum
  - start_time
  - end_time

