 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: res_cross_dt_cross_item_level_reporting_download
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Download reporting app from partnerapp/dashboard
# TTL for cache, Time in seconds
caching_ttl: 1500
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 200
# contract type
type: sql
sql: > 
  SELECT
  CASE
      WHEN {{.category_enum}} = 1 THEN concat(concat(year("merchant_item_revenue_created_at", 'Asia/Kolkata') , month("merchant_item_revenue_created_at", 'Asia/Kolkata'), '_'), day("merchant_item_revenue_created_at", 'Asia/Kolkata'), '_')
      WHEN {{.category_enum}} = 2 THEN concat(
        CASE
            WHEN month("merchant_item_revenue_created_at", 'Asia/Kolkata') = 12 AND weekOfYear("merchant_item_revenue_created_at", 'Asia/Kolkata') = 1 THEN CAST(year("merchant_item_revenue_created_at", 'Asia/Kolkata') + 1 AS INT)
            ELSE year("merchant_item_revenue_created_at", 'Asia/Kolkata')
        END,
        weekOfYear("merchant_item_revenue_created_at", 'Asia/Kolkata'), 
        '_'
      )
      WHEN {{.category_enum}} = 3 THEN concat(year("merchant_item_revenue_created_at", 'Asia/Kolkata') , month("merchant_item_revenue_created_at", 'Asia/Kolkata'), '_')
      ELSE ''
  END AS group_category,

  res_id AS res_id, 
  item_name,
  item_category,
  item_sub_category AS item_subcategory,

  sum(item_quantity) AS item_quantity_sold,
  sum(item_revenue)/sum(item_quantity) AS unit_cost_of_item,
  sum(orders_containing_item) AS orders_with_item,
  sum(item_quantity)/sum(orders_containing_item) AS item_quantity_per_order,
  avg(rating_till_date) AS item_rating

  FROM mx_dish_items_performance  
  WHERE merchant_item_revenue_created_at BETWEEN {{.start_dt}} AND {{.end_dt}}
  AND res_id IN ({{.res_ids}})

  GROUP BY group_category, res_id, item_name, item_category, item_subcategory
filters:
  - res_ids
  - start_dt
  - end_dt
  - category_enum
