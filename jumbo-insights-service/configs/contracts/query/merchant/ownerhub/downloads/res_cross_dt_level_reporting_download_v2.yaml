 # can be pinot/trino
query_backend: trino
tenant: Zomato
table: insights_etls.ba_hub_analytics
identifier: res_cross_dt_level_reporting_download_v2

audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Download reporting app from partnerapp/dashboard v2
# TTL for cache, Time in seconds
caching_ttl: 1500
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 200
# contract type
type: sql
sql: > 
  SELECT
    CASE
      WHEN {{.category_enum}} = 1 THEN
      date_format(from_unixtime("timestamp" / 1000), '%d/%m/%Y')
      WHEN {{.category_enum}} = 2 THEN
        concat(
          'Week ',
          cast(week_of_year(date_trunc('week', from_unixtime("timestamp" / 1000))) as varchar),
          ', ',
          CASE
            WHEN month(date_trunc('week', from_unixtime("timestamp" / 1000))) = 12
                AND week_of_year(date_trunc('week', from_unixtime("timestamp" / 1000))) = 1
              THEN cast(year(date_trunc('week', from_unixtime("timestamp" / 1000))) + 1 as varchar)
            ELSE cast(year(date_trunc('week', from_unixtime("timestamp" / 1000))) as varchar)
          END
        )
      WHEN {{.category_enum}} = 3 THEN
        date_format(from_unixtime("timestamp" / 1000), '%b %Y')
      ELSE ''
          END AS group_category,
    res_id,
    res_name,
    subzone_name,
    city_name,
    COALESCE(sum(delivered_orders), 0) as delivered_orders,
    COALESCE(sum(delivered_merchant_received_amount), 0) as total_sales,
    COALESCE(sum(delivered_merchant_received_amount) / nullif(sum(delivered_orders), 0), 0) as average_order_value,
    COALESCE(sum(delivered_merchant_received_amount) * 100.0 / nullif(sum(similar_res_sales), 0), 0) as market_share,
    COALESCE(avg(res_delivery_rating_till_date), 0) as average_rating,
    COALESCE(sum(rated_orders), 0) as rated_orders,
    COALESCE(sum(bad_orders), 0) as bad_orders,
    COALESCE(sum(composite_mx_rejected_orders), 0) as rejected_orders,
    COALESCE(sum(kpt_delayed_orders), 0) as kpt_delayed_orders,
    COALESCE(sum(low_rated_orders), 0) as low_rated_orders,
    COALESCE(sum(supply_mx_sent_refund) + sum(supply_mx_attr_non_refund), 0) as customer_complaints,
    COALESCE(sum(supply_mx_sent_refund), 0) as mx_sent_refund_complaints,
    COALESCE(sum(supply_mx_attr_non_refund), 0) as mx_attr_non_refund_complaints,
    COALESCE(sum(supply_mx_sent_refund_pp) + sum(supply_mx_attr_non_refund_pp), 0) as poor_packaging,
    COALESCE(sum(supply_mx_sent_refund_pq) + sum(supply_mx_attr_non_refund_pq), 0) as poor_quality,
    COALESCE(sum(supply_mx_sent_refund_wo) + sum(supply_mx_attr_non_refund_wo), 0) as wrong_orders,
    COALESCE(sum(supply_mx_sent_refund_mi) + sum(supply_mx_attr_non_refund_mi), 0) as missing_items,
    COALESCE(sum(supply_wimo_ors) + sum(supply_fnd_ors) + sum(supply_delay_ors), 0) as self_logs_other_ors,
    COALESCE(sum(composite_mx_lost_sales), 0) as lost_sales,
    COALESCE(sum(actual_visibility) * 100.00 / nullif(sum(expected_visibility), 0), 0) as online_percentage,
    COALESCE((sum(expected_visibility) - sum(actual_visibility)) / 3600, 0) as offline_hrs,
    COALESCE(sum(kitchent_prep_time) * 1.00 / nullif(sum(kitchent_prep_time_not_null), 0), 0) as kpt,
    COALESCE(sum(for_accurate_orders) * 100.0 / nullif(sum(total_orders), 0), 0) as for_accuracy,
    COALESCE(sum(impressions), 0) as impressions,
    COALESCE(sum(menu_opens) * 100.0 / nullif(sum(impressions), 0), 0) as impressions_to_menu,
    COALESCE(sum(menu_opens), 0) as menu_opens,
    COALESCE(sum(cart_builts) * 100.0 / nullif(sum(menu_opens), 0), 0) as menu_to_cart,
    COALESCE(sum(cart_builts), 0) as cart_builds,
    COALESCE(sum(total_orders) * 100.0 / nullif(sum(cart_builts), 0), 0) as cart_to_order,
    COALESCE(sum(total_orders), 0) as orders_placed,
    COALESCE(sum(new_users), 0) as new_user_orders,
    COALESCE(sum(repeat_users), 0) as repeat_user_orders,
    COALESCE(sum(lapsed_users), 0) as lapsed_user_orders,
    COALESCE(sum(breakfast_orders), 0) as breakfast_orders,
    COALESCE(sum(lunch_orders), 0) as lunch_orders,
    COALESCE(sum(snacks_orders), 0) as snacks_orders,
    COALESCE(sum(dinner_orders), 0) as dinner_orders,
    COALESCE(sum(late_night_orders), 0) as late_night_orders,
    COALESCE(sum(ads_cv), 0) as sales_from_ads,
    COALESCE(sum(ad_menu_opens) * 100.0 / nullif(sum(delivered_ad_impression), 0), 0) as ads_ctr,
    COALESCE(sum(delivered_ad_impression), 0) as ads_impressions,
    COALESCE(sum(ad_menu_opens), 0) as ads_menu_opens,
    COALESCE(sum(ad_orders), 0) as ads_orders,
    COALESCE(sum(billed_revenue), 0) as ads_spend,
    COALESCE(sum(ad_gmv) / nullif(sum(billed_revenue), 0), 0) as ads_roi,
    COALESCE(sum(composite_mx_offer_sales), 0) as orders_with_offers,
    COALESCE(sum(composite_mx_offer_orders), 0) as composite_mx_offer_orders,
    COALESCE(sum(composite_discount_applied_amount), 0) as discount_given,
    COALESCE(sum(composite_discount_applied_amount) * 100.0 / nullif(sum(composite_mx_offer_sales), 0), 0) as effective_discount
  FROM 
    insights_etls.ba_hub_analytics
  WHERE
      dt BETWEEN date_format(from_unixtime(cast({{.start_timestamp}} AS BIGINT)) , '%Y%m%d')AND date_format(from_unixtime(cast({{.end_timestamp}} AS BIGINT)) , '%Y%m%d')
      AND res_id IN ({{.res_ids}})
  GROUP BY 1, 2, 3, 4, 5
  ORDER BY res_id

filters:
  - res_ids
  - category_enum
  - start_timestamp
  - end_timestamp
