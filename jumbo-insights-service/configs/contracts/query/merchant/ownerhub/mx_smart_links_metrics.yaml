query_backend: pinot
tenant: Zomato
table: mx_smart_links_metrics
identifier: mx_smart_link_stats
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Insights for mx smart link
# TTL for cache, Time in seconds
caching_ttl: 3600 # one hour
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT sum(menu_opens) as menu_opens,
    sum(cart_builds) as cart_builds,
    sum(order_makes) as order_makes,
    sum(res_not_serviceable) as res_not_serviceable,
    user_type
  FROM mx_smart_links_metrics
  WHERE "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
  AND agg_level = {{.agg_level}}
  AND shareable_id = {{.shareable_id}}
  GROUP BY user_type
filters:
  - start_time
  - end_time
  - shareable_id
  - agg_level
