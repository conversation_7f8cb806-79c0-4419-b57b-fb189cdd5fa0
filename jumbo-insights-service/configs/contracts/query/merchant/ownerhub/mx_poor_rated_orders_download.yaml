# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: mx_poor_rated_orders_download
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Total poor rated orders

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT merchant_order_res_id, COUNT(consumer_order_id) as total_poor_rated_orders,
  weekOfYear("consumer_order_created_at"*1000, 'Asia/Kolkata') as ist_isoweek,
  day("consumer_order_created_at"*1000, 'Asia/Kolkata')  as ist_day,
  month("consumer_order_created_at"*1000, 'Asia/Kolkata')  as ist_month,
  year("consumer_order_created_at"*1000, 'Asia/Kolkata')  as ist_year
  FROM composite_order_events
  WHERE consumer_order_created_at BETWEEN {{.start_time}} AND {{.end_time}}
  AND merchant_order_res_id IN ({{.merchant_order_res_id}})
  AND consumer_order_rating IN ({{.consumer_order_rating}})
  GROUP BY merchant_order_res_id, ist_isoweek, ist_day, ist_month, ist_year
filters:
  - merchant_order_res_id
  - consumer_order_rating
  - start_time
  - end_time
