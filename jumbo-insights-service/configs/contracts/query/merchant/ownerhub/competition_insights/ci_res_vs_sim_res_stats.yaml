# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: ci_res_vs_sim_res_stats
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: get res level sales for sim res to calculate ranks

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    sum(
      case
        when res_id = {{.res_id}} then merchant_received_amount
        else 0
      end
    ) as your_sales,
    percentile(
      case
        when res_id != {{.res_id}} then merchant_received_amount
        else 0
      end,
      90
    ) as sim_res_sales,

    sum(
      case
        when res_id = {{.res_id}} then total_orders
        else 0
      end
    ) as your_orders,
    percentile(
      case
        when res_id != {{.res_id}} then total_orders
        else 0
      end,
      90
    ) as sim_res_orders,

    sum(
      case
        when res_id = {{.res_id}} then merchant_received_amount
        else 0
      end
    )/sum(
      case
        when res_id = {{.res_id}} then total_orders
        else 0
      end
    ) as your_avg_order_value,
    sum(
      case
        when res_id != {{.res_id}} then merchant_received_amount
        else 0
      end
    )/sum(
      case
        when res_id != {{.res_id}} then total_orders
        else 0
      end
    ) as sim_res_avg_order_value,

    sum(
      case
        when res_id = {{.res_id}} then menu_opens
        else 0
      end
    ) as menu_opens,
    percentile(
      case
        when res_id != {{.res_id}} then menu_opens
        else 0
      end,
      90
    ) as sim_res_menu_opens,

    sum(
      case
        when res_id = {{.res_id}} then total_orders
        else 0
      end
    )*100.0/sum(
      case
        when res_id = {{.res_id}} then menu_opens
        else 0
      end
    ) as your_menu_to_order,
    sum(
      case
        when res_id != {{.res_id}} then total_orders
        else 0
      end
    )*100.0/sum(
      case
        when res_id != {{.res_id}} then menu_opens
        else 0
      end
    ) as sim_res_menu_to_order,

    sum(
      case
        when res_id = {{.res_id}} then cart_builts
        else 0
      end
    )*100.0/sum(
      case
        when res_id = {{.res_id}} then menu_opens
        else 0
      end
    ) as your_menu_to_cart,
    sum(
      case
        when res_id != {{.res_id}} then cart_builts
        else 0
      end
    )*100.0/sum(
      case
        when res_id != {{.res_id}} then menu_opens
        else 0
      end
    ) as sim_res_menu_to_cart,

    sum(
      case
        when res_id = {{.res_id}} then total_orders
        else 0
      end
    )*100.0/sum(
      case
        when res_id = {{.res_id}} then cart_builts
        else 0
      end
    ) as your_cart_to_order,
    sum(
      case
        when res_id != {{.res_id}} then total_orders
        else 0
      end
    )*100.0/sum(
      case
        when res_id != {{.res_id}} then cart_builts
        else 0
      end
    ) as sim_res_cart_to_order,

    sum(
      case
        when res_id = {{.res_id}} then ad_menu_opens
        else 0
      end
    )*100.0/sum(
      case
        when res_id = {{.res_id}} then delivered_ad_impression
        else 0
      end
    ) as your_ads_ctr,
    sum(
      case
        when res_id != {{.res_id}} then ad_menu_opens
        else 0
      end
    )*100.0/sum(
      case
        when res_id != {{.res_id}} then delivered_ad_impression
        else 0
      end
    ) as sim_res_ads_ctr,

    sum(
      case
        when res_id = {{.res_id}} then ad_menu_opens
        else 0
      end
    ) as your_ad_menu_opens,
    percentile(
      case
        when res_id != {{.res_id}} then ad_menu_opens
        else 0
      end,
      90
    ) as sim_res_ad_menu_opens,

    sum(
      case
        when res_id = {{.res_id}} then composite_mx_offer_orders
        else 0
      end
    )*100.0/sum(
      case
        when res_id = {{.res_id}} then delivered_orders
        else 0
      end
    ) as your_orders_from_offers_share,
    sum(
      case
        when res_id != {{.res_id}} then composite_mx_offer_orders
        else 0
      end
    )*100.0/sum(
      case
        when res_id != {{.res_id}} then delivered_orders
        else 0
      end
    ) as sim_res_orders_from_offers_share,

    sum(
      case
        when res_id = {{.res_id}} then composite_discount_applied_amount
        else 0
      end
    )/sum(
      case
        when res_id = {{.res_id}} then total_orders
        else 0
      end
    ) as your_discount_given_per_order,
    sum(
      case
        when res_id != {{.res_id}} then composite_discount_applied_amount
        else 0
      end
    )/sum(
      case
        when res_id != {{.res_id}} then total_orders
        else 0
      end
    ) as sim_res_discount_given_per_order,

    sum(
      case
        when res_id = {{.res_id}} then composite_discount_applied_amount
        else 0
      end
    )*100.0/sum(
      case
        when res_id = {{.res_id}} then bill_subtotal
        else 0
      end
    ) as your_effective_discount,
    sum(
      case
        when res_id != {{.res_id}} then composite_discount_applied_amount
        else 0
      end
    )*100.0/sum(
      case
        when res_id != {{.res_id}} then bill_subtotal
        else 0
      end
    ) as sim_res_effective_discount, 

    sum(
      case
        when res_id = {{.res_id}} then items_with_image
        else 0
      end
    )*100.0/sum(
      case
        when res_id = {{.res_id}} then total_items
        else 0
      end
    ) as your_image_coverage,
    sum(
      case
        when res_id != {{.res_id}} then items_with_image
        else 0
      end
    )*100.0/sum(
      case
        when res_id != {{.res_id}} then total_items
        else 0
      end
    ) as sim_res_image_coverage,

    sum(
      case
        when res_id = {{.res_id}} then packaging_charges
        else 0
      end
    )*100.0/sum(
      case
        when res_id = {{.res_id}} then total_orders
        else 0
      end
    ) as your_packaging_charge,
    sum(
      case
        when res_id != {{.res_id}} then packaging_charges
        else 0
      end
    )*100.0/sum(
      case
        when res_id != {{.res_id}} then total_orders
        else 0
      end
    ) as sim_res_packaging_charge,

    sum(
      case
        when res_id = {{.res_id}} then items_with_description
        else 0
      end
    )/sum(
      case
        when res_id = {{.res_id}} then total_items
        else 0
      end
    ) as your_description_coverage,
    sum(
      case
        when res_id != {{.res_id}} then items_with_description
        else 0
      end
    )/sum(
      case
        when res_id != {{.res_id}} then total_items
        else 0
      end
    ) as sim_res_description_coverage,

    sum(
      case
        when res_id = {{.res_id}} then total_items
        else 0
      end
    )/sum(
      case
        when res_id = {{.res_id}} then 1
        else 0
      end
    ) as your_total_items,
    sum(
      case
        when res_id != {{.res_id}} then total_items
        else 0
      end
    )/sum(
      case
        when res_id != {{.res_id}} then 1
        else 0
      end      
    ) as sim_res_total_items,

    sum(
      case
        when res_id = {{.res_id}} then supply_mx_sent_refund + supply_mx_attr_non_refund
        else 0
      end
    )*100.0/sum(
      case
        when res_id = {{.res_id}} then total_orders
        else 0
      end
    ) as your_complaints,
    sum(
      case
        when res_id != {{.res_id}} then supply_mx_sent_refund + supply_mx_attr_non_refund
        else 0
      end
    )*100.0/sum(
      case
        when res_id != {{.res_id}} then total_orders
        else 0
      end
    ) as sim_res_complaints,

    sum(
      case
        when res_id = {{.res_id}} then composite_mx_rejected_orders
        else 0
      end
    )*100.0/sum(
      case
        when res_id = {{.res_id}} then total_orders
        else 0
      end
    ) as your_rejections,
    sum(
      case
        when res_id != {{.res_id}} then composite_mx_rejected_orders
        else 0
      end
    )*100.0/sum(
      case
        when res_id != {{.res_id}} then total_orders
        else 0
      end
    ) as sim_res_rejections,

    sum(
      case
        when res_id = {{.res_id}} then low_rated_orders
        else 0
      end
    )*100.0/sum(
      case
        when res_id = {{.res_id}} then total_orders
        else 0
      end
    ) as your_poor_rated_orders,
    sum(
      case
        when res_id != {{.res_id}} then low_rated_orders
        else 0
      end
    )*100.0/sum(
      case
        when res_id != {{.res_id}} then total_orders
        else 0
      end
    ) as sim_res_poor_rated_orders,

    sum(
      case
        when res_id = {{.res_id}} then actual_visibility
        else 0
      end
    )*100.0/sum(
      case
        when res_id = {{.res_id}} then expected_visibility
        else 0
      end
    ) as your_online_percentage,
    sum(
      case
        when res_id != {{.res_id}} then actual_visibility
        else 0
      end
    )*100.0/sum(
      case
        when res_id != {{.res_id}} then expected_visibility
        else 0
      end
    ) as sim_res_online_percentage,

    (sum(
      case
        when res_id = {{.res_id}} then actual_visibility
        else 0
      end
    )/sum(
      case
        when res_id = {{.res_id}} then 1
        else 0
      end
    ))/(60*60) as your_avg_online_hrs_per_day,
    (sum(
      case
        when res_id != {{.res_id}} then actual_visibility
        else 0
      end
    )/sum(
      case
        when res_id != {{.res_id}} then 1
        else 0
      end
    ))/(60*60) as sim_res_avg_online_hrs_per_day,

    sum(
      case
        when res_id != {{.res_id}} then kitchent_prep_time
        else 0
      end
    )/sum(
      case
        when res_id != {{.res_id}} then kitchent_prep_time_not_null
        else 0
      end
    ) as your_avg_kpt,
    sum(
      case
        when res_id != {{.res_id}} then kitchent_prep_time
        else 0
      end
    )/sum(
      case
        when res_id != {{.res_id}} then kitchent_prep_time_not_null
        else 0
      end
    ) as sim_res_avg_kpt,

    sum(
      case
        when res_id != {{.res_id}} then kpt_delayed_orders
        else 0
      end
    )*100.0/sum(
      case
        when res_id != {{.res_id}} then delivered_orders
        else 0
      end
    ) as your_delayed_orders,
    sum(
      case
        when res_id != {{.res_id}} then kpt_delayed_orders
        else 0
      end
    )*100.0/sum(
      case
        when res_id != {{.res_id}} then delivered_orders
        else 0
      end
    ) as sim_res_delayed_orders
  FROM ba_hub_daily_stats
  WHERE res_id in ({{.res_ids}})
  AND "timestamp" BETWEEN {{.start_timestamp}} AND {{.end_timestamp}} 

filters:
  - res_id
  - res_ids
  - start_timestamp
  - end_timestamp