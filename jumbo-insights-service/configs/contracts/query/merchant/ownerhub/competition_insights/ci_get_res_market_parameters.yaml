# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: mx_res_cuisine_stats
identifier: ci_get_res_market_parameters
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Res level metrics for getting similar res

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    res_id,
    cuisine_id,
    cuisine_dish_id,
    cuisine,
    percentile(avg_order_value, 50) as avg_order_value,
    lastWithTime(subzone_id, "timestamp", 'STRING') as subzone_id,
    lastWithTime(zone_id, "timestamp", 'STRING') as zone_id,
    lastWithTime(city_id, "timestamp", 'STRING') as city_id,
    sum(items_sold) as items_sold,
    sum(item_sales) as item_sales,
    sum(item_sales)*100.0/sum(res_sales) as sales_share
  FROM mx_res_cuisine_stats
  WHERE res_id in ({{.res_ids}})
  AND "timestamp" BETWEEN {{.start_timestamp}} AND {{.end_timestamp}} 
  GROUP BY res_id, cuisine_id, cuisine_dish_id, cuisine

filters:
  - res_ids
  - start_timestamp
  - end_timestamp