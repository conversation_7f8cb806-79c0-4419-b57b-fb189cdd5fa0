# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: ci_get_res_lvl_sales_for_similar_res
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: get res level sales for sim res to calculate ranks

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    res_id,
    sum(delivered_merchant_received_amount) as sales,
    sum(delivered_orders) as orders,
    sum(merchant_received_amount)/sum(total_orders) as avg_order_value,

    sum(menu_opens) as menu_opens,
    sum(total_orders)*100.0/sum(menu_opens) as menu_to_order,
    sum(cart_builts)*100.0/sum(menu_opens) as menu_to_cart,
    sum(total_orders)*100.0/sum(cart_builts) as cart_to_order,

    sum(ad_menu_opens)*100.0/sum(delivered_ad_impression) as ads_ctr,
    sum(ad_menu_opens) as ad_menu_opens,
    sum(ads_cv)*100.0/sum(commissionable_value) as ads_cv_share,

    sum(composite_mx_offer_orders)*100.0/sum(delivered_orders) as orders_from_offers_share,
    sum(composite_discount_applied_amount)/sum(total_orders) as discount_given_per_order,
    sum(composite_discount_applied_amount)*100.0/sum(bill_subtotal) as effective_discount,
    sum(packaging_charges)/sum(total_orders) as packaging_charge,

    sum(items_with_image)*100.0/sum(total_items) as image_coverage,
    sum(items_with_description)*100.0/sum(total_items) as description_coverage,
    avg(total_items) as total_items,

    (
      sum(supply_mx_sent_refund) + sum(supply_mx_attr_non_refund)
    )*100.0/sum(total_orders) as complaints,
    sum(composite_mx_rejected_orders)*100.0/sum(total_orders) as rejections,
    sum(low_rated_orders)*100.0/sum(total_orders) as poor_rated_orders,

    sum(actual_visibility)*100.0/sum(expected_visibility) as online_percentage,
    avg(actual_visibility)/(60*60) as avg_online_hrs_per_day,
    (sum(expected_visibility) - sum(actual_visibility))/(60*60) as offline_hrs,
    sum(kitchent_prep_time)/sum(kitchent_prep_time_not_null) as avg_kpt,
    sum(kpt_delayed_orders)*100.0/sum(delivered_orders) as delayed_orders

  FROM ba_hub_daily_stats
  WHERE res_id in ({{.res_ids}})
  AND "timestamp" BETWEEN {{.start_timestamp}} AND {{.end_timestamp}} 
  GROUP BY res_id

filters:
  - res_ids
  - start_timestamp
  - end_timestamp