# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: mx_res_cuisine_stats
identifier: ci_get_cuisine_mkt_share
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: get cuisine sales for res and similar res

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    ist_year,
    ist_isoweek,
    cuisine_id,
    cuisine_dish_id,
    cuisine,
    sum(
      case
        when res_id IN ({{.res_id}}) then item_sales
        else 0
      end
    )*100.0/sum(item_sales) as cuisine_share
  FROM mx_res_cuisine_stats
  WHERE res_id in ({{.res_ids}})
  AND "timestamp" BETWEEN {{.start_timestamp}} AND {{.end_timestamp}} 
  GROUP BY ist_year, ist_isoweek, cuisine_id, cuisine_dish_id, cuisine

filters:
  - res_id
  - res_ids
  - start_timestamp
  - end_timestamp