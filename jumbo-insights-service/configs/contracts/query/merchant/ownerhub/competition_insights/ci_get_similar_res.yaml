# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: mx_res_cuisine_stats
identifier: ci_get_similar_res
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: get similar res based on input params

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    res_id,
    sum(items_sold) as items_sold,
    sum(item_sales) as item_sales
  FROM mx_res_cuisine_stats
  WHERE cuisine_id IN ({{.cuisine_ids}})
  AND "timestamp" BETWEEN {{.start_timestamp}} AND {{.end_timestamp}} 
  AND case
        when {{.get_subzone}} = 1 then subzone_id
        when {{.get_zone}} = 1 then zone_id
        when {{.get_city}} = 1 then city_id
        else 0 = 1
      end in ({{.location_id}})
  AND avg_order_value between {{.avg_order_val_start}} and {{.avg_order_val_end}}
  AND orders_per_day >= 5
  AND sales_share >= 20
  GROUP BY res_id

filters:
  - cuisine_ids
  - start_timestamp
  - end_timestamp
  - get_subzone
  - get_zone
  - get_city
  - location_id
  - avg_order_val_start
  - avg_order_val_end