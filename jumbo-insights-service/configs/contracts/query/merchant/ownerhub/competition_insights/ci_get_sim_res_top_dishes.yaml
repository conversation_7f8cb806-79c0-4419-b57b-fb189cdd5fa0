# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: mx_res_dish_stats
identifier: ci_get_sim_res_top_dishes
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Get top dishes for a set of restaurants

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >

  SELECT
    dish_tag,
    dish_id,
    MAX(
      CASE
        WHEN dish_diet_type = 'veg' THEN 1
        ELSE 0
      END
    ) AS veg_flag,
    PERCENTILE(avg_item_cost, 90) AS avg_item_cost,
    SUM(item_sales) AS item_sales
  FROM mx_res_dish_stats 
  WHERE res_id in ({{.res_ids}})
  GROUP BY dish_tag, dish_id

filters:
  - res_ids