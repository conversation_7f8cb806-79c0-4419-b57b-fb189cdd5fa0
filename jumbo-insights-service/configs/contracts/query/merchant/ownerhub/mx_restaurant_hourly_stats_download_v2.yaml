# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: restaurant_hourly_stats
identifier: mx_restaurant_hourly_stats_download_v2
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: All real time metrics
# Time in second
caching_ttl: 1500
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT res_id, SUM(menu_impressions) as menu_impressions,
  SUM(menu_opens) as menu_opens,
  SUM(cart_created) as cart_created,
  SUM(order_placed) as order_placed,
  SUM(ads_impressions) as ads_impressions,
  SUM(ads_clicks) as ads_clicks,
  SUM(offline_hours) as offline_hours,
    CASE
      WHEN {{.category_enum}} = 1 THEN concat(concat(year("timestamp", 'Asia/Kolkata') , month("timestamp", 'Asia/Kolkata'), '_'), day("timestamp", 'Asia/Kolkata'), '_')
      WHEN {{.category_enum}} = 2 THEN concat(year("timestamp", 'Asia/Kolkata') , weekOfYear("timestamp", 'Asia/Kolkata'), '_')
      WHEN {{.category_enum}} = 3 THEN concat(year("timestamp", 'Asia/Kolkata') , month("timestamp", 'Asia/Kolkata'), '_')
      ELSE ''
    END AS group_category
  from restaurant_hourly_stats
  WHERE "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
  AND group_category != ''
  AND res_id IN ({{.res_id}})
  GROUP BY res_id, group_category
filters:
  - res_id
  - start_time
  - end_time
  - category_enum