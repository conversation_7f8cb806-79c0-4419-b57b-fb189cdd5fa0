# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: restaurant_hourly_stats
identifier: mx_restaurant_hourly_stats
audit:
  author_email: inti.<PERSON><PERSON><PERSON>@zomato.com
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: All real time metrics
# Time in second
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 160
# Dimensions
columns:
  - name: res_id
  - name: menu_impressions
    func: sum
    source_column: menu_impressions
  - name: menu_opens
    func: sum
    source_column: menu_opens
  - name: cart_created
    func: sum
    source_column: cart_created
  - name: order_placed
    func: sum
    source_column: order_placed
  - name: ads_impressions
    func: sum
    source_column: ads_impressions
  - name: ads_clicks
    func: sum
    source_column: ads_clicks
  - name: offline_hours
    func: sum
    source_column: offline_hours
# Group by columns
aggregations:
  - res_id
filters:
  - res_id
