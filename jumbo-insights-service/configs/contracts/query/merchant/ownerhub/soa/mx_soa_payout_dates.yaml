# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: res_o2_payout_details
identifier: mx_soa_payout_dates
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Restaurant payout dates for input payout cycle range

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
      payout_timestamp,
      COUNT(order_id) AS total_rows
  FROM res_o2_payout_details
  WHERE res_id IN (
    {{.res_ids}}
  )
  AND order_date BETWEEN {{.start_timestamp}} AND {{.end_timestamp}}
  GROUP BY payout_timestamp

filters:
  - res_ids
  - start_timestamp
  - end_timestamp