# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: res_o2_payout_details
identifier: order_lvl_payout_details
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Restaurant payout details on an aggregated level

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
      order_id,
      merchant_order_state,
      res_id,
      merchant_order_created_at,
      MAX(
        CASE
          WHEN "status" = 'settled' THEN 1
          ELSE 0
        END
      ) AS "status", 

      SUM(
        CASE
            WHEN transfer_type = 'z->mx' AND row_type = 'ledger' THEN net_amount
            ELSE 0
        END
      ) - SUM(
            CASE 
                WHEN transfer_type = 'mx->z' AND row_type = 'ledger' THEN net_amount
                ELSE 0
            END
      ) as ledger_gross_revenue,      
      SUM(
        CASE
            WHEN transfer_type = 'z->mx' AND row_type = 'paidout' THEN net_amount
            ELSE 0
        END
      ) - SUM(
            CASE 
                WHEN transfer_type = 'mx->z' AND row_type = 'paidout' THEN net_amount
                ELSE 0
            END
      ) as paidout_gross_revenue,

      SUM(
        CASE
            WHEN transfer_type = 'z->mx' AND row_type = 'ledger' THEN final_amount
            ELSE 0
        END
      ) - SUM(
            CASE
                WHEN transfer_type = 'mx->z' AND row_type = 'ledger' THEN final_amount
                ELSE 0
            END
      ) AS ledger_net_receivable,  
      SUM(
        CASE
            WHEN transfer_type = 'z->mx' AND row_type = 'paidout' THEN final_amount
            ELSE 0
        END
      ) - SUM(
            CASE
                WHEN transfer_type = 'mx->z' AND row_type = 'paidout' THEN final_amount
                ELSE 0
            END
      ) AS paidout_net_receivable,  

      SUM(
        CASE
            WHEN status = 'pending' AND transfer_type = 'z->mx' THEN final_amount
            ELSE 0
        END
      ) - SUM(
            CASE
                WHEN status = 'pending' and transfer_type = 'mx->z' THEN final_amount
                ELSE 0
            END
      ) AS unsettled_amount
  FROM res_o2_payout_details
  WHERE res_id IN (
    {{.res_ids}}
  )
  AND merchant_order_state IN (
    {{.merchant_order_states}}
  )
  AND service_id IN (
    {{.service_ids}}
  )
  AND payout_timestamp BETWEEN {{.start_timestamp}} AND {{.end_timestamp}}

  GROUP BY order_id, merchant_order_state, res_id, merchant_order_created_at

filters:
  - res_ids
  - service_ids
  - merchant_order_states
  - start_timestamp
  - end_timestamp