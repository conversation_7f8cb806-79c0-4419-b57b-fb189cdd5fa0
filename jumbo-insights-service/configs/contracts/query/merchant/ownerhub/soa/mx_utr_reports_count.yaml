# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: res_o2_payout_details
identifier: mx_soa_utr_count
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Restaurant payout details on an aggregated level

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
    count(DISTINCT utr_number) AS total_count
  FROM res_o2_payout_details
  WHERE res_id IN (
    {{.res_ids}}
  )
  AND payout_timestamp BETWEEN {{.start_timestamp}} AND {{.end_timestamp}}
  AND row_type = 'paidout'

filters:
  - res_ids
  - start_timestamp
  - end_timestamp