# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: res_o2_payout_details
identifier: mx_soa_expenses
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Restaurant expenses aggregated along with the count of expenses

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
      res_id,
      COUNT(DISTINCT order_id) AS no_of_expenses,
      SUM(
        CASE
            WHEN transfer_type = 'mx->z' AND row_type = 'ledger' THEN final_amount
            ELSE 0
        END
      ) - SUM(
            CASE 
                WHEN transfer_type = 'z->mx' AND row_type = 'ledger' THEN final_amount
                ELSE 0
            END
      ) AS ledger_total_expenses,
      SUM(
        CASE
            WHEN transfer_type = 'mx->z' AND row_type = 'paidout' THEN final_amount
            ELSE 0
        END
      ) - SUM(
            CASE 
                WHEN transfer_type = 'z->mx' AND row_type = 'paidout' THEN final_amount
                ELSE 0
            END
      ) AS paidout_total_expenses
  FROM res_o2_payout_details
  WHERE res_id IN (
    {{.res_ids}}
  )
  AND service_id IN (
    {{.service_ids}}
  )
  AND dining_business_flag IN (
    {{.dining_business_flag}}
  )
  AND payout_timestamp BETWEEN {{.start_timestamp}} AND {{.end_timestamp}}
  GROUP BY res_id

filters:
  - res_ids
  - service_ids
  - start_timestamp
  - end_timestamp
  - dining_business_flag