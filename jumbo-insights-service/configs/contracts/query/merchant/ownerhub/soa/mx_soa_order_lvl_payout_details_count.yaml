# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: res_o2_payout_details
identifier: order_lvl_payout_details_count
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Restaurant payout details on an aggregated level

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
      COUNT(DISTINCT order_id) AS total_count
  FROM res_o2_payout_details
  WHERE res_id IN (
    {{.res_ids}}
  )
  AND merchant_order_state IN (
    {{.merchant_order_states}}
  )
  AND service_id IN (
    {{.service_ids}}
  )  
  AND payout_timestamp BETWEEN {{.start_timestamp}} AND {{.end_timestamp}}

filters:
  - res_ids
  - service_ids
  - merchant_order_states
  - start_timestamp
  - end_timestamp