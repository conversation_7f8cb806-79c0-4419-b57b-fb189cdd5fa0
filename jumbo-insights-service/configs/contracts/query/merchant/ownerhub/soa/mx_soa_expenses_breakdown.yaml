# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: res_o2_payout_details
identifier: mx_soa_expenses_breakdown
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Restaurant expenses details for Ads, Dining, Hyperpure, Onboarding etc ...

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
      order_id,
      res_id,
      CASE
          WHEN service_id IN ('PL_HYP', 'HYP') THEN 'Hyperpure'
          WHEN service_id IN ('PL_ADS', 'ADS_CPO') AND dining_business_flag = 1 THEN 'Ads - Dining'
          WHEN service_id IN ('PL_ADS', 'ADS_CPO') AND dining_business_flag = 0 THEN 'Ads - Delivery'
          WHEN service_id IN ('PL_RESOBD') THEN 'Onboarding'
          ELSE service_id
      END AS expense_type,
      order_date,
      MAX(
        CASE
          WHEN "status" = 'settled' THEN 1
          ELSE 0
        END
      ) AS "status",
      SUM(
        CASE
            WHEN transfer_type = 'mx->z' AND row_type = 'ledger' THEN final_amount
            ELSE 0
        END
      ) - SUM(
            CASE 
                WHEN transfer_type = 'z->mx' AND row_type = 'ledger' THEN final_amount
                ELSE 0
            END
      ) AS ledger_billed_amount,
      SUM(
        CASE
            WHEN transfer_type = 'mx->z' AND row_type = 'paidout' THEN final_amount
            ELSE 0
        END
      ) - SUM(
            CASE 
                WHEN transfer_type = 'z->mx' AND row_type = 'paidout' THEN final_amount
                ELSE 0
            END
      ) AS paidout_billed_amount,
      lastWithTime(product_type, "payout_timestamp", 'STRING') AS product_type,
      lastWithTime(ad_campaign_for_res_id, "payout_timestamp", 'STRING') AS ad_campaign_for_res_id
  FROM res_o2_payout_details
  WHERE res_id IN (
    {{.res_ids}}
  )
  AND service_id IN (
    {{.service_ids}}
  )
  AND dining_business_flag IN (
    {{.dining_business_flag}}
  )
  AND payout_timestamp BETWEEN {{.start_timestamp}} AND {{.end_timestamp}}
  GROUP BY order_id, res_id, expense_type, order_date

filters:
  - res_ids
  - service_ids
  - start_timestamp
  - end_timestamp
  - dining_business_flag