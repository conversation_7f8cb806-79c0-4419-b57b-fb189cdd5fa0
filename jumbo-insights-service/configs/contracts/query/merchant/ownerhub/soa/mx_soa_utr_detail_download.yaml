# can be pinot/trino
query_backend: trino
tenant: Zomato
catalog: zomato
schema: insights_etls
table: res_o2_payout_details
identifier: mx_soa_utr_detail_download
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Restaurant payout details on an aggregated level

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
    utr_number,
    order_id,
    order_date as order_date,
    payout_timestamp as settlement_date,
    "action" as order_type,
    SUM(
      CASE
          WHEN transfer_type = 'z->mx' THEN final_amount
          ELSE 0
      END
    ) - SUM(
          CASE
              WHEN transfer_type = 'mx->z' THEN final_amount
              ELSE 0
          END
    ) AS amount
  FROM {{.table}}
  WHERE res_id IN (
    {{.res_ids}}
  ) 
  AND
    CASE
      WHEN {{.input_utr_numbers}} = 1 THEN utr_number IN ( {{.utr_numbers}} )
      WHEN {{.input_utr_numbers}} = 0 THEN TRUE
      ELSE FALSE
    END
  AND dt BETWEEN DATE_FORMAT(
                      FROM_UNIXTIME(CAST({{.start_timestamp}} AS BIGINT)),
                      '%Y%m%d'
                    )
             AND DATE_FORMAT(
                      FROM_UNIXTIME(CAST({{.end_timestamp}} AS BIGINT)),
                      '%Y%m%d'
                    )
  AND row_type = 'paidout'
  GROUP BY utr_number, order_id, order_date, payout_timestamp, "action"  

filters:
  - res_ids
  - input_utr_numbers
  - utr_numbers
  - start_timestamp
  - end_timestamp