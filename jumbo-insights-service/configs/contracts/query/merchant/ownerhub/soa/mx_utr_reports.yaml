# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: res_o2_payout_details
identifier: mx_soa_utr
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Restaurant payout details on an aggregated level

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
    utr_number,
    res_id,
    utr_date,
    status,
    SUM(
      CASE
          WHEN transfer_type = 'z->mx' THEN final_amount
          ELSE 0
      END
    ) - SUM(
          CASE
              WHEN transfer_type = 'mx->z' THEN final_amount
              ELSE 0
          END
    ) AS amount
  FROM res_o2_payout_details
  WHERE res_id IN (
    {{.res_ids}}
  )
  AND payout_timestamp BETWEEN {{.start_timestamp}} AND {{.end_timestamp}}
  AND row_type = 'paidout'
  GROUP BY utr_number, res_id, utr_date, status

filters:
  - res_ids
  - start_timestamp
  - end_timestamp