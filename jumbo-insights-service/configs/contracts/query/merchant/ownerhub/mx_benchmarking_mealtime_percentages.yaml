# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: d1_meal_time_percentages
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: BA Hub stats

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT res_id, LASTWITHTIME(cuisine_string,ingestion_time,'STRING') as cuisine_string, 
  LASTWITHTIME(avg_ov_bucket,ingestion_time,'STRING') as avg_ov_bucket,
  LASTWITHTIME(city_id,ingestion_time,'INT') as city_id,
  SUM(delivered_orders) as total_orders,
  SUM(delivered_merchant_received_amount) as total_sales,
  SUM(breakfast_orders)*100.00/SUM(delivered_orders) as breakfast_orders_percentage,
  SUM(breakfast_sales)*100.00/SUM(delivered_merchant_received_amount) as breakfast_sales_percentage, 
  SUM(lunch_sales )*100.00/SUM(delivered_merchant_received_amount) as lunch_sales_percentage,
  SUM(lunch_orders)*100.00/sum(delivered_orders) as lunch_orders_percentage,
  SUM(snacks_sales)*100.00/SUM(delivered_merchant_received_amount) as snack_sales_percentage,
  SUM(snacks_orders)*100.00/SUM(delivered_orders) as snack_orders_percentage,
  SUM(dinner_sales)*100.00/SUM(delivered_merchant_received_amount) as dinner_sales_percentage,
  SUM(dinner_orders)*100.00/SUM(delivered_orders) as dinner_orders_percentage,
  SUM(late_night_sales)*100.00/SUM(delivered_merchant_received_amount) as late_night_sales_percentage,
  SUM(late_night_orders)*100.00/SUM(delivered_orders) as late_night_orders_percentage
  FROM {{.table}}
  WHERE "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
  AND res_id IN ({{.res_id}})
  AND delivered_orders > 0
  GROUP BY res_id
filters:
  - res_id
  - start_time
  - end_time
