# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: mx_dish_items_performance
identifier: mx_dish_trends
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Dish trends widget
# Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 160
# Dimensions
columns:
  - name: res_id
  - name: item_name
  - name: total_item_revenue
    func: sum
    source_column: item_revenue
  - name: total_item_quantity
    func: sum
    source_column: item_quantity
# Group by columns
aggregations:
  - res_id
  - item_name
filters:
  - res_id
