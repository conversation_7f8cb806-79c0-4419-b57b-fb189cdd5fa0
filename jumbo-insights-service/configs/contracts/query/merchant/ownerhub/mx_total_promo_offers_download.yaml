# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: mx_offer_orders_download
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Total Offer sales and orders

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT merchant_order_res_id, SUM(merchant_order_total_cost) as total_offer_sales,
  COUNT(consumer_order_id) as total_promo_orders,
  weekOfYear("consumer_order_created_at"*1000, 'Asia/Kolkata') as ist_isoweek,
  day("consumer_order_created_at"*1000, 'Asia/Kolkata')  as ist_day,
  month("consumer_order_created_at"*1000, 'Asia/Kolkata')  as ist_month,
  year("consumer_order_created_at"*1000, 'Asia/Kolkata')  as ist_year
  FROM composite_order_events
  WHERE "consumer_order_created_at" BETWEEN {{.start_time}} AND {{.end_time}}
  AND merchant_order_res_id IN ({{.merchant_order_res_id}})
  AND consumer_order_discount_applied_flag = {{.consumer_order_discount_applied_flag}}
  AND merchant_order_state IN ({{.merchant_order_state}})
  GROUP BY merchant_order_res_id, ist_isoweek, ist_day, ist_month, ist_year
filters:
  - merchant_order_res_id
  - start_time
  - end_time
  - consumer_order_discount_applied_flag
  - merchant_order_state
