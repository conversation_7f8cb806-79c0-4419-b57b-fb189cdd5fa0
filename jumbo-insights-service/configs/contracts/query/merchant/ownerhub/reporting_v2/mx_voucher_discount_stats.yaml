# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: mx_voucher_discount_stats
identifier: mx_voucher_discount_stats
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub promo stats - rate card id level performance

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
  entity_id AS discount_id, 
  SUM(num_delivered_orders) AS num_delivered_orders,
  SUM(total_merchant) AS total_merchant,
  SUM(total_merchant_discount) AS total_merchant_discount

  FROM {{.table}} 
  WHERE entity_id IN ({{.discount_ids}})
  AND res_id IN ({{.res_id}})
  GROUP BY discount_id
  
filters:
  - res_id
  - discount_ids