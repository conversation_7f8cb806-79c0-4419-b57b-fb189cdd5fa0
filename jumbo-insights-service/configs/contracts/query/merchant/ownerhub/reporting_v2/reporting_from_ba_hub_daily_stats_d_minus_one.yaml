# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: reporting_from_ba_hub_daily_stats_d_minus_one
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub reporting page - ad sales, market share, online percentage, complaints

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
  CASE
      WHEN {{.agg_level_enum}} = 1 THEN 'daily'
      WHEN {{.agg_level_enum}} = 2 THEN 'weekly'
      WHEN {{.agg_level_enum}} = 3 THEN 'monthly'
      ELSE 'NULL'
  END as agg_type, 
  CASE
      WHEN {{.agg_level_enum}} = 1 THEN concat(concat(ist_year, 
                                                      CASE 
                                                          WHEN ist_month < 10 THEN concat('0', ist_month, '') 
                                                          ELSE ist_month
                                                      END,
                                                      '_'),
                                               CASE 
                                                   WHEN ist_day < 10 THEN concat('0', ist_day, '')
                                                   ELSE ist_day 
                                               END,
                                               '_')
      WHEN {{.agg_level_enum}} = 2 THEN concat(
            CASE
                WHEN ist_month = 12 and ist_isoweek = 1 THEN CAST(ist_year + 1 AS INT)
                ELSE ist_year
            END, 
            CASE 
              WHEN ist_isoweek < 10 THEN concat('0', ist_isoweek, '')
              ELSE ist_isoweek
            END, 
            '_'
        )
      WHEN {{.agg_level_enum}} = 3 THEN concat(ist_year, 
                                               CASE 
                                                  WHEN ist_month < 10 then concat('0', ist_month, '')
                                                  ELSE ist_month
                                               END, 
                                               '_')
      ELSE 'NULL'
  END as agg_value, 

  sum(delivered_orders) as delivered_orders,
  sum(similar_res_orders) as similar_res_orders,
  sum(delivered_merchant_received_amount) as total_sales,
  sum(similar_res_sales) as similar_res_sales,

  lastWithTime(res_delivery_rating_till_date, "timestamp", 'DOUBLE') as res_delivery_rating,
  sum(supply_mx_sent_refund) + sum(supply_mx_attr_non_refund) as complaints,
  sum(total_orders) as total_orders,

  sum(ops_hr_sum) as ops_hr_sum,
  sum(gmt_shutdown_hours_sum) as gmt_shutdown_hours_sum,
  sum(lp_shutdown_hours_sum) as lp_shutdown_hours_sum,

  sum(new_users) as new_users,
  sum(repeat_users) as repeat_users, 
  sum(lapsed_users) as lapsed_users,
  sum(unique_user_count) as total_users,

  sum(ads_cv) as sales_from_ads,
  sum(delivered_ad_impression) as impressions_from_ads,

  cast(sum(rider_wait_time)/sum(rider_wait_time_not_null) as int) as rider_wait_time,
  cast(sum(kitchent_prep_time)/sum(kitchent_prep_time_not_null) as int) as kitchen_prep_time
  
  FROM ba_hub_daily_stats 
  WHERE res_id in ({{.res_id}})
  AND (
        "timestamp" BETWEEN {{.start_time_1}} AND {{.end_time_1}} OR 
        "timestamp" BETWEEN {{.start_time_2}} AND {{.end_time_2}} OR 
        "timestamp" BETWEEN {{.start_time_3}} AND {{.end_time_3}} OR
        "timestamp" BETWEEN {{.start_time_4}} AND {{.end_time_4}} OR
        "timestamp" BETWEEN {{.start_time_5}} AND {{.end_time_5}}
      )
      
  GROUP BY agg_type, agg_value

filters:
  - agg_level_enum
  - res_id
  - start_time_1
  - end_time_1
  - start_time_2
  - end_time_2
  - start_time_3
  - end_time_3
  - start_time_4
  - end_time_4
  - start_time_5
  - end_time_5  