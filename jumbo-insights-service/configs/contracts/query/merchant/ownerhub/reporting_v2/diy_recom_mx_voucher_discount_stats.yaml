# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: mx_voucher_discount_stats
identifier: offer_recom_mx_voucher_discount_stats
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: DIY promo recommendation on Create Offers page

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
  res_id, entity_id, nrl_segment, pg_segment, offer_type, discount_type, mealtime, recommended_discount_value, 
  recommended_upper_limit, recommended_mov, expected_growth_value 
  FROM {{.table}} 
  WHERE entity_type IN ('DIY_RECOMMENDATION')
  AND res_id = {{.res_id}}
  AND dt = ToDateTime(ago('P2D'), 'yyyyMMdd')

filters:
  - res_id