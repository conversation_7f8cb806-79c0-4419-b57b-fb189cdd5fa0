# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: reporting_trends_detailed
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub reporting trends - stats for web view page

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
  CASE 
      WHEN {{.agg_level_enum}} = 1 THEN concat('year', ist_year, '_') 
      WHEN {{.agg_level_enum}} = 2 THEN concat('year', ist_year, '_') 
      WHEN {{.agg_level_enum}} = 3 THEN concat(
        'year', 
        CASE
            WHEN ist_month = 12 and ist_isoweek = 1 THEN CAST(ist_year + 1 AS INT)
            ELSE ist_year
        END, 
        '_'
      )  
      WHEN {{.agg_level_enum}} = 4 THEN concat(concat('year', ist_year, '_'), 
                                               CASE 
                                                   WHEN ist_month < 10 THEN concat('0', ist_month, '')
                                                   ELSE ist_month
                                               END,
                                               '_')
      ELSE 'NULL'     
  END as parent_aggregation_type,

  CASE 
      WHEN {{.agg_level_enum}} = 1 THEN concat('quarter', ist_quarter, '_') 
      WHEN {{.agg_level_enum}} = 2 THEN concat('month', 
                                               CASE 
                                                    WHEN ist_month < 10 THEN concat('0', ist_month, '')
                                                    ELSE ist_month
                                               END,
                                               '_')
      WHEN {{.agg_level_enum}} = 3 THEN concat('week',
                                               CASE WHEN ist_isoweek < 10 THEN concat('0', ist_isoweek, '')
                                                    ELSE ist_isoweek
                                               END,     
                                               '_') 
      WHEN {{.agg_level_enum}} = 4 THEN concat('day', 
                                               CASE 
                                                   WHEN ist_day < 10 THEN concat('0', ist_day, '')
                                                   ELSE ist_day
                                               END,
                                               '_')
      ELSE 'NULL'
  END as aggregation_type,

  CASE 
      WHEN {{.agg_level_enum}} = 1 and ist_quarter = 1 THEN 'Jan - Mar'
      WHEN {{.agg_level_enum}} = 1 and ist_quarter = 2 THEN 'Apr - Jun'
      WHEN {{.agg_level_enum}} = 1 and ist_quarter = 3 THEN 'Jul - Sep'
      WHEN {{.agg_level_enum}} = 1 and ist_quarter = 4 THEN 'Oct - Dec'     
      WHEN {{.agg_level_enum}} = 2 and ist_month = 1 THEN concat('Jan', ist_year, ' ')
      WHEN {{.agg_level_enum}} = 2 and ist_month = 2 THEN concat('Feb', ist_year, ' ')
      WHEN {{.agg_level_enum}} = 2 and ist_month = 3 THEN concat('Mar', ist_year, ' ')     
      WHEN {{.agg_level_enum}} = 2 and ist_month = 4 THEN concat('Apr', ist_year, ' ')     
      WHEN {{.agg_level_enum}} = 2 and ist_month = 5 THEN concat('May', ist_year, ' ')     
      WHEN {{.agg_level_enum}} = 2 and ist_month = 6 THEN concat('Jun', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 2 and ist_month = 7 THEN concat('Jul', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 2 and ist_month = 8 THEN concat('Aug', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 2 and ist_month = 9 THEN concat('Sep', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 2 and ist_month = 10 THEN concat('Oct', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 2 and ist_month = 11 THEN concat('Nov', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 2 and ist_month = 12 THEN concat('Dec', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 3 THEN concat('Week', 
                                               CASE
                                                   WHEN ist_isoweek < 10 THEN concat('0', ist_isoweek, '')
                                                   ELSE ist_isoweek 
                                               END, 
                                               ' ')
      WHEN {{.agg_level_enum}} = 4 THEN concat(CASE
                                                   WHEN ist_day < 10 THEN concat('0', ist_day, '')
                                                   ELSE ist_day 
                                               END,  
                                               CASE 
                                                   WHEN ist_month = 1 THEN 'Jan'
                                                   WHEN ist_month = 2 THEN 'Feb'
                                                   WHEN ist_month = 3 THEN 'Mar'
                                                   WHEN ist_month = 4 THEN 'Apr'
                                                   WHEN ist_month = 5 THEN 'May'
                                                   WHEN ist_month = 6 THEN 'Jun'
                                                   WHEN ist_month = 7 THEN 'Jul'
                                                   WHEN ist_month = 8 THEN 'Aug'
                                                   WHEN ist_month = 9 THEN 'Sep'
                                                   WHEN ist_month = 10 THEN 'Oct'
                                                   WHEN ist_month = 11 THEN 'Nov'
                                                   WHEN ist_month = 12 THEN 'Dec'
                                                   ELSE 'NULL'
                                               END,
                                               ' ')
      ELSE 'NULL'     
  END as aggregation_type_string,
  CASE 
      WHEN {{.agg_level_enum}} = 1 THEN concat(ist_year, ist_quarter, '_')
      WHEN {{.agg_level_enum}} = 2 THEN concat(ist_year, 
                                               CASE 
                                                   WHEN ist_month < 10 THEN concat('0', ist_month, '')
                                                   ELSE ist_month
                                               END,
                                               '_')
      WHEN {{.agg_level_enum}} = 3 THEN concat(
            CASE
                WHEN ist_month = 12 and ist_isoweek = 1 THEN CAST(ist_year + 1 AS INT)
                ELSE ist_year
            END, 
            CASE 
                WHEN ist_isoweek < 10 THEN concat('0', ist_isoweek, '')
                ELSE ist_isoweek
            END,     
            '_'
        )
      WHEN {{.agg_level_enum}} = 4 THEN dt
      ELSE 'NULL'
  END as agg_value,
  CASE 
      WHEN {{.agg_level_enum}} = 1 AND ist_day_of_quarter <= {{.no_of_days}} THEN {{.no_of_days}}
      WHEN {{.agg_level_enum}} = 2 AND ist_day <= {{.no_of_days}} THEN {{.no_of_days}}
      WHEN {{.agg_level_enum}} = 3 AND ist_day_of_week <= {{.no_of_days}} THEN {{.no_of_days}}
      WHEN {{.agg_level_enum}} = 4 THEN {{.no_of_days}}
      ELSE 0
  END as no_of_days,

  sum(delivered_merchant_received_amount) as sales,
  sum(composite_mx_lost_sales) as lost_sales,
  sum(delivered_orders) as delivered_orders,
  sum(total_orders) as total_orders,
  sum(delivered_merchant_received_amount)*1.00/sum(delivered_orders) as aov,
  sum(delivered_merchant_received_amount)*100.00/sum(similar_res_sales) as market_share,

  avg(res_delivery_rating_till_date) as average_rating,
  sum(rated_orders)*100.00/sum(total_orders) as rated_orders,

  sum(bad_orders)*100.00/sum(total_orders) as bad_orders,
  sum(composite_mx_rejected_orders)*100.00/sum(total_orders) as rejected_orders,
  sum(kpt_delayed_orders)*100.00/sum(total_orders) as delayed_orders,
  sum(low_rated_orders)*100.00/sum(total_orders) as poor_rated,
  (sum(supply_mx_sent_refund) + sum(supply_mx_attr_non_refund))*100.00/sum(total_orders) as complaints,

  sum(supply_wimo_ors) + sum(supply_fnd_ors) + sum(supply_delay_ors) as self_logs_other_ors,
  
  sum(supply_mx_sent_refund) + sum(supply_mx_attr_non_refund) as total_complaints_absolute,
  sum(supply_mx_sent_refund)*100.00/(sum(supply_mx_sent_refund) + sum(supply_mx_attr_non_refund)) as mx_sent_refund_compaints_percentage,
  sum(supply_mx_attr_non_refund)*100.00/(sum(supply_mx_sent_refund) + sum(supply_mx_attr_non_refund)) as mx_attr_non_refund_complaints_percentage,

  sum(actual_visibility)*100.00/sum(expected_visibility) as online_percentage,
  (sum(expected_visibility) - sum(actual_visibility))/60 as offline_time_mins,

  sum(kitchent_prep_time)*1.00/sum(kitchent_prep_time_not_null) as kpt_mins,
  sum(for_accurate_orders)*100.00/sum(total_orders) as for_accurate_orders,

  sum(impressions) as impressions,
  
  sum(menu_opens)*100.00
    /sum(case when impressions = 0
                    then menu_opens
                else impressions end) as i2m,
  sum(cart_builts)*100.00
    /sum(case when cart_builts > menu_opens
                  then cart_builts 
              else menu_opens end) as m2c, 
  sum(pnl_table_order_count)*100.00
    /sum(case when pnl_table_order_count > cart_builts
                  then pnl_table_order_count 
              else cart_builts end) as c2o,
  sum(pnl_table_order_count)*100.00
    /sum(case when cart_builts > menu_opens
                  then cart_builts 
              else menu_opens end) as m2o,

  sum(new_users)*100.00/sum(unique_user_count) as new_users,
  sum(repeat_users)*100.00/sum(unique_user_count) as repeat_users,
  sum(lapsed_users)*100.00/sum(unique_user_count) as lapsed_users,
  sum(unique_user_count) as total_users,

  sum(ads_cv) as sales_from_ads,
  sum(ad_menu_opens)*100.00/sum(delivered_ad_impression) as ads_ctr,
  sum(new_users_acquired)*100.00/sum(new_users) as new_users_from_ads,
  sum(ad_orders) as ads_orders,
  sum(delivered_ad_impression) as ads_impressions,
  sum(billed_revenue) as ads_spend,
  (sum(ads_cv) - sum(grow_max_ads_cv))*1.00/(sum(billed_revenue) - sum(grow_max_billed_revenue)) as ads_roi,
  sum(ad_orders)*100.00/sum(total_orders) as orders_percentage_from_ads,

  sum(composite_mx_offer_sales) as sales_from_offers,
  sum(composite_mx_offer_orders)*100.00/sum(delivered_orders) as sales_from_offers_orders,
  sum(composite_discount_applied_amount) as discount_given,
  sum(composite_discount_applied_amount)*100.00/sum(composite_mx_offer_sales) as effective_discount,
  
  AVG(menu_score) as menu_score,
  sum(items_with_image)*100.00/sum(total_items) as items_with_image,
  sum(items_with_description)*100.00/sum(total_items) as items_with_description,
  sum(total_catalogue_with_serving_info)*100.00/sum(total_items) as items_with_serving_info

  FROM {{.table}}
  WHERE "timestamp" BETWEEN {{.start_time}} 
                        AND {{.end_time}}
  AND res_id in ({{.res_id}})
  AND no_of_days = {{.no_of_days}}
  AND ist_day_of_quarter > 0
  AND ist_day_of_week > 0
  GROUP BY parent_aggregation_type, aggregation_type, aggregation_type_string, no_of_days, agg_value

filters:
  - start_time
  - end_time
  - res_id
  - agg_level_enum
  - no_of_days