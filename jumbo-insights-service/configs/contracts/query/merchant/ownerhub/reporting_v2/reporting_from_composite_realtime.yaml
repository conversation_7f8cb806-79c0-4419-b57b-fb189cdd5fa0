# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: reporting_from_composite_realtime
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub reporting page - sales, orders and rating metrics

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
  CASE
      WHEN {{.agg_level_enum}} = 1 THEN 'daily'
      WHEN {{.agg_level_enum}} = 2 THEN 'weekly'
      WHEN {{.agg_level_enum}} = 3 THEN 'monthly'
      ELSE 'NULL'
  END as agg_type, 
  CASE
      WHEN {{.agg_level_enum}} = 1 THEN merchant_order_created_ist_dt
      WHEN {{.agg_level_enum}} = 2 THEN concat(
            CASE
                WHEN merchant_order_created_ist_month = 12 and merchant_order_created_ist_isoweek = 1 THEN CAST(merchant_order_created_ist_year + 1 AS INT)
                ELSE merchant_order_created_ist_year
            END,         
            CASE 
                WHEN merchant_order_created_ist_isoweek < 10 THEN CONCAT('0', merchant_order_created_ist_isoweek, '')
                ELSE merchant_order_created_ist_isoweek
            END, 
            '_'
        )
      WHEN {{.agg_level_enum}} = 3 THEN concat(merchant_order_created_ist_year, 
                                              CASE 
                                                  WHEN merchant_order_created_ist_month < 10 THEN CONCAT('0', merchant_order_created_ist_month, '')
                                                  ELSE merchant_order_created_ist_month
                                              END,
                                              '_')
      ELSE 'NULL'
  END as agg_value, 

  SUM(CASE 
          WHEN merchant_order_state in ({{.mx_completed_order_state}}) THEN merchant_order_total_cost
          ELSE 0
      END) as total_sales,
  SUM(CASE 
          WHEN merchant_order_state in ({{.mx_completed_order_state}}) THEN 1
          ELSE 0
      END) as delivered_orders,
  COUNT(consumer_order_id) as total_orders,
  SUM(CASE
          WHEN merchant_order_state in ({{.mx_reject_order_state}}) AND 
               merchant_order_reject_reason_id in ({{.mx_reject_reason_id}}) THEN 1
          ELSE 0
      END) as mx_rejected_orders,
  SUM(CASE
          WHEN merchant_order_kpt_delay_secs >= 600 THEN 1
          ELSE 0
      END) as mx_delayed_orders,  
  SUM(CASE
          WHEN consumer_order_rating in ({{.low_rating_id}}) THEN 1
          ELSE 0
      END) as mx_poor_rated_orders,  
  SUM(CASE
          WHEN merchant_order_state in ({{.mx_reject_order_state}}) AND 
               merchant_order_reject_reason_id in ({{.mx_reject_reason_id}}) THEN merchant_order_total_cost
          ELSE 0
      END) as lost_sales,

  SUM(CASE
          WHEN merchant_order_state in ({{.mx_completed_order_state}}) AND consumer_order_discount_applied_flag = 1 THEN merchant_order_total_cost
          ELSE 0
      END) as sales_from_offers
  

  FROM composite_order_events 
  WHERE merchant_order_res_id in ({{.res_id}})
  AND (
        "merchant_order_created_at" BETWEEN {{.start_time_1}} AND {{.end_time_1}} OR 
        "merchant_order_created_at" BETWEEN {{.start_time_2}} AND {{.end_time_2}} OR 
        "merchant_order_created_at" BETWEEN {{.start_time_3}} AND {{.end_time_3}} OR
        "merchant_order_created_at" BETWEEN {{.start_time_4}} AND {{.end_time_4}} OR
        "merchant_order_created_at" BETWEEN {{.start_time_5}} AND {{.end_time_5}}
      )
  AND consumer_order_delivery_mode IN ('DELIVERY', 'delivery')
  GROUP BY agg_type, agg_value

filters:
  - agg_level_enum
  - res_id
  - start_time_1
  - end_time_1
  - start_time_2
  - end_time_2
  - start_time_3
  - end_time_3
  - start_time_4
  - end_time_4
  - start_time_5
  - end_time_5  
  - mx_reject_order_state
  - mx_completed_order_state
  - mx_reject_reason_id
  - low_rating_id