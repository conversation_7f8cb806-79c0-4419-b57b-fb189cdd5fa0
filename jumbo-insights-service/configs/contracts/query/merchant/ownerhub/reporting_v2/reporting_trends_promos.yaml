# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_promo_analytics
identifier: reporting_trends_promos
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub promos trends - overall aggregates

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
  CASE 
      WHEN {{.agg_level_enum}} in (1, 4) THEN CONCAT(CONCAT('year', ist_year, '_'), 
                                                     CASE 
                                                        WHEN ist_month < 10 THEN CONCAT('0', ist_month, '')
                                                        ELSE ist_month
                                                     END, '_')
      WHEN {{.agg_level_enum}} IN (2) THEN CONCAT(
            'year', 
            CASE
                WHEN ist_month = 12 and ist_isoweek = 1 THEN CAST(ist_year + 1 AS INT)
                ELSE ist_year
            END, 
            '_'
        )                                                     
      WHEN {{.agg_level_enum}} IN (3) THEN CONCAT('year', ist_year, '_')
      ELSE 'NULL'     
  END AS parent_aggregation_type,
  CASE 
      WHEN {{.agg_level_enum}} in (1, 4) THEN CONCAT('day', 
                                                     CASE 
                                                         WHEN ist_day < 10 THEN CONCAT('0', ist_day, '')
                                                         ELSE ist_day
                                                     END, '_')
      WHEN {{.agg_level_enum}} = 2 THEN CONCAT('week',
                                               CASE 
                                                   WHEN ist_isoweek < 10 THEN CONCAT('0', ist_isoweek, '')
                                                   ELSE ist_isoweek
                                               END, '_') 
      WHEN {{.agg_level_enum}} = 3 THEN CONCAT('month', 
                                               CASE 
                                                   WHEN ist_month < 10 THEN CONCAT('0', ist_month, '')
                                                   ELSE ist_month
                                               END, '_')
      ELSE 'NULL'
  END AS aggregation_type,
  CASE 
      WHEN {{.agg_level_enum}} in (1, 4) THEN CONCAT(CASE
                                                         WHEN ist_day < 10 THEN CONCAT('0', ist_day, '')
                                                         ELSE ist_day 
                                                     END,  
                                                     CASE 
                                                        WHEN ist_month = 1 THEN 'Jan'
                                                        WHEN ist_month = 2 THEN 'Feb'
                                                        WHEN ist_month = 3 THEN 'Mar'
                                                        WHEN ist_month = 4 THEN 'Apr'
                                                        WHEN ist_month = 5 THEN 'May'
                                                        WHEN ist_month = 6 THEN 'Jun'
                                                        WHEN ist_month = 7 THEN 'Jul'
                                                        WHEN ist_month = 8 THEN 'Aug'
                                                        WHEN ist_month = 9 THEN 'Sep'
                                                        WHEN ist_month = 10 THEN 'Oct'
                                                        WHEN ist_month = 11 THEN 'Nov'
                                                        WHEN ist_month = 12 THEN 'Dec'
                                                        ELSE 'NULL'
                                                     END, ' ')
      WHEN {{.agg_level_enum}} = 2 THEN CONCAT('Week', 
                                               CASE
                                                   WHEN ist_isoweek < 10 THEN CONCAT('0', ist_isoweek, '')
                                                   ELSE ist_isoweek 
                                               END, ' ')
      WHEN {{.agg_level_enum}} = 3 and ist_month = 1 THEN CONCAT('Jan', ist_year, ' ')
      WHEN {{.agg_level_enum}} = 3 and ist_month = 2 THEN CONCAT('Feb', ist_year, ' ')
      WHEN {{.agg_level_enum}} = 3 and ist_month = 3 THEN CONCAT('Mar', ist_year, ' ')     
      WHEN {{.agg_level_enum}} = 3 and ist_month = 4 THEN CONCAT('Apr', ist_year, ' ')     
      WHEN {{.agg_level_enum}} = 3 and ist_month = 5 THEN CONCAT('May', ist_year, ' ')     
      WHEN {{.agg_level_enum}} = 3 and ist_month = 6 THEN CONCAT('Jun', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 3 and ist_month = 7 THEN CONCAT('Jul', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 3 and ist_month = 8 THEN CONCAT('Aug', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 3 and ist_month = 9 THEN CONCAT('Sep', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 3 and ist_month = 10 THEN CONCAT('Oct', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 3 and ist_month = 11 THEN CONCAT('Nov', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 3 and ist_month = 12 THEN CONCAT('Dec', ist_year, ' ')  
      ELSE 'NULL'     
  END AS aggregation_type_string,
  CASE 
      WHEN {{.agg_level_enum}} in (1, 4) THEN dt
      WHEN {{.agg_level_enum}} = 2 THEN CONCAT(
            CASE
                WHEN ist_month = 12 and ist_isoweek = 1 THEN CAST(ist_year + 1 AS INT)
                ELSE ist_year
            END, 
            CASE 
                WHEN ist_isoweek < 10 THEN CONCAT('0', ist_isoweek, '')
                ELSE ist_isoweek
            END, 
            '_'
        )      
      WHEN {{.agg_level_enum}} = 3 THEN CONCAT(ist_year, 
                                               CASE 
                                                   WHEN ist_month < 10 THEN CONCAT('0', ist_month, '')
                                                   ELSE ist_month
                                               END, '_')

      
      ELSE 'NULL'
  END AS agg_value,
  CASE
      WHEN {{.agg_level_enum}} = 1 THEN {{.no_of_days}}
      WHEN {{.agg_level_enum}} = 2 AND ist_day_of_week <= {{.no_of_days}} THEN {{.no_of_days}}
      WHEN {{.agg_level_enum}} = 3 AND ist_day <= {{.no_of_days}} THEN {{.no_of_days}}
      WHEN {{.agg_level_enum}} = 4 AND ist_day_of_week = {{.no_of_days}} THEN {{.no_of_days}}
      ELSE 0
  END AS no_of_days,
  CASE
      WHEN {{.pill_enum}} = 1 AND pg_segment IN ('MM', 'UM') THEN 'premium_customer'
      WHEN {{.pill_enum}} = 1 AND pg_segment IN ('LA') THEN 'offer_sensitive_customer'
      WHEN {{.pill_enum}} = 2 THEN nrl_segment
      WHEN {{.pill_enum}} = 3 THEN mealtime
      ELSE 'overall'
  END AS pill_type,
  
  SUM(gmv_sv) AS gross_sales,
  SUM(mvd_orders) AS orders_from_offers,
  SUM(mvd) AS discount_given,
  SUM(mvd)*100.0/SUM(gmv_sv) AS effective_discount,
  SUM(order_sessions)*100.0/SUM(menu_opens) AS menu_to_order,
  SUM(cart_builts)*100.0/SUM(menu_opens) AS menu_to_cart,
  SUM(order_sessions)*100.0/SUM(cart_builts) AS cart_to_order,
  SUM(case when dt>='20240528' THEN brand_pack_purchases ELSE 0 END) AS subscription_count,
  SUM(CASE WHEN dt>='20240528' then brand_pack_purchase_revenue ELSE 0 end) AS revenue_from_fee,
  SUM(CASE WHEN dt>='20240528' then brand_pack_gmv_sv ELSE 0 end) AS brand_pack_gross_sales,
  SUM(CASE WHEN dt>='20240528' then brand_pack_orders ELSE 0 end) AS brand_pack_orders,
  SUM(CASE WHEN dt>='20240528' then brand_pack_discount ELSE 0 end) AS brand_pack_discount,
  SUM(CASE WHEN dt>='20240528' then brand_pack_discount ELSE 0 end)*100.0/SUM(CASE WHEN dt>='20240528' then  brand_pack_gmv_sv ELSE 0 end) AS brand_pack_effective_discount

  FROM {{.table}} 
  WHERE "timestamp" BETWEEN {{.start_time}} 
                        AND {{.end_time}}
  AND res_id in ({{.res_id}})
  AND no_of_days = {{.no_of_days}}
  GROUP BY parent_aggregation_type, aggregation_type, aggregation_type_string, no_of_days, agg_value, pill_type

filters:
  - start_time
  - end_time
  - res_id
  - agg_level_enum
  - no_of_days
  - pill_enum