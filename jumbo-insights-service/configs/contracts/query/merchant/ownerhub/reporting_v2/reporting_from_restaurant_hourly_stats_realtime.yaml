# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: restaurant_hourly_stats
identifier: reporting_from_restaurant_hourly_stats_realtime
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub reporting page - funnel conversion | a2i, m2c, c2o metrics

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
  CASE
      WHEN {{.agg_level_enum}} = 1 THEN 'daily'
      WHEN {{.agg_level_enum}} = 2 THEN 'weekly'
      WHEN {{.agg_level_enum}} = 3 THEN 'monthly'
      ELSE 'NULL'
  END as agg_type, 
  CASE
      WHEN {{.agg_level_enum}} = 1 THEN concat(concat(year("timestamp", 'Asia/Kolkata'), 
                                                      CASE 
                                                         WHEN month("timestamp", 'Asia/Kolkata') < 10 THEN concat('0', month("timestamp", 'Asia/Kolkata'), '')
                                                         ELSE month("timestamp", 'Asia/Kolkata')
                                                      END,
                                                      '_'),
                                               CASE
                                                  WHEN day("timestamp", 'Asia/Kolkata') < 10 THEN concat('0', day("timestamp", 'Asia/Kolkata'), '')
                                                  ELSE day("timestamp", 'Asia/Kolkata') 
                                               END,
                                               '_')
      WHEN {{.agg_level_enum}} = 2 THEN concat(year("timestamp", 'Asia/Kolkata'),
                                               CASE 
                                                  WHEN weekOfYear("timestamp", 'Asia/Kolkata') < 10 THEN concat('0', weekOfYear("timestamp", 'Asia/Kolkata'), '')
                                                  ELSE weekOfYear("timestamp", 'Asia/Kolkata')
                                               END, 
                                               '_')
      WHEN {{.agg_level_enum}} = 3 THEN concat(year("timestamp", 'Asia/Kolkata'), 
                                               CASE 
                                                  WHEN month("timestamp", 'Asia/Kolkata') < 10 THEN concat('0', month("timestamp", 'Asia/Kolkata'), '')
                                                  ELSE month("timestamp", 'Asia/Kolkata')
                                               END, 
                                               '_')
      ELSE 'NULL'
  END as agg_value, 

  sum(menu_impressions) as impressions,
  sum(menu_opens) as menu_opens,
  sum(cart_created) as cart_created, 
  sum(order_placed) as order_makes
  
  FROM restaurant_hourly_stats 
  WHERE res_id in ({{.res_id}})
  AND (
        ("timestamp" BETWEEN {{.start_time_1}} AND {{.end_time_1}}) 
        OR 
        ("timestamp" BETWEEN {{.start_time_2}} AND {{.end_time_2}}) 
        OR 
        ("timestamp" BETWEEN {{.start_time_3}} AND {{.end_time_3}}) 
        OR
        ("timestamp" BETWEEN {{.start_time_4}} AND {{.end_time_4}}) 
        OR
        ("timestamp" BETWEEN {{.start_time_5}} AND {{.end_time_5}})
      )
      
  GROUP BY agg_type, agg_value

filters:
  - agg_level_enum
  - res_id
  - start_time_1
  - end_time_1
  - start_time_2
  - end_time_2
  - start_time_3
  - end_time_3
  - start_time_4
  - end_time_4
  - start_time_5
  - end_time_5  