# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: mx_dish_items_performance
identifier: reporting_dish_trends_detailed
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub reporting trends - dish trends stats

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
  CASE 
      WHEN {{.agg_level_enum}} = 1 THEN CONCAT('year', ist_year, '_') 
      WHEN {{.agg_level_enum}} = 2 THEN CONCAT('year', ist_year, '_') 
      WHEN {{.agg_level_enum}} = 3 THEN CONCAT(
            'year', 
            CASE
                WHEN ist_month = 12 and ist_isoweek = 1 THEN CAST(ist_year + 1 AS INT)
                ELSE ist_year
            END, 
            '_'
        )  
      WHEN {{.agg_level_enum}} = 4 THEN CONCAT(CONCAT('year', ist_year, '_'), 
                                               CASE 
                                                   WHEN ist_month < 10 THEN CONCAT('0', ist_month, '')
                                                   ELSE ist_month
                                               END, '_')
      ELSE 'NULL'     
  END AS parent_aggregation_type,

  CASE 
      WHEN {{.agg_level_enum}} = 2 THEN CONCAT('month', 
                                               CASE 
                                                    WHEN ist_month < 10 THEN CONCAT('0', ist_month, '')
                                                    ELSE ist_month
                                               END, '_')
      WHEN {{.agg_level_enum}} = 3 THEN CONCAT('week',
                                              CASE WHEN ist_isoweek < 10 THEN CONCAT('0', ist_isoweek, '')
                                                   ELSE ist_isoweek
                                              END, '_') 
      WHEN {{.agg_level_enum}} = 4 THEN CONCAT('day', 
                                               CASE 
                                                   WHEN ist_day < 10 THEN CONCAT('0', ist_day, '')
                                                   ELSE ist_day
                                               END, '_')
      ELSE 'NULL'
  END AS aggregation_type,

  CASE 
      WHEN {{.agg_level_enum}} = 2 and ist_month = 1 THEN CONCAT('Jan', ist_year, ' ')
      WHEN {{.agg_level_enum}} = 2 and ist_month = 2 THEN CONCAT('Feb', ist_year, ' ')
      WHEN {{.agg_level_enum}} = 2 and ist_month = 3 THEN CONCAT('Mar', ist_year, ' ')     
      WHEN {{.agg_level_enum}} = 2 and ist_month = 4 THEN CONCAT('Apr', ist_year, ' ')     
      WHEN {{.agg_level_enum}} = 2 and ist_month = 5 THEN CONCAT('May', ist_year, ' ')     
      WHEN {{.agg_level_enum}} = 2 and ist_month = 6 THEN CONCAT('Jun', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 2 and ist_month = 7 THEN CONCAT('Jul', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 2 and ist_month = 8 THEN CONCAT('Aug', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 2 and ist_month = 9 THEN CONCAT('Sep', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 2 and ist_month = 10 THEN CONCAT('Oct', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 2 and ist_month = 11 THEN CONCAT('Nov', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 2 and ist_month = 12 THEN CONCAT('Dec', ist_year, ' ')  
      WHEN {{.agg_level_enum}} = 3 THEN CONCAT('Week', 
                                               CASE
                                                   WHEN ist_isoweek < 10 THEN CONCAT('0', ist_isoweek, '')
                                                   ELSE ist_isoweek 
                                               END, ' ')
      WHEN {{.agg_level_enum}} = 4 THEN CONCAT(CASE
                                                  WHEN ist_day < 10 THEN CONCAT('0', ist_day, '')
                                                   ELSE ist_day 
                                               END,  
                                               CASE 
                                                   WHEN ist_month = 1 THEN 'Jan'
                                                   WHEN ist_month = 2 THEN 'Feb'
                                                   WHEN ist_month = 3 THEN 'Mar'
                                                   WHEN ist_month = 4 THEN 'Apr'
                                                   WHEN ist_month = 5 THEN 'May'
                                                   WHEN ist_month = 6 THEN 'Jun'
                                                   WHEN ist_month = 7 THEN 'Jul'
                                                   WHEN ist_month = 8 THEN 'Aug'
                                                   WHEN ist_month = 9 THEN 'Sep'
                                                   WHEN ist_month = 10 THEN 'Oct'
                                                   WHEN ist_month = 11 THEN 'Nov'
                                                   WHEN ist_month = 12 THEN 'Dec'
                                                   ELSE 'NULL'
                                               END, ' ')
      ELSE 'NULL'     
  END AS aggregation_type_string,
  CASE 
      WHEN {{.agg_level_enum}} = 2 THEN CONCAT(ist_year, 
                                              CASE 
                                                  WHEN ist_month < 10 THEN CONCAT('0', ist_month, '')
                                                  ELSE ist_month
                                              END, '_')
      WHEN {{.agg_level_enum}} = 3 THEN CONCAT(
            CASE
                WHEN ist_month = 12 and ist_isoweek = 1 THEN CAST(ist_year + 1 AS INT)
                ELSE ist_year
            END, 
            CASE 
                WHEN ist_isoweek < 10 THEN CONCAT('0', ist_isoweek, '')
                ELSE ist_isoweek
            END, 
            '_'
        )
      WHEN {{.agg_level_enum}} = 4 THEN dt
      ELSE 'NULL'
  END AS agg_value,
  CASE 
      WHEN {{.agg_level_enum}} = 4 THEN day_level_revenue_rank
      WHEN {{.agg_level_enum}} = 3 THEN week_level_revenue_rank
      WHEN {{.agg_level_enum}} = 2 THEN month_level_revenue_rank
      ELSE 0
  END AS revenue_rank,
  item_name,
  SUM(item_revenue) AS total_item_revenue,
  LASTWITHTIME(
                CASE 
                    WHEN {{.agg_level_enum}} = 4 THEN day_level_total_item_revenue
                    WHEN {{.agg_level_enum}} = 3 THEN week_level_total_item_revenue
                    WHEN {{.agg_level_enum}} = 2 THEN month_level_total_item_revenue
                    ELSE 0
                END, "merchant_item_revenue_created_at", 'DOUBLE') AS total_revenue,
   SUM(item_revenue)*100.0
  /LASTWITHTIME(
                    CASE 
                        WHEN {{.agg_level_enum}} = 4 THEN day_level_total_item_revenue
                        WHEN {{.agg_level_enum}} = 3 THEN week_level_total_item_revenue
                        WHEN {{.agg_level_enum}} = 2 THEN month_level_total_item_revenue
                        ELSE 0
                    END, "merchant_item_revenue_created_at", 'DOUBLE'
                ) AS total_item_revenue_percentage


  FROM mx_dish_items_performance
  WHERE "merchant_item_revenue_created_at" BETWEEN {{.start_time}} AND {{.end_time}}
  AND res_id IN ({{.res_id}})
  AND revenue_rank BETWEEN 1 AND 5
  
  group by parent_aggregation_type, aggregation_type, aggregation_type_string, agg_value, revenue_rank, item_name 
  
filters:
  - start_time
  - end_time
  - res_id
  - agg_level_enum