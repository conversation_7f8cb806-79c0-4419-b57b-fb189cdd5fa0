# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: mx_voucher_discount_stats
identifier: asv_mx_voucher_discount_stats
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: ASV based MOV recommendations for PROMO creation

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
  entity_id, 
  LASTWITHTIME(asv, "timestamp", 'INT') AS asv

  FROM {{.table}} 
  WHERE entity_type IN ('RES_PERCENTILE_ASV')
  AND res_id = {{.res_id}}
  GROUP BY entity_id
  
filters:
  - res_id