# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: reporting_sales_overview_trends
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: OwnerHub reporting page - sales overview widget - sales, delivered orders and aov

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
  CASE
      WHEN {{.agg_level_enum}} = 1 THEN 'daily'
      WHEN {{.agg_level_enum}} = 2 THEN 'weekly'
      WHEN {{.agg_level_enum}} = 3 THEN 'monthly'
      ELSE 'NULL'
  END as agg_type, -- daily, monthly, weekly
  CASE
      WHEN {{.agg_level_enum}} = 1 THEN merchant_order_created_ist_dt
      WHEN {{.agg_level_enum}} = 2 THEN concat(merchant_order_created_ist_year, 
                                               CASE 
                                                   WHEN merchant_order_created_ist_isoweek < 10 THEN CONCAT('0', merchant_order_created_ist_isoweek, '')
                                                   ELSE merchant_order_created_ist_isoweek
                                               END, 
                                               '_')
      WHEN {{.agg_level_enum}} = 3 THEN concat(merchant_order_created_ist_year, 
                                               CASE 
                                                   WHEN merchant_order_created_ist_month < 10 THEN CONCAT('0', merchant_order_created_ist_month, '')
                                                   ELSE merchant_order_created_ist_month
                                               END,
                                               '_')
      ELSE 'NULL'
  END as agg_value, 
  COUNT(consumer_order_id) as delivered_orders, 
  SUM(merchant_order_total_cost) as total_sales,
  SUM(merchant_order_total_cost)*1.00/COUNT(consumer_order_id) as average_order_value

  FROM composite_order_events 
  WHERE "merchant_order_created_at" BETWEEN {{.start_time}}
                                        AND {{.end_time}}
  AND merchant_order_state = 'COMPLETED'
  AND merchant_order_res_id in ({{.res_id}})
  AND CASE
          WHEN {{.agg_level_enum}} = 1 THEN merchant_order_created_ist_hour <= hour({{.end_time}}*1000, 'UTC')
          WHEN {{.agg_level_enum}} = 2 THEN dayOfWeek(merchant_order_created_at*1000, 'UTC') <= dayOfWeek({{.end_time}}*1000, 'UTC')
          WHEN {{.agg_level_enum}} = 3 THEN merchant_order_created_ist_day <= month({{.end_time}}*1000, 'UTC')
          ELSE 1=1
      END

  GROUP BY agg_type, agg_value
  ORDER BY agg_value

filters:
  - res_id
  - start_time
  - end_time
  - agg_level_enum

