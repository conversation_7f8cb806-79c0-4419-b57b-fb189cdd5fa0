# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: d1_compare_performance
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: BA Hub stats

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT res_id, LASTWITHTIME(cuisine_string,ingestion_time,'STRING') as cuisine_string, 
  LASTWITHTIME(avg_ov_bucket,ingestion_time,'STRING') as avg_ov_bucket,
  LASTWITHTIME(city_id,ingestion_time,'INT') as city_id,
  SUM(low_rated_orders) as total_poor_rated_orders, SUM(kpt_delayed_orders) as total_delayed_orders, 
  SUM(composite_mx_rejected_orders) as total_rejected_orders,
  SUM(supply_mx_sent_refund) + SUM(supply_mx_attr_non_refund) as total_complaints,
  SUM(total_orders) as total_orders,
  SUM(kitchent_prep_time) as total_kpt,
  SUM(actual_visibility) as total_actual_visibility,
  SUM(expected_visibility) as total_expected_visibility,
  SUM(impressions) as res_impressions,
  SUM(menu_opens) as menu_opens, SUM(cart_builts) as cart_created,
  SUM(low_rated_orders_city) as total_poor_rated_orders_city, SUM(kpt_delayed_orders_city) as total_delayed_orders_city, 
  SUM(composite_mx_rejected_orders_city) as total_rejected_orders_city, SUM(total_complaints_city) as total_complaints_city,
  SUM(total_orders_city) as total_orders_city,
  SUM(kitchent_prep_time_city) as total_kpt_city,
  SUM(actual_visibility_city) as total_actual_visibility_city,
  SUM(expected_visibility_city) as total_expected_visibility_city,
  SUM(impressions_city) as res_impressions_city,
  SUM(menu_opens_city) as menu_opens_city, SUM(cart_builts_city) as cart_created_city
  FROM {{.table}}
  WHERE "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
  AND res_id IN ({{.res_id}})
  GROUP BY res_id
filters:
  - res_id
  - start_time
  - end_time
