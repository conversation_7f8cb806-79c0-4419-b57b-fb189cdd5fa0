# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: restaurant_daily_stats
identifier: mx_restaurant_daily_stats_download
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: all d-1 lag metrics
# Time in second
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT res_id, SUM(total_orders) as total_orders, SUM(total_complaints) as total_complaints,
  MAX(delivery_rating) as delivery_rating,
  SUM(total_users) as total_users,
  SUM(new_users) as new_users,
  weekOfYear("timestamp", 'Asia/Kolkata') as ist_isoweek,
  day("timestamp", 'Asia/Kolkata')  as ist_day, month("timestamp", 'Asia/Kolkata')  as ist_month,
  year("timestamp", 'Asia/Kolkata')  as ist_year
  FROM restaurant_daily_stats
  WHERE "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
  AND res_id IN ({{.res_id}})
  GROUP BY res_id, ist_isoweek, ist_day, ist_month, ist_year
filters:
  - res_id
  - start_time
  - end_time
