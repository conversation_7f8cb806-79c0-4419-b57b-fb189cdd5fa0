# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: mx_delayed_orders_download_v2
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Total delayed orders

# Time in second
caching_ttl: 1500
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT merchant_order_res_id, COUNT(consumer_order_id) as total_delayed_orders,
  CASE
    WHEN {{.category_enum}} = 1 THEN concat(concat(year("merchant_order_created_at"*1000, 'Asia/Kolkata') , month("merchant_order_created_at"*1000, 'Asia/Kolkata'), '_'), day("merchant_order_created_at"*1000, 'Asia/Kolkata'), '_')
    WHEN {{.category_enum}} = 2 THEN concat(year("merchant_order_created_at"*1000, 'Asia/Kolkata') , weekOfYear("merchant_order_created_at"*1000, 'Asia/Kolkata'), '_')
    WHEN {{.category_enum}} = 3 THEN concat(year("merchant_order_created_at"*1000, 'Asia/Kolkata') , month("merchant_order_created_at"*1000, 'Asia/Kolkata'), '_')
    ELSE ''
  END AS group_category
  FROM composite_order_events
  WHERE merchant_order_created_at BETWEEN {{.start_time}} AND {{.end_time}}
  AND group_category != ''
  AND merchant_order_res_id IN ({{.merchant_order_res_id}})
  AND merchant_order_kpt_delay_secs > {{.merchant_order_kpt_delay_secs}}
  GROUP BY merchant_order_res_id, group_category
filters:
  - merchant_order_res_id
  - merchant_order_kpt_delay_secs
  - start_time
  - end_time
  - category_enum
