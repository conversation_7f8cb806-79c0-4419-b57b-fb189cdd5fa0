# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: d1_meal_time_trends
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: BA Hub stats

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT res_id,
  SUM(total_orders) as total_orders,
  SUM(delivered_orders) as delivered_orders,
  SUM(delivered_merchant_received_amount) as total_sales,
  SUM(delivered_merchant_received_amount)/SUM(delivered_orders) as avg_order_value,
  CASE
    WHEN {{.category_enum}} = 1 THEN concat(concat(ist_year , ist_month, '_'), ist_day, '_')
    WHEN {{.category_enum}} = 2 THEN concat(ist_year , ist_isoweek, '_')
    WHEN {{.category_enum}} = 3 THEN concat(ist_year , ist_month, '_')
  ELSE ''
  END AS group_category
  FROM {{.table}}
  WHERE "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
  AND res_id IN ({{.res_id}})
  GROUP BY group_category, res_id
  ORDER BY group_category
filters:
  - res_id
  - start_time
  - end_time
  - category_enum
