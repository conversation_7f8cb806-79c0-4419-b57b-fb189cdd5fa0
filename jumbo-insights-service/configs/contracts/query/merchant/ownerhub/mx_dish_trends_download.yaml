# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: mx_dish_items_performance
identifier: mx_dish_trends_download
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Dish trends widget
# Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT res_id, item_name,
  SUM(item_revenue) as total_item_revenue,
  SUM(item_quantity) as total_item_quantity,
  weekOfYear("merchant_item_revenue_created_at", 'Asia/Kolkata') as ist_isoweek,
  day("merchant_item_revenue_created_at", 'Asia/Kolkata')  as ist_day,
  month("merchant_item_revenue_created_at", 'Asia/Kolkata')  as ist_month,
  year("merchant_item_revenue_created_at", 'Asia/Kolkata')  as ist_year
  FROM mx_dish_items_performance
  WHERE "merchant_item_revenue_created_at" BETWEEN {{.start_time}} AND {{.end_time}}
  AND res_id IN ({{.res_id}})
  GROUP BY res_id, item_name, ist_isoweek, ist_day, ist_month, ist_year
filters:
  - res_id
  - start_time
  - end_time
