# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: mx_dish_items_performance
identifier: mx_dish_trends_download_v2
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Dish trends widget
# Time in seconds
caching_ttl: 1500
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT res_id, item_name,

  CASE 
      WHEN {{.category_enum}} = 1 THEN day_level_revenue_rank
      WHEN {{.category_enum}} = 2 THEN week_level_revenue_rank
      WHEN {{.category_enum}} = 3 THEN month_level_revenue_rank
      ELSE 0
  END as revenue_rank,

  CASE
      WHEN {{.category_enum}} = 1 THEN concat(concat(ist_year , ist_month, '_'), ist_day, '_')
      WHEN {{.category_enum}} = 2 THEN concat(ist_year, ist_isoweek, '_')
      WHEN {{.category_enum}} = 3 THEN concat(ist_year, ist_month, '_')
      ELSE ''
  END AS group_category,

  SUM(item_revenue) as total_item_revenue,
  SUM(item_quantity) as total_item_quantity

  FROM mx_dish_items_performance
  WHERE "merchant_item_revenue_created_at" BETWEEN {{.start_time}} AND {{.end_time}}
  AND res_id IN ({{.res_id}})
  AND revenue_rank BETWEEN 1 AND 5

  GROUP BY res_id, item_name, revenue_rank, group_category
filters:
  - res_id
  - start_time
  - end_time
  - category_enum
