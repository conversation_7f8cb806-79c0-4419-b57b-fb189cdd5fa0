# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: marketing_planner_res_stats
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Res level stats for marketing planner

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    res_id,
    SUM(delivered_merchant_received_amount) AS total_sales,
    SUM(composite_discount_applied_amount - salt_discount)*100/SUM(bill_subtotal) AS effective_discount,
    SUM(total_orders)*100.0/SUM(menu_opens) AS m2o,
    SUM(commissionable_value) AS total_cv,
    SUM(total_orders) AS total_orders,
    SUM(commissionable_value)/SUM(total_orders) AS aov,
    SUM(billed_revenue)*100.00/SUM(commissionable_value) as ads_spends_by_cv,
    (SUM(ads_cv) - SUM(grow_max_ads_cv))/(SUM(billed_revenue) - SUM(grow_max_billed_revenue)) AS ads_roi,
    SUM(merchant_voucher_discount)/SUM(total_orders) as mvdo,
    SUM(zvd_final)/SUM(total_orders) as zvdo,
    SUM(total_orders)*100.0/SUM(cart_builts) AS c2o,
    (SUM(ad_rev) + SUM(commission_revenue) + SUM(mx_recoup) + SUM(mx_rejection_penalty)
      - SUM(zvd_final) - SUM(mx_attr_refund_amount) - SUM(mx_refund)
    )/SUM(total_orders) as cmpo
  FROM ba_hub_daily_stats 
  WHERE res_id in (
    {{.res_ids}}
  )
  AND "timestamp" BETWEEN {{.start_timestamp_millis}} AND {{.end_timestamp_millis}} 
  GROUP BY res_id

filters:
  - res_ids
  - start_timestamp_millis
  - end_timestamp_millis
  
