# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: marketing_planner_movs
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Marketing planner MOV ASV percentiles

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    consumer_order_res_id,
    PERCENTILE(merchant_order_total_cost, 20) AS "20percentile",
    PERCENTILE(merchant_order_total_cost, 30) AS "30percentile",
    PERCENTILE(merchant_order_total_cost, 50) AS "50percentile",
    PERCENTILE(merchant_order_total_cost, 70) AS "70percentile",
    PERCENTILE(merchant_order_total_cost, 80) AS "80percentile",
    PERCENTILE(merchant_order_total_cost, 90) AS "90percentile"
  FROM composite_order_events
  WHERE "consumer_order_created_at" BETWEEN {{.start_timestamp}} AND {{.end_timestamp}} 
  AND consumer_order_delivery_mode IN ('DELIVERY', 'delivery')
  AND merchant_order_res_id in ({{.res_ids}})
  GROUP BY consumer_order_res_id

filters:
  - res_ids
  - start_timestamp
  - end_timestamp