# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_promo_analytics
identifier: marketing_planner_res_stats_pg
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Res level stats for marketing planner at a PG segment granularity

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    CASE
      WHEN {{.get_weeknum}} = 1 THEN CONCAT(
          CASE
              WHEN ist_month = 12 and ist_isoweek = 1 THEN CAST(ist_year + 1 AS INT)
              ELSE ist_year
          END, 
          CASE 
              WHEN ist_isoweek < 10 THEN CONCAT('0', ist_isoweek, '')
              ELSE ist_isoweek
          END,
          '-'
        )
      WHEN {{.get_monthnum}} = 1 THEN DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:yyyy-MM tz(Asia/Kolkata)',
        '1:DAYS'
      )     
      ELSE 'agg'
    END AS week,
    res_id,
    CASE
        WHEN {{.get_pg_segment}} = 1 THEN pg_segment
        ELSE 'overall'
    END AS pill_type,
    SUM(gmv_mra) AS total_sales,
    SUM(total_orders) AS total_orders,
    SUM(commissionable_amount) AS total_cv,
    SUM(commissionable_amount)/SUM(total_orders) AS aov,
    SUM(total_orders)*100.0/SUM(menu_opens) AS m2o,
    SUM(merchant_voucher_discount)/SUM(total_orders) as mvdo,
    (SUM(merchant_voucher_discount) + SUM(bogo_discount) + SUM(total_brand_pack_discount))*100/SUM(bill_subtotal) AS effective_discount,
    SUM(zvd_final)/SUM(total_orders) as zvdo,
    SUM(total_orders)*100.0/SUM(cart_builts) AS c2o
  FROM 
    {{.table}} 
  WHERE 
    res_id in ({{.res_ids}})
    AND "timestamp" BETWEEN {{.start_timestamp_millis}} AND {{.end_timestamp_millis}} 
  GROUP BY week, pill_type, res_id

filters:
  - res_ids
  - start_timestamp_millis
  - end_timestamp_millis
  - get_pg_segment
  - get_weeknum
  - get_monthnum
  
