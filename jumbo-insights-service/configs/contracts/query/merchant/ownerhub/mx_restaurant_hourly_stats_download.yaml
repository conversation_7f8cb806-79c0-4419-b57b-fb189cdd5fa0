# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: restaurant_hourly_stats
identifier: mx_restaurant_hourly_stats_download
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: All real time metrics
# Time in second
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT res_id, SUM(menu_impressions) as menu_impressions,
  SUM(menu_opens) as menu_opens,
  SUM(cart_created) as cart_created,
  SUM(order_placed) as order_placed,
  SUM(ads_impressions) as ads_impressions,
  SUM(ads_clicks) as ads_clicks,
  SUM(offline_hours) as offline_hours,
  weekOfYear("timestamp", 'Asia/Kolkata') as ist_isoweek,
  day("timestamp", 'Asia/Kolkata')  as ist_day, month("timestamp", 'Asia/Kolkata')  as ist_month,
  year("timestamp", 'Asia/Kolkata')  as ist_year from restaurant_hourly_stats
  WHERE "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
  AND res_id IN ({{.res_id}})
  GROUP BY res_id, ist_isoweek, ist_day, ist_month, ist_year
filters:
  - res_id
  - start_time
  - end_time
