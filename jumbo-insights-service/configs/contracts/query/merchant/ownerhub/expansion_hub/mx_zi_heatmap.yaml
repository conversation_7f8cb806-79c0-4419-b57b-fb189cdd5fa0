# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: supply_demand_cell_heatmap 
identifier: mx_zi_heatmap
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: get zi outlet expansion heatmap info

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
    brand_name,
    city_id,
    cell_id,
    supply,
    demand
  FROM supply_demand_cell_heatmap 
  WHERE brand_id IN ({{.brand_id}})
  AND city_id IN ({{.city_ids}})

filters:
  - brand_id
  - city_ids