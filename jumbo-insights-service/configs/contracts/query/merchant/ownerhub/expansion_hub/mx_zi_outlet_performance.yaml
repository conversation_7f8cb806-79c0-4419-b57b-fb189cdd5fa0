# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: mx_zi_outlet_performance
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Z Intelligence home page outlet performance query

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
    CASE
        WHEN {{.location_agg_type}} = 1 THEN subzone_id
        WHEN {{.location_agg_type}} = 2 THEN city_id
        WHEN {{.location_agg_type}} = 3 THEN locality_id
        WHEN {{.location_agg_type}} = 4 THEN res_id
        ELSE 0
    END AS location_agg_type_id,
    CASE
        WHEN {{.location_agg_type}} = 1 THEN subzone_name
        WHEN {{.location_agg_type}} = 2 THEN city_name
        WHEN {{.location_agg_type}} = 3 THEN locality_name
        WHEN {{.location_agg_type}} = 4 THEN res_name
        ELSE 'all'
    END AS location_agg_type_name,
    MAX(city_id) AS city_id,
    MAX(locality_id) AS lclty_id,
    COUNT(DISTINCT res_id) AS total_res_count,
    SUM(total_orders)/COUNT(DISTINCT dt) AS avg_orders_per_day,
    SUM(delivered_merchant_received_amount)/SUM(delivered_orders) AS aov
  FROM ba_hub_daily_stats
  WHERE brand_id > 0
  AND ( 
    CASE WHEN {{.filter_res_ids}} = 1 THEN res_id IN ({{.res_id_array}}) ELSE TRUE END
  ) AND (
    CASE WHEN {{.filter_supply_brand_ids}} = 1 THEN brand_id IN ({{.brand_id_array}}) ELSE TRUE END
  )
  AND brand_id > 0
  AND locality_id > 0
  AND "timestamp" BETWEEN {{.start_time_unix_millis}}
                      AND {{.end_time_unix_millis}}
  GROUP BY location_agg_type_id, location_agg_type_name

filters:
  - filter_res_ids
  - res_id_array
  - filter_supply_brand_ids
  - brand_id_array
  - location_agg_type
  - start_time_unix_millis
  - end_time_unix_millis