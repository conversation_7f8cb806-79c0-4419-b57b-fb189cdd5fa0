# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: new_outlets_location_intelligence 
identifier: mx_zi_intelligence_map_and_list_view
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: get all cities for res mapped to user

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
    brand_name,
    cell_id,
    cell_lat,
    cell_long,
    chunk,
    city_id,
    city_name,
    currently_operational_flag,
    default_flag,
    demand_flag,
    locality_id,
    locality_name,
    opd,
    percent_range,
    spd,
    supply_flag
  FROM new_outlets_location_intelligence 
  WHERE brand_id IN ({{.brand_id}}) 
  AND city_id IN ({{.city_ids}})

filters:
  - brand_id
  - city_ids