# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: new_outlets_location_intelligence 
identifier: mx_zi_expansion_city_types
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: get all cities for res mapped to user

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
    city_id,
    city_name,
    COUNT(*) AS recos
  FROM new_outlets_location_intelligence 
  WHERE brand_id IN ({{.brand_id}})
  AND default_flag = 1
  GROUP BY city_id, city_name

filters:
  - brand_id