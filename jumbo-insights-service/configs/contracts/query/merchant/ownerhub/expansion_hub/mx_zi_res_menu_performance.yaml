# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: mx_dish_items_performance
identifier: mx_zi_res_menu_performance
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Z Intelligence home page menu items performance query

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT
    item_name,
    MAX(catalogue_id) AS catalogue_id,
    SUM(item_quantity)/COUNT(DISTINCT ist_month) AS orders_per_month,
    SUM(item_revenue)/COUNT(DISTINCT ist_month) AS sales_per_month 
  FROM mx_dish_items_performance
  WHERE res_id IN (
    {{.res_ids}}
  )
  AND "merchant_item_revenue_created_at" BETWEEN {{.start_time_unix_millis}} 
                                             AND {{.end_time_unix_millis}}
  GROUP BY item_name

filters:
  - res_ids
  - start_time_unix_millis
  - end_time_unix_millis