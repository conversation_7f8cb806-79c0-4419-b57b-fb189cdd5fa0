# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: mx_total_sales
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Total Sales and delivered orders for Merchant
# TTL for cache, Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 160
# Dimensions
columns:
  - name: merchant_order_res_id
  - name: total_sales
    func: sum
    source_column: merchant_order_total_cost
  - name: delivered_orders
    func: count
    source_column: consumer_order_id
# Group by columns
aggregations:
  - merchant_order_res_id
filters:
  - merchant_order_res_id
  - merchant_order_state
