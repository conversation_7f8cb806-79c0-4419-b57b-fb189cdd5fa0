# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: d1_compare_performance_city_level
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: BA Hub stats

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT city_id, SUM(low_rated_orders) as total_poor_rated_orders,
  SUM(kpt_delayed_orders) as total_delayed_orders,
  SUM(composite_mx_rejected_orders) as total_rejected_orders,
  SUM(supply_mx_sent_refund) + SUM(supply_mx_attr_non_refund) as total_complaints,
  SUM(total_orders) as order_placed,
  SUM(kitchent_prep_time) as total_kpt,
  SUM(actual_visibility) as total_actual_visibility,
  SUM(expected_visibility) as total_expected_visibility,
  SUM(impressions) as res_impressions,
  SUM(menu_opens) as menu_opens,
  SUM(cart_builts) as cart_created
  FROM {{.table}}
  WHERE "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
  AND total_orders > 0
  AND city_id IN ({{.city_id}})
  AND primary_cuisine IN ({{.cuisines}})
  AND avg_ov_bucket IN ({{.avg_ov_bucket}})
  GROUP BY city_id
filters:
  - avg_ov_bucket
  - cuisines
  - city_id
  - total_poor_rated_orders_percentile
  - total_delayed_orders_percentile
  - total_rejected_orders_percentile
  - total_complaints_percentile
  - total_orders_percentile
  - total_kpt_percentile
  - total_actual_visibility_percentile
  - total_expected_visibility_percentile
  - res_impressions_percentile
  - menu_opens_percentile
  - cart_created_percentile
  - start_time
  - end_time
