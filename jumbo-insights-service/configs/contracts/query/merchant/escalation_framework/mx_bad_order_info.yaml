# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: mx_bad_order_info
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Merchant bad order stats

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
  res_id,
  (SUM(supply_mx_sent_refund_pp) + SUM(supply_mx_attr_non_refund_pp))*100/SUM(total_orders) AS poor_packaging_share,
  (SUM(supply_mx_sent_refund_pq) + SUM(supply_mx_attr_non_refund_pq))*100/SUM(total_orders) AS poor_quality_share,
  SUM(composite_mx_rejected_orders)*100.0/SUM(total_orders) AS rejections_share,
  SUM(bad_orders)*100.0/SUM(total_orders) AS customer_complaints_share

  FROM {{.table}}
  WHERE "timestamp" BETWEEN {{.start_timestamp}} 
                        AND {{.end_timestamp}}
  AND res_id in ({{.res_id}})
  GROUP BY res_id

filters:
  - res_id
  - start_timestamp
  - end_timestamp
