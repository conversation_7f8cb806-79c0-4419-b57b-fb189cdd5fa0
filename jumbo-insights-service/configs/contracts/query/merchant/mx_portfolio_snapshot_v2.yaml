# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: ba_hub_portfolio_snapshot_v2
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: BA Hub stats

# Time in second
caching_ttl: 1500
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT res_id, SUM(commissionable_value) as commissionable_value,
  SUM(commissionable_value_minus_7) as commissionable_value_minus_7,
  SUM(delivered_merchant_received_amount) as merchant_received_amount,
  SUM(actual_visibility) as actual_visibility,
  SUM(expected_visibility) as expected_visibility,
  SUM(delivered_orders) as delivered_orders,
  CASE
    WHEN {{.category_enum}} = 1 THEN concat(concat(year("timestamp", 'Asia/Kolkata') , month("timestamp", 'Asia/Kolkata'), '_'), day("timestamp", 'Asia/Kolkata'), '_')
    WHEN {{.category_enum}} = 2 THEN concat(year("timestamp", 'Asia/Kolkata') , weekOfYear("timestamp", 'Asia/Kolkata'), '_')
    WHEN {{.category_enum}} = 3 THEN concat(year("timestamp", 'Asia/Kolkata') , month("timestamp", 'Asia/Kolkata'), '_')
  ELSE ''
  END AS group_category
  FROM {{.table}}
  WHERE "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
  AND res_id IN ({{.res_id}})
  GROUP BY res_id, group_category
filters:
  - res_id
  - start_time
  - end_time
  - category_enum
