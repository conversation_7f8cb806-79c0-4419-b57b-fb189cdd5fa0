# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: order_rejected_summary
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description : Get total cost of rejected orders aggregated daily and filtered by rejection id, inside a time range
# TTL for cache, Time in seconds
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 120
# Request per second
rate_limit: 20
# Dimensions
columns:
  - name: merchant_order_total_cost
    func: sum
    source_column: merchant_order_total_cost
  - name: merchant_order_created_ist_day
  - name: merchant_order_created_ist_isoweek
  - name: merchant_order_created_ist_month
# Group by columns
aggregations:
  - merchant_order_created_ist_day
  - merchant_order_created_ist_isoweek
  - merchant_order_created_ist_month
filters:
  - merchant_order_res_id
  - merchant_order_state
  - merchant_order_reject_reason_id
