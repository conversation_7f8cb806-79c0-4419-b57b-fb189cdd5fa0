# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: d1_customer_experience_detailed
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: BA Hub stats

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    res_id, 
    SUM(rated_1_orders) as rated_1_orders, 
    SUM(rated_2_orders) as rated_2_orders, 
    SUM(rated_3_orders) as rated_3_orders, 
    SUM(rated_4_orders) as rated_4_orders,
    SUM(rated_5_orders) as rated_5_orders, 
    SUM(supply_mx_sent_refund_wo) + SUM(supply_mx_attr_non_refund_wo) as wrong_orders,
    SUM(supply_mx_sent_refund_mi) + SUM(supply_mx_attr_non_refund_mi) as missing_items, 
    SUM(supply_mx_sent_refund_pq) + SUM(supply_mx_attr_non_refund_pq) as poor_quality,
    SUM(supply_mx_sent_refund_pp) + SUM(supply_mx_attr_non_refund_pp) as poor_packaging, 
    SUM(dst_order_delayed) as order_delayed,
    SUM(dst_poor_quantity) as poor_quantity, 
    SUM(dst_out_of_stock) as out_of_stock,
    SUM(supply_mx_sent_refund) + SUM(supply_mx_attr_non_refund) as total_complaints, 
    SUM(mx_nearing_closing_time) as nearing_close_time_rejections,
    SUM(mx_not_operational_today) as res_not_operational_today, 
    SUM(mx_restaurant_is_closed) as res_is_closed,
    SUM(mx_restaurant_has_not_opened_yet) as res_not_open, 
    SUM(mx_item_out_of_stock) as item_out_of_stock_rejections, 
    SUM(mx_kitchen_is_full) as kitchen_is_full_rejections, 
    SUM(mx_no_delivery_boys) as no_delivery_boy_rejections,
    SUM(mx_out_of_subzone_area) as mx_out_of_subzone_area_rejections, 
    SUM(mx_no_answer) as mx_no_answer_rejections,
    SUM(mx_content_issue) as mx_content_issue_rejections, 
    SUM(mx_moq_issue) as mx_moq_issue_rejections,
    SUM(mx_wrong_res_address) as mx_wrong_res_address_rejections, 
    SUM(composite_mx_rejected_orders) as total_rejected_orders,
    SUM(kpt_delay_10_to_20_minutes) as kpt_delay_10_to_20_mins, 
    SUM(kpt_delay_20_to_30_minutes) as kpt_delay_20_to_30_mins, 
    SUM(kpt_delay_30_plus_minutes) as kpt_delay_30_plus_mins
  FROM {{.table}}
  WHERE "timestamp" BETWEEN {{.start_time}} AND {{.end_time}}
  AND res_id IN ({{.res_id}})
  GROUP BY res_id
filters:
  - res_id
  - start_time
  - end_time
