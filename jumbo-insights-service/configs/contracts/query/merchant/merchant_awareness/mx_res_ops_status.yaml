# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: res_visibility_stats
identifier: mx_res_ops_status
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Merchant time interval based operational status

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    time_str,
    CAST(day AS INT) AS day,
    operational_status_type,
    CAST(mins_diff AS INT) AS mins_diff
  FROM {{.table}}
  WHERE res_id IN ({{.res_id}})
  AND "timestamp" BETWEEN {{.start_timestamp}} 
                      AND {{.end_timestamp}}
  
filters:
  - res_id
  - start_timestamp
  - end_timestamp
