# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: mx_dish_items_performance
identifier: mx_res_ioos_orders
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Merchant item level out of stock rejections

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    item_name, 
    SUM(oos_orders) AS ioos_orders
  FROM {{.table}}
  WHERE res_id IN ({{.res_id}})
  AND oos_orders > 0
  AND "merchant_item_revenue_created_at" BETWEEN {{.start_timestamp}} 
                                             AND {{.end_timestamp}}
  GROUP BY item_name
filters:
  - res_id
  - start_timestamp
  - end_timestamp
