# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: mx_res_daily_loss
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Merchant loss metrics like orders, sales and customers

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
  res_id,
  SUM(composite_mx_lost_sales) AS lost_sales,
  SUM(composite_mx_rejected_orders) AS rejected_orders,
  SUM(composite_mx_rejected_orders) AS lost_customers,
  SUM(mx_item_out_of_stock) AS mx_ioos_rejections,
  SUM(mx_kitchen_is_full) AS mx_kitchen_is_full,
  SUM(mx_restaurant_is_closed) AS mx_outlet_closed,
  lastWithTime(last_week_estimated_lost_sales, "timestamp", 'DOUBLE') AS estimated_lost_sales
          
  FROM {{.table}}  
  WHERE "timestamp" BETWEEN {{.start_timestamp}} AND {{.end_timestamp}}
  AND res_id in ({{.res_id}})
  GROUP BY res_id
  
filters:
  - res_id
  - start_timestamp
  - end_timestamp
