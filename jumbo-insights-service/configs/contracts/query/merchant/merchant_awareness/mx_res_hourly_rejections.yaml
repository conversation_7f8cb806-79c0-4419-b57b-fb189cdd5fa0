# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: mx_res_hourly_rejections
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Merchant hourly order rejections

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
  merchant_order_created_ist_hour AS hour_num, 
  COUNT(consumer_order_id) AS hourly_rejections
          
  FROM {{.table}}  
  WHERE "merchant_order_created_at" BETWEEN {{.start_timestamp}} AND {{.end_timestamp}}
  AND merchant_order_res_id IN ({{.res_id}})
  AND consumer_order_delivery_mode IN ('DELIVERY', 'delivery')
  AND merchant_order_state IN ({{.mx_reject_order_state}})
  AND merchant_order_reject_reason_id IN ({{.mx_reject_reason_id}})
  GROUP BY hour_num
filters:
  - res_id
  - start_timestamp
  - end_timestamp
  - mx_reject_order_state
  - mx_reject_reason_id
