# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: mx_res_reject_reason_lvl_daily_loss
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Merchant reason level rejected orders and lost sales

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
  merchant_order_res_id,
  COUNT(consumer_order_id) AS reason_attributed_lost_orders,
  SUM(merchant_order_total_cost) AS reason_attributed_lost_sales
          
  FROM {{.table}}  
  WHERE "merchant_order_created_at" BETWEEN {{.start_timestamp}} AND {{.end_timestamp}}
  AND merchant_order_res_id IN ({{.res_id}})
  AND consumer_order_delivery_mode IN ('DELIVERY', 'delivery')
  AND merchant_order_state IN ({{.mx_reject_order_state}})
  AND merchant_order_reject_reason_id IN ({{.mx_reject_reason_id}})
  GROUP BY merchant_order_res_id
filters:
  - res_id
  - start_timestamp
  - end_timestamp
  - mx_reject_order_state
  - mx_reject_reason_id
