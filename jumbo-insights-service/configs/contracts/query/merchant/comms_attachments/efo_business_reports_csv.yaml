# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: composite_order_events
identifier: efo_business_reports_csv
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: create attachement csvs for sending out comms to mx for Quick Orders

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
    consumer_order_res_id AS "Outlet ID",
    merchant_order_created_ist_dt AS "Order Date",
    consumer_order_id AS "Order ID",
    DATETIMECONVERT(
      merchant_order_state_history_accepted_time,
      '1:SECONDS:EPOCH',
      '1:SECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm',
      '1:SECONDS'
    ) AS "Order Accepted",
    DATETIMECONVERT(
      merchant_order_state_history_ready_time,
      '1:SECONDS:EPOCH',
      '1:SECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm',
      '1:SECONDS'
    ) AS "Order Ready",
    DATETIMECONVERT(
      logistics_order_state_history_arrived_time,
      '1:SECONDS:EPOCH',
      '1:SECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm',
      '1:SECONDS'
    ) AS "Rider Arrived",
    DATETIMECONVERT(
      logistics_order_state_history_picked_time,
      '1:SECONDS:EPOCH',
      '1:SECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm',
      '1:SECONDS'
    ) AS "Picked Up",  
    CASE
      WHEN merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time 
        THEN logistics_order_state_history_picked_time - merchant_order_state_history_ready_time
      WHEN merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time 
        THEN logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time
      ELSE 0
    END AS "Handover Time",
    CASE
      WHEN merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time 
        AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time <= 120
          THEN 'Accurate'
      WHEN merchant_order_state_history_ready_time < logistics_order_state_history_arrived_time 
        AND logistics_order_state_history_picked_time - logistics_order_state_history_arrived_time <= 120
          THEN 'Accurate'
      ELSE 'Inaccurate'
    END AS "FOR Accuracy",
    CASE
      WHEN merchant_order_state_history_ready_time >= logistics_order_state_history_arrived_time
        AND logistics_order_state_history_picked_time - merchant_order_state_history_ready_time <= 120
          THEN merchant_order_state_history_ready_time - merchant_order_state_history_accepted_time
      ELSE logistics_order_state_history_picked_time - merchant_order_state_history_accepted_time - 120
    END AS "KPT"
  FROM composite_order_events
  WHERE merchant_order_created_at BETWEEN {{.start_timestamp}} 
                                    AND {{.end_timestamp}}
  AND consumer_order_delivery_speed = 'EXPRESS_FOOD_DELIVERY'
  AND merchant_order_res_id in ({{.res_ids}})

filters:
  - res_ids
  - start_timestamp
  - end_timestamp