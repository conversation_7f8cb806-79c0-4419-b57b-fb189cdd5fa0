# can be pinot/trino
query_backend: clickhouse
tenant: zanalytics
table: mx_enterprise_stats
identifier: enterprise_metric_reports_es_clickhouse_dt
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query to power enterprise dashboard metrics - gives the max refresh dt in table

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: > 
  SELECT
      max(dt) as max_dt
  FROM 
      s3(
      's3://zanalytics-jumbo/insights_etls.db/mx_enterprise_stats/dt=*/period_type=*/dimension_type=*/*',
      'Parquet'
      )
  WHERE
      dt between {{.start_dt}} and {{.end_dt}}
      and period_type = {{.period_type}}
      and dimension_type = 'overall'

filters:
  - start_dt
  - end_dt
  - period_type

