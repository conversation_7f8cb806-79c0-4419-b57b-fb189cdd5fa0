# can be pinot/trino
query_backend: clickhouse
tenant: zanalytics
table: mx_enterprise_stats
identifier: enterprise_metric_reports_es_clickhouse_o_v2
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query to power enterprise dashboard metrics - Offer Metrics

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: > 
  with
  base as (
      
      select 
          case
              when {{.agg_enum}} = 0 then toString(ist_day)
              when {{.agg_enum}} in (2, 3) then toString(dt)
              when {{.agg_enum}} = 5 then concat(toString(ist_year), '-', toString(ist_month))
              when {{.agg_enum}} = 7 then toString(toDayOfWeek(toDateTime("timestamp", 'Asia/Kolkata')))
              when {{.agg_enum}} = 8 then (
                    case 
                      when ist_month = 12 and ist_isoweek = 1 
                        then concat(toString(ist_year + 1), '_', leftPad(toString(ist_isoweek), 2, '0')) 
                      else concat(toString(ist_year), '_', leftPad(toString(ist_isoweek), 2, '0'))
                    end
                )
              else 'agg'
          end as agg_level_str,
          case
              when {{.granularity}} = 2 then city_id
              when {{.granularity}} = 3 then res_id
              when {{.granularity}} = 4 then brand_id
              else 10000
          end as dimension_id,
          case
              when {{.granularity}} = 2 then city_name
              when {{.granularity}} = 3 then res_name
              when {{.granularity}} = 4 then brand_name
              else dimension_value
          end as dimension_name,
          case
              when {{.granularity}} = 2 then 'Overall'
              when {{.granularity}} = 3 then concat(subzone_name,'#',city_name)
              when {{.granularity}} = 4 then 'Overall'
          else 'Overall'
          end as subzone_city,
          min("timestamp") as agg_level,    
          sum(total_orders) as total_orders,
          sum(delivered_orders) as delivered_orders,
          sum(merchant_received_amount) as merchant_received_amount,
          sum(commissionable_amount) as commissionable_amount,
          sum(bill_subtotal) as bill_subtotal,
          sum(packaging_charges) as packaging_charges,
          sum(mvd_discount) as mvd_discount, 
          sum(zvd_discount) as zvd_discount,
          count(distinct res_id) as res_count,
  
          sum(mvd_bill_subtotal) as mvd_bill_subtotal,
          sum(mvd_orders) as mvd_orders,        
          sum(dish_discount) as dish_discount,
          sum(bogo_discount) as bogo_discount,
          sum(mx_refund) as mx_refund, 
          sum(freebie_discount) as freebie_discount,
          sum(gold_discount) as gold_discount,
          sum(dotd_orders) as dotd_orders,
          sum(dotd_discount) as dotd_discount,
          sum(flash_sale_orders) as flash_sale_orders,
          sum(flash_sale_discount) as flash_sale_discount,
          sum(ad_spends) as ad_spends,
          sum(ad_cv) as ad_cv
      from s3(
          's3://zanalytics-jumbo/insights_etls.db/mx_enterprise_stats/dt=*/period_type=*/dimension_type=*/*',
          'Parquet'
      )
      where 
      CASE 
        WHEN -1 in ({{.dt_array}})
            THEN dt between {{.start_dt}} and {{.end_dt}}
        ELSE dt in ({{.dt_array}})
      END
      and period_type = {{.period_type}}
      and dimension_type = {{.dimension_type}}
      and 
          CASE 
          WHEN {{.filter_applied}} = 0
          THEN 
              (
                  (CASE WHEN -2 in ({{.brand_ids}}) THEN 1=0 ELSE brand_id in ({{.brand_ids}}) END)
                  OR (CASE WHEN -2 in ({{.city_ids}}) THEN 1=0 ELSE city_id in ({{.city_ids}}) END)
                  OR (CASE WHEN -2 in ({{.legal_entity_ids}}) THEN 1=0 ELSE merchant_id in ({{.legal_entity_ids}}) END)
                  OR (CASE WHEN -2 in ({{.res_ids}}) THEN 1=0 ELSE res_id in ({{.res_ids}}) END)
              )
          ELSE 
              (
                (
                  (CASE WHEN -2 in ({{.brand_ids}}) THEN 1=0 ELSE brand_id in ({{.brand_ids}}) END)
                  OR (CASE WHEN -2 in ({{.city_ids}}) THEN 1=0 ELSE city_id in ({{.city_ids}}) END)
                  OR (CASE WHEN -2 in ({{.legal_entity_ids}}) THEN 1=0 ELSE merchant_id in ({{.legal_entity_ids}}) END)
                  OR (CASE WHEN -2 in ({{.res_ids}}) THEN 1=0 ELSE res_id in ({{.res_ids}}) END)
              )
              AND
                (
                  (CASE WHEN -1 in ({{.selected_brand_ids}}) THEN 1=1 ELSE brand_id in ({{.selected_brand_ids}}) END)
                  AND (CASE WHEN -1 in ({{.selected_city_ids}}) THEN 1=1 ELSE city_id in ({{.selected_city_ids}}) END)
                  AND (CASE WHEN -1 in ({{.selected_legal_entity_ids}}) THEN 1=1 ELSE merchant_id in ({{.selected_legal_entity_ids}}) END)
                  AND (CASE WHEN -1 in ({{.selected_res_ids}}) THEN 1=1 ELSE res_id in ({{.selected_res_ids}}) END)
                )
              )
          END
      group by agg_level_str, dimension_id, dimension_name, subzone_city
      
  )

  select 
      agg_level_str,
      dimension_id,
      dimension_name,
      agg_level,
      subzone_city,

      mvd_bill_subtotal AS gross_sales_offers_value,
      coalesce(mvd_bill_subtotal * 100.0 / nullif((bill_subtotal + packaging_charges) ,0) ,0) AS gross_sales_offers_ratio,
      mvd_orders AS offer_orders_count,
      coalesce(mvd_orders * 100.0 / nullif(total_orders ,0) ,0) AS offer_orders_ratio,
      coalesce((mvd_discount) * 100.0 / nullif(bill_subtotal ,0),0) AS effective_discount_ratio,
      coalesce((mvd_discount) * 1.0 / nullif(total_orders ,0),0) AS discount_per_order_ratio,
      mvd_discount AS total_discount_value,
      coalesce(mvd_discount * 100.0 / nullif((mvd_discount + zvd_discount),0) ,0) AS promo_discount_ratio,
      coalesce(dish_discount * 100.0 / nullif(mvd_discount ,0) ,0) AS dish_discount_ratio,
      coalesce(bogo_discount * 100.0 / nullif(mvd_discount ,0) ,0) AS bogo_discount_ratio,
      coalesce(freebie_discount * 100.0 / nullif(mvd_discount ,0) ,0) AS freebie_discount_ratio,
      coalesce(gold_discount * 100.0 / nullif(mvd_discount ,0) ,0) AS gold_discount_ratio,
      dotd_orders AS dotd_orders_count,
      dotd_discount AS dotd_discount_value,
      flash_sale_orders AS flash_sale_orders_count,
      flash_sale_discount AS flash_sale_discount_value,
      zvd_discount AS zvd_discount_value,
      coalesce(zvd_discount * 1.0 / nullif(total_orders ,0) ,0) AS zvd_discount_per_order_ratio,
      coalesce(zvd_discount * 100.0 / nullif((mvd_discount + zvd_discount) ,0) ,0) AS zvd_discount_ratio,
      coalesce(zvd_discount * 100.0 / nullif(ad_cv ,0) ,0) AS ads_billed_spent_as_zvd_ratio,
      mvd_discount AS promo_discount_value,
      dish_discount AS dish_discount_value,
      bogo_discount AS bogo_discount_value,
      freebie_discount AS freebie_discount_value,
      gold_discount AS gold_discount_value
  FROM
      base

filters:
  - agg_enum
  - granularity
  - city_ids
  - res_ids
  - filter_applied
  - legal_entity_ids
  - brand_ids
  - selected_res_ids
  - selected_city_ids
  - selected_legal_entity_ids
  - selected_brand_ids
  - start_dt
  - end_dt
  - dimension_type
  - period_type
  - dt_array

