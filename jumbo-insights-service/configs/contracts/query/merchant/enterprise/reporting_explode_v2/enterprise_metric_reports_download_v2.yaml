# can be pinot/trino
query_backend: trino
tenant: Zomato
catalog: zomato
schema: insights_etls
table: mx_enterprise_stats
identifier: enterprise_metric_reports_download_v2
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchant-orchestration-service
  pd_service_name: merchant_platform
  description: Enterprise Metrics download
# Time in ms
sla: 60000
# Request per second
rate_limit: 10
caching_ttl: 300
refresh_interval: 150
# contract type
type: sql
sql: >
    SELECT 
        CASE
        WHEN {{.agg_enum}} = 0 THEN date_format(
        from_unixtime("timestamp"), 
        '%d'
        )

        WHEN {{.agg_enum}} IN (2, 3) THEN dt

        WHEN {{.agg_enum}} = 5 THEN date_format(
        from_unixtime("timestamp"), 
        '%Y-%m'
        )

        WHEN {{.agg_enum}} = 7 THEN date_format(
        from_unixtime("timestamp"), 
        '%a'
        )

        WHEN {{.agg_enum}} = 8 THEN (
        CASE 
        WHEN ist_month = 12 AND ist_isoweek = 1 THEN 
            concat(CAST(ist_year + 1 AS VARCHAR), lpad(CAST(ist_isoweek AS VARCHAR), 2, '0'), '_')
        ELSE 
            concat(CAST(ist_year AS VARCHAR), lpad(CAST(ist_isoweek AS VARCHAR), 2, '0'), '_')
        END
        )
        ELSE 'agg'
        END AS agg_level_str,
        CASE
        WHEN {{.granularity}} = 1 then cast(base.res_id as varchar)
        WHEN {{.granularity}} = 2 then base.brand_name
        WHEN {{.granularity}} = 3 then base.brand_name
        WHEN {{.granularity}} = 4 then base.brand_name
        WHEN {{.granularity}} = 5 then cast(base.res_id as varchar)
        ELSE '10000'
        END as dimension_id,
        CASE
        WHEN {{.granularity}} = 1 then base.city_name
        WHEN {{.granularity}} = 2 then 'Overall'
        WHEN {{.granularity}} = 3 then base.city_name
        WHEN {{.granularity}} = 4 then base.city_name
        WHEN {{.granularity}} = 5 then 'Overall'
        ELSE '10000'
        END as city_name,
        CASE
        WHEN {{.granularity}} = 1 then base.subzone_name
        WHEN {{.granularity}} = 2 then 'Overall'
        WHEN {{.granularity}} = 3 then 'Overall'
        WHEN {{.granularity}} = 4 then base.subzone_name
        WHEN {{.granularity}} = 5 then base.subzone_name
        ELSE '10000'
        END as subzone_name,
        CASE
        WHEN {{.granularity}} = 1 then base.brand_name
        WHEN {{.granularity}} = 5 then base.brand_name
        ELSE 'Overall'
        END as brand_name,
        CASE
          WHEN dimension_type in ('mealtime_nrl_pg', 'mealtime_nrl', 'mealtime_pg', 'mealtime') THEN split_part(dimension_value, '_', 1)
        ELSE 'all'
        END as mealtime,
        CASE
          WHEN dimension_type in ('mealtime_nrl_pg', 'mealtime_nrl') THEN split_part(dimension_value, '_', 2)
          WHEN dimension_type in ('nrl_pg', 'nrl') THEN split_part(dimension_value, '_', 1)
        ELSE 'all'
        END as nrl,
        CASE
          WHEN dimension_type in ('mealtime_nrl_pg') THEN split_part(dimension_value, '_', 3)
          WHEN dimension_type in ('nrl_pg', 'mealtime_pg') THEN split_part(dimension_value, '_', 2)
          WHEN dimension_type in ('pg') THEN split_part(dimension_value, '_', 1)
        ELSE 'all'
        END as pg,
        min("timestamp") AS agg_level,
        coalesce(sum(bill_subtotal) + sum(packaging_charges),0)*1.00 as gross_sale_value,
        coalesce(sum(bill_subtotal),0)*1.00 as subtotal_value,
        coalesce(sum(commissionable_amount) ,0)*1.00 as commissionable_value,
        coalesce(sum(bill_subtotal) + sum(packaging_charges) -  sum(mvd_discount) ,0)*1.00 as net_sales_value,
        coalesce(sum(total_orders),0) as total_orders_count,
        coalesce(sum(delivered_orders) ,0) as delivered_orders_count,
        coalesce(sum(bill_subtotal)*1.00 / nullif(sum(delivered_orders),0) ,0) as average_subtotal_value,
        coalesce((sum(bill_subtotal) + sum(packaging_charges))*1.00 /  nullif(sum(delivered_orders),0) ,0) as average_gross_sale_value,
        coalesce(sum(commissionable_amount)*1.00/nullif(sum(delivered_orders),0),0) as average_commissionable_value,
        coalesce((sum(bill_subtotal) + sum(packaging_charges) -  sum(mvd_discount))*1.00 / nullif(sum(delivered_orders),0) ,0) as average_order_value,
        coalesce(sum(order_items)*1.00 / nullif(sum(delivered_orders),0) ,0) as items_per_order_ratio,
        coalesce((sum(bill_subtotal) + sum(packaging_charges) -  sum(mvd_discount))*1.00 /nullif(count(distinct res_id),0),0) as sales_per_outlet_ratio,
        coalesce(sum(delivered_orders) *1.00 / nullif(count(distinct res_id) ,0) ,0) as orders_per_outlet_ratio,
        coalesce(sum(cx_cancelled_mx_refund),0)*1.00 as mx_refund_cx_cancelled_orders_value,
        coalesce(sum(packaging_charges),0)*1.00 as packaging_charges_value,
        coalesce(sum(mvd_bill_subtotal),0)*1.00 AS gross_sales_offers_value,
        coalesce(sum(impressions) ,0) as impressions_count,
        coalesce(sum(menu_opens)  ,0) as menu_opens_count,
        coalesce(sum(cart_builds) ,0) as cart_builds_count,
        coalesce(sum(order_makes) ,0) as order_placed_count,
        coalesce(sum(menu_opens)*100.0 / nullif(sum(impressions) ,0) ,0) as i2m_ratio,
        coalesce(sum(order_makes)*100.0 / nullif(sum(menu_opens) ,0) ,0) as m2o_ratio,
        coalesce(sum(cart_builds)*100.0 / nullif(sum(menu_opens) ,0) ,0) as m2c_ratio,
        coalesce(sum(order_makes)*100.0 / nullif(sum(cart_builds),0) ,0)  as c2o_ratio,
        coalesce(sum(mvd_bill_subtotal) * 100.00 / nullif(sum(bill_subtotal) + sum(packaging_charges),0 ),0) AS gross_sales_offers_ratio,
        coalesce(sum(search_menu_opens),0)*1.00 as brand_search_mo_count,
        coalesce(sum(recommended_for_you_menu_opens),0) as recommended_for_you_mo_count,
        coalesce(sum(dish_or_cuisine_menu_opens),0) as dishcuisine_search_mo_count,
        coalesce(sum(home_page_menu_opens),0) as homepage_listing_mo_count,
        coalesce(sum(offers_page_menu_opens),0) as offers_page_mo_count,
        coalesce(sum(campaign_menu_opens),0) as campaign_pages_mo_count,
        coalesce(sum(other_menu_opens),0) as other_mo_count,
        coalesce(sum(mvd_orders),0) AS offer_orders_count,
        coalesce(sum(mvd_orders) * 100.00 / nullif(sum(total_orders),0),0) AS offer_orders_ratio,
        coalesce(sum(mvd_discount) * 1.00 / nullif(sum(bill_subtotal),0),0) AS effective_discount_ratio,
        coalesce(sum(mvd_discount) * 1.00 / nullif(sum(total_orders),0),0) AS discount_per_order_ratio,
        coalesce(sum(mvd_discount + dish_discount + bogo_discount + freebie_discount + gold_discount + dotd_discount + flash_sale_discount),0)*1.00 AS total_discount_value,
        coalesce(sum(mvd_discount) * 100.00 / nullif(sum(mvd_discount) + sum(zvd_discount),0),0) AS promo_discount_ratio,
        coalesce(sum(dish_discount)  * 100.00 / nullif(sum(mvd_discount),0),0) AS dish_discount_ratio,
        coalesce(sum(bogo_discount)  * 100.00 / nullif(sum(mvd_discount),0),0) AS bogo_discount_ratio,
        coalesce(sum(freebie_discount) * 100.00 / nullif(sum(mvd_discount),0),0) AS freebie_discount_ratio,
        coalesce(sum(gold_discount) * 100.00 / nullif(sum(mvd_discount),0),0) AS gold_discount_ratio,
        coalesce(sum(dotd_orders),0) AS dotd_orders_count,
        coalesce(sum(dotd_discount),0)*1.00 AS dotd_discount_value,
        coalesce(sum(flash_sale_orders),0) AS flash_sale_orders_count,
        coalesce(sum(flash_sale_discount) ,0)*1.00 AS flash_sale_discount_value,
        coalesce(sum(zvd_discount),0)*1.00 AS zvd_discount_value,
        coalesce(sum(zvd_discount) * 1.00 / nullif(sum(total_orders),0),0) AS zvd_discount_per_order_ratio,
        coalesce(sum(zvd_discount) * 100.00 / nullif(sum(mvd_discount) + sum(zvd_discount),0),0) AS zvd_discount_ratio,
        coalesce(sum(zvd_discount) * 100.00 / nullif(sum(ad_cv),0),0) AS ads_billed_spent_as_zvd_ratio,
        coalesce(sum(rating_sum)*1.00 / nullif(sum(rated_orders),0),0) as food_rating_value,
        coalesce((sum(one_rated_orders) + sum(two_rated_orders))*100.00 / nullif(sum(total_orders),0),0) as poor_rated_orders_ratio,
        coalesce((sum(mx_rejected_orders))*100.00 / nullif(sum(total_orders),0),0) as rejected_orders_ratio,
        coalesce((sum(mx_ioos_rejected_orders))*100.00 / nullif(sum(total_orders),0),0) as rejected_ioos_ratio,
        coalesce((sum(mx_kif_rejected_orders))*100.00 / nullif(sum(total_orders),0),0) as rejected_kif_ratio,
        coalesce(sum(mx_outlet_closed_rejected_orders)*100.00 / nullif(sum(total_orders),0),0) as rejected_outlet_closed_ratio,
        coalesce(sum(mx_timeout_rejected_orders) *100.00 / nullif(sum(total_orders),0),0) as rejected_timeout_ratio,
        coalesce(sum(mx_device_issues_rejected_orders) *100.00 / nullif(sum(total_orders),0),0) as rejected_di_ratio,
        coalesce(sum(mx_others_rejected_orders)*100.00 / nullif(sum(total_orders),0),0) as rejected_other_ratio,
        coalesce(sum(mx_ors) *100.00 /nullif(sum(total_orders),0),0) as complaints_ratio,
        coalesce(sum(pq_ors) *100.00 / nullif(sum(total_orders),0),0) as complaints_pq_ratio,
        coalesce(sum(wo_ors) *100.00 / nullif(sum(total_orders),0),0) as complaints_wi_ratio,
        coalesce(sum(pp_ors) * 100.00 / nullif(sum(total_orders),0),0) AS complaints_pp_ratio,
        coalesce(sum(mi_ors)  * 100.00 / nullif(sum(total_orders),0),0) AS complaints_mi_ratio,
        coalesce(sum(kpt_delayed_orders) * 100.00 / nullif(sum(total_orders),0),0) AS complaints_kpt_delay_ratio,
        coalesce(sum(others_ors) * 100.00 / nullif(sum(total_orders),0),0) AS complaints_other_ratio,
        coalesce(sum(refund_orders) * 100.00 / nullif(sum(ors),0),0) AS refunded_complaints_ratio,
        coalesce((sum(cx_refund) + sum(mx_refund)),0)*1.00 AS refunded_value,
        coalesce(sum(mx_refund) * 100.00 / nullif(sum(cx_refund) + sum(mx_refund),0),0) AS refunded_mx_ratio,
        coalesce(sum(cx_rejected_orders) * 100.00 / nullif(sum(total_orders),0),0) AS cancellation_cx_ratio,
        coalesce(sum(actual_visibility) * 100.00 / nullif(sum(expected_visibility),0),0) AS online_time_ratio,
        coalesce(sum(sl_pool) * 100.00 / nullif(sum(overall_pool),0),0) AS salience_loss_ratio,
        coalesce(count(distinct res_id),0) AS total_outlets_count,
        coalesce(sum(mx_rejected_orders),0) as rejected_orders_count,
        coalesce(sum(mx_ioos_rejected_orders),0) as rejected_ioos_orders_count,
        coalesce(sum(mx_kif_rejected_orders),0) as rejected_kif_orders_count,
        coalesce(sum(mx_outlet_closed_rejected_orders),0) as rejected_outlet_closed_orders_count,
        coalesce(sum(mx_timeout_rejected_orders) ,0) as rejected_timeout_orders_count,
        coalesce(sum(mx_device_issues_rejected_orders) ,0) as rejected_di_orders_count,
        coalesce(sum(mx_others_rejected_orders),0) as rejected_other_orders_count,
        coalesce(sum(mx_ors) ,0) as complaints_count,
        coalesce(sum(pq_ors) ,0) as complaints_pq_count,
        coalesce(sum(wo_ors) ,0) as complaints_wi_count,
        coalesce(sum(pp_ors),0) AS complaints_pp_count,
        coalesce(sum(mi_ors) ,0) AS complaints_mi_count,
        coalesce(sum(kpt_delayed_orders),0) AS complaints_kpt_delay_count,
        coalesce(sum(others_ors) , 0) AS complaints_other_count,
        coalesce(sum(refund_orders), 0) AS refunded_orders_count,
        coalesce((sum(expected_visibility) - sum(actual_visibility)) * 1.00, 0) AS offline_time_in_secs,
        coalesce(sum(mvd_discount), 0)*1.00 AS promo_discount_value,
        coalesce(sum(dish_discount) ,0)*1.00 AS dish_discount_value,
        coalesce(sum(bogo_discount) ,0)*1.00 AS bogo_discount_value,
        coalesce(sum(freebie_discount), 0)*1.00 AS freebie_discount_value,
        coalesce(sum(gold_discount), 0)*1.00 AS gold_discount_value,
        coalesce(sum(kpt) * 1.00 / nullif(sum(delivered_orders),0),0) AS average_kpt_value,
        coalesce(sum(kpt_delayed_orders) * 100.00 / nullif(sum(total_orders),0),0) AS kpt_delayed_ratio,
        coalesce(sum(for_accurate_orders) * 100.00 / nullif(sum(total_orders),0),0) AS for_accuracy_ratio,
        coalesce(sum(handover_time_breach_orders)  * 100.00 / nullif(sum(total_orders),0),0) AS handover_spill_ratio,
        coalesce(sum(ad_impressions),0) AS ad_impressions_count,
        coalesce(sum(ad_menu_opens) * 100.00 / nullif(sum(ad_impressions),0),0) AS ad_ctr_ratio,
        coalesce(sum(ad_menu_opens),0) AS ad_menu_opens_count,
        coalesce(sum(ad_orders) * 100.00 / nullif(sum(ad_menu_opens),0),0) AS ad_m2o_ratio,
        coalesce(sum(ad_cart_builds) * 100.00 / nullif(sum(ad_menu_opens),0),0) AS ad_m2c_ratio,
        coalesce(sum(ad_orders) * 100.00 / nullif(sum(ad_cart_builds),0),0) AS ad_c2o_ratio,
        coalesce(sum(ad_spends) * 1.00 / nullif(sum(total_orders),0),0) AS ad_spends_per_order_value,
        coalesce(sum(ad_cv),0)*1.00 AS ad_sales_value,
        coalesce(sum(ad_cv) * 100.00 / nullif(sum(commissionable_amount),0) ,0) AS ad_sales_ratio,
        coalesce(sum(ad_orders),0)*1.00 AS ad_orders_value,
        coalesce(sum(ad_orders) * 100.00 / nullif(sum(total_orders),0),0) AS ad_orders_ratio,
        coalesce(sum(ad_spends),0)*1.00 AS ad_spends,
        coalesce(sum(ad_cv) * 1.00 / nullif(sum(ad_spends),0),0) AS roi,
        coalesce(sum(ad_spends)  * 100.00 / nullif(sum(commissionable_amount),0),0) AS ad_spends_cv_ratio,
        coalesce(sum(large_order_merchant_received_amount),0)*1.00 AS large_order_net_sales_value,
        coalesce(sum(large_order_delivered_orders) ,0) AS large_order_delivered_order_count,
        coalesce(sum(large_order_merchant_received_amount) * 1.00 / nullif(sum(large_order_total_orders),0),0) AS large_order_aov_value,
        coalesce(sum(large_order_mx_rejected_orders) * 100.00 / nullif(sum(large_order_total_orders),0),0) AS large_order_rejection_ratio,
        coalesce(sum(large_order_kpt)  * 1.0 / nullif(sum(large_order_delivered_orders),0) ,0) AS large_order_average_kpt,
        coalesce(sum(large_order_kpt_delayed_orders) * 100.00 / nullif(sum(large_order_total_orders),0),0) AS large_order_kpt_delayed_ratio,
        coalesce(sum(large_order_rating)  * 1.00 / nullif(sum(large_order_rated_orders),0),0) AS large_order_food_rating_value,
        coalesce(sum(large_order_ors) * 100.00 / nullif(sum(large_order_total_orders),0),0) AS large_order_complaints_ratio,
        coalesce(sum(large_order_mi_ors) * 100.00 / nullif(sum(large_order_total_orders),0),0) AS large_order_complaints_mi_ratio,
        coalesce(sum(large_order_pp_ors) * 100.00 / nullif(sum(large_order_total_orders),0),0) AS large_order_complaints_pp_ratio,
        coalesce(sum(large_order_pq_ors) * 100.00 / nullif(sum(large_order_total_orders),0),0) AS large_order_complaints_pq_ratio,
        coalesce(sum(large_order_wo_ors) * 100.00 / nullif(sum(large_order_total_orders),0),0) AS large_order_complaints_wo_ratio,
        coalesce(sum(large_order_others_ors) * 100.00 / nullif(sum(large_order_total_orders),0),0) AS large_order_complaints_others_ratio,
        coalesce(sum(large_order_ors),0) AS large_order_complaints_count,
        coalesce(sum(large_order_mi_ors),0) AS large_order_complaints_mi_count,
        coalesce(sum(large_order_pp_ors),0) AS large_order_complaints_pp_count,
        coalesce(sum(large_order_pq_ors),0) AS large_order_complaints_pq_count,
        coalesce(sum(large_order_wo_ors),0) AS large_order_complaints_wo_count,
        coalesce(sum(large_order_others_ors),0) AS large_order_complaints_others_count,
        coalesce((sum(ad_cv) + sum(commission_amount) + sum(mx_recoup) + sum(mx_rejection_penalty)- sum(zvd_discount) - sum(mx_attr_refund_amount) - sum(mx_refund)) * 1.00 / nullif(sum(total_orders),0),0) as cmpo
    FROM
        insights_etls.mx_enterprise_stats base
    WHERE
      CASE 
        WHEN '-1' in ({{.dt_array}})
            THEN dt between {{.start_dt}} and {{.end_dt}}
        ELSE dt in ({{.dt_array}})
      END
      AND period_type = {{.period_type}} 
      AND dimension_type = {{.dimension_type}}
      AND 
        CASE 
            WHEN {{.filter_applied}} = 0
            THEN 
                (
                    (CASE WHEN -2 in ({{.brand_ids}}) THEN 1=0 ELSE base.brand_id in ({{.brand_ids}}) END)
                    OR (CASE WHEN -2 in ({{.city_ids}}) THEN 1=0 ELSE base.city_id in ({{.city_ids}}) END)
                    OR (CASE WHEN -2 in ({{.legal_entity_ids}}) THEN 1=0 ELSE base.merchant_id in ({{.legal_entity_ids}}) END)
                    OR (CASE WHEN -2 in ({{.res_ids}}) THEN 1=0 ELSE base.res_id in ({{.res_ids}}) END)
                )
            ELSE 
                (
                (
                    (CASE WHEN -2 in ({{.brand_ids}}) THEN 1=0 ELSE base.brand_id in ({{.brand_ids}}) END)
                    OR (CASE WHEN -2 in ({{.city_ids}}) THEN 1=0 ELSE base.city_id in ({{.city_ids}}) END)
                    OR (CASE WHEN -2 in ({{.legal_entity_ids}}) THEN 1=0 ELSE base.merchant_id in ({{.legal_entity_ids}}) END)
                    OR (CASE WHEN -2 in ({{.res_ids}}) THEN 1=0 ELSE base.res_id in ({{.res_ids}}) END)
                )
                AND
                (
                    (CASE WHEN -1 in ({{.selected_brand_ids}}) THEN 1=1 ELSE base.brand_id in ({{.selected_brand_ids}}) END)
                    AND (CASE WHEN -1 in ({{.selected_city_ids}}) THEN 1=1 ELSE base.city_id in ({{.selected_city_ids}}) END)
                    AND (CASE WHEN -1 in ({{.selected_legal_entity_ids}}) THEN 1=1 ELSE base.merchant_id in ({{.selected_legal_entity_ids}}) END)
                    AND (CASE WHEN -1 in ({{.selected_res_ids}}) THEN 1=1 ELSE base.res_id in ({{.selected_res_ids}}) END)
                )
                )
            END
    GROUP BY 1, 2, 3, 4, 5, 6, 7, 8


filters:
  - agg_enum
  - city_ids
  - res_ids
  - granularity
  - filter_applied
  - legal_entity_ids
  - brand_ids
  - selected_res_ids
  - selected_city_ids
  - selected_legal_entity_ids
  - selected_brand_ids
  - start_dt
  - end_dt
  - dimension_type
  - period_type
  - dt_array