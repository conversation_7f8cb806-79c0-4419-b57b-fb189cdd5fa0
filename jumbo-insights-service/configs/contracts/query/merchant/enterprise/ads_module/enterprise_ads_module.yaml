# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: user_ads_daily_stats
identifier: enterprise_ads_module
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query to power Ads module on Enterprise Dashboard

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: > 
  SELECT 
    CASE
      WHEN {{.nrl_flag}} = 1 then nrl 
    ELSE 'overall' 
    END as nrl,
    CASE
      WHEN {{.spending_flag}} = 1 then ps_segment 
    ELSE 'overall' 
    END AS spending_potential,
    res_id,
    campaign_id, 
    sum(ad_orders) as ad_orders,
    sum(ads_revenue) as sales,
    sum(ads_revenue)*1.0/sum(ads_spend) as roi,
    sum(impressions) as impressions,
    sum(clicks)*100.00/sum(impressions) as i2m,
    sum(ad_orders)*100.00/sum(clicks) as m2o,
    sum(clicks) as menu_opens,
    sum(ads_spend) as ads_spends
  from
    user_ads_daily_stats 
  WHERE
    "timestamp" BETWEEN {{.start_timestamp_millis}} AND {{.end_timestamp_millis}}
    and campaign_id in ({{.campaign_ids}})
  group by nrl, spending_potential, res_id, campaign_id

filters:
  - nrl_flag
  - spending_flag
  - campaign_ids
  - start_timestamp_millis
  - end_timestamp_millis

