# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: ba_hub_daily_stats
identifier: enterprise_home_overview_bhds
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: overview of all metrics for enterprise dashboard BHDS

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  
  SELECT
    CASE
      WHEN {{.agg_enum}} = 2 THEN DATETIMECONVERT(
        "timestamp" + 19800000,
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:dd',
        '1:DAYS'
      )
      WHEN {{.agg_enum}} = 3 THEN DATETIMECONVERT(
        "timestamp" + 19800000,
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:EEE',
        '1:DAYS'
      )
      WHEN {{.agg_enum}} = 4 THEN  DATETIMECONVERT(
        "timestamp" + 19800000,
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:yyyy-MM',
        '1:DAYS'
      ) 
      ELSE 'agg'
    END AS agg_level_str,
    min("timestamp")+ 19800000 AS agg_level,
    SUM(delivered_merchant_received_amount) AS sales,
    SUM(delivered_orders) AS orders,
    SUM(composite_mx_rejected_orders) AS rejected_orders,
    SUM(actual_visibility)*100.0/SUM(expected_visibility) AS online_percentage,
    100.0 - SUM(actual_visibility)*100.0/SUM(expected_visibility) AS offline_percentage
  FROM 
    ba_hub_daily_stats
  WHERE
    CASE 
        WHEN {{.filter_applied}} = 0
        THEN 
            (
                (CASE WHEN -2 in ({{.brand_ids}}) THEN 1=0 ELSE brand_id in ({{.brand_ids}}) END)
                OR (CASE WHEN -2 in ({{.city_ids}}) THEN 1=0 ELSE city_id in ({{.city_ids}}) END)
                OR (CASE WHEN -2 in ({{.legal_entity_ids}}) THEN 1=0 ELSE merchant_id in ({{.legal_entity_ids}}) END)
                OR (CASE WHEN -2 in ({{.res_ids}}) THEN 1=0 ELSE res_id in ({{.res_ids}}) END)
            )
        ELSE 
            (
              (
                (CASE WHEN -2 in ({{.brand_ids}}) THEN 1=0 ELSE brand_id in ({{.brand_ids}}) END)
                OR (CASE WHEN -2 in ({{.city_ids}}) THEN 1=0 ELSE city_id in ({{.city_ids}}) END)
                OR (CASE WHEN -2 in ({{.legal_entity_ids}}) THEN 1=0 ELSE merchant_id in ({{.legal_entity_ids}}) END)
                OR (CASE WHEN -2 in ({{.res_ids}}) THEN 1=0 ELSE res_id in ({{.res_ids}}) END)
            )
            AND
              (
                (CASE WHEN -1 in ({{.selected_brand_ids}}) THEN 1=1 ELSE brand_id in ({{.selected_brand_ids}}) END)
                AND (CASE WHEN -1 in ({{.selected_city_ids}}) THEN 1=1 ELSE city_id in ({{.selected_city_ids}}) END)
                AND (CASE WHEN -1 in ({{.selected_legal_entity_ids}}) THEN 1=1 ELSE merchant_id in ({{.selected_legal_entity_ids}}) END)
                AND (CASE WHEN -1 in ({{.selected_res_ids}}) THEN 1=1 ELSE res_id in ({{.selected_res_ids}}) END)
              )
            )
        END
    AND "timestamp" BETWEEN {{.start_timestamp_millis}} AND {{.end_timestamp_millis}} 
  GROUP BY agg_level_str

filters:
  - agg_enum
  - res_ids
  - city_ids
  - filter_applied
  - legal_entity_ids
  - brand_ids
  - selected_res_ids
  - selected_city_ids
  - selected_legal_entity_ids
  - selected_brand_ids
  - start_timestamp_millis
  - end_timestamp_millis
  
