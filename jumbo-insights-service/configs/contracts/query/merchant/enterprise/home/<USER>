# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: restaurant_hourly_stats
identifier: enterprise_home_trend_brand_rhs
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: overview of all metrics for enterprise dashboard RHS

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT
    CASE
      WHEN {{.agg_enum}} = 1 THEN DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:HH',
        '1:HOURS'
      )
      WHEN {{.agg_enum}} = 2 THEN DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:dd',
        '1:DAYS'
      )
      WHEN {{.agg_enum}} = 3 THEN DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:EEE',
        '1:DAYS'
      )
      WHEN {{.agg_enum}} = 4 THEN  DATETIMECONVERT(
        "timestamp",
        '1:MILLISECONDS:EPOCH',
        '1:SECONDS:SIMPLE_DATE_FORMAT:yyyy-MM',
        '1:DAYS'
      )
    ELSE 'agg'
    END AS agg_level_str,
    brand_id,
    brand_name,
    SUM(
    CASE 
      WHEN {{.metric_name}} in ('net_sales') THEN delivered_merchant_received_amount
      WHEN {{.metric_name}} in ('orders') THEN orders_completed
      WHEN {{.metric_name}} in ('online_time') THEN actual_online_hours*100.00
      WHEN {{.metric_name}} in ('rejections') THEN orders_rejected
    ELSE 0
    END)  
    /
      (SUM(
        CASE 
          WHEN {{.metric_name}} in ('net_sales') THEN 0 
          WHEN {{.metric_name}} in ('orders') THEN 0 
          WHEN {{.metric_name}} in ('online_time') THEN expected_online_hours-1 
          WHEN {{.metric_name}} in ('rejections') THEN 0 
      ELSE 0 
      END)
    +1) AS metric_val,
    COUNT(distinct res_id) as res_count,
    min("timestamp") AS agg_level
  FROM 
    restaurant_hourly_stats
  WHERE
    CASE 
        WHEN {{.filter_applied}} = 0
        THEN 
            (
                (CASE WHEN -2 in ({{.brand_ids}}) THEN 1=0 ELSE brand_id in ({{.brand_ids}}) END)
                OR (CASE WHEN -2 in ({{.city_ids}}) THEN 1=0 ELSE city_id in ({{.city_ids}}) END)
                OR (CASE WHEN -2 in ({{.legal_entity_ids}}) THEN 1=0 ELSE merchant_id in ({{.legal_entity_ids}}) END)
                OR (CASE WHEN -2 in ({{.res_ids}}) THEN 1=0 ELSE res_id in ({{.res_ids}}) END)
            )
        ELSE 
            (
              (
                (CASE WHEN -2 in ({{.brand_ids}}) THEN 1=0 ELSE brand_id in ({{.brand_ids}}) END)
                OR (CASE WHEN -2 in ({{.city_ids}}) THEN 1=0 ELSE city_id in ({{.city_ids}}) END)
                OR (CASE WHEN -2 in ({{.legal_entity_ids}}) THEN 1=0 ELSE merchant_id in ({{.legal_entity_ids}}) END)
                OR (CASE WHEN -2 in ({{.res_ids}}) THEN 1=0 ELSE res_id in ({{.res_ids}}) END)
            )
            AND
              (
                (CASE WHEN -1 in ({{.selected_brand_ids}}) THEN 1=1 ELSE brand_id in ({{.selected_brand_ids}}) END)
                AND (CASE WHEN -1 in ({{.selected_city_ids}}) THEN 1=1 ELSE city_id in ({{.selected_city_ids}}) END)
                AND (CASE WHEN -1 in ({{.selected_legal_entity_ids}}) THEN 1=1 ELSE merchant_id in ({{.selected_legal_entity_ids}}) END)
                AND (CASE WHEN -1 in ({{.selected_res_ids}}) THEN 1=1 ELSE res_id in ({{.selected_res_ids}}) END)
              )
            )
        END
    AND "timestamp" BETWEEN {{.start_timestamp_millis}} AND {{.end_timestamp_millis}} 
  GROUP BY agg_level_str, brand_id, brand_name

filters:
  - agg_enum
  - metric_name
  - res_ids
  - city_ids
  - filter_applied
  - legal_entity_ids
  - brand_ids
  - selected_res_ids
  - selected_city_ids
  - selected_legal_entity_ids
  - selected_brand_ids
  - start_timestamp_millis
  - end_timestamp_millis
  