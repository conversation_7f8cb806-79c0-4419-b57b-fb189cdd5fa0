# can be pinot/trino
query_backend: clickhouse
tenant: zanalytics
table: mx_enterprise_stats
identifier: enterprise_metric_reports_es_clickhouse_sq_v2_nest
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query to power enterprise dashboard metrics - Service Quality Metrics

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: > 
  with
  base as (
      SELECT 
          CASE
              when {{.agg_enum}} = 0 then toString(ist_day)
              when {{.agg_enum}} in (2, 3) then toString(dt)
              when {{.agg_enum}} = 5 then concat(toString(ist_year), '-', toString(ist_month))
              when {{.agg_enum}} = 7 then toString(toDayOfWeek(toDateTime("timestamp", 'Asia/Kolkata')))
              when {{.agg_enum}} = 8 then (
                  case 
                    when ist_month = 12 and ist_isoweek = 1 
                    then concat(toString(ist_year + 1), '_', leftPad(toString(ist_isoweek), 2, '0')) 
                    else concat(toString(ist_year), '_', leftPad(toString(ist_isoweek), 2, '0'))
                  end
              )
              else 'agg'
          END as agg_level_str,
          CASE 
              WHEN substring({{.resolution}}, 1, 1) = 1 then city_id
          ELSE -999
          END as cityid,
          CASE  
              WHEN substring({{.resolution}}, 2, 1) = 1 then brand_id
          ELSE -999
          END as brandid,
          CASE  
              WHEN substring({{.resolution}}, 3, 1) = 1 then res_id
          ELSE -999
          END as resid,
          CASE 
              WHEN substr({{.resolution}}, 1, 1) = '1' then city_id
              WHEN substr({{.resolution}}, 2, 1) = '1' then brand_id
              WHEN substr({{.resolution}}, 3, 1) = '1' then res_id
              when substr({{.resolution}}, 4, 1) = '1' then merchant_id
              when substr({{.resolution}}, 5, 1) = '1' then zone_id
              ELSE -999
          END as breakup_id,
          dimension_value,
          MAX(
            CASE  
              WHEN substring({{.resolution}}, 1, 1) = 1 then city_name
              ELSE 'Overall'
            END
          ) as city_name_list,  
          MAX(
            CASE  
              WHEN substring({{.resolution}}, 2, 1) = 1 then brand_name
              ELSE 'Overall'
            END
          ) as brand_name_list,
          MAX(
            CASE  
              WHEN substring({{.resolution}}, 3, 1) = 1 then res_name
              ELSE 'Overall'
            END 
          ) as res_name_list,
          MAX(
            CASE  
               WHEN substring({{.resolution}}, 3, 1) = 1 then concat(subzone_name,'#',city_name)
               ELSE 'Overall'
            END
          ) as subzone_city,
          MAX(
            CASE 
              WHEN substr({{.resolution}}, 1, 1) = '1' then city_name
              WHEN substr({{.resolution}}, 2, 1) = '1' then brand_name
              WHEN substr({{.resolution}}, 3, 1) = '1' then res_name
              WHEN substr({{.resolution}}, 4, 1) = '1' then merchant_name
              WHEN substr({{.resolution}}, 5, 1) = '1' then zone_name
              ELSE 'bad_row'
            END
          ) as breakup_name,
          min("timestamp") as agg_level,         
          sum(total_orders) as total_orders,
          sum(delivered_orders) as delivered_orders,
          sum(merchant_received_amount) as merchant_received_amount,
          sum(commissionable_amount) as commissionable_amount,
          sum(bill_subtotal) as bill_subtotal,
          sum(packaging_charges) as packaging_charges,
          sum(mvd_discount) as mvd_discount, 
          sum(zvd_discount) as zvd_discount,
          sum(order_items) as order_items,
          count(distinct res_id) as res_count,
          
          sum(rating_sum) as rating_sum,
          sum(rated_orders) as rated_orders,
          sum(one_rated_orders) as one_rated_orders,
          sum(two_rated_orders) as two_rated_orders,
          sum(mx_rejected_orders) as mx_rejected_orders,
          sum(mx_ioos_rejected_orders) as mx_ioos_rejected_orders,
          sum(mx_kif_rejected_orders) as mx_kif_rejected_orders,
          sum(mx_outlet_closed_rejected_orders) as mx_outlet_closed_rejected_orders,
          sum(mx_timeout_rejected_orders) as mx_timeout_rejected_orders,
          sum(mx_device_issues_rejected_orders) as mx_device_issues_rejected_orders,
          sum(mx_others_rejected_orders) as mx_others_rejected_orders,
          sum(ors) as ors,
          sum(mx_ors) as mx_ors,
          sum(pq_ors) as pq_ors,
          sum(wo_ors) as wo_ors,
          sum(pp_ors) as pp_ors,
          sum(mi_ors) as mi_ors,
          sum(others_ors) as others_ors,
          sum(kpt_delayed_orders) as kpt_delayed_orders,
          sum(refund_orders) as refund_orders,
          
          sum(cx_refund) as cx_refund, 
          sum(mx_refund) as mx_refund,
          
          sum(cx_rejected_orders) as cx_rejected_orders,
          sum(actual_visibility) as actual_visibility,
          sum(expected_visibility) as expected_visibility,
          sum(overall_pool) as overall_pool,
          sum(sl_pool) as sl_pool
      
      FROM
          s3(
              's3://zanalytics-jumbo/insights_etls.db/mx_enterprise_stats/dt=*/period_type=*/dimension_type=*/*',
              'Parquet'
          )
      WHERE 
          CASE 
              WHEN -1 in ({{.dt_array}})
              THEN dt between {{.start_dt}} and {{.end_dt}}
          ELSE dt in ({{.dt_array}})
          END
          AND period_type = {{.period_type}}
          AND dimension_type = {{.dimension_type}}
          AND 
          CASE 
              WHEN {{.filter_applied}} = 0
          THEN 
              (
                  (CASE WHEN -2 in ({{.brand_ids}}) THEN 1=0 ELSE brand_id in ({{.brand_ids}}) END)
                  OR (CASE WHEN -2 in ({{.city_ids}}) THEN 1=0 ELSE city_id in ({{.city_ids}}) END)
                  OR (CASE WHEN -2 in ({{.legal_entity_ids}}) THEN 1=0 ELSE merchant_id in ({{.legal_entity_ids}}) END)
                  OR (CASE WHEN -2 in ({{.res_ids}}) THEN 1=0 ELSE res_id in ({{.res_ids}}) END)
              )
          ELSE 
              (
                  (
                      (CASE WHEN -2 in ({{.brand_ids}}) THEN 1=0 ELSE brand_id in ({{.brand_ids}}) END)
                      OR (CASE WHEN -2 in ({{.city_ids}}) THEN 1=0 ELSE city_id in ({{.city_ids}}) END)
                      OR (CASE WHEN -2 in ({{.legal_entity_ids}}) THEN 1=0 ELSE merchant_id in ({{.legal_entity_ids}}) END)
                      OR (CASE WHEN -2 in ({{.res_ids}}) THEN 1=0 ELSE res_id in ({{.res_ids}}) END)
                  )
              AND
                  (   
                  (CASE WHEN -1 in ({{.selected_brand_ids}}) THEN 1=1 ELSE brand_id in ({{.selected_brand_ids}}) END)
                  AND (CASE WHEN -1 in ({{.selected_city_ids}}) THEN 1=1 ELSE city_id in ({{.selected_city_ids}}) END)
                  AND (CASE WHEN -1 in ({{.selected_legal_entity_ids}}) THEN 1=1 ELSE merchant_id in ({{.selected_legal_entity_ids}}) END)
                  AND (CASE WHEN -1 in ({{.selected_res_ids}}) THEN 1=1 ELSE res_id in ({{.selected_res_ids}}) END)
                  )
              )
          END
      GROUP BY 1, 2, 3, 4, 5, 6
  )  
      
  SELECT 
      agg_level_str,
      breakup_id,
      breakup_name,
      brandid , 
      brand_name_list as brand_name,
      cityid,
      city_name_list as city_name,
      resid,
      res_name_list as res_name, 
      subzone_city,
      dimension_value,
      agg_level,
      res_count,
      
      coalesce(rating_sum*1.0 / nullif(rated_orders ,0) ,0) as food_rating_value,
      coalesce((one_rated_orders + two_rated_orders)*100.0 / nullif(total_orders ,0) ,0) as poor_rated_orders_ratio,
      coalesce((mx_rejected_orders)*100.0 / nullif(total_orders ,0) ,0) as rejected_orders_ratio,
      coalesce((mx_ioos_rejected_orders)*100.0 / nullif(total_orders ,0) ,0) as rejected_ioos_ratio,
      coalesce((mx_kif_rejected_orders)*100.0 / nullif(total_orders ,0) ,0) as rejected_kif_ratio,
      coalesce(mx_outlet_closed_rejected_orders*100.0 / nullif(total_orders ,0) ,0) as rejected_outlet_closed_ratio,
      coalesce(mx_timeout_rejected_orders*100.0/ nullif(total_orders ,0) ,0) as rejected_timeout_ratio,
      coalesce(mx_device_issues_rejected_orders*100.0/ nullif(total_orders ,0) ,0) as rejected_di_ratio,
      coalesce(mx_others_rejected_orders*100.0/ nullif(total_orders ,0) ,0) as rejected_other_ratio,
      coalesce(mx_ors*100.0/ nullif(total_orders ,0) ,0) as complaints_ratio,
      coalesce(pq_ors*100.0/ nullif(total_orders ,0) ,0) as complaints_pq_ratio,
      coalesce(wo_ors*100.0/ nullif(total_orders ,0) ,0) as complaints_wi_ratio,
      coalesce(pp_ors * 100.0  / nullif(total_orders ,0) ,0) AS complaints_pp_ratio,
      coalesce(mi_ors * 100.0  / nullif(total_orders ,0) ,0) AS complaints_mi_ratio,
      coalesce(kpt_delayed_orders * 100.0  / nullif(total_orders ,0) ,0) AS complaints_kpt_delay_ratio,
      coalesce(others_ors * 100.0  / nullif(total_orders ,0) ,0) AS complaints_other_ratio,
      coalesce(refund_orders * 100.0  / nullif(ors ,0) ,0) AS refunded_complaints_ratio,
      (cx_refund + mx_refund) AS refunded_value,
      coalesce(mx_refund * 100.0 / nullif((cx_refund + mx_refund) ,0) ,0) AS refunded_mx_ratio,
      coalesce(cx_rejected_orders * 100.0 / nullif(total_orders ,0) ,0) AS cancellation_cx_ratio,
      coalesce(actual_visibility * 100.0 / nullif(expected_visibility ,0) ,0) AS online_time_ratio,
      coalesce(sl_pool * 100.0 / nullif(overall_pool ,0) ,0) AS salience_loss_ratio,
      res_count AS total_outlets_count,
      mx_rejected_orders as rejected_orders_count,
      mx_ioos_rejected_orders as rejected_ioos_orders_count,
      mx_kif_rejected_orders as rejected_kif_orders_count,
      mx_outlet_closed_rejected_orders as rejected_outlet_closed_orders_count,
      mx_timeout_rejected_orders as rejected_timeout_orders_count,
      mx_device_issues_rejected_orders as rejected_di_orders_count,
      mx_others_rejected_orders as rejected_other_orders_count,
      mx_ors as complaints_count,
      pq_ors as complaints_pq_count,
      wo_ors as complaints_wi_count,
      pp_ors AS complaints_pp_count,
      mi_ors AS complaints_mi_count,
      kpt_delayed_orders AS complaints_kpt_delay_count,
      others_ors  AS complaints_other_count,
      refund_orders AS refunded_orders_count,
      (expected_visibility - actual_visibility) * 1.00 AS offline_time_in_secs
  FROM 
      base

filters:
  - agg_enum
  - resolution
  - dt_array
  - start_dt
  - end_dt
  - period_type
  - dimension_type
  - filter_applied
  - brand_ids
  - city_ids
  - legal_entity_ids
  - res_ids
  - selected_brand_ids
  - selected_city_ids
  - selected_legal_entity_ids
  - selected_res_ids

