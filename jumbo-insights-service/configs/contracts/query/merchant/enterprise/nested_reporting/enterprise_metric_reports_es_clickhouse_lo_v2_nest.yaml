# can be pinot/trino
query_backend: clickhouse
tenant: zanalytics
table: mx_enterprise_stats
identifier: enterprise_metric_reports_es_clickhouse_lo_v2_nest
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: query to power enterprise dashboard metrics - Large Order Metrics

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: > 
  with
  base as (
      SELECT 
          CASE
              when {{.agg_enum}} = 0 then toString(ist_day)
              when {{.agg_enum}} in (2, 3) then toString(dt)
              when {{.agg_enum}} = 5 then concat(toString(ist_year), '-', toString(ist_month))
              when {{.agg_enum}} = 7 then toString(toDayOfWeek(toDateTime("timestamp", 'Asia/Kolkata')))
              when {{.agg_enum}} = 8 then (
                  case 
                    when ist_month = 12 and ist_isoweek = 1 
                    then concat(toString(ist_year + 1), '_', leftPad(toString(ist_isoweek), 2, '0')) 
                    else concat(toString(ist_year), '_', leftPad(toString(ist_isoweek), 2, '0'))
                  end
              )
              else 'agg'
          END as agg_level_str,
          CASE 
              WHEN substring({{.resolution}}, 1, 1) = 1 then city_id
          ELSE -999
          END as cityid,
          CASE  
              WHEN substring({{.resolution}}, 2, 1) = 1 then brand_id
          ELSE -999
          END as brandid,
          CASE  
              WHEN substring({{.resolution}}, 3, 1) = 1 then res_id
          ELSE -999
          END as resid,
          CASE 
              WHEN substr({{.resolution}}, 1, 1) = '1' then city_id
              WHEN substr({{.resolution}}, 2, 1) = '1' then brand_id
              WHEN substr({{.resolution}}, 3, 1) = '1' then res_id
              when substr({{.resolution}}, 4, 1) = '1' then merchant_id
              when substr({{.resolution}}, 5, 1) = '1' then zone_id
              ELSE -999
          END as breakup_id,
          dimension_value,
          MAX(
            CASE  
              WHEN substring({{.resolution}}, 1, 1) = 1 then city_name
              ELSE 'Overall'
            END
          ) as city_name_list,  
          MAX(
            CASE  
              WHEN substring({{.resolution}}, 2, 1) = 1 then brand_name
              ELSE 'Overall'
            END
          ) as brand_name_list,
          MAX(
            CASE  
              WHEN substring({{.resolution}}, 3, 1) = 1 then res_name
              ELSE 'Overall'
            END 
          ) as res_name_list,
          MAX(
            CASE  
               WHEN substring({{.resolution}}, 3, 1) = 1 then concat(subzone_name,'#',city_name)
               ELSE 'Overall'
            END
          ) as subzone_city,
          MAX(
            CASE 
              WHEN substr({{.resolution}}, 1, 1) = '1' then city_name
              WHEN substr({{.resolution}}, 2, 1) = '1' then brand_name
              WHEN substr({{.resolution}}, 3, 1) = '1' then res_name
              WHEN substr({{.resolution}}, 4, 1) = '1' then merchant_name
              WHEN substr({{.resolution}}, 5, 1) = '1' then zone_name
              ELSE 'bad_row'
            END
          ) as breakup_name,
          min("timestamp") as agg_level,        
          sum(total_orders) as total_orders,
          sum(delivered_orders) as delivered_orders,
          sum(merchant_received_amount) as merchant_received_amount,
          sum(commissionable_amount) as commissionable_amount,
          sum(bill_subtotal) as bill_subtotal,
          sum(packaging_charges) as packaging_charges,
          count(distinct res_id) as res_count,
          
          sum(large_order_merchant_received_amount) as large_order_merchant_received_amount,
          sum(large_order_delivered_orders) as large_order_delivered_orders,
          sum(large_order_total_orders) as large_order_total_orders,
          sum(large_order_mx_rejected_orders) as large_order_mx_rejected_orders, 
          sum(large_order_kpt) as large_order_kpt, 
          sum(large_order_kpt_delayed_orders) as large_order_kpt_delayed_orders, 
          sum(large_order_rated_orders) as large_order_rated_orders,
          sum(large_order_rating) as large_order_rating,
          sum(large_order_ors) as large_order_ors,
          sum(large_order_mi_ors) as large_order_mi_ors,
          sum(large_order_pp_ors) as large_order_pp_ors,
          sum(large_order_pq_ors) as large_order_pq_ors,
          sum(large_order_wo_ors) as large_order_wo_ors,
          sum(large_order_others_ors) as large_order_others_ors
      FROM
          s3(
              's3://zanalytics-jumbo/insights_etls.db/mx_enterprise_stats/dt=*/period_type=*/dimension_type=*/*',
              'Parquet'
          )
      WHERE 
          CASE 
              WHEN -1 in ({{.dt_array}})
              THEN dt between {{.start_dt}} and {{.end_dt}}
          ELSE dt in ({{.dt_array}})
          END
          AND period_type = {{.period_type}}
          AND dimension_type = {{.dimension_type}}
          AND 
          CASE 
              WHEN {{.filter_applied}} = 0
          THEN 
              (
                  (CASE WHEN -2 in ({{.brand_ids}}) THEN 1=0 ELSE brand_id in ({{.brand_ids}}) END)
                  OR (CASE WHEN -2 in ({{.city_ids}}) THEN 1=0 ELSE city_id in ({{.city_ids}}) END)
                  OR (CASE WHEN -2 in ({{.legal_entity_ids}}) THEN 1=0 ELSE merchant_id in ({{.legal_entity_ids}}) END)
                  OR (CASE WHEN -2 in ({{.res_ids}}) THEN 1=0 ELSE res_id in ({{.res_ids}}) END)
              )
          ELSE 
              (
                  (
                      (CASE WHEN -2 in ({{.brand_ids}}) THEN 1=0 ELSE brand_id in ({{.brand_ids}}) END)
                      OR (CASE WHEN -2 in ({{.city_ids}}) THEN 1=0 ELSE city_id in ({{.city_ids}}) END)
                      OR (CASE WHEN -2 in ({{.legal_entity_ids}}) THEN 1=0 ELSE merchant_id in ({{.legal_entity_ids}}) END)
                      OR (CASE WHEN -2 in ({{.res_ids}}) THEN 1=0 ELSE res_id in ({{.res_ids}}) END)
                  )
              AND
                  (   
                  (CASE WHEN -1 in ({{.selected_brand_ids}}) THEN 1=1 ELSE brand_id in ({{.selected_brand_ids}}) END)
                  AND (CASE WHEN -1 in ({{.selected_city_ids}}) THEN 1=1 ELSE city_id in ({{.selected_city_ids}}) END)
                  AND (CASE WHEN -1 in ({{.selected_legal_entity_ids}}) THEN 1=1 ELSE merchant_id in ({{.selected_legal_entity_ids}}) END)
                  AND (CASE WHEN -1 in ({{.selected_res_ids}}) THEN 1=1 ELSE res_id in ({{.selected_res_ids}}) END)
                  )
              )
          END
      GROUP BY 1, 2, 3, 4, 5, 6
  )  
      
  SELECT 
      agg_level_str,
      breakup_id,
      breakup_name,
      brandid , 
      brand_name_list as brand_name,
      cityid,
      city_name_list as city_name,
      resid,
      res_name_list as res_name, 
      subzone_city,
      dimension_value,
      agg_level,
      res_count,

      large_order_merchant_received_amount AS large_order_net_sales_value,
      large_order_delivered_orders AS large_order_delivered_order_count,
      coalesce(large_order_merchant_received_amount * 1.0 / nullif( large_order_total_orders ,0) ,0) AS large_order_aov_value,
      coalesce(large_order_mx_rejected_orders * 100.0 / nullif( large_order_total_orders ,0) ,0) AS large_order_rejection_ratio,
      coalesce(large_order_kpt * 60.0 / nullif( large_order_delivered_orders ,0) ,0) AS large_order_average_kpt,
      coalesce(large_order_kpt_delayed_orders * 100.0 / nullif( large_order_total_orders ,0) ,0) AS large_order_kpt_delayed_ratio,
      coalesce(large_order_rating * 1.0 / nullif( large_order_rated_orders ,0) ,0) AS large_order_food_rating_value,
      coalesce(large_order_ors * 100.00 / nullif( large_order_total_orders ,0) ,0) AS large_order_complaints_ratio,
      coalesce(large_order_mi_ors * 100.00 / nullif( large_order_total_orders ,0) ,0) AS large_order_complaints_mi_ratio,
      coalesce(large_order_pp_ors * 100.00 / nullif( large_order_total_orders ,0) ,0) AS large_order_complaints_pp_ratio,
      coalesce(large_order_pq_ors * 100.00 / nullif( large_order_total_orders ,0) ,0) AS large_order_complaints_pq_ratio,
      coalesce(large_order_wo_ors * 100.00 / nullif( large_order_total_orders ,0) ,0) AS large_order_complaints_wo_ratio,
      coalesce(large_order_others_ors * 100.00 / nullif( large_order_total_orders ,0) ,0) AS large_order_complaints_others_ratio,
      
      large_order_ors AS large_order_complaints_count,
      large_order_mi_ors AS large_order_complaints_mi_count,
      large_order_pp_ors AS large_order_complaints_pp_count,
      large_order_pq_ors AS large_order_complaints_pq_count,
      large_order_wo_ors AS large_order_complaints_wo_count,
      large_order_others_ors AS large_order_complaints_others_count
  FROM
      base

filters:
  - agg_enum
  - resolution
  - dt_array
  - start_dt
  - end_dt
  - period_type
  - dimension_type
  - filter_applied
  - brand_ids
  - city_ids
  - legal_entity_ids
  - res_ids
  - selected_brand_ids
  - selected_city_ids
  - selected_legal_entity_ids
  - selected_res_ids
