query_backend: pinot
tenant: Zomato
table: restaurant_daily_stats
identifier: merchant_winback_retention
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Show user-winback retention data for Opt Out requests

# Time in second
caching_ttl: 1200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 100
# Request per second
rate_limit: 200
# contract type
type: sql
sql: >
  SELECT 
  LASTWITHTIME(wb_reten_pc, "timestamp", 'INT') as current_retention,
  LASTWITHTIME(drop_reten, "timestamp", 'INT') as drop_retention
  FROM {{.table}}
  WHERE "timestamp" >= {{.start_time}} 
  AND res_id = {{.res_id}}
  LIMIT 1
filters:
  - start_time
  - res_id
  
