# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: district_events_ticket
identifier: district_events_ticket_sales_distribution_by_item
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ed-api-gateway
  pd_service_name: ed-api-gateway-service
  description: Get ticket sales distribution for selected district events
# TTL for cache, Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select 
  item_id,
  count(*) as total_tickets,
  sum(ticket_base_price) as total_revenue
  from {{.table}}
  where "timestamp" >= {{.start_timestamp_ms}} and "timestamp" <= {{.end_timestamp_ms}}
  and district_event_id in ({{.event_ids}})
  AND case when 'All'  IN ({{.show_ids}}) then true else show_id IN ({{.show_ids}}) end
  AND case when 'All' IN ({{.group_ids}}) then true else group_id IN ({{.group_ids}}) end
  AND case when 'All' IN ({{.phase_group_ids}}) then true else phase_group_id IN ({{.phase_group_ids}}) end
  AND case when 'All' IN ({{.ticket_types}}) then true else ticket_type IN ({{.ticket_types}}) end
  AND case when 'All' IN ({{.item_ids}}) then true else item_id IN ({{.item_ids}}) end
  group by 1
filters:
  - start_timestamp_ms
  - end_timestamp_ms
  - event_ids
  - show_ids
  - group_ids
  - phase_group_ids
  - ticket_types
  - item_ids
