# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: district_events_ticket
identifier: district_events_ticket_buyers_distribution_by_source
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ed-api-gateway
  pd_service_name: ed-api-gateway-service
  description: Get ticket sales trend for selected district events
# TTL for cache, Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select
  utm_source,
  count(*) as tickets,
  count(DISTINCT order_id) as orders,
  count(DISTINCT user_id) as users
  from {{.table}}
  where timestamp_added >= {{.start_timestamp_ms}} AND timestamp_added <= {{.end_timestamp_ms}}
  AND district_event_id in ({{.event_ids}})
  AND case when 'All' IN ({{.sources}}) then true else utm_source IN ({{.sources}}) end
  AND case when 'All' IN ({{.platforms}}) then true else source IN ({{.platforms}}) end
  AND case when -5 IN ({{.city_ids}}) then true else location IN ({{.city_ids}}) end
  AND ticket_type NOT IN ('TICKET_TYPE_BLOCKED', 'TICKET_TYPE_COMPLIMENTARY')
  AND action NOT IN ('ACTION_REFUNDED', 'ACTION_TRANSFERRED')
  group by 1
filters:
  - start_timestamp_ms
  - end_timestamp_ms
  - event_ids
  - city_ids
  - sources
  - platforms
