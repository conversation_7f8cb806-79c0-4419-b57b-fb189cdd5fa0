# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: district_events_business_tracking
identifier: district_events_total_visitors
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ed-api-gateway
  pd_service_name: ed-api-gateway-service
  description: Get total visitors for selected district events
# TTL for cache, Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select sum(count) as total_visitors, count(distinct device_id) as total_distinct_users
  from {{.table}}
  where timestamp_ms >= {{.start_timestamp_ms}} and timestamp_ms <= {{.end_timestamp_ms}}
  and district_event_id in ( {{.event_ids}} )
  and page_event_name in ( 'Page Loaded', 'Event Booking Confirmed Page Loaded')
  AND case when 'All' IN ({{.page_names}}) then true else page_name IN ({{.page_names}}) end
filters:
  - start_timestamp_ms
  - end_timestamp_ms
  - event_ids
  - page_names
