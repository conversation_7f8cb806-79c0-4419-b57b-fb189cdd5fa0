query_backend: pinot
tenant: Zomato
table: shopping_daily_stats
identifier: shopping_business_insights
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: ed-shopping
  pd_service_name: ed-shopping
  description: stats for shopping merchant dashboard

# Time in second
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# Dimensions
type: sql
sql: >
  SELECT
  SUM(gmv) AS gmv,
  SUM(net_gmv) AS net_gmv,
  SUM(final_commission_paid) as final_commission_paid,
  SUM(orders) AS orders,
  SUM(txn_users) AS txn_users,
  SUM(new_user_gmv) AS new_user_gmv,
  SUM(repeat_user_gmv) AS repeat_user_gmv,
  SUM(new_user_txn) AS new_user_txn,
  SUM(repeat_user_txn) AS repeat_user_txn,
  SUM(bill_amount_aov)*1.0/(COUNT(bill_amount_aov)+0.000000001) AS bill_amount_aov,
  SUM(total_sdp_opens) AS total_sdp_opens,
  SUM(search_sdp_viewed) AS search_sdp_viewed,
  SUM(new_sdp_sessions) AS new_sdp_sessions,
  SUM(repeat_sdp_sessions) AS repeat_sdp_sessions,
  SUM(impressions_sessions) AS impressions_sessions,
  SUM(new_impressions_sessions) AS new_impressions_sessions,
  SUM(repeat_impressions_sessions) AS repeat_impressions_sessions,
  SUM(overall_taps_sessions) AS overall_taps_sessions,
  SUM(new_taps_sessions) as new_taps_sessions,
  SUM(repeat_taps_sessions) AS repeat_taps_sessions
  FROM {{.table}}
  WHERE store_id IN ({{.store_id}})
  AND dt >= {{.start_dt}}
  AND dt <= {{.end_dt}}
  AND agg_window = {{.agg_window}}

filters:
  - store_id
  - start_dt
  - end_dt
  - agg_window
