query_backend: pinot
tenant: Zomato
table: zomato_chat_agent_metrics
identifier: chat_avg_time_per_channel
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: chat-v2-backend-service
  pd_service_name: chat-backend-v2-service
  description: Average metric time for agents broken down by individual channels

# Time in second
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 200
# Request per second
rate_limit: 1000

# contract type
type: sql
sql: >
  SELECT 
    channel_id as channel_id,
    AVG(CAST(event_value AS INT)) as value
  FROM {{.table}}
  WHERE event_name = {{.event_name}}
  AND channel_id IN ({{.channel_ids}})
  AND "time" BETWEEN {{.start_time}} AND {{.end_time}}
  GROUP BY channel_id
filters:
  - event_name
  - channel_ids
  - start_time
  - end_time
