 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: rider_daily_metrics
identifier: fc_weekly_dp_type_movement_ids
audit:
  author_email: k<PERSON><PERSON><PERSON>.<EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Get fleetcoach mapped drivers who moved from one category to another
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select delivery_driver_id as driver_id
  from {{.table}}
  where dt = {{.dt}}
  and zone_id = {{.zone_id}}
  and fleet_coach_id = {{.fleet_coach_id}}
  and dp_type = {{.current_dp_type}}
  and previous_dp_type = {{.previous_dp_type}}
filters:
  - dt
  - zone_id
  - fleet_coach_id
  - current_dp_type
  - previous_dp_type
