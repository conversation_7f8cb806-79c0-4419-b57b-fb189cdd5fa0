 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: rider_daily_metrics
identifier: rider_category_metrics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Get dp category count of fleetcoach mapped drivers
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  SELECT count(DISTINCT delivery_driver_id) AS dp_count, dp_type
  FROM {{.table}}
  WHERE dt <= {{.end_date}}
  AND dt >= {{.start_date}}
  AND zone_id IN ({{.zone_ids}})
  AND dp_type != 'Regular'
  AND fleet_coach_id = {{.fleet_coach_id}}
  GROUP BY dp_type
filters:
  - start_date
  - end_date
  - zone_ids
  - fleet_coach_id
