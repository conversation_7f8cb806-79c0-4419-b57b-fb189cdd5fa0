 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: rider_daily_metrics
identifier: fc_weekly_active_fleet_metrics
audit:
  author_email: k<PERSON><EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Get weekly active fleet metrics of fleetcoach mapped drivers
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select count(distinct delivery_driver_id) as fo_category_count
  from {{.table}}
  where dt >= {{.start_dt}} and dt <= {{.end_dt}}
  and zone_id = {{.zone_id}}
  and fleet_coach_id = {{.fleet_coach_id}}
  and fo_category = {{.fo_category}}
filters:
  - start_dt
  - end_dt
  - zone_id
  - fleet_coach_id
  - fo_category
