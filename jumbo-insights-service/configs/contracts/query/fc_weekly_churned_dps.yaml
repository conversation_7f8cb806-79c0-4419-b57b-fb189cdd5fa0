 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: rider_daily_metrics
identifier: fc_weekly_churned_dps
audit:
  author_email: k<PERSON>hn<PERSON>.<EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Get churned delivery driver ids of fleetcoach mapped drivers
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  select DISTINCT delivery_driver_id as churned_dps
  from {{.table}}
  where dt = {{.dt}}
  and zone_id = {{.zone_id}}
  and fleet_coach_id = {{.fleet_coach_id}}
  and dp_type = '21 day churn'

filters:
  - dt
  - zone_id
  - fleet_coach_id
