 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: rider_daily_metrics
identifier: rider_last_connected_filter
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Filter riders based on their last interaction with the fleet coach 
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 40
# contract type
type: sql
sql: >
  SELECT delivery_driver_id,
  MAX(last_met) AS last_met, 
  MAX(last_call_connected) AS last_call_connected
  FROM {{.table}}
  WHERE delivery_driver_id IN ({{.delivery_driver_ids}})
  AND dt = {{.date}}
  AND fleet_coach_id = {{.fleet_coach_id}}
  GROUP BY delivery_driver_id
filters:
  - date
  - delivery_driver_ids
  - fleet_coach_id