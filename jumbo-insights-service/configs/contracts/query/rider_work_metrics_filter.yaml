 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: rider_hourly_metrics
identifier: rider_work_metrics_filter
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Filter riders based on their orders, earning and login time within a timeframe
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT delivery_driver_id,
  SUM(orders) AS total_orders, SUM(earnings) AS total_earnings,
  SUM(login_time_seconds) AS total_login_time
  FROM {{.table}}
  WHERE delivery_driver_id IN ({{.delivery_driver_ids}})
  AND hour BETWEEN {{.hour_start}} AND {{.hour_end}}
  AND "timestamp" BETWEEN {{.time_start}} AND {{.time_end}}
  AND joining_date >= ({{.joining_date}})
  GROUP BY delivery_driver_id
filters:
  - hour_start
  - hour_end
  - time_start
  - time_end
  - delivery_driver_ids
  - joining_date