 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: rider_daily_metrics
identifier: rider_type_filter
audit:
  author_email: jain.saura<PERSON><PERSON>@zomato.com
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Filter riders based on their type like marathoner, substitute etc
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT delivery_driver_id
  FROM {{.table}}
  WHERE dp_type IN ({{.dp_types}})
  AND delivery_driver_id IN ({{.delivery_driver_ids}})
  AND dt = ({{.date}})
filters:
  - date
  - dp_types
  - delivery_driver_ids
