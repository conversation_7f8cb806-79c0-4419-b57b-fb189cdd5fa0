# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: story_insights_hourly_stats
identifier: story_insights_subject_trends
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: stories-service
  pd_service_name: stories-service
  description: Get stories stats at subject level
# TTL for cache, half an hour
caching_ttl: 1800
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 900
# Time in ms
sla: 120
# Request per second
rate_limit: 20
# Dimensions
columns:
  - name: subject_id
  - name: bookmarks
    func: sum
    source_column: bookmarks
  - name: shares
    func: sum
    source_column: shares
  - name: res_page_visits
    func: sum
    source_column: res_page_visits
  - name: views
    func: sum
    source_column: views
# Group by columns
aggregations:
  - subject_id
filters:
  - subject_id
  - subject_type