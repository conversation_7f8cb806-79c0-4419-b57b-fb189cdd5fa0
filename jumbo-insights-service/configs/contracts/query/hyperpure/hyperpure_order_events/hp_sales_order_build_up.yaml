query_backend: pinot
tenant: Zomato
table: hyperpure_order_events
identifier: hp_sales_order_build_up
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: hp-consumer-service-v2
  pd_service_name: prod_hp_consumer_service_v2
  description: For fetching live ordering info for a sales poc
# TTL for cache, Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >

  select
  DATETIMECONVERT(
    order_placed_time,
    '1:SECONDS:EPOCH',
    '1:DAYS:SIMPLE_DATE_FORMAT:yyyyMMdd tz(Asia/Kolkata)',
    '1:DAYS'
  ) AS dt,
  amsaleslead,
  count(distinct order_number) orders,
  count(distinct buyer_outlet_id) outlets,
  count(distinct account_id) accounts,
  sum(total_order_price) as gmv,
  sum(total_order_weight) as tonnage
  from {{.table}}
  where order_placed_time < {{.time_before}}
  and order_placed_time >= {{.time_after}}
  and amsaleslead = {{.sales_poc_email}}
  and order_status != 'CANCELLED'
  group by
  1,
  2
  order by
  2,
  1
  
filters:
  - time_after
  - time_before
  - sales_poc_email