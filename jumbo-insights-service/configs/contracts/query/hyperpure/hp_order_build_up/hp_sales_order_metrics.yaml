query_backend: pinot
tenant: Zomato
table: hp_sales_poc_order_metrics
identifier: hp_sales_order_metrics
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: hp-consumer-service-v2
  pd_service_name: prod_hp_consumer_service_v2
  description: For fetching live ordering info for a sales poc
# TTL for cache, Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 50
# Request per second
rate_limit: 20
# contract type
type: sql
sql: >
  select accounts, accounts_l7, outlets, outlets_l7, amsaleslead, gmv, gmv_l7, tonnage, tonnage_l7,
  orders, order_l7, last_updated_at
  from {{.table}} 
  where amsaleslead={{.sales_poc_email}}
  
filters:
  - sales_poc_email