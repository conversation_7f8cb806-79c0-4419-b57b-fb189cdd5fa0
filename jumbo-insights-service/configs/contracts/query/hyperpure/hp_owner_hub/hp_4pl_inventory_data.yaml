query_backend: trino
tenant: Zomato
table: bin_inventory_detail
identifier: hp_4pl_inventory_data
catalog: hive
schema: hp_wms
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: hp-wms-service-v2
  pd_service_name: prod_hp_wms_service_v2
  description: Live inventory data for 4PL 
# TTL for cache, Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 90000
# Request per second
rate_limit: 10
# contract type
type: sql
sql: >
  with cdc_inventory as 
  (select aid,
    json_extract_scalar(after, '$.batch_number') AS batch_number,
    cast(json_extract(after, '$.batch_quantity_left') as integer) AS batch_quantity_left,
    cast(json_extract(after, '$.batch_quantity_reserved') as integer) AS batch_quantity_reserved,
    cast(json_extract_scalar(after, '$.product_number') as integer) AS product_number,
    json_extract_scalar(after, '$.bin_number') AS bin_number,
    json_extract_scalar(after, '$.created_at') AS created_at,
    json_extract_scalar(after, '$.bin_type') AS bin_type,
    json_extract_scalar(after, '$.expiry_date') AS expiry_date,
    CAST(json_extract_scalar(after, '$.latest_in_price') AS double) AS latest_in_price,
    json_extract_scalar(after, '$.warehouse_code') AS warehouse_code,
    cast(from_unixtime(ts_ms/1000) as timestamp) ts, ts_ms
    from hp_wms_cdc.bin_inventory_details
    where dt>=date_format(current_date - interval '1' day, '%Y%m%d')
    ),
    cd_inventory_v2 as (
    select 
    company_id, a.*
    from cdc_inventory as a
    inner join hp_wms.product_company_mapping as b on (a.product_number=b.product_number)
    )
    ,cdc as 
    ( select *, 
    row_number() over(partition by bin_number, batch_number, product_number, warehouse_code order by ts_ms desc, aid desc) rn
    from cd_inventory_v2
    )
    ,cdc_base as 
    (select company_id,
    warehouse_code,
    batch_number,
    batch_quantity_left+batch_quantity_reserved as batch_quantity_left ,
    product_number,
    bin_number,
    bin_type,
    latest_in_price,
    date(from_iso8601_timestamp(expiry_date) AT TIME ZONE 'Asia/Kolkata') as expiry_date,
    ts,
    'cdc' as data_source 
    from
    cdc
    where rn=1
    )
    ,hp_wms as 
    (select company_id,
    warehouse_code,
    batch_number,
    batch_quantity_left+batch_quantity_reserved as qty,
    a.product_number,
    bin_number,
    bin_type,
    latest_in_price,
    date(expiry_date+interval'330'minute) as expiry_date,
    a.updated_at+interval'330'minute ts,
    'hpwms' as data_source
    from  hp_wms.bin_inventory_details as a
    inner join hp_wms.product_company_mapping as b on (a.product_number=b.product_number)
    )
    ,inv_data as 
    (
    select *, count(*) over (partition by warehouse_code, product_number, batch_number, bin_number order by data_source) counts
    from
    (select * from cdc_base
    union all
    select * from hp_wms)
    )
    ,PUTAWAY_items_BASE AS 
    (SELECT
    json_extract_scalar(after, '$.batch_number') AS batch_number,
    cast(json_extract(after, '$.total_batch_quantity') as integer) AS total_batch_quantity,
    cast(json_extract_scalar(after, '$.product_number') as integer) AS product_number,
    json_extract_scalar(after, '$.created_at') AS created_at,
    json_extract_scalar(after, '$.expiry_date') AS expiry_date,
    CAST(json_extract_scalar(after, '$.cost_price') AS double) AS cost_price,
    json_extract_scalar(after, '$.putaway_item_status') AS putaway_item_status,
    cast(json_extract_scalar(after, '$.putaway_list_id') as int) AS putaway_list_id,
    json_extract_scalar(after, '$.updated_by_user_type') AS updated_by,
    json_extract_scalar(after, '$.type') AS type,
    cast(from_unixtime(ts_ms/1000) as timestamp) ts, 
    ts_ms
    FROM hp_wms_cdc.putaway_list_ITEM 
    where dt>=date_format(current_date - interval '10' day, '%Y%m%d') 
    )
    ,PUTAWAY_items as
    (SELECT A.*,row_number() over(partition by putaway_list_id,batch_number,A.product_number order by ts desc) as rank
    FROM 
    PUTAWAY_items_BASE A
    INNER JOIN hp_wms.product_company_mapping AS B ON (A.product_number=B.product_number)
    )
    ,putaway_list_BASE as (
    select cast(json_extract_scalar(after, '$.id') as int) AS id,
    (json_extract_scalar(after, '$.status')) AS status,
    (json_extract_scalar(after, '$.warehouse_code')) AS warehouse_code,
    cast(from_unixtime(ts_ms/1000) as timestamp) ts
    FROM hp_wms_cdc.putaway_list
    where dt>=date_format(current_date - interval '10' day, '%Y%m%d')
    )
    ,putaway_list as
    (SELECT *, ROW_NUMBER() OVER(PARTITION BY id ORDER BY TS DESC) AS RANK
    FROM 
    putaway_list_BASE)
    , base as
    (
    select 
    company_id,
    warehouse_code,
    batch_number,
    product_number,
    date(expiry_date) as expiry_date,
    latest_in_price,
    batch_quantity_left
    from inv_data
    where (counts=1 or data_source='cdc') and  bin_type='NORMAL'
    union
    SELECT 
    company_id,
    warehouse_code,
    batch_number,
    a.product_number,
    date(from_iso8601_timestamp(expiry_date) AT TIME ZONE 'Asia/Kolkata') as expiry_date,
    cost_price,
    total_batch_quantity
    FROM PUTAWAY_items AS A
    INNER JOIN hp_wms.product_company_mapping AS B ON (A.product_number=B.product_number)
    left join putaway_list as c on (a.putaway_list_id=c.id AND C.RANK=1)
    WHERE 
    type='GOOD' 
    AND status!='COMPLETED'
    AND putaway_item_status!='COMPLETED'
    AND A.RANK=1
    )
    select warehouse_code as WarehouseCode, a.batch_number as BatchNumber,p.name as ProductName, a.product_number as ProductNumber,
    date_format(expiry_date, '%Y-%m-%d') as ExpiryDate,
    sum(batch_quantity_left) as Quantity, 
    1.00*sum(batch_quantity_left*latest_in_price)/sum(batch_quantity_left) as Price
    from base as a
    left join hp_wms.product as p on (a.product_number=p.product_number)
    where batch_quantity_left>0
    and (
      LENGTH({{.warehouse_code}}) = 0 OR warehouse_code = {{.warehouse_code}}
    )
    and company_id={{.company_id}}
    group by 1,2,3,4,5
    order by warehouse_code , a.product_number , a.batch_number
filters:
  - company_id
  - warehouse_code
