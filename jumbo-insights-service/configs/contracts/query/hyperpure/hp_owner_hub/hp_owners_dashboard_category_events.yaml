query_backend: pinot
tenant: Zomato
table: hp_owner_hub_daily_stats
identifier: hp_owners_dashboard_category_events
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: hp-consumer-service-v2
  pd_service_name: prod_hp_consumer_service_v2
  description: Order events category wise for  dashboard in hyperpure consumer app
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  select 
  buyer_outlet_id, duration_dt, category_id, sum(gmv) gmv, 
  sum(savings_delivery_charge) savings_delivery_charge,
  sum(savings_cart_level) savings_cart_offer,
  sum(savings_new_user) savings_new_user_chashback,
  sum(savings_milestone) savings_milestone_cashback,
  sum(savings_trial_offer) savings_trial_offer,
  sum(savings_qbp_based) savings_qbp_based
  from {{.table}}
  where dt between {{.start_date}} and {{.end_date}}
  and duration_type={{.duration_enum}}
  and buyer_outlet_id in ({{.outlet_ids}})
  group by 1,2,3
filters:
  - outlet_ids
  - start_date
  - end_date
  - duration_enum