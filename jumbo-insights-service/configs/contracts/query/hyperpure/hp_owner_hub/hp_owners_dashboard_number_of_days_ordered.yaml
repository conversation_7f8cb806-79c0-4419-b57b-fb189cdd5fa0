query_backend: pinot
tenant: Zomato
table: hp_owner_hub_daily_stats
identifier: hp_owners_dashboard_number_of_days_ordered
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: hp-consumer-service-v2
  pd_service_name: prod_hp_consumer_service_v2
  description: Number of days ordered field for dashboard in hyperpure consumer app
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  select 
  duration_dt, count(distinct dt) ordering_days
  from {{.table}}
  where duration_dt between {{.start_date}} and {{.end_date}}
  and dt >= {{.start_date}}
  and duration_type={{.duration_enum}}
  and buyer_outlet_id in ({{.outlet_ids}})
  group by 1
filters:
  - outlet_ids
  - start_date
  - end_date
  - duration_enum