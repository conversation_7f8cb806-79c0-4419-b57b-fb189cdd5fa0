query_backend: trino
tenant: Zomato
catalog: zomato
schema: insights_etls
table: hp_buyer_order_details
identifier: hp_buyer_order_details_download
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: hp-wms-service-v2
  pd_service_name: prod_hp_wms_service_v2
  description: For order download for hyperpure merchants
# TTL for cache, Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 150
# Time in ms
sla: 60000
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  select order_number, order_status
  from  {{.table}} 
  and dt between {{.start_date}} and {{.end_date}}
  and case when {{.entity_status_filter}} = 1 then order_status in ({{.entity_status_values}}) else 1=1 end
filters:
  - entity_status_filter
  - entity_status_values
  - start_date
  - end_date
