query_backend: pinot
tenant: Zomato
table: hp_sales_hub_daily_stats
identifier: hp_sales_category_breakup_new_v2
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: hp-consumer-service-v2
  pd_service_name: prod_hp_consumer_service_v2
  description: For category-wise breakup of metrics for hyperpure sales app
# TTL for cache, Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 50
# Request per second
rate_limit: 20
# contract type
type: sql
sql: >
  select 
  case when {{.entity_type_filter}} = 1 then buyer_outlet_id else buyer_account_id end entity_id,
  duration_dt,category_id,sum(gmv) gmv, sum(margin) margin, sum(tonnage) tonnage 
  from  {{.table}} 
  where  duration_type={{.duration_enum}}
  and day between {{.start_day_number}} and {{.end_day_number}} 
  and case when {{.duration_filter_type}} = 1 then dt else duration_dt end between {{.start_date}} and {{.end_date}}
  and case when {{.entity_type_filter}} = 1 then buyer_outlet_id else buyer_account_id end  in ({{.entity_ids}})
  and case when {{.entity_type_account_filter}} != 0  then buyer_outlet_id in ({{.sub_entity_ids}}) else 1=1 end
  and case when {{.category_id}}!= 0 then category_id={{.category_id}} else 1=1 end
  group by 1,2,3
filters:
  - entity_ids
  - entity_type_account_filter
  - sub_entity_ids
  - entity_type_filter
  - start_day_number
  - end_day_number
  - start_date
  - end_date
  - category_id
  - duration_enum
  - duration_filter_type