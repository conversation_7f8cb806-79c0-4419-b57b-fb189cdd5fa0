query_backend: pinot
tenant: Zomato
table: hp_sales_hub_daily_stats
identifier: hp_sales_ordering_day_count
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: hp-consumer-service-v2
  pd_service_name: prod_hp_consumer_service_v2
  description: For fetching ordering day count for account/outlet
# TTL for cache, Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 600
# Time in ms
sla: 50
# Request per second
rate_limit: 20
# contract type
type: sql
sql: >
  select 
  count(distinct dt) as ordering_day_count
  from {{.table}} 
  where duration_type={{.duration_enum}}
  and dt between {{.start_date}} and {{.end_date}}
  and case when {{.entity_type_filter}} = 1 then buyer_outlet_id else buyer_account_id end in ({{.entity_ids}})
  and case when {{.entity_type_account_filter}} != 0  then buyer_outlet_id in ({{.sub_entity_ids}}) else 1=1 end
filters:
  - entity_ids
  - entity_type_account_filter
  - sub_entity_ids
  - entity_type_filter
  - start_date
  - end_date
  - duration_enum
