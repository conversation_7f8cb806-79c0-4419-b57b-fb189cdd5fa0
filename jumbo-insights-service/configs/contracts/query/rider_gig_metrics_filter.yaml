 # can be pinot/trino
query_backend: pinot
tenant: Zomato
table: rider_hourly_metrics
identifier: rider_gig_metrics_filter
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: logistics-fleet-coach
  pd_service_name: rider_experience
  description: Filter riders based on their gig data within a timeframe
# TTL for cache, Time in seconds
caching_ttl: 7200
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 3600
# Time in ms
sla: 50
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  SELECT delivery_driver_id,
  SUM(booked_gigs_count) AS total_booked_count, SUM(canceled_gigs_count) AS total_canceled_count,
  SUM(completed_gigs_count) AS total_completed_count, SUM(no_show_gigs_count) AS total_no_show_count
  FROM {{.table}}
  WHERE delivery_driver_id IN ({{.delivery_driver_ids}})
  AND hour BETWEEN {{.hour_start}} AND {{.hour_end}}
  AND "timestamp" BETWEEN {{.time_start}} AND {{.time_end}}
  GROUP BY delivery_driver_id
filters:
  - hour_start
  - hour_end
  - time_start
  - time_end
  - delivery_driver_ids