# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: trend_stats
identifier: ztrends_price_distribution
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: merchantpod
  pd_service_name: o2merchant-analytics-pager
  description: Order volume for each price range bucket
# TTL for cache, Time in seconds
caching_ttl: 3600 # one hour
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 300
# Time in ms
sla: 50
# Request per second
rate_limit: 160
# Dimensions
columns:
  - name: item_entity_type
  - name: item_entity_id
  - name: item_name
  - name: bucket_id
  - name: order_volume
    func: sum
    source_column: order_volume
# Group by columns
aggregations:
  - item_entity_type
  - item_entity_id
  - item_name
  - bucket_id

filters:
  - item_entity_type
  - item_entity_id
  - location_entity_type
  - location_entity_id
