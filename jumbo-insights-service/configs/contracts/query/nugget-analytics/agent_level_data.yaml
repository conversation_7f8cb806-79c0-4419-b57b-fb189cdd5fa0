query_backend: clickhouse
tenant: nugget-analytics
table: unified_ticket_events
identifier: nugget_agent_level_data
audit:
  author_email: <EMAIL>
  team_email: <EMAIL>
  service: chat-v2-backend-service
  pd_service_name: chat-backend-v2-service
  description: Get agent level data for nugget analytics
# TTL for cache, Time in seconds
caching_ttl: 300
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 60
# Time in ms
sla: 200
# Request per second
rate_limit: 100
# contract type
type: sql
sql: >
  WITH base AS (
    SELECT
      CAST(
        property_access_key_value_json.sys_assigned_agent_id [1],
        'String'
      ) AS sys_agent_id,
      CAST(
        property_access_key_value_json.sys_current_assigned_agent_id [1],
        'String'
      ) AS current_agent_id,
      *,
      CAST(
        property_access_key_value_json.sys_is_sla_frt_breached [1],
        'Boolean'
      ) AS is_frt_breached,
      CAST(
        property_access_key_value_json.sys_is_sla_resolution_time_breached [1],
        'Boolean'
      ) AS is_aht_breached,
      CAST(
        property_access_key_value_json.sys_is_sla_frt_breached [1],
        'Boolean'
      ) AS is_irt_breached,
      CAST(
        property_access_key_value_json.sys_agent_assigned_at [1],
        'Int64'
      ) * 1000 as assigned_at
    FROM
      {{.unified_ticket_events_table}}
    WHERE
      coalesce(sys_agent_id, current_agent_id) IN ({{.agent_ids}})
      AND ts BETWEEN toDateTime({{.start_time}})
      AND toDateTime({{.end_time}})
      AND (
        status IN ('STATUS_OPEN', 'STATUS_CLOSED')
        OR event_name IN (
          'ticket_agent_last_replied',
          'ticket_update_frt',
          'csat_submitted',
          'ticket_update_sla_breached_property'
        )
      )
  ),
  avg_agent_rating AS (
    SELECT
      resolved_by as agent_id,
      avg(rating) as avg_rating
    FROM
      {{.one_support_events_table}}
    WHERE
      event_name = 'csat_submitted'
      AND resolved_by_role = 'CLIENT_TYPE_AGENT'
      AND resolved_by IN ({{.agent_ids}})
      AND ts BETWEEN toDateTime({{.start_time}})
      AND toDateTime({{.end_time}})
    group by
      resolved_by
  ),
  agent_metrics_wide AS (
    SELECT
      coalesce(sys_agent_id, current_agent_id) AS agent_id,
      count(
        DISTINCT CASE
          WHEN status = 'STATUS_OPEN' THEN ticket_id
        END
      ) AS total_assigned_tickets,
      count(
        DISTINCT CASE
          WHEN event_name = 'ticket_close_ticket' THEN ticket_id
        END
      ) AS total_resolved_tickets,
      count(
        CASE
          WHEN event_name = 'ticket_update_sla_breached_property'
          AND is_irt_breached = true THEN ticket_id
        END
      ) AS irt_breached_tickets,
      count(
        CASE
          WHEN event_name = 'ticket_update_sla_breached_property'
          AND is_frt_breached = true THEN ticket_id
        END
      ) AS frt_breached_tickets,
      count(
        CASE
          WHEN event_name = 'ticket_update_sla_breached_property'
          AND is_aht_breached = true THEN ticket_id
        END
      ) AS aht_breached_tickets,
      count(
        DISTINCT CASE
          WHEN event_name = 'ticket_agent_last_replied' THEN ticket_id
        END
      ) AS agent_responded_tickets,
      count(
        CASE
          WHEN event_name = 'ticket_agent_last_replied' THEN ticket_id
        END
      ) AS agent_responses,
      AVG(
        CASE
          WHEN event_name = 'ticket_update_frt' THEN updated_at - assigned_at
        end
      ) / 60000 AS avg_frt_mins
    FROM
      base
    GROUP BY
      agent_id
  ),
  all_agents AS (
    SELECT
      DISTINCT coalesce(sys_agent_id, current_agent_id) AS agent_id
    FROM
      base
  )
  SELECT
    aa.agent_id,
    metric_name,
    metric_value
  FROM
    all_agents AS aa
    LEFT JOIN agent_metrics_wide AS amw ON aa.agent_id = amw.agent_id
    LEFT JOIN avg_agent_rating as rat on aa.agent_id = rat.agent_id ARRAY
    JOIN ['total_assigned_tickets', 'total_resolved_tickets', 'agent_responses', 'agent_responded_tickets', 'irt_breached_tickets', 'frt_breached_tickets', 'aht_breached_tickets', 'avg_frt_mins', 'avg_rating'] AS metric_name,
    [
          cast(amw.total_assigned_tickets as Float64),
          cast(amw.total_resolved_tickets as Float64),
          cast(amw.agent_responses as Float64),
          cast(amw.agent_responded_tickets as Float64),
          cast(amw.irt_breached_tickets as Float64),
          cast(amw.frt_breached_tickets as Float64),
          cast(amw.aht_breached_tickets as Float64),
          amw.avg_frt_mins,
          rat.avg_rating
          
      ] AS metric_value
  ORDER BY
    aa.agent_id,
    metric_name

table_names:
  - one_support_events_table
  - unified_ticket_events_table

filters:
  - agent_ids
  - start_time
  - end_time
