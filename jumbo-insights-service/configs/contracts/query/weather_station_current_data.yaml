# can be pinot/trino
query_backend: pinot
tenant: Zomato
table: weather_station_events
identifier: weather_station_current_data
audit:
  author_email: aman.sri<PERSON><PERSON><EMAIL>
  team_email: <EMAIL>
  service: weather-service
  pd_service_name: delivery-rules
  description: Weather Station Current data
# Time in seconds
caching_ttl: 3600
# Irrespective of cache value, fresh result will be fetched after the refresh interval
# Time in seconds, 0 to disable fetch from cache
refresh_interval: 900
# Time in ms
sla: 100
# Request per second
rate_limit: 160
# Dimensions
columns:
  - name: current_temperature_deg_c
    func: max
    source_column: current_temperature_deg_c
  - name: current_humidity_perc
    func: max
    source_column: current_humidity_perc
  - name: dew_point_temperature_deg_c
    func: max
    source_column: dew_point_temperature_deg_c
  - name: pressure_hpa
    func: max
    source_column: pressure_hpa
  - name: rainfall_intensity_mm
    func: max
    source_column: rainfall_intensity_mm
  - name: rainfall_accumulation_mm
    func: max
    source_column: rainfall_accumulation_mm
  - name: wind_direction_deg
    func: max
    source_column: wind_direction_deg
  - name: wind_speed_ms
    func: max
    source_column: wind_speed_ms
# Group by columns
aggregations:
  - report_time  
filters:
  - report_time
  - device_serial_number
