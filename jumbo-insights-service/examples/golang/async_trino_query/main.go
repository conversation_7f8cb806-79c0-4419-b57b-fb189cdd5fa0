package main

import (
	"context"
	"encoding/json"
	"fmt"

	"crypto/tls"
	"flag"
	"log"
	"time"

	// "github.com/Zomato/go/grpc/credentials"
	"github.com/Zomato/go/grpc/credentials/insecure"
	asyncquerypb "github.com/Zomato/jumbo-insights-service-client-golang/asyncquerier/v1"
	commonpb "github.com/Zomato/jumbo-insights-service-client-golang/common"
	querypb "github.com/Zomato/jumbo-insights-service-client-golang/querier/v1"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
)

type FilterType string

type MetricFilter struct {
	Key      FilterType  `json:"filter"`
	Operator string      `json:"filter_op"`
	DataType string      `json:"filter_type"`
	Value    interface{} `json:"filter_value"`
}

func convertFilterOpToQueryFilterOp(op string) querypb.Filter_ComparisonOperator {
	switch op {
	case "EQUALS":
		return querypb.Filter_EQUALS
	default:
		return querypb.Filter_EQUALS
	}
}

func convertInterfaceToProtoAnyValue(ctx context.Context, datatype string,
	val interface{},
) *commonpb.AnyValue {
	switch datatype {
	case "STRING":
		var stringVal string
		ConvertToStuct(ctx, val, &stringVal)
		return &commonpb.AnyValue{
			Value: &commonpb.AnyValue_StringValue{
				StringValue: stringVal,
			},
		}
	case "INT":
		var intVal int64
		ConvertToStuct(ctx, val, &intVal)
		return &commonpb.AnyValue{
			Value: &commonpb.AnyValue_IntValue{
				IntValue: intVal,
			},
		}
	case "ARRAY_INT":
		var arrInt []int64
		ConvertToStuct(ctx, val, &arrInt)
		arrFilters := &commonpb.ArrayValue{}
		for i := range arrInt {
			arrFilters.Values = append(arrFilters.Values,
				&commonpb.AnyValue{Value: &commonpb.AnyValue_IntValue{IntValue: arrInt[i]}})
		}

		return &commonpb.AnyValue{
			Value: &commonpb.AnyValue_ArrayValue{
				ArrayValue: arrFilters,
			},
		}
	case "ARRAY_STRING":
		var arrInt []string
		ConvertToStuct(ctx, val, &arrInt)
		arrFilters := &commonpb.ArrayValue{}
		for i := range arrInt {
			arrFilters.Values = append(arrFilters.Values,
				&commonpb.AnyValue{Value: &commonpb.AnyValue_StringValue{StringValue: arrInt[i]}})
		}

		return &commonpb.AnyValue{
			Value: &commonpb.AnyValue_ArrayValue{
				ArrayValue: arrFilters,
			},
		}
	}

	return nil
}

func ConvertToStuct(ctx context.Context, obj, result interface{}) {
	data, err := json.Marshal(obj)

	if err != nil {
		// log.ErrorfLog(ctx, "[Jumbo-insight][ConvertToStruct] error while marshalling data: %s", err)
		return
	}

	err = json.Unmarshal(data, &result)
	if err != nil {
		fmt.Println(err)
		// log.ErrorfLog(ctx, "[Jumbo-insight][ConvertToStruct] error while unmarshalling data: %s", err)
	}
}

func convertMetricFilterToQueryFilters(ctx context.Context,
	metricFilters []MetricFilter) []*querypb.Filter {
	queryFilters := make([]*querypb.Filter, 0)
	for _, filter := range metricFilters {
		val := convertInterfaceToProtoAnyValue(ctx, filter.DataType, filter.Value)
		queryFilters = append(queryFilters, &querypb.Filter{
			Field: string(filter.Key),
			Op:    convertFilterOpToQueryFilterOp(filter.Operator),
			Value: val,
		})
	}

	return queryFilters
}

func main() {
	address := "localhost:7001"
	authorityHeader := "jumbo-insights-service"

	// address := "kuma-gateway.eks.zdev.net:80"
	// address := "zomato-grpc.stage.grofer.io:443"
	// authorityHeader := "jumbo-insights-service.eks.zdev.net"

	// address := "zomato-grpc.preprod.grofer.io:443"
	// authorityHeader := "jumbo-insights-service.zomans.com"

	// address := "zomato-grpc.prod.grofer.io:443"
	// authorityHeader := "jumbo-insights-service.zomans.com"

	flag.Parse()
	var creds credentials.TransportCredentials

	tlsEnabled := false
	if tlsEnabled {
		creds = credentials.NewTLS(&tls.Config{
			ServerName: authorityHeader,
		})
	} else {
		creds = insecure.NewCredentials()
	}

	conn, err := grpc.NewClient(address, grpc.WithTransportCredentials(creds), grpc.WithAuthority(authorityHeader))
	if err != nil {
		log.Fatalf("did not connect: %v", err) //nolint: ignore-fatal
	}
	defer conn.Close()

	c := asyncquerypb.NewAsyncQuerierServiceClient(conn)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	TestAsyncTrinoQueryRequest(ctx, c)
}

func TestAsyncTrinoQueryRequest(ctx context.Context, c asyncquerypb.AsyncQuerierServiceClient) {
	Filters := []MetricFilter{}

	Filters = append(
		Filters,
		MetricFilter{
			Key:      "interval",
			Operator: "EQUALS",
			DataType: "STRING",
			Value:    "7",
		},
	)

	queryFilters := convertMetricFilterToQueryFilters(ctx, Filters)

	req := asyncquerypb.AsyncQueryRequest{
		ClientId:   "search_management_service",
		ContractId: "search_management_service_keyword_metrics",
		Querier: &querypb.Querier{
			Filters: queryFilters,
		},
		QueryContext: &asyncquerypb.AsyncQueryContext{
			User:       "<EMAIL>",
			FileName:   "search_management_service_keyword_metrics",
			ResultType: asyncquerypb.AsyncQueryContext_CSV,
		},
	}

	res, err := c.AsyncQuery(ctx, &req)
	if err != nil {
		log.Fatalf("Request submission failed: %v", err) //nolint: ignore-fatal
	}
	log.Printf("Response \n%v", res)

	execId := res.QueryExecutionInfo.ExecutionId

	log.Printf("Execution ID: %v", execId)

	res, err = c.GetQueryResult(ctx, &asyncquerypb.GetQueryResultRequest{
		ClientId:    "search_management_service",
		ContractId:  "product_knowledge_metadata_change_log",
		ExecutionId: execId,
	})
	if err != nil {
		log.Printf("Get content failed: %v", err) //nolint: ignore-fatal
	}

	log.Printf("Response \n%v", res)
}
