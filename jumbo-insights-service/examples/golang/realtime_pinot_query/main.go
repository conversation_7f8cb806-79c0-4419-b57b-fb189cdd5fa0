package main

import (
	"context"
	"crypto/tls"

	// "crypto/tls"
	"flag"
	"log"
	"time"

	"github.com/Zomato/go/grpc/credentials/insecure"
	commonpb "github.com/Zomato/jumbo-insights-service-client-golang/common"
	querypb "github.com/Zomato/jumbo-insights-service-client-golang/querier/v1"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
)

func main() {
	// address := "127.0.0.1:7001"
	// authorityHeader := "jumbo-insights-service-grpc-main-svc"

	// address := "kuma-gateway.eks.zdev.net:80"
	address := "zomato-grpc.stage.grofer.io:443"
	authorityHeader := "jumbo-insights-service.eks.zdev.net"

	// address := "zomato-grpc.preprod.grofer.io:443"
	// authorityHeader := "jumbo-insights-service.zomans.com"

	// address := "zomato-grpc.prod.grofer.io:443"
	// authorityHeader := "jumbo-insights-service.zomans.com"

	flag.Parse()
	var creds credentials.TransportCredentials

	tlsEnabled := true
	if tlsEnabled {
		creds = credentials.NewTLS(&tls.Config{
			ServerName: authorityHeader,
		})
	} else {
		creds = insecure.NewCredentials()
	}
	// Set up a connection to the server.
	// conn, err := grpc.NewClient(address, grpc.WithTransportCredentials(creds), grpc.WithAuthority(authorityHeader))
	conn, err := grpc.NewClient(address, grpc.WithTransportCredentials(creds), grpc.WithAuthority(authorityHeader))
	if err != nil {
		log.Fatalf("did not connect: %v", err) //nolint: ignore-fatal
	}
	defer conn.Close()

	c := querypb.NewQuerierServiceClient(conn)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	TestPinotQueryRequest(ctx, c)
}

func TestPinotQueryRequest(ctx context.Context, c querypb.QuerierServiceClient) {
	filters := [...]*querypb.Filter{
		{
			Field: "ads_type",
			Op:    querypb.Filter_EQUALS,
			Value: &commonpb.AnyValue{
				Value: &commonpb.AnyValue_StringValue{
					StringValue: "dish",
				},
			},
		},
	}

	req := querypb.QueryRequest{
		ClientId: "test",
		QueryId:  "frontend_level_sales_quantity_metric",
		Querier: &querypb.Querier{
			Filters: filters[:],
			// Interval: &querypb.Interval{
			// 	StartTime:     1661700600000,
			// 	EndTime:       1661700600000,
			// 	DatetimeField: "timestamp",
			// },
		},
	}

	res, err := c.Query(ctx, &req)
	if err != nil {
		log.Fatalf("Get content failed: %v", err) //nolint: ignore-fatal
	}
	log.Printf("Response \n%v", res)
}
