# syntax = docker/dockerfile:1

########################################
## Build Stage
########################################
FROM public.ecr.aws/zomato/golang:1.24.5-bookworm-multiarch as builder

# add a label to clean up later
LABEL stage=intermediate

# setup the working directory
WORKDIR /go/src

# add netrc file to allow access to private github repo
COPY .netrc /root/.netrc

# install dependencies
ENV GO111MODULE=on
COPY ./go.mod ./go.mod
COPY ./go.sum ./go.sum
COPY ./client/golang ./client/golang
RUN --mount=type=cache,target=/go/pkg/mod \
    go mod download -x

# add source code
COPY . .

# add required files from host
COPY ./configs /root/configs

# build the source
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o insights-service-linux-amd64 -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn"

########################################
## Production Stage
########################################
FROM public.ecr.aws/zomato/zomato/base:v3-multiarch-0.5

# set working directory
WORKDIR /root

# copy required files from builder
COPY --from=builder /go/src/insights-service-linux-amd64 ./insights-service-linux-amd64
COPY --from=builder /go/src/configs ./configs

CMD ["./insights-service-linux-amd64"]
# CMD ["tail", "-f", "/dev/null"]