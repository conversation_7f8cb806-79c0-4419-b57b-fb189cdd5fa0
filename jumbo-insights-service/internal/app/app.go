package app

import (
	"context"
	"errors"
	"strings"

	"github.com/Zomato/go/grpc"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/jumbo-insights-service/internal/util"
	"github.com/Zomato/jumbo-insights-service/internal/worker/trinoasync"
)

var (
	ErrInvalidModeConfig error = errors.New("invalid app mode config")
)

// App Struct
type App struct {
	mode       string
	grpcServer *grpc.Server
}

// NewApp returns App
func NewApp() *App {
	return &App{}
}

// WithGRPCServer sets GRPC Server
func (app *App) WithGRPCServer(srv *grpc.Server) *App {
	app.grpcServer = srv
	return app
}

// WithMode sets app mode
func (app *App) WithMode(mode string) *App {
	app.mode = mode
	return app
}

// Run starts the app according to mode
func (app *App) Run(ctx context.Context) error {
	switch strings.ToLower(app.mode) {
	case util.TrinoWorker:
		return app.runTrinoAsyncQueryWorker(ctx)
	case util.PinotIngestionWorker:
		return app.runPinotIngestionWorker(ctx)
	case util.GRPC:
		return app.runGRPCServer()
	default:
		return ErrInvalidModeConfig
	}
}

// runGRPCServer starts the grpc server
func (app *App) runGRPCServer() error {
	if app.grpcServer == nil {
		log.Panicf("GRPC Server not registered...")
	}
	return app.grpcServer.Serve()
}

// runPinotIngestionWorker starts the pinot ingestion workers
func (app *App) runPinotIngestionWorker(ctx context.Context) error {
	log.FromContext(ctx).Info("starting worker...")
	log.FromContext(ctx).Info("exiting...")
	return nil
}

// runTrinoAsyncQueryWorker starts the trino async query workers
func (app *App) runTrinoAsyncQueryWorker(ctx context.Context) error {
	log.FromContext(ctx).Info("starting worker...")
	ctx2, cancel := context.WithCancel(ctx)
	trinoasync.Run(ctx2, cancel)
	log.FromContext(ctx).Info("exiting...")
	return nil
}
