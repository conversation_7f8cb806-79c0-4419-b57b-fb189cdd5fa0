package env

import (
	"context"
	"errors"
	"fmt"

	jumbo "github.com/Zomato/go/jumbo-v2"
	"github.com/Zomato/go/redis"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/pkg/clickhouse"
	"github.com/Zomato/jumbo-insights-service/pkg/crypto"
	"github.com/Zomato/jumbo-insights-service/pkg/dynamodb"
	"github.com/Zomato/jumbo-insights-service/pkg/pinot"
	"github.com/Zomato/jumbo-insights-service/pkg/ratelimiter"
	"github.com/Zomato/jumbo-insights-service/pkg/s3"
	"github.com/Zomato/jumbo-insights-service/pkg/sqs"
	"github.com/Zomato/jumbo-insights-service/pkg/trino"
	"google.golang.org/grpc"
)

// Env is the application environment exposing application scoped instances
// in request handlers
type Env struct {
	redisClient       *redis.Client
	queryContracts    map[string]*models.Query
	jumboClient       *jumbo.JumboClient
	rateLimiterClient *ratelimiter.Client
	sqsClient         *sqs.Client
	dynamoDBClient    *dynamodb.Client
	s3Client          *s3.Client
	trinoConfigs      map[string]*trino.Config
	cryptoClient      *crypto.Client
	pinotConfigs      map[string]*pinot.Config
	clickhouseClients map[string]*clickhouse.Client
}

type envContextKey string

const (
	// EnvCtxKey is the key to set and retrieve Env in context
	EnvCtxKey envContextKey = "env"
)

// NewEnv returns a new Env instance
func NewEnv(options ...func(env *Env)) Env {
	env := Env{}

	for _, option := range options {
		option(&env)
	}

	return env
}

// WithRedisConnection sets a redis connection in the Env
func WithRedisConnection(redisConn *redis.Client) func(*Env) {
	return func(env *Env) {
		env.redisClient = redisConn
	}
}

// Redis retrieves the redis connection from the Env
func (env *Env) Redis() *redis.Client {
	return env.redisClient
}

// WithRateLimiterClient sets the rate limiter client
func WithRateLimiterClient(client *ratelimiter.Client) func(*Env) {
	return func(env *Env) {
		env.rateLimiterClient = client
	}
}

// RateLimiterClient retrieves the rate limiter client
func (env *Env) RateLimiterClient() *ratelimiter.Client {
	return env.rateLimiterClient
}

// WithJumboClient stores jumbo-client in env
func WithJumboClient(client *jumbo.JumboClient) func(*Env) {
	return func(env *Env) {
		env.jumboClient = client
	}
}

// JumboClient retrieves the jumbo-client from the Env
func (env *Env) JumboClient() *jumbo.JumboClient {
	return env.jumboClient
}

// WithQueryContracts sets the query contacts in the Env
func WithQueryContracts(queryContracts map[string]*models.Query) func(*Env) {
	return func(env *Env) {
		env.queryContracts = queryContracts
	}
}

// QueryContracts retrieves the query contracts from the Env
func (env *Env) QueryContracts() map[string]*models.Query {
	return env.queryContracts
}

// WithSQSClient stores sqs-client in env
func WithSQSClient(client *sqs.Client) func(*Env) {
	return func(env *Env) {
		env.sqsClient = client
	}
}

// SQSClient retrieves the sqs-client from the Env
func (env *Env) SQSClient() *sqs.Client {
	return env.sqsClient
}

// WithDynamoDBClient stores sqs-client in env
func WithDynamoDBClient(dynamoDB *dynamodb.Client) func(*Env) {
	return func(env *Env) {
		env.dynamoDBClient = dynamoDB
	}
}

// DynamoDBClient retrieves the dynamodb connection from the Env
func (env *Env) DynamoDBClient() *dynamodb.Client {
	return env.dynamoDBClient
}

// WithS3Client stores sqs-client in env
func WithS3Client(s3Client *s3.Client) func(*Env) {
	return func(env *Env) {
		env.s3Client = s3Client
	}
}

// S3 retrieves the dynamodb connection from the Env
func (env *Env) S3() *s3.Client {
	return env.s3Client
}

// WithTrinoConfig stores trino connection config in env
func WithTrinoConfigs(trinoConfigs map[string]*trino.Config) func(*Env) {
	return func(env *Env) {
		env.trinoConfigs = trinoConfigs
	}
}

// TrinoConfig retrives the trino connection config from the Env
func (env *Env) TrinoConfig(tenant string) (*trino.Config, error) {
	config, ok := env.trinoConfigs[tenant]
	if !ok {
		return nil, fmt.Errorf("trino config not found for tenant: %s", tenant)
	}
	return config, nil
}

// WithCryptoClient stores crypto-client in env
func WithCryptoClient(cryptoClient *crypto.Client) func(*Env) {
	return func(env *Env) {
		env.cryptoClient = cryptoClient
	}
}

// CryptoClient retrieves the crypto client from the Env
func (env *Env) CryptoClient() *crypto.Client {
	return env.cryptoClient
}

// WithPinotConfig stores pinot connection config in env
func WithPinotConfigs(pinotConfigs map[string]*pinot.Config) func(*Env) {
	return func(env *Env) {
		env.pinotConfigs = pinotConfigs
	}
}

// PinotConfig retrives the pinot connection config from the Env
func (env *Env) PinotConfig(tenant string) (*pinot.Config, error) {
	config, ok := env.pinotConfigs[tenant]
	if !ok {
		return nil, fmt.Errorf("pinot configs not found for tenant: %s", tenant)
	}
	return config, nil
}
func WithClickhouseClients(clickhouseClients map[string]*clickhouse.Client) func(*Env) {
	return func(env *Env) {
		env.clickhouseClients = clickhouseClients
	}
}

// PinotConfig retrives the pinot connection config from the Env
func (env *Env) ClickHouseClient(tenant string) (*clickhouse.Client, error) {
	client, ok := env.clickhouseClients[tenant]
	if !ok {
		return nil, fmt.Errorf("clickhouse client not found for tenant: %s", tenant)
	}
	return client, nil
}

// WithContext returns a context containing the env Value
func (env *Env) WithContext(ctx context.Context) context.Context {
	nctx := context.WithValue(ctx, EnvCtxKey, env)
	return nctx
}

// FromContext retrieves the Env from the current context, if no Env is found
// in the context and error is returned
func FromContext(ctx context.Context) (*Env, error) {
	env, ok := ctx.Value(EnvCtxKey).(*Env)
	if !ok {
		return env, errors.New("failed to get environment from context")
	}
	return env, nil
}

// UnaryServerInterceptor returns a new unary server interceptor that adds Env to the context.
func UnaryServerInterceptor(env Env) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		nctx := env.WithContext(ctx)

		// Call the handler
		h, err := handler(nctx, req)
		return h, err
	}
}
