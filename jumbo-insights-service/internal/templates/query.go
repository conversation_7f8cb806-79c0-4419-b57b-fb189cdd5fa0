package templates

import (
	"context"
	"errors"
	"fmt"
	"os"
	"strings"

	"github.com/Zomato/go/config"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/env"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/serde"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
	"gopkg.in/yaml.v2"

	log "github.com/Zomato/go/logger"
)

var errNoQueryContract error = errors.New("query contract not found")

const YamlItem string = "yaml"
const DefaultQueryType = "dsl"

// This will the query contract
func GetQueryContract(ctx context.Context, contractId string, contractVersion string) (*models.Query, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "query/GetQueryContract", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	contractType := config.GetString(ctx, "contract.contract_type")
	if contractType == "yaml" {
		ev, err := env.FromContext(ctx)
		if err != nil {
			return nil, err
		}

		queryContract, ok := ev.QueryContracts()[contractId]
		if !ok {
			log.FromContext(ctx).WithError(errNoQueryContract).WithFields(map[string]interface{}{
				"event_name": "query_contract",
				"table_name": contractId,
			}).Error("Got error while reading the query contract")
			return nil, errNoQueryContract
		}

		return queryContract, nil
	}
	templateData, err := GetTemplateFromId(ctx, contractId, contractVersion)
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name":  "query_contract",
			"contract_id": contractId,
		}).Error("Got error while reading the query contract")
		return nil, err
	}
	queryContract := TemplateToQueryModel(templateData)

	return queryContract, nil
}
func TemplateToQueryModel(templateData *models.DynamoTemplate) *models.Query {
	queryContract := &models.Query{}
	queryContract.QueryBackend = strings.ToLower(templateData.TableConfig.Backend)
	queryContract.Identifier = templateData.TemplateId
	queryContract.Tenant = strings.ToLower(templateData.Tenant)
	queryContract.Catalog = templateData.TableConfig.Catalog
	queryContract.Schema = templateData.TableConfig.Schema
	queryContract.Table = templateData.TableConfig.Table
	queryContract.CachingTTL = int64(templateData.Performance.CachingTtl)
	queryContract.RefreshInterval = int64(templateData.Performance.RefreshInterval)
	queryContract.SLA = int64(templateData.Performance.Sla)
	queryContract.RateLimit = int64(templateData.Performance.RateLimit)
	queryContract.SQL = templateData.Query.Sql
	queryContract.ContractType = templateData.Query.Type
	queryContract.Filters = templateData.Query.Filters
	queryContract.Decryption = templateData.Decryption
	queryContract.Audit = templateData.Audit
	queryContract.ContractVersion = templateData.TemplateVersion
	queryContract.Aggregations = templateData.Aggregations
	queryContract.Columns = templateData.Columns
	return queryContract
}
func QueryToTemplateModel(queryContract *models.Query) *models.DynamoTemplate {
	templateData := &models.DynamoTemplate{}
	templateData.TemplateId = queryContract.Identifier
	templateData.TemplateVersion = "1"
	templateData.Tenant = strings.ToUpper(queryContract.Tenant)
	templateData.Audit = queryContract.Audit
	templateData.Decryption = queryContract.Decryption
	templateData.TableConfig = models.TableConfig{
		Backend: strings.ToUpper(queryContract.QueryBackend),
		Catalog: queryContract.Catalog,
		Schema:  queryContract.Schema,
		Table:   queryContract.Table,
	}
	templateData.Query = models.QueryConfig{
		Type:    queryContract.ContractType,
		Sql:     queryContract.SQL,
		Filters: queryContract.Filters,
	}
	templateData.Performance = models.PerformanceConfig{
		RefreshInterval: int32(queryContract.RefreshInterval),
		RateLimit:       int32(queryContract.RateLimit),
		Sla:             int32(queryContract.SLA),
		CachingTtl:      int32(queryContract.CachingTTL),
	}
	templateData.Aggregations = queryContract.Aggregations
	templateData.Columns = queryContract.Columns
	templateData.ItemType = YamlItem
	if templateData.Query.Type == "" {
		templateData.Query.Type = DefaultQueryType
	}
	return templateData

}

func YamlToDynamoRegister(ctx context.Context) error {
	contractPath := config.GetString(ctx, "contract.query_contract")
	filesMap, err := serde.GetConfigFileInfo(contractPath)
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name": "get_query_contracts",
		}).Error("Got error while reading the query contracts")
		return err
	}

	var errorsEncountered []error

	for filePath, file := range filesMap {
		var queryContract *models.Query
		data, err := os.ReadFile(filePath)
		if err != nil {
			log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
				"event_name":          "get_query_contract",
				"query_contract_file": file.Name(),
			}).Error("Got error while reading the query contract")
			errorsEncountered = append(errorsEncountered, fmt.Errorf("read error for %s: %w", filePath, err))
			continue
		}
		err = yaml.Unmarshal([]byte(data), &queryContract)
		if err != nil {
			log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
				"event_name": "query_contract_yaml_unmarshal",
				"file_name":  file.Name(),
			}).Error("Got error while marshalling the query contract")
			errorsEncountered = append(errorsEncountered, fmt.Errorf("unmarshal error for %s: %w", filePath, err))
			continue
		}
		log.FromContext(ctx).Info("Read contract: ", queryContract.Identifier)
		template := QueryToTemplateModel(queryContract)
		err = RegisterTemplate(ctx, template)
		if err != nil {
			log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
				"event_name": "putting_template_in_dynamodb",
				"file_name":  file.Name(),
			}).Error("Got error while registering the template")
			errorsEncountered = append(errorsEncountered, fmt.Errorf("register error for %s: %w", filePath, err))
		}
	}
	if len(errorsEncountered) > 0 {
		var sb strings.Builder
		sb.WriteString("Template processing encountered the following errors:\n")
		for i, err := range errorsEncountered {
			sb.WriteString(fmt.Sprintf("  %d. %v\n", i+1, err))
		}
		return errors.New(sb.String())
	}
	return nil
}
