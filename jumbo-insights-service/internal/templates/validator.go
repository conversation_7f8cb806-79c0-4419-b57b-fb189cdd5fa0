package templates

import (
	"errors"
	"reflect"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/jumbo-insights-service/internal/models"
)

func areSlicesEqual(a, b interface{}) bool {
	return reflect.DeepEqual(a, b)
}

var (
	errRequiredFieldsMissing  error = errors.New("required fields are not provided in template request")
	errLengthOfFieldsExceeded error = errors.New("length of fields exceeds the limit")
	errInvalidrequest         error = errors.New("there is no change in current version and given template")
)

func validateTemplateRequest(data *models.DynamoTemplate) error {
	// return if compulsory fields are not provided
	err := checkRequiredFields(data)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "field-missing",
			"template":   data,
		}).Error("required fields are missing")
		return errRequiredFieldsMissing
	}

	// check length of fields
	err = checkLengthOfFields(data)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "field length validation",
			"template":   data,
		}).Error("length of fields exceeds the limit")
		return errLengthOfFieldsExceeded
	}
	return nil
}
func checkRequiredFields(data *models.DynamoTemplate) error {
	if data.TemplateId == "" {
		return errRequiredFieldsMissing
	}
	return nil
}

func checkLengthOfFields(data *models.DynamoTemplate) error {
	if len(data.TemplateId) > 36 || len(data.Audit.Description) > 512 || len(data.TemplateVersion) > 36 {
		return errLengthOfFieldsExceeded
	}
	return nil
}
func validateTemplateWithCurrent(template *models.DynamoTemplate, currentTemplate *models.DynamoTemplate) error {
	if (template.Audit == currentTemplate.Audit) && areSlicesEqual(template.Decryption, currentTemplate.Decryption) && areSlicesEqual(template.Query, currentTemplate.Query) &&
		(template.Tenant == currentTemplate.Tenant) && (template.Performance == currentTemplate.Performance) && (template.TableConfig == currentTemplate.TableConfig) && (template.TemplateStatus == currentTemplate.TemplateStatus) && areSlicesEqual(template.Columns, currentTemplate.Columns) && areSlicesEqual(template.Aggregations, currentTemplate.Aggregations) {
		log.WithError(errInvalidrequest).WithFields(map[string]interface{}{
			"event_name":     "validate_template_with_current",
			"template":       template,
			"given_template": currentTemplate,
		}).Error("there is no change in current version and the given template")
		return errInvalidrequest
	}
	return nil

}
