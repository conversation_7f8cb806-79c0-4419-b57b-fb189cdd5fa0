package templates

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/env"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/pkg/dynamodb"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

func isTemplateIdUnique(ctx context.Context, templateId string, version string) (bool, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/isTemplateIdUnique", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	_, err := GetLatestTemplate(ctx, templateId, version)
	if err == ErrTemplateNotFound {
		return true, nil
	} else if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "get_latest_template",
			"templateId": templateId,
		}).Error("failed to get the latest template")
		return false, err
	}
	return false, nil
}
func getNewVersion(ctx context.Context, templateId string, status string) (string, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/getNewVersion", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	version := strings.ToLower(status)
	latestTemplate, err := GetLatestTemplate(ctx, templateId, version)
	if err != nil {
		return "", err
	}
	currentLatestVersionStr := latestTemplate.TemplateVersion

	currentLatestVersion, err := strconv.Atoi(currentLatestVersionStr)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name":       "version_from_string_to_int",
			"template_version": currentLatestVersionStr,
		}).Error("got following error while converting string to int")
		return "", err
	}
	newVersionStr := strconv.Itoa(currentLatestVersion + 1)
	return newVersionStr, nil
}
func putItem(ctx context.Context, templateDetails interface{}) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/putItem", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return err
	}
	err = env.DynamoDBClient().PutItemTemplate(ctx, templateDetails)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "put_item_template",
			"template":   templateDetails,
		}).Error("got following error while putting template in dynamodb")
		return err
	}
	return nil
}
func getListTemplate(ctx context.Context) ([]dynamodb.ResponseParams, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/getListTemplate", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return nil, err
	}
	templateList, err := env.DynamoDBClient().GetUniqueTemplateList(ctx)
	return templateList, err
}
func getListTemplateById(ctx context.Context, templateId string) ([]*models.DynamoTemplate, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/getListTemplateById", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return nil, err
	}
	templates, err := env.DynamoDBClient().GetListTemplateFromDb(ctx, templateId)
	if err != nil {
		return nil, err
	}
	if len(templates) != 0 {
		return templates[1:], nil
	}
	return templates, nil
}

func UpdateTemplateItem(ctx context.Context, pk string, sk string, updateValues *models.UpdateTemplateStatusInput) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/UpdateTemplateItem", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return err
	}

	var updateValuesMap map[string]interface{}
	data, err := json.Marshal(updateValues)
	if err != nil {
		log.WithError(err).Error("unable to marshal input struct")
		return err
	}
	err = json.Unmarshal(data, &updateValuesMap)
	if err != nil {
		log.WithError(err).Error("unable to unmarshal input struct")
		return err
	}

	key := dynamodb.Keys{
		PartitionKey: pk,
		SortKey:      sk,
	}
	inputRequest := &dynamodb.UpdateItemParams{
		ValuesToBeUpdated: updateValuesMap,
		ValuesToBeRemoved: []string{},
		KeyCondition:      key,
	}
	return env.DynamoDBClient().UpdateItem(ctx, inputRequest, nil)
}
