package templates

import (
	"context"
	"errors"
	"fmt"

	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/env"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/serde"
	"github.com/Zomato/jumbo-insights-service/pkg/dynamodb"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

var ErrNotExist error = errors.New("template with this templateId not exist")
var ErrTemplateIdNotNull error = errors.New("templateid should not be empty")

func GetTemplateFromId(ctx context.Context, templateId string, templateVersion string) (*models.DynamoTemplate, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/GetTemplateFromId", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	if templateId == "" {
		log.WithError(ErrTemplateIdNotNull).WithFields(map[string]interface{}{
			"event_name": "GetTemplateFromId/null_templateid",
		}).Error("templateid should not be empty")
		return nil, ErrTemplateIdNotNull
	}
	pk := templateId
	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return nil, err
	}
	template_data := &models.DynamoTemplate{}
	if templateVersion == "" {
		template_data, err = GetLatestTemplate(ctx, templateId, config.GetString(ctx, "service.env"))
		if err != nil {
			return nil, err
		}
		return template_data, nil
	}

	key := fmt.Sprintf("%s:%s", templateId, templateVersion)
	template_data = VerifyAndGetCache(ctx, key)
	if template_data != nil && template_data.TemplateId != "" {
		return template_data, nil
	}

	sk := templateVersion
	keys := dynamodb.Keys{
		PartitionKey: pk,
		SortKey:      sk,
	}
	inputRequest := dynamodb.GetItemParams{KeyCondition: keys}
	_, err = env.DynamoDBClient().GetItem(ctx, inputRequest, &template_data)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name":       "get_template_from_id",
			"templateId":       templateId,
			"template_version": templateVersion,
		}).Error("failed to get the template for the given templateId and version")
		return nil, err
	}
	if template_data.TemplateId == "" {
		log.WithError(ErrTemplateNotFound).WithFields(map[string]interface{}{
			"event_name":      "get_template_from_id",
			"templateId":      templateId,
			"templateVersion": templateVersion,
		}).Error("this template version is not exist")
		return nil, ErrTemplateNotFound

	}
	err = SetCache(ctx, key, template_data)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name":       "set_template_cache",
			"templateId":       templateId,
			"template_version": template_data.TemplateVersion,
		}).Error("Got following error while setting cache for the template")

	}
	return template_data, nil

}

// func GetLatestTemplate(ctx context.Context, templateId string) (*models.DynamoTemplate, error) {
// 	if ztracer.IsZTracerEnabled(ctx) {
// 		tctx, span := tracer.StartSpan(ctx, "templates/GetLatestTemplate", tracer.WithSpanKind(tracer.SpanKindInternal))
// 		ctx = tctx
// 		defer span.End()
// 	}
// 	template_data := &models.DynamoTemplate{}
// 	template_data, err := GetLatestTemplateByEnv(ctx, templateId, "prod_latest")
// 	if err != nil {
// 		return nil, err
// 	}
// 	if template_data.TemplateId != "" {
// 		return template_data, nil
// 	}
// 	template_data, err = GetLatestTemplateByEnv(ctx, templateId, "preprod_latest")
// 	if err != nil {
// 		return nil, err
// 	}
// 	return template_data, nil
// }

func GetLatestTemplate(ctx context.Context, templateId string, serviceEnv string) (*models.DynamoTemplate, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/GetLatestTemplateByEnv", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	env, err := env.FromContext(ctx)
	envlatestVersion := fmt.Sprintf("%s_%s", serviceEnv, "latest")
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return nil, err
	}
	key := fmt.Sprintf("%s:%s", templateId, envlatestVersion)
	template_data := VerifyAndGetCache(ctx, key)
	if template_data != nil {
		return template_data, nil
	}
	latest_template := &models.LatestDynamoTemplate{}
	keys := dynamodb.Keys{
		PartitionKey: templateId,
		SortKey:      envlatestVersion,
	}
	inputRequest := dynamodb.GetItemParams{KeyCondition: keys}
	_, err = env.DynamoDBClient().GetItem(ctx, inputRequest, latest_template)

	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name":      "get_latest_template",
			"templateId":      templateId,
			"templateversion": envlatestVersion,
		}).Error("failed to get the latest version for this templateId")
		return nil, err
	}
	if latest_template.TemplateId == "" {
		log.WithError(ErrTemplateNotFound).WithFields(map[string]interface{}{
			"event_name": "get_template_from_id",
			"templateId": templateId,
		}).Error("latest version of this templateId not exist")
		return nil, ErrTemplateNotFound

	}
	template_data = serde.ChangeLatestDynamoToDynamo(latest_template)
	err = SetCache(ctx, key, template_data)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "set_template_cache",
			"templateId": templateId,
		}).Error("Got following error while setting cache for the template")
	}
	return template_data, nil
}
