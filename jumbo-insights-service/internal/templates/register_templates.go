package templates

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/serde"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

var ErrAlreadyExist error = errors.New("templateId is already exist")
var ErrFailedTest error = errors.New("failed to test the template")

const PreProdVer string = "preprod"
const ProdVer string = "prod"

func RegisterTemplate(ctx context.Context, templateDetails *models.DynamoTemplate) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/RegisterTemplate", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	// data validation
	err := validateTemplateRequest(templateDetails)
	if err != nil {
		return err
	}
	version := config.GetString(ctx, "service.env")
	templateDetails.TemplateStatus = strings.ToUpper(version)
	templateDetails.AddedAt = time.Now().Format("2006-01-02 15:04:05")
	if templateDetails.TemplateStatus == "PROD" || templateDetails.TemplateStatus == "PREPROD" {
		if templateDetails.ItemType == "yaml" {
			templateDetails.TemplateStatus = strings.ToUpper(PreProdVer)
			version = PreProdVer
			result, err := isTemplateIdUnique(ctx, templateDetails.TemplateId, version)
			if err != nil {
				return err
			}
			if !result {
				newVersion, err := getNewVersion(ctx, templateDetails.TemplateId, templateDetails.TemplateStatus)
				if err != nil {
					return err
				}
				templateDetails.TemplateVersion = newVersion
			}
			err = putItem(ctx, serde.ChangeDynamoToLatestDynamo(templateDetails))
			templateDetails.ItemType = "yaml"
			if err != nil {
				log.WithError(err).WithFields(map[string]interface{}{
					"event_name": "register_template_as_latest",
					"templateId": templateDetails.TemplateId,
				}).Error("failed to store the template as latest version")
				return err
			}
			latestVersion := fmt.Sprintf("%s_%s", version, "latest")
			key := fmt.Sprintf("%s:%s", templateDetails.TemplateId, latestVersion)
			err = SetCache(ctx, key, templateDetails)
			if err != nil {
				log.WithError(err).WithFields(map[string]interface{}{
					"event_name": "setting_latest_template_cache",
					"template":   templateDetails,
				}).Error("got the error while setting this latest template as cache")
				// return err
			}
			templateDetails.TemplateStatus = strings.ToUpper(ProdVer)
			version = ProdVer
		} else {
			templateDetails.TemplateStatus = strings.ToUpper(PreProdVer)
			version = PreProdVer
		}
	}
	result, err := isTemplateIdUnique(ctx, templateDetails.TemplateId, version)
	if err != nil {
		return err
	}
	if !result {
		if templateDetails.ItemType == "yaml" {
			err := UpdateSyncTemplates(ctx, templateDetails)
			return err
		} else {
			log.WithError(ErrAlreadyExist).WithFields(map[string]interface{}{
				"event_name": "already_exist",
				"templateId": templateDetails.TemplateId,
			}).Error("templateId is already exist")
			return ErrAlreadyExist
		}
	}
	// Insert the item into DynamoDB
	templateDetails.ItemType = "Template"
	err = putItem(ctx, templateDetails)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "register_template",
			"templateId": templateDetails.TemplateId,
		}).Error("failed to store the template in dynamodb")
		return err
	}
	err = putItem(ctx, serde.ChangeDynamoToLatestDynamo(templateDetails))
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "register_template_as_latest",
			"templateId": templateDetails.TemplateId,
		}).Error("failed to store the template in dynamodb as latest version")
		return err
	}
	return nil

}
func UpdateSyncTemplates(ctx context.Context, templateDetails *models.DynamoTemplate) error {
	newVersion, err := getNewVersion(ctx, templateDetails.TemplateId, templateDetails.TemplateStatus)
	if err != nil {
		return err
	}
	templateDetails.TemplateVersion = newVersion
	templateDetails.ItemType = "Template"
	err = putItem(ctx, templateDetails)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "register_template",
			"templateId": templateDetails.TemplateId,
		}).Error("failed to store the template in dynamodb")
		return err
	}
	key := fmt.Sprintf("%s:%s", templateDetails.TemplateId, templateDetails.TemplateVersion)
	err = SetCache(ctx, key, templateDetails)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "set_template_cache",
			"templateId": templateDetails.TemplateId,
		}).Error("Got following error while setting cache for the template")
	}
	err = putItem(ctx, serde.ChangeDynamoToLatestDynamo(templateDetails))
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "register_template_as_latest",
			"templateId": templateDetails.TemplateId,
		}).Error("failed to store the template in dynamodb as latest version")
		return err
	}
	latestVersion := fmt.Sprintf("%s_%s", strings.ToLower(templateDetails.TemplateStatus), "latest")
	key = fmt.Sprintf("%s:%s", templateDetails.TemplateId, latestVersion)
	err = SetCache(ctx, key, templateDetails)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "set_template_cache",
			"templateId": templateDetails.TemplateId,
		}).Error("Got following error while setting cache for the latest template")
	}

	return nil

}
func RegisterTestTemplate(ctx context.Context, templateDetails *models.DynamoTemplate) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/RegisterTestTemplate", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	err := validateTemplateRequest(templateDetails)
	if err != nil {
		return err
	}
	templateDetails.TemplateStatus = strings.ToUpper(config.GetString(ctx, "service.env"))
	templateDetails.AddedAt = time.Now().Format("2006-01-02 15:04:05")
	err = putItem(ctx, templateDetails)
	if err != nil {
		log.WithError(ErrFailedTest).WithFields(map[string]interface{}{
			"event_name": "test_template",
			"templateId": templateDetails.TemplateId,
		}).Error("failed to test the template", err)
		return ErrFailedTest
	}
	return nil
}
