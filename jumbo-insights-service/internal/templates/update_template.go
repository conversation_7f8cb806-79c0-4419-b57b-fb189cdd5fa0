package templates

import (
	"context"
	"fmt"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/serde"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

func UpdateTemplate(ctx context.Context, template *models.DynamoTemplate) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/UpdateTemplate", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	err := validateTemplateRequest(template)
	if err != nil {
		return err
	}
	current_data, err := GetTemplateFromId(ctx, template.TemplateId, template.TemplateVersion)
	if err != nil {
		return err
	}
	if current_data.TemplateId == "" {
		log.WithError(ErrTemplateNotFound).WithFields(map[string]interface{}{
			"event_name":       "get_template_from_id",
			"templateId":       template.TemplateId,
			"template_version": template.TemplateVersion,
		}).Error("this template version is not exist")
		return ErrTemplateNotFound
	}
	err = validateTemplateWithCurrent(template, current_data)
	if err != nil {
		return err
	}
	if template.TemplateStatus == "PROD" {
		template.TemplateStatus = "PREPROD"
	}
	newVersion, err := getNewVersion(ctx, template.TemplateId, template.TemplateStatus)
	if err != nil {
		return err
	}
	template.TemplateVersion = newVersion
	template.ItemType = "Template"
	latest_template := serde.ChangeDynamoToLatestDynamo(template)
	err = putItem(ctx, template)
	if err != nil {
		return err
	}
	err = putItem(ctx, latest_template)
	if err != nil {
		return err
	}
	key := fmt.Sprintf("%s:%s", latest_template.TemplateId, latest_template.LatestVersion)
	err = SetCache(ctx, key, template)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "setting_latest_template_cache",
			"template":   template,
		}).Error("got the error while setting this latest template as cache")
	}
	return nil
}
