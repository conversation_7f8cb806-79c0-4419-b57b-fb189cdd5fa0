package templates

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/serde"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

var ErrTemplateNotFound error = errors.New("this template version is not exist")
var ErrTemplateInProd error = errors.New("this template is already in prod")

func MoveToProd(ctx context.Context, templateId string, templateVersion string) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/MoveToProd", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	if templateId == "" {
		log.WithError(ErrTemplateIdNotNull).WithFields(map[string]interface{}{
			"event_name": "GetTemplateFromId/null_templateid",
		}).Error("templateid should not be empty")
		return ErrTemplateIdNotNull
	}
	template_data, err := GetTemplateFromId(ctx, templateId, templateVersion)
	if err == ErrTemplateNotFound {
		log.WithError(ErrTemplateNotFound).WithFields(map[string]interface{}{
			"event_name": "get_template_from_id",
		}).Error("this template version is not exist")
		return ErrTemplateNotFound

	} else if err != nil {
		return err
	}
	if template_data.TemplateStatus == "PROD" {
		log.WithError(ErrTemplateInProd).WithFields(map[string]interface{}{
			"event_name": "get_template_from_id",
		}).Error("this template is already in prod")
		return ErrTemplateInProd
	}
	templateVersion = template_data.TemplateVersion
	err = UpdateTemplateItem(ctx, templateId, templateVersion, &models.UpdateTemplateStatusInput{
		TemplateStatus: "PROD",
		Added_At:       time.Now().Format("2006-01-02 15:04:05"),
	})
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "update_template_status",
		}).Error("got the following error while updating the templateStatus")
		return err
	}
	template_data.TemplateStatus = "PROD"
	latest_template := serde.ChangeDynamoToLatestDynamo(template_data)
	key := fmt.Sprintf("%s:%s", templateId, templateVersion)
	err = SetCache(ctx, key, template_data)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name":       "set_template_cache",
			"templateId":       templateId,
			"template_version": templateVersion,
		}).Error("Got following error while setting cache for the template")
	}
	result, err := CheckAndSetLatestProdTemplate(ctx, latest_template)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "check_and_set_latest_template",
		}).Error("got the following error while checking and setting this template as latest")
	}
	if result {
		key := fmt.Sprintf("%s:%s", templateId, "prod_latest")
		err = SetCache(ctx, key, template_data)
		if err != nil {
			log.WithError(err).WithFields(map[string]interface{}{
				"event_name":       "set_latest_template_cache",
				"templateId":       templateId,
				"template_version": templateVersion,
			}).Error("Got following error while setting cache for the latest template")
		}

	}
	return nil

}

func CheckAndSetLatestProdTemplate(ctx context.Context, latestTemplate *models.LatestDynamoTemplate) (bool, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/CheckAndSetLatestProdTemplate", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	current_latest, err := GetLatestTemplate(ctx, latestTemplate.TemplateId, config.GetString(ctx, "service.env"))
	if err != nil && err != ErrTemplateNotFound {
		return false, err
	}
	version, err := strconv.Atoi(latestTemplate.TemplateVersion)
	if err != nil {
		log.WithError(err).WithField("TemplateVersion", current_latest.TemplateVersion).Error("failed to convert this to int")
		return false, err
	}
	if current_latest != nil {
		current_version, err := strconv.Atoi(current_latest.TemplateVersion)
		if err != nil {
			log.WithError(err).WithField("TemplateVersion", current_latest.TemplateVersion).Error("failed to convert this to int")
			return false, err
		}
		if current_version > version {
			return false, nil
		}

	}
	latestTemplate.AddedAt = time.Now().Format("2006-01-02 15:04:05")
	err = putItem(ctx, latestTemplate)
	if err != nil {
		return false, err
	}
	return true, nil

}
