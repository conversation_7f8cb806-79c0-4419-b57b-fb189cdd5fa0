package templates

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/env"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

var ErrNotFound error = errors.New("cache not found")

func GetCachedResult(ctx context.Context, key string) ([]byte, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/GetCachedResult", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return nil, err
	}
	data, err := env.Redis().HMGet(ctx, key, "template")
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "cached_result",
			"key":        key,
		}).Error("failed to get the cache result")
		tracer.NoticeError(ctx, err)
		return nil, err
	}
	return []byte(data[0].(string)), nil

}
func SetCache(ctx context.Context, key string, templateData *models.DynamoTemplate) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/SetCache", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return err
	}
	data, err := json.Marshal(templateData)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "set_marshal_template",
			"template":   templateData,
		}).Error("error in marshalling template to json")
		tracer.NoticeError(ctx, err)
		return err
	}
	valueSet := map[string]interface{}{
		"template":        data,
		"last_updated_at": time.Now().UnixNano() / int64(time.Millisecond),
	}
	_, err = env.Redis().HMSet(ctx, key, valueSet)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "set_cache",
			"key":        key,
		}).Error("error while setting the cache")
		tracer.NoticeError(ctx, err)
		return err
	}
	_, err = env.Redis().Expire(ctx, key, time.Duration(config.GetInt64(ctx, "contract.caching_ttl"))*time.Second)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "expire_cache",
			"templateId": templateData.TemplateId,
		}).Error("got the error when setting the expiry time for the cache")
		tracer.NoticeError(ctx, err)
		return err
	}
	return nil
}

func VerifyCache(ctx context.Context, key string) (bool, int64, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/VerifyCache", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return false, 0, err
	}
	val, err := env.Redis().Exists(ctx, key)
	if err != nil {
		return false, 0, err
	}
	if val != 1 {
		return false, 0, ErrNotFound
	}
	result, err := env.Redis().HMGet(ctx, key, "last_updated_at")
	if err != nil {
		return false, 0, err
	}
	val, errInt := strconv.ParseInt(result[0].(string), 10, 64)
	if errInt != nil {
		return false, 0, err
	}
	return true, val, nil
}
func VerifyAndGetCache(ctx context.Context, key string) *models.DynamoTemplate {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/VerifyAndGetCache", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	template_data := &models.DynamoTemplate{}
	cacheExist, last_updated_at, err := VerifyCache(ctx, key)
	if err == ErrNotFound {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "verify_cache",
			"key":        key,
		}).Error("cache is not exist for this key")
		return nil

	} else if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "verify_cache",
			"key":        key,
		}).Error("got the following error while verifying the cache exist or not")
		return nil
	}
	now := time.Now().UnixNano() / int64(time.Millisecond)
	if cacheExist && now-last_updated_at < config.GetInt64(ctx, "contract.caching_ttl")*1000 {
		data, err := GetCachedResult(ctx, key)
		if err != nil {
			return nil
		}
		err = json.Unmarshal(data, template_data)
		if err != nil {
			log.WithError(err).WithFields(map[string]interface{}{
				"event_name": "unmarshalling_cache_byte",
				"key":        key,
			}).Error("Got following error while unmarshalling the byte")
			return nil
		}
	}
	return template_data

}
