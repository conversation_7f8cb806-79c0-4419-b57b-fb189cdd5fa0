package templates

import (
	"context"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/pkg/dynamodb"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

func ListTemplate(ctx context.Context) ([]dynamodb.ResponseParams, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/ListTemplate", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	templates, err := getListTemplate(ctx)
	if err != nil {
		return nil, err
	}
	return templates, err
}
func ListTemplateByID(ctx context.Context, templateId string) ([]*models.DynamoTemplate, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "templates/ListTemplateByID", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	if templateId == "" {
		log.WithError(ErrTemplateIdNotNull).WithFields(map[string]interface{}{
			"event_name": "GetTemplateFromId/null_templateid",
		}).Error("templateid should not be empty")
		return nil, ErrTemplateIdNotNull
	}
	templates, err := getListTemplateById(ctx, templateId)
	if err != nil {
		return nil, err
	}
	return templates, err
}
