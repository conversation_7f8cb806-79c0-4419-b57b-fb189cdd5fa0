package util

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"time"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/cespare/xxhash/v2"
)

var (
	ErrNoResult                     error = errors.New("query return no result")
	ErrTimeSeriesFilterMissing      error = errors.New("invalid request: interval field missing")
	ErrPinotBrokerResponseException error = errors.New("pinot broker response exceptions")
	ErrInvalidQueryBackend          error = errors.New("invalid query backend in the contract")

	ZomatoTenant  string = "Zomato"
	BlinkitTenant string = "Blinkit"

	ProdEnv    string = "prod"
	PreProdEnv string = "preprod"
	DevEnv     string = "dev"

	TrinoWorker          string = "trino_worker"
	PinotIngestionWorker string = "pinot_worker"
	GRPC                 string = "grpc"

	QueryHashTimeChunkDuration time.Duration = 30 * time.Minute
)

func GetColumnValue(ctx context.Context, value interface{}, dataType string) (interface{}, string) {
	switch dataType {
	case "LONG", "INT":
		jsonNumber := value.(json.Number)
		intValue, err := jsonNumber.Int64()
		if err != nil {
			log.FromContext(ctx).Error(err)
		}
		return intValue, "INT"
	case "FLOAT", "DOUBLE":
		// Pinot returns NaN and Infinity as string
		var floatValue float64
		switch value := value.(type) {
		case string:
			switch value {
			case "NaN":
				floatValue = math.NaN()
			case "Infinity":
				floatValue = math.Inf(1)
			case "-Infinity":
				floatValue = math.Inf(-1)
			}
		case json.Number:
			castToFloat := func(value interface{}) float64 {
				jsonNumber := value.(json.Number)
				fvalue, err := jsonNumber.Float64()
				if err != nil {
					log.FromContext(ctx).Error(err)
				}
				return fvalue
			}
			floatValue = castToFloat(value)
		}
		return floatValue, "DOUBLE"
	case "BOOLEAN", "BOOL":
		var boolValue bool
		if value == true {
			boolValue = true
		} else {
			boolValue = false
		}
		return boolValue, "BOOLEAN"
	case "STRING", "VARCHAR", "TEXT":
		return value, "STRING"
	default:
		return nil, "UNKNOWN"
	}
}

func GetQueryHash(contractID, contractVersion, clientID, query string) string {
	now := time.Now()
	rounded := now.Round(QueryHashTimeChunkDuration)
	return fmt.Sprintf("%s::%s::%s::%d::%d", clientID, contractID, contractVersion, xxhash.Sum64String(query), rounded.UnixMilli())
}

func GetDecryptionConfigMap(decyrptions []models.Decryption) map[string]string {
	decryptionMap := make(map[string]string)
	for _, decyrption := range decyrptions {
		decryptionMap[decyrption.SqlColumn] = decyrption.SourceColumn
	}
	return decryptionMap
}
