package util

import (
	"time"

	log "github.com/Zomato/go/logger"
)

const (
	timeFormat = "2006-01-02 15:04:05"
	DateFormat = "02/01/2006"
)

type Date struct {
	Day   int32
	Month int32
	Year  int32
}

func GetCurrentTimeString() string {
	return GetCurrentTime().Format(timeFormat)
}

func GetCurrentTime() time.Time {
	loc, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		log.WithError(err).Error("error while loading location")
	}
	return time.Now().In(loc)
}
