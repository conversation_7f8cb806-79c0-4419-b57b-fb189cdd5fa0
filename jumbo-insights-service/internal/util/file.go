package util

import (
	"fmt"
	"regexp"

	log "github.com/Zomato/go/logger"
)

func GetLocalCSVPath(executionID, fileName string) string {
	return fmt.Sprintf(`/tmp/%s_%s.csv`, executionID, fileName)
}

func GetLocalZipPath(executionID, fileName string) string {
	return fmt.Sprintf(`/tmp/%s_%s.zip`, executionID, fileName)
}

func GetS3FilePath(clientID, contractID, contractVersion, executionID, fileName, resultType string) string {
	return fmt.Sprintf("%s/%s/%s/%s/%s.%s", clientID, contractID, contractVersion, executionID, fileName, resultType)
}

func SanitizeName(fileName string) string {
	// Remove characters not allowed in file names
	reg, err := regexp.Compile(`[^a-zA-Z0-9_]+`)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"file_name": fileName,
		}).Warn("Got error while sanitizing file name")
		return fileName
	}
	sanitized := reg.ReplaceAllString(fileName, "")

	return sanitized
}
