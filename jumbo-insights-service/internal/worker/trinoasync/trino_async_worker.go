package trinoasync

import (
	"context"
	"encoding/json"

	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/metrics"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/go/tracer/attribute"
	"github.com/Zomato/jumbo-insights-service/internal/asyncquerier"
	"github.com/Zomato/jumbo-insights-service/internal/env"
	metric "github.com/Zomato/jumbo-insights-service/internal/metrics"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/querier"
	"github.com/Zomato/jumbo-insights-service/internal/repository/queryengine"
	"github.com/Zomato/jumbo-insights-service/internal/repository/writer"
	"github.com/Zomato/jumbo-insights-service/internal/templates"
	"github.com/Zomato/jumbo-insights-service/internal/util"
	jislog "github.com/Zomato/jumbo-insights-service/pkg/log"
	"github.com/Zomato/jumbo-insights-service/pkg/sqs"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
	"github.com/hashicorp/go-multierror"
)

// Run starts the workers
func Run(ctx context.Context, cancel context.CancelFunc) {
	var wg sync.WaitGroup
	go func() { //nolint: ignore-recover
		signals := make(chan os.Signal, 1)
		signal.Notify(signals, os.Interrupt, syscall.SIGTERM)
		<-signals

		cancel()
	}()

	env, err := env.FromContext(ctx)
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return
	}
	sqsClient := env.SQSClient()

	for i := int32(0); i < int32(sqsClient.Config.WorkerPool); i++ {
		wg.Add(1)
		go runWorker(ctx, &wg) //nolint: ignore-recover
	}
	wg.Wait()
}

// runWorker starts each worker and executes jobs
func runWorker(ctx context.Context, wg *sync.WaitGroup) {
	defer wg.Done()
	var shouldStop = false
	go func() { //nolint: ignore-recover
		<-ctx.Done()
		shouldStop = true
	}()

	env, err := env.FromContext(ctx)
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return
	}

	sqsClient := env.SQSClient()

	for !shouldStop {
		sqsMessages, err := sqsClient.GetMessages(ctx, config.GetString(ctx, "queues.trino.url"))
		if err != nil {
			log.FromContext(ctx).WithError(err).Error("could not fetch job from sqs")
			tracer.NoticeError(ctx, err)
			break
		}

		for _, msg := range sqsMessages {
			processSQSPayload(ctx, msg)
		}
	}
}

// processSQSPayload process the sqs payload and executes jobs
func processSQSPayload(ctx context.Context, msg *sqs.Message) {
	if msg == nil {
		return
	}

	payload := &models.SQSPayload{}
	err := json.Unmarshal(msg.Payload, payload)
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("could not unmarshal message")
		tracer.NoticeError(ctx, err)
		return
	}

	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "trino/AsyncWorker", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		executionID := attribute.String("execution_id", payload.ExecutionID)
		contractID := attribute.String("contract_id", payload.ContractId)
		clientID := attribute.String("client_id", payload.ClientID)
		traceID := attribute.String("trace_id", payload.TraceId)
		span.SetAttributes(executionID, contractID, clientID, traceID)
		defer span.End()
	}

	ctx = jislog.GetContextWithLogger(ctx, map[jislog.MetadataKey]string{
		jislog.TraceIDKey: payload.TraceId,
	})

	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"event_name":   "query_dequeued",
		"execution_id": payload.ExecutionID,
		"contract_id":  payload.ContractId,
		"client_id":    payload.ClientID,
		"engine":       "trino",
	})

	sqs_pickup_delay := time.Now().UnixMilli() - payload.EnqueueTime
	log.WithFields(map[string]interface{}{
		"sqs_pickup_delay": sqs_pickup_delay,
	}).Info("Trino Query Dequeued from queue")

	labels := []metrics.Label{metrics.String(metric.ClientIDDimension, payload.ClientID),
		metrics.String(metric.QueryIDDimension, payload.ContractId),
		metrics.String(metric.SourceDimension, "trinoQueryWorker"),
	}
	metric.SQSPickupDelay.Record(ctx, sqs_pickup_delay, labels...)

	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return
	}
	sqsClient := env.SQSClient()
	errs := executeMessage(ctx, payload)
	if errs != nil {
		tracer.NoticeError(ctx, errs)
		return
	}

	err = sqsClient.DeleteMessage(ctx, config.GetString(ctx, "queues.trino.url"), msg.ReceiptHandle)
	if err != nil {
		log.WithError(err).Error("could not delete job from sqs")
		tracer.NoticeError(ctx, err)
	}
}

func executeMessage(ctx context.Context, data *models.SQSPayload) (errs error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "trinoWorker/ExecuteMessage", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	/*
		- Fetch dynamodb for query status
		- If query is in ENQUEUED/FAILED/(RUNNING for more than 5-10mins) state, pick it up else ignore the msg
		- Update the status in dynamodb to SUBMITTED/RUNNING
		- Execute the query
	*/

	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"execution_id": data.ExecutionID,
		"contract_id":  data.ContractId,
		"client_id":    data.ClientID,
		"engine":       "trino",
	})

	dynamoQueryState, err := asyncquerier.GetQueryState(ctx, data.ExecutionID, data.ClientID)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "get_query_state_from_dynamodb",
		}).Error("Failed to get query state from dynamodb")
		errs = multierror.Append(errs, err)
		return errs
	}

	if !shouldProcessQuery(dynamoQueryState) {
		log.WithFields(map[string]interface{}{
			"event_name":  "skipping_query",
			"query_state": dynamoQueryState.State,
		}).Warn("Skipping query execution")
		return nil
	}

	err = asyncquerier.UpdateQueryState(ctx, data.ExecutionID, data.ClientID, &asyncquerier.UpdateQueryStateInput{
		State: string(models.QueryStateRunning),
	})
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "update_query_state",
		}).Error("Failed to update query state")
		errs = multierror.Append(errs, err)
		return errs
	}

	queryConfig, err := templates.GetQueryContract(ctx, data.ContractId, data.ContractVersion)
	if err != nil {
		errs = multierror.Append(errs, err)
		return errs
	}

	writerClient, err := writer.GetClient(ctx, &writer.Config{
		ClientID:            dynamoQueryState.ClientID,
		ContractID:          dynamoQueryState.ContractId,
		ExecutionID:         dynamoQueryState.ExecutionID,
		FileName:            dynamoQueryState.FileName,
		ResultType:          dynamoQueryState.ResultType,
		DecryptionConfigMap: util.GetDecryptionConfigMap(queryConfig.Decryption),
	})
	if err != nil {
		errs = multierror.Append(errs, err)
		err = asyncquerier.UpdateQueryState(ctx, data.ExecutionID, data.ClientID, &asyncquerier.UpdateQueryStateInput{
			State:          string(models.QueryStateFailed),
			CompletionTime: int(time.Now().UnixMilli()),
		})
		if err != nil {
			log.WithError(err).WithFields(map[string]interface{}{
				"event_name": "update_query_state",
			}).Error("Failed to update query state")
			errs = multierror.Append(errs, err)
		}
		return errs
	}
	defer func() {
		err = writerClient.CleanUp(ctx)
		if err != nil {
			errs = multierror.Append(errs, err)
		}
	}()

	client, err := getQueryClient(dynamoQueryState, queryConfig, writerClient)
	if err != nil {
		errs = multierror.Append(errs, err)
		err = asyncquerier.UpdateQueryState(ctx, data.ExecutionID, data.ClientID, &asyncquerier.UpdateQueryStateInput{
			State:          string(models.QueryStateFailed),
			CompletionTime: int(time.Now().UnixMilli()),
		})
		if err != nil {
			log.WithError(err).WithFields(map[string]interface{}{
				"event_name": "update_query_state",
			}).Error("Failed to update query state")
			errs = multierror.Append(errs, err)
		}
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "get_query_client",
		}).Error("Failed to get query client")
		return errs
	}

	log.Info("Executing trino query")
	queryResult, err := client.ExecuteSQL(ctx, dynamoQueryState.Query)
	switch {
	case err == util.ErrNoResult:
		// Commenting this out till merchant team changes there code for empty result handling

		// err = asyncquerier.UpdateQueryState(ctx, data.ExecutionID, data.ClientID, &asyncquerier.UpdateQueryStateInput{
		// 	State:          string(models.QueryStateFinished),
		// 	CompletionTime: int(time.Now().UnixMilli()),
		// })
		// if err != nil {
		// 	log.WithError(err).WithFields(map[string]interface{}{
		// 		"event_name": "update_query_state",
		// 	}).Error("Failed to update query state")
		// 	errs = multierror.Append(errs, err)
		// 	return errs
		// }
		log.Info("Query execution completed, but got no result")
		// return nil
	case err != nil:
		errs = multierror.Append(errs, err)
		err = asyncquerier.UpdateQueryState(ctx, data.ExecutionID, data.ClientID, &asyncquerier.UpdateQueryStateInput{
			State:          string(models.QueryStateFailed),
			CompletionTime: int(time.Now().UnixMilli()),
			Error:          err.Error(),
		})
		if err != nil {
			log.WithError(err).WithFields(map[string]interface{}{
				"event_name": "update_query_state",
			}).Error("Failed to update query state")
			errs = multierror.Append(errs, err)
		}
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "query_execute_error",
		}).Error("Failed to execute query")
		return errs
	}

	labels := []metrics.Label{metrics.String(metric.ClientIDDimension, data.ClientID),
		metrics.String(metric.QueryIDDimension, data.ContractId),
		metrics.String(metric.TableNameDimension, queryConfig.Table),
		metrics.String(metric.SourceDimension, "trinoQueryWorker"),
	}
	metric.QueryTotalRows.Record(ctx, int64(queryResult.RowCount), labels...)
	metric.QueryRunTime.Record(ctx, int64(queryResult.RunTimeMS), labels...)
	metric.QueryProcessingTime.Record(ctx, int64(queryResult.ProcessingTimeMS), labels...)

	env, err := env.FromContext(ctx)
	if err != nil {
		errs = multierror.Append(errs, err)
		return errs
	}

	s3Key := util.GetS3FilePath(dynamoQueryState.ClientID,
		dynamoQueryState.ContractId,
		dynamoQueryState.ContractVersion,
		dynamoQueryState.ExecutionID,
		dynamoQueryState.FileName,
		dynamoQueryState.ResultType)
	file, err := os.Open(queryResult.FilePath)
	if err != nil {
		log.WithError(err).Error("Not able to open the file")
		errs = multierror.Append(errs, err)
		return errs
	}
	defer file.Close()
	err = env.S3().UploadToS3(ctx, config.GetString(ctx, "s3.bucket"), s3Key, file)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "upload_to_s3",
		}).Error("Failed to upload file to s3")
		errs = multierror.Append(errs, err)
		return errs
	}

	err = asyncquerier.UpdateQueryState(ctx, data.ExecutionID, data.ClientID, &asyncquerier.UpdateQueryStateInput{
		State:          string(models.QueryStateFinished),
		CompletionTime: int(time.Now().UnixMilli()),
		S3Key:          s3Key,
	})
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "update_query_state",
		}).Error("Failed to update query state")
		errs = multierror.Append(errs, err)
		return errs
	}
	log.Info("Query execution completed")
	return nil
}

func shouldProcessQuery(dynamoQueryState *models.DynamoQueryState) bool {
	switch dynamoQueryState.State {
	case string(models.QueryStateEnqueued), string(models.QueryStateSubmitted):
		return true
	default:
		return false
	}
}

func getQueryClient(dynamoQueryState *models.DynamoQueryState, queryConfig *models.Query, writerClient writer.Client) (queryengine.Client, error) {
	clientConfig := models.ClientConfig{
		ClientId:     dynamoQueryState.ClientID,
		ContractId:   dynamoQueryState.ContractId,
		Tenant:       queryConfig.Tenant,
		QueryBackend: queryConfig.QueryBackend,
		TableName:    queryConfig.Table,
		SchemaName:   queryConfig.Schema,
		Catalog:      queryConfig.Catalog,
		User:         queryConfig.Audit.TeamEmail,
		TeamEmail:    queryConfig.Audit.TeamEmail,
		FileName:     dynamoQueryState.FileName,
		ResultType:   models.ResultType(dynamoQueryState.ResultType),
		ExecutionID:  dynamoQueryState.ExecutionID,
	}

	client, err := querier.GetQueryClient(&clientConfig, writerClient)
	if err != nil {
		return nil, err
	}

	return client, nil
}
