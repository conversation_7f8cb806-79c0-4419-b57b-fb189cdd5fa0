package querier

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/env"
	"github.com/Zomato/jumbo-insights-service/internal/util"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

var (
	ErrKeyNotFound  error = errors.New("key doesn't exist")
	ErrPageNotFound error = errors.New("requested page doesn't exist")
)

// query_identifier_{hash} : {1:result, 2:result, "chunks": chunk_size, "last_updated": updated_at}

func CheckIfCacheExist(ctx context.Context, queryIdentifier string, queryHash uint64) (bool, int64, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "query/CheckIfCacheExist", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	ev, err := env.FromContext(ctx)
	if err != nil {
		return false, 0, err
	}

	if queryHash == 0 {
		return false, 0, nil
	}

	key := fmt.Sprintf("%s:%d", queryIdentifier, queryHash)
	// if !config.GetBool(ctx, "redis.enabled") {
	// 	log.FromContext(ctx).WithFields(map[string]interface{}{
	// 		"event_name":       "set_result_cache",
	// 		"query_identifier": queryIdentifier,
	// 		"key":              key,
	// 	}).Info("Redis is disabled, skipping checking if cache exists or not")
	// 	return false, 0, nil
	// }

	noOfKeys, err := ev.Redis().Exists(ctx, key)
	if err != nil {
		return false, 0, err
	}

	if noOfKeys != 1 {
		return false, 0, ErrKeyNotFound
	}
	vals, err := ev.Redis().HMGet(ctx, key, "last_updated")
	if err != nil {
		return false, 0, err
	}
	val, errInt := strconv.ParseInt(vals[0].(string), 10, 64)
	if errInt != nil {
		return false, 0, errInt
	}
	return true, val, ev.Redis().Nil
}

func GetCachedResult(ctx context.Context, queryIdentifier string, queryHash uint64, pageToken int32) ([]byte, int32, bool, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "query/GetCachedResult", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	ev, err := env.FromContext(ctx)
	if err != nil {
		return nil, 0, false, err
	}
	key := fmt.Sprintf("%s:%d", queryIdentifier, queryHash)
	vals, err := ev.Redis().HMGet(ctx, key, fmt.Sprintf("chunk_%d", pageToken))
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name":       "redis_hmget",
			"query_identifier": queryIdentifier,
			"key":              key,
			"page_token":       pageToken,
		}).Error("Got error while retrieving the page value")
		return nil, 0, false, err
	}
	nextPage, state, err := GetNextPageStatus(ctx, queryIdentifier, queryHash, pageToken)
	if err != nil {
		if err != util.ErrNoResult {
			log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
				"event_name":       "next_page_status",
				"query_identifier": queryIdentifier,
				"key":              key,
				"page_token":       pageToken,
			}).Error("Got error while checking the next page status")
		}
		return nil, 0, false, err
	}

	return []byte(vals[0].(string)), nextPage, state, err
}

func GetNextPageStatus(ctx context.Context, queryIdentifier string, queryHash uint64, pageToken int32) (int32, bool, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "query/GetNextPageStatus", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	ev, err := env.FromContext(ctx)
	if err != nil {
		return 0, false, err
	}
	key := fmt.Sprintf("%s:%d", queryIdentifier, queryHash)
	vals, err := ev.Redis().HMGet(ctx, key, "total_chunks")
	if err != nil {
		return 0, false, err
	}
	val, errInt := strconv.ParseInt(vals[0].(string), 10, 64)
	if errInt != nil {
		return 0, false, errInt
	}
	totalPages := int32(val)
	err = nil
	if totalPages == 0 {
		return 0, false, util.ErrNoResult
	}
	if pageToken > totalPages-1 {
		return 0, false, ErrPageNotFound
	}
	if pageToken < totalPages-1 {
		return pageToken + 1, true, err
	}
	return 0, true, err
}

func SetResultCache(ctx context.Context, queryIdentifier string, queryHash uint64, valueSet map[string]interface{}, cachingTTL int64) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "query/SetResultCache", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	ev, err := env.FromContext(ctx)
	if err != nil {
		return err
	}
	key := fmt.Sprintf("%s:%d", queryIdentifier, queryHash)

	// if !config.GetBool(ctx, "redis.enabled") {
	// 	log.FromContext(ctx).WithFields(map[string]interface{}{
	// 		"event_name":       "set_result_cache",
	// 		"query_identifier": queryIdentifier,
	// 		"key":              key,
	// 	}).Info("Redis is disabled, skipping setting result in cache")
	// 	return nil
	// }

	_, err = ev.Redis().HMSet(ctx, key, valueSet)
	if err != nil {
		return err
	}

	_, err = ev.Redis().Expire(ctx, key, time.Duration(cachingTTL)*time.Second)

	return err
}
