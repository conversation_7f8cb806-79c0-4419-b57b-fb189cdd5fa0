package querier

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"text/template"

	"github.com/Zomato/jumbo-insights-service/internal/env"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/util"
	"github.com/Zomato/jumbo-insights-service/pkg/ratelimiter"

	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/repository/queryengine"
	"github.com/Zomato/jumbo-insights-service/internal/repository/writer"
	"github.com/Zomato/jumbo-insights-service/internal/templates"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

var (
	errMaxLimitThreshold error  = errors.New("number of rows requested more than the max limit")
	PinotBackend         string = "pinot"
	TrinoBackend         string = "trino"
	SQLContract          string = "sql"
	OpenSQLContract      string = "open_sql"
	ClickhouseBackend    string = "clickhouse"
)

func GetQueryMeta(ctx context.Context, contractId string, contractVersion string, querier *models.Querier, pagination *models.PaginationOptions) (*models.Query, string, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "querier/GetQueryMeta", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	queryConfig, err := templates.GetQueryContract(ctx, contractId, contractVersion)
	var sql string

	if queryConfig == nil {
		return queryConfig, sql, err
	}

	err = validateQueryFilters(ctx, queryConfig, querier.Filters, contractId)
	if err != nil {
		return queryConfig, sql, err
	}
	err = validateTableNames(ctx, queryConfig, querier.TableNames, contractId)
	if err != nil {
		return queryConfig, sql, err
	}

	sql, err = getQuery(ctx, queryConfig, querier, pagination)
	return queryConfig, sql, err
}

func getQuery(ctx context.Context, queryConfig *models.Query, querier *models.Querier, pagination *models.PaginationOptions) (string, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "querier/getQuerySQL", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	var sql string
	var err error
	switch queryConfig.ContractType {
	case SQLContract:
		sql, err = getQueryFromSQLTemplate(ctx, queryConfig, querier, pagination)
	case OpenSQLContract:
		sql, err = getQueryFromOpenSQLTemplate(ctx, queryConfig, querier, pagination)
	default:
		sql, err = getQueryFromDSL(ctx, queryConfig, querier, pagination)
	}
	log.FromContext(ctx).WithFields(map[string]interface{}{
		"event_name":    "get_query",
		"contract_id":   queryConfig.Identifier,
		"query_backend": queryConfig.QueryBackend,
		"contract_type": queryConfig.ContractType,
	}).Info(sql)
	return sql, err
}

func getQueryFromSQLTemplate(ctx context.Context, queryConfig *models.Query, querier *models.Querier, pagination *models.PaginationOptions) (string, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "querier/getQueryFromSQLTemplate", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	var tableName string
	if queryConfig.QueryBackend == ClickhouseBackend {
		prefix := fmt.Sprintf("clickhouse.%s.", strings.ToLower(queryConfig.Tenant))
		tableName = fmt.Sprintf("%s.%s", config.GetString(ctx, prefix+"auth.database"), queryConfig.Table)

	} else if queryConfig.Catalog == "" || queryConfig.Schema == "" {
		tableName = queryConfig.Table
	} else {
		tableName = fmt.Sprintf("%s.%s.%s", queryConfig.Catalog, queryConfig.Schema, queryConfig.Table)
	}
	parameters := map[string]string{
		"table": tableName,
	}
	for _, filter := range querier.Filters {
		parameters[filter.Name] = getValueExpression(filter.Value.Type, filter.Value.Value)
	}

	for _, tableName := range querier.TableNames {
		parameters[tableName.Name] = tableName.Value
	}
	sqlBuffer := &bytes.Buffer{}
	t := template.Must(template.New("").Parse(queryConfig.SQL))
	err := t.Execute(sqlBuffer, parameters)
	if err != nil {
		return "", err
	}

	paginationSQLBuilder, err := getPaginationFilters(ctx, pagination)
	if err != nil {
		return "", err
	}

	sql := fmt.Sprintf("%s %s", sqlBuffer.String(), strings.Join(paginationSQLBuilder, " "))
	return sql, err
}

/*
 * GetQueryFromOpenSQLTemplate generates a SQL query from an open SQL template.
 * Open SQL queries are those where statements are open-ended and can be provided by the user.
 * An example of such a query is: SELECT {{.select_columns}} FROM {{.table}} WHERE {{.where_conditions}}
 *
 * Note: To include strings within the query, enclose the string in double quotes.
 * For example - "week = 40 AND runnr_city_name = ''Pune''"
 */
func getQueryFromOpenSQLTemplate(ctx context.Context, queryConfig *models.Query, querier *models.Querier, pagination *models.PaginationOptions) (string, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "querier/getQueryFromOpenSQLTemplate", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	var tableName string
	sqlTemplate := queryConfig.SQL

	if queryConfig.Catalog == "" || queryConfig.Schema == "" {
		tableName = queryConfig.Table
	} else {
		tableName = fmt.Sprintf("%s.%s.%s", queryConfig.Catalog, queryConfig.Schema, queryConfig.Table)
	}

	sqlTemplate = strings.Replace(sqlTemplate, "{{.table}}", tableName, 1)

	for _, filter := range querier.Filters {
		if filter.Value.Type != "STRING" {
			return "", fmt.Errorf("only string values are allowed for open sql queries")
		}

		placeholder := fmt.Sprintf("{{.%s}}", filter.Name)
		expressionValue, ok := filter.Value.Value.(string)
		if !ok {
			return "", fmt.Errorf("value is not a string")
		}

		if len(expressionValue) > 1 && expressionValue[0] == '\'' && expressionValue[len(expressionValue)-1] == '\'' {
			expressionValue = expressionValue[1 : len(expressionValue)-1]
		} else {
			return "", fmt.Errorf("value is not a valid string")
		}

		sqlTemplate = strings.Replace(sqlTemplate, placeholder, expressionValue, -1)
	}

	// Replace all four single quotes with a single quote to parse strings & prevent SQL injection
	sqlTemplate = strings.Replace(sqlTemplate, "''''", "'", -1)
	paginationSQLBuilder, err := getPaginationFilters(ctx, pagination)
	if err != nil {
		return "", err
	}

	sqlTemplate = fmt.Sprintf("%s %s", sqlTemplate, strings.Join(paginationSQLBuilder, " "))
	return sqlTemplate, nil
}

func getQueryFromDSL(ctx context.Context, queryConfig *models.Query, querier *models.Querier, pagination *models.PaginationOptions) (string, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "querier/getQueryFromDSL", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	var sql string
	columns := queryConfig.Columns
	var selectBuilder, whereBuilder, groupByBuilder, sqlBuilder []string
	for _, column := range columns {
		expr := column.Name
		if column.Func != "" {
			expr = fmt.Sprintf(`%s("%s") as %s`, column.Func, column.SourceColumn, expr)
		}
		selectBuilder = append(selectBuilder, expr)
	}
	interval := querier.Interval
	if interval == nil {
		return sql, util.ErrTimeSeriesFilterMissing
	}
	whereBuilder = append(whereBuilder, fmt.Sprintf(`"%s" BETWEEN %d AND %d`, interval.DatetimeField, interval.StartTime, interval.EndTime))
	for _, filter := range querier.Filters {
		whereBuilder = append(whereBuilder, fmt.Sprintf(`"%s" %s`, filter.Name, getFilterExpression(filter.Value, filter.Op)))
	}
	var tableName string
	if queryConfig.Catalog == "" || queryConfig.Schema == "" {
		tableName = queryConfig.Table
	} else {
		tableName = fmt.Sprintf("%s.%s.%s", queryConfig.Catalog, queryConfig.Schema, queryConfig.Table)
	}
	groupByBuilder = append(groupByBuilder, queryConfig.Aggregations...)
	sqlBuilder = append(sqlBuilder, fmt.Sprintf("SELECT %s FROM %s WHERE %s GROUP BY %s",
		strings.Join(selectBuilder, ", "),
		tableName, strings.Join(whereBuilder, " AND "),
		strings.Join(groupByBuilder, ", ")))
	paginationSQLBuilder, err := getPaginationFilters(ctx, pagination)
	if err == nil {
		sqlBuilder = append(sqlBuilder, paginationSQLBuilder...)
		sql = strings.Join(sqlBuilder, " ")
	} else {
		sql = ""
	}
	return sql, err
}

func GetQueryResult(ctx context.Context, queryConfig *models.Query, query string, clientId string, queryId string, queryVersion string) (*models.ResultTable, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "querier/GetAndUpdateCache", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	allow, err := allowRequest(ctx, queryId, int(queryConfig.RateLimit))
	if !allow {
		return nil, err
	}

	clientConfig := models.ClientConfig{
		ClientId:        clientId,
		ContractId:      queryId,
		ContractVersion: queryVersion,
		Tenant:          queryConfig.Tenant,
		QueryBackend:    queryConfig.QueryBackend,
		TableName:       queryConfig.Table,
		SchemaName:      queryConfig.Schema,
		Catalog:         queryConfig.Catalog,
		User:            queryConfig.Audit.TeamEmail,
		TeamEmail:       queryConfig.Audit.TeamEmail,
	}

	writerClient, err := writer.GetClient(ctx, &writer.Config{
		ClientID:   clientId,
		ContractID: queryId,
		ResultType: string(models.ResultTypeRaw),
	})
	if err != nil {
		return nil, err
	}
	client, err := GetQueryClient(&clientConfig, writerClient)
	if err != nil {
		return nil, err
	}

	return client.ExecuteSQL(ctx, query)
}

// Allow reports whether an event may happen now.
func allowRequest(ctx context.Context, queryID string, limit int) (bool, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "querier/AllowRequest", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	// Check for rate limiting
	ev, err := env.FromContext(ctx)
	if err != nil {
		return true, err
	}
	limiter := ev.RateLimiterClient().GetOrCreateRateLimiter(ctx, queryID, limit)
	err = limiter.Limit(ctx)
	switch {
	case errors.As(err, &ratelimiter.BucketOverflowError{}):
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name":       "rate_limiting",
			"query_identifier": queryID,
			"limit":            limit,
		}).Error("Request Denied, rate limit breached")
		return false, err
	case err != nil:
		tracer.NoticeError(ctx, err)
		return true, err
	default:
		return true, nil
	}
}

func GetQueryClient(clientConfig *models.ClientConfig, writerClient writer.Client) (queryengine.Client, error) {
	var client queryengine.Client
	var err error
	switch clientConfig.QueryBackend {
	case PinotBackend:
		client, err = queryengine.NewPinotClient(clientConfig)
	case TrinoBackend:
		client, err = queryengine.NewTrinoClient(clientConfig, writerClient)
	case ClickhouseBackend:
		client, err = queryengine.NewClickhouseClient(clientConfig)
	default:
		err = util.ErrInvalidQueryBackend
	}
	return client, err
}

func getPaginationFilters(ctx context.Context, pagination *models.PaginationOptions) ([]string, error) {
	var err error
	var paginationSQLBuilder []string
	if pagination == nil {
		return paginationSQLBuilder, err
	}
	if pagination.SortKey != "" {
		paginationSQLBuilder = append(paginationSQLBuilder, fmt.Sprintf(`ORDER BY "%s" %s`, pagination.SortKey, pagination.Order))
	}
	if pagination.Limit != 0 {
		paginationSQLBuilder = append(paginationSQLBuilder, fmt.Sprintf(" LIMIT %d", pagination.Limit))
	}
	if pagination.Limit > config.GetInt32(ctx, "querier.max_rows") {
		err = errMaxLimitThreshold
	}

	return paginationSQLBuilder, err
}

func getFilterOperator(operator models.ComparisonOperator) string {
	switch operator {
	case models.NotEquals:
		return "!="
	case models.GreaterThan:
		return ">"
	case models.LessThan:
		return "<"
	case models.NotIn:
		return "NOT IN"
	case models.In:
		return "IN"
	case models.Between:
		return "BETWEEN"
	default:
		return "="
	}
}

func getValueExpression(dataType string, dataValue interface{}) string {
	switch dataType {
	case "STRING":
		return fmt.Sprintf(" %s ", dataValue)
	case "INT":
		return fmt.Sprintf(" %d ", dataValue)
	case "DOUBLE":
		return fmt.Sprintf(" %f ", dataValue)
	case "BYTE":
		return fmt.Sprintf(" %s ", dataValue)
	case "ARRAY_VALUE":
		var stringValue []string
		for _, data := range dataValue.([]interface{}) {
			stringValue = append(stringValue, fmt.Sprint(data))
		}
		return fmt.Sprintf(" %s ", strings.Join(stringValue, ", "))
	default:
		return ""
	}
}

func getFilterExpression(anyValue *models.AnyValue, operator models.ComparisonOperator) string {
	filterOp := getFilterOperator(operator)
	switch anyValue.Type {
	case "STRING", "INT", "DOUBLE", "BYTE":
		return fmt.Sprintf(" %s %s ", filterOp, getValueExpression(anyValue.Type, anyValue.Value))
	case "ARRAY_VALUE":
		return fmt.Sprintf(" %s (%s) ", filterOp, strings.Trim(strings.Replace(fmt.Sprint(anyValue.Value), " ", ",", -1), "[]"))
	case "BETWEEN_VALUE":
		betweenValue := anyValue.Value.(models.BetweenValue)
		return fmt.Sprintf(" %s %s and %s", filterOp,
			getValueExpression(betweenValue.FromValue.Type, betweenValue.FromValue.Value),
			getValueExpression(betweenValue.ToValue.Type, betweenValue.ToValue.Value))
	default:
		return " 1 = 1 "
	}
}

func validateQueryFilters(ctx context.Context, queryConfig *models.Query, providedFilters []*models.Filter, queryIdentifier string) error {
	approvedFiltersMap := make(map[string]string)
	for _, approvedFilter := range queryConfig.Filters {
		approvedFiltersMap[approvedFilter] = ""
	}
	var err error
	for _, providedFilters := range providedFilters {
		if _, ok := approvedFiltersMap[providedFilters.Name]; !ok {
			err = fmt.Errorf("filter(%s) in the req is not whitelisted", providedFilters.Name)
			log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
				"event_name":       "validate_filters",
				"query_identifier": queryIdentifier,
				"filter":           providedFilters.Name,
			}).Error("Request validation failed")
			break
		}
		if providedFilters.Value == nil {
			err = fmt.Errorf("no value for the filter(%s) provided", providedFilters.Name)
			log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
				"event_name":       "nil_filter",
				"query_identifier": queryIdentifier,
				"filter":           providedFilters.Name,
			}).Error("Request validation failed")
			break
		}
	}
	return err
}

func validateTableNames(ctx context.Context, queryConfig *models.Query, providedTableNames []*models.TableName, queryIdentifier string) error {
	approvedTableNamesMap := make(map[string]string)
	for _, approvedTableName := range queryConfig.TableNames {
		approvedTableNamesMap[approvedTableName] = ""
	}
	var err error
	for _, providedTableName := range providedTableNames {
		if _, ok := approvedTableNamesMap[providedTableName.Name]; !ok {
			err = fmt.Errorf("table name(%s) in the req is not whitelisted", providedTableName.Name)
			log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
				"event_name":       "validate_table_names",
				"query_identifier": queryIdentifier,
				"table_name":       providedTableName.Name,
			}).Error("Request validation failed")
			break
		}
		// valiadate that table_name has valid table_names
		regex := regexp.MustCompile(`^[a-zA-Z_.]+$`)
		if !regex.MatchString(providedTableName.Value) {
			err = fmt.Errorf("table name(%s) is not valid", providedTableName.Value)
			log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
				"event_name":       "invalid_table_name",
				"query_identifier": queryIdentifier,
				"table_name":       providedTableName.Value,
			}).Error("Request validation failed")
			break
		}
	}
	return err
}
