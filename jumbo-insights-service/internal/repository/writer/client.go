package writer

import (
	"context"
	"errors"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/jumbo-insights-service/internal/models"
)

var (
	errInvalidWriterClient error = errors.New("invalid writer client type")
)

type Config struct {
	ClientID            string
	ContractID          string
	ExecutionID         string
	FileName            string
	ResultType          string
	DecryptionConfigMap map[string]string
}

type Client interface {
	SetSchema(ctx context.Context, schema *models.MessageSchema) error
	ProcessRow(ctx context.Context, row *models.Row) error
	Close(ctx context.Context) error
	CleanUp(ctx context.Context) error
	Compress(ctx context.Context) error
	GetResultTable() *models.ResultTable
}

func GetClient(ctx context.Context, config *Config) (Client, error) {
	var writerClient Client
	var err error
	switch models.ResultType(config.ResultType) {
	case models.ResultTypeCSV:
		writerClient, err = NewCSVClient(ctx, config)
	case models.ResultTypeZIP:
		writerClient, err = NewZIPClient(ctx, config)
	case models.ResultTypeRaw:
		writerClient, err = NewRAWClient(ctx, config)
	default:
		err = errInvalidWriterClient
	}
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name":   "get_writer",
			"client_id":    config.ClientID,
			"contract_id":  config.ContractID,
			"execution_id": config.ExecutionID,
		}).Error("Failed to initiliaze writer")
		return nil, err
	}
	return writerClient, nil
}
