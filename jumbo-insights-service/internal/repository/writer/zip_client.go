package writer

import (
	"archive/zip"
	"context"
	"fmt"
	"io"
	"os"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/util"
)

type ZIPClient struct {
	csvClient *CSVClient
	config    *Config
}

func NewZIPClient(ctx context.Context, config *Config) (*ZIPClient, error) {
	csvClient, err := NewCSVClient(ctx, config)
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name":   "initialise_csv_writer",
			"client_id":    config.ClientID,
			"contract_id":  config.ContractID,
			"execution_id": config.ExecutionID,
			"file_name":    config.FileName,
		}).Error("Failed to initiliaze csv writer client")
		return nil, err
	}
	return &ZIPClient{
		csvClient: csvClient,
		config:    config,
	}, nil
}

func (client *ZIPClient) SetSchema(ctx context.Context, schema *models.MessageSchema) error {
	return client.csvClient.SetSchema(ctx, schema)
}

func (client *ZIPClient) ProcessRow(ctx context.Context, row *models.Row) error {
	return client.csvClient.ProcessRow(ctx, row)
}

func (client *ZIPClient) Close(ctx context.Context) error {
	return client.csvClient.Close(ctx)
}

func (client *ZIPClient) CleanUp(ctx context.Context) error {
	err := client.csvClient.CleanUp(ctx)
	if err != nil {
		return err
	}
	err = os.Remove(util.GetLocalZipPath(client.config.ExecutionID, client.config.FileName))
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name":   "cleanup_query_files",
			"client_id":    client.config.ClientID,
			"contract_id":  client.config.ContractID,
			"execution_id": client.config.ExecutionID,
			"file_name":    client.config.FileName,
		}).Error("Failed to delete zip file")
		return err
	}
	return nil
}

func (client *ZIPClient) Compress(ctx context.Context) error {
	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"client_id":    client.config.ClientID,
		"contract_id":  client.config.ContractID,
		"execution_id": client.config.ExecutionID,
		"file_name":    client.config.FileName,
	})
	// Open the original CSV file
	csvFile, err := os.Open(util.GetLocalCSVPath(client.config.ExecutionID, client.config.FileName))
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "open_csv_file",
		}).Error("Failed to open csv file")
		return err
	}
	defer csvFile.Close()

	// Create a new zip file
	zipFile, err := os.Create(util.GetLocalZipPath(client.config.ExecutionID, client.config.FileName))
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "create_zip_file",
		}).Error("Failed to get zip file pointer")
		return err
	}
	defer zipFile.Close()

	// Create a new zip writer
	ZIPClient := zip.NewWriter(zipFile)

	// Create a new CSV file in the zip
	csvZIPClient, err := ZIPClient.Create(fmt.Sprintf("%s.csv", client.config.FileName))
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "create_zip_csv_file",
		}).Error("Failed to create entry for csv file in zip")
		return err
	}

	// Copy the original CSV to the zip
	written, err := io.Copy(csvZIPClient, csvFile)
	log.Info(fmt.Sprintf("Copied %d bytes", written))
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "write_zip_csv_file",
		}).Error("Failed to write csv file to zip")
		return err
	}

	err = ZIPClient.Flush()
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "flush_zip_writer",
		}).Error("Failed to flush zip writer")
		return err
	}

	return ZIPClient.Close()
}

func (client *ZIPClient) GetResultTable() *models.ResultTable {
	return &models.ResultTable{
		FilePath: util.GetLocalZipPath(client.config.ExecutionID, client.config.FileName),
	}
}
