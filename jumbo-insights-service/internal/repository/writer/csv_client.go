package writer

import (
	"context"
	"fmt"
	"os"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/env"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/util"
	"github.com/Zomato/jumbo-insights-service/pkg/crypto"
	"github.com/Zomato/jumbo-insights-service/pkg/csv"
)

type CSVClient struct {
	writer                 *csv.Writer
	config                 *Config
	cryptoClient           *crypto.Client
	decryptionIndexNameMap map[int]string
}

func NewCSVClient(ctx context.Context, config *Config) (*CSVClient, error) {
	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"client_id":    config.ClientID,
		"contract_id":  config.ContractID,
		"execution_id": config.ExecutionID,
		"file_name":    config.FileName,
	})
	csvWriter, err := csv.NewWriter(ctx, util.GetLocalCSVPath(config.ExecutionID, config.FileName))
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "initialise_csv_client",
		}).Error("Failed to initiliaze csv client")
		return nil, err
	}

	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		return nil, err
	}

	return &CSVClient{
		writer:       csvWriter,
		config:       config,
		cryptoClient: env.CryptoClient(),
	}, nil
}

func (client *CSVClient) SetSchema(ctx context.Context, schema *models.MessageSchema) error {
	err := client.writer.WriteHeader(ctx, schema.ColumnNames)
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name":   "set_schema",
			"client_id":    client.config.ClientID,
			"contract_id":  client.config.ContractID,
			"execution_id": client.config.ExecutionID,
			"file_name":    client.config.FileName,
		}).Error("Failed to write csv header")
		return err
	}
	indexToNameMap := make(map[int]string)
	for index, columnName := range schema.ColumnNames {
		if sourceColumn, ok := client.config.DecryptionConfigMap[columnName]; ok {
			indexToNameMap[index] = sourceColumn
		}
	}
	client.decryptionIndexNameMap = indexToNameMap
	return nil
}

func (client *CSVClient) ProcessRow(ctx context.Context, row *models.Row) error {
	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"event_name":   "process_row",
		"client_id":    client.config.ClientID,
		"contract_id":  client.config.ContractID,
		"execution_id": client.config.ExecutionID,
		"file_name":    client.config.FileName,
	})
	stringValues, err := client.convertToStringSlice(ctx, row.Columns)
	if err != nil {
		log.WithError(err).Error("Failed to convert to string slice")
		return err
	}
	err = client.writer.WriteRow(ctx, stringValues)
	if err != nil {
		log.WithError(err).Error("Failed to write csv row")
		return err
	}
	return nil
}

func (client *CSVClient) Close(ctx context.Context) error {
	err := client.writer.Close(ctx)
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name":   "close_csv_writer",
			"client_id":    client.config.ClientID,
			"contract_id":  client.config.ContractID,
			"execution_id": client.config.ExecutionID,
			"file_name":    client.config.FileName,
		}).Error("Failed to close csv file")
		return err
	}

	return nil
}

func (client *CSVClient) CleanUp(ctx context.Context) error {
	err := os.Remove(util.GetLocalCSVPath(client.config.ExecutionID, client.config.FileName))
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name":   "cleanup_query_files",
			"client_id":    client.config.ClientID,
			"contract_id":  client.config.ContractID,
			"execution_id": client.config.ExecutionID,
			"file_name":    client.config.FileName,
		}).Error("Failed to delete csv file")
		return err
	}
	return nil
}

func (client *CSVClient) Compress(ctx context.Context) error {
	return nil
}

func (client *CSVClient) GetResultTable() *models.ResultTable {
	return &models.ResultTable{
		FilePath: util.GetLocalCSVPath(client.config.ExecutionID, client.config.FileName),
	}
}

func (client *CSVClient) convertToStringSlice(ctx context.Context, rowValues []*models.AnyValue) ([]string, error) {
	var stringValues []string
	for index, rowValue := range rowValues {
		stringValue := fmt.Sprint(rowValue.Value)
		sourceColumn, ok := client.decryptionIndexNameMap[index]
		switch {
		case stringValue == "", stringValue == "<nil>":
			stringValues = append(stringValues, "")
		case ok:
			inputParam := &crypto.DecryptInputParam{
				CipherText:   stringValue,
				SourceColumn: sourceColumn,
			}
			decryptedValue, err := client.cryptoClient.Decrypt(ctx, inputParam)
			if err != nil {
				tracer.NoticeError(ctx, err)
				return stringValues, err
			}
			stringValues = append(stringValues, decryptedValue)
		default:
			stringValues = append(stringValues, stringValue)
		}
	}
	return stringValues, nil
}
