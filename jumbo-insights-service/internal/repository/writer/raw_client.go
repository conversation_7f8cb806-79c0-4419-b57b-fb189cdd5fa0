package writer

import (
	"context"

	"github.com/Zomato/jumbo-insights-service/internal/models"
)

type RawClient struct {
	resultTable *models.ResultTable
	config      *Config
}

func NewRAWClient(ctx context.Context, config *Config) (*RawClient, error) {
	return &RawClient{
		resultTable: &models.ResultTable{},
		config:      config,
	}, nil
}

func (client *RawClient) SetSchema(ctx context.Context, schema *models.MessageSchema) error {
	client.resultTable.Schema = schema
	return nil
}

func (client *RawClient) ProcessRow(ctx context.Context, row *models.Row) error {
	client.resultTable.Rows = append(client.resultTable.Rows, row)
	return nil
}

func (client *RawClient) Close(ctx context.Context) error {
	return nil
}

func (client *RawClient) CleanUp(ctx context.Context) error {
	client.resultTable.RowCount = int32(len(client.resultTable.Rows))
	return nil
}

func (client *RawClient) Compress(ctx context.Context) error {
	return nil
}

func (client *RawClient) GetResultTable() *models.ResultTable {
	return client.resultTable
}
