package queryengine

import (
	"context"
	"reflect"
	"strings"
	"time"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/env"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/util"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

type ClickHouseClient struct {
	ClickHouseConfig *models.ClientConfig
}

func NewClickhouseClient(ClickHouseConfig *models.ClientConfig) (*ClickHouseClient, error) {
	return &ClickHouseClient{
		ClickHouseConfig: ClickHouseConfig,
	}, nil
}

func (repository *ClickHouseClient) ExecuteSQL(ctx context.Context, query string) (*models.ResultTable, error) {
	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"contract_id": repository.ClickHouseConfig.ContractId,
	})

	log.WithFields(map[string]interface{}{
		"event_name": "executing_query",
	}).Info("Executing Query")

	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "ClickHouse/ExecuteSQL", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	env, err := env.FromContext(ctx)
	if err != nil {
		return nil, err
	}
	start := time.Now()
	clickhouseClient, err := env.ClickHouseClient(strings.ToLower(repository.ClickHouseConfig.Tenant))
	if err != nil {
		return nil, err
	}
	data, err := clickhouseClient.ExecuteSQL(ctx, repository.ClickHouseConfig.TableName, query)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "execution_sql",
		}).Error("Got error while executing query")
		return nil, err
	}
	var rows []*models.Row
	var columnSchemas []string

	for data.Next() {
		columns := data.ColumnTypes()
		if len(columnSchemas) == 0 {
			for _, colType := range columns {
				columnSchemas = append(columnSchemas, getColType(colType.ScanType().String()))
			}
		}
		values := make([]interface{}, len(columns))
		var col []*models.AnyValue

		for i, column := range columns {
			values[i] = reflect.New(column.ScanType()).Interface()

		}
		if err = data.Scan(values...); err != nil {
			log.WithError(err).WithFields(map[string]interface{}{
				"event_name": "clickhouse_scanning_row",
				"query":      query,
			}).Error("Got error scanning the row")
			return nil, err
		}
		for i, column := range columns {
			val := models.AnyValue{
				Type: getColType(column.ScanType().String()),
				Value: func() interface{} {
					column_value := reflect.ValueOf(values[i]).Elem()
					if column.Nullable() {
						if !column_value.IsNil() {
							return column_value.Elem().Interface()
						} else {
							return reflect.Zero(column_value.Type().Elem()).Interface()
						}
					} else {
						return column_value.Interface()
					}
				}(), // Store the actual value, not the pointer
			}
			col = append(col, &val)
		}
		rows = append(rows, &models.Row{Columns: col})
	}

	if err := data.Err(); err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "result_error",
		}).Error("Error while processing the query result")
		return nil, err
	}

	if err = data.Close(); err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "result_close_error",
		}).Error("Got error while closing the rows")
		return nil, err
	}

	runTimeMs := time.Since(start).Milliseconds()
	if len(rows) == 0 {
		log.WithError(util.ErrNoResult).WithFields(map[string]interface{}{
			"event_name": "zero_rows",
		}).Error("Got 0 rows from sql")
		return nil, util.ErrNoResult
	}

	columnCount := int32(len(columnSchemas))
	rowCount := int32(len(rows))
	log.WithFields(map[string]interface{}{
		"event_name":   "query_response",
		"column_count": columnCount,
		"row_count":    rowCount,
		"run_time":     runTimeMs,
	}).Info("Query response")
	return &models.ResultTable{
		Rows:        rows,
		ColumnCount: columnCount,
		RowCount:    rowCount,
		Schema: &models.MessageSchema{
			ColumnNames:   data.Columns(),
			ColumnSchemas: columnSchemas,
		},
		RunTimeMS: runTimeMs,
	}, nil
}

func getColType(dbType string) string {
	if strings.HasPrefix(dbType, "*") {
		dbType = strings.TrimPrefix(dbType, "*")
	}
	dbType = strings.ToLower(dbType)
	switch {
	case strings.HasPrefix(dbType, "uint"):
		return "UINT"
	case strings.HasPrefix(dbType, "int"):
		return "INT"
	case strings.HasPrefix(dbType, "varchar") || strings.HasPrefix(dbType, "string"):
		return "STRING"
	case strings.HasPrefix(dbType, "double") || strings.HasPrefix(dbType, "real") || strings.HasPrefix(dbType, "float"):
		return "DOUBLE"
	case strings.HasPrefix(dbType, "bool"):
		return "BOOLEAN"
	default:
		return dbType
	}
}
