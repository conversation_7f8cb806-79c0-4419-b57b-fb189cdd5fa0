package queryengine

import (
	"context"
	"strings"
	"time"

	"github.com/Zomato/go/config"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/dataplatform/jumboinsights/pinotquerylogs"
	"github.com/Zomato/jumbo-insights-service/internal/env"
	"github.com/Zomato/jumbo-insights-service/internal/jumbo"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/util"
	"github.com/Zomato/jumbo-insights-service/pkg/pinot"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
	pinotClient "github.com/startreedata/pinot-client-go/pinot"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

var JumboTableName string = "pinot_query_logs"

type PinotClient struct {
	ClientConfig *models.ClientConfig
}

type pinotJumboConfig struct {
	runTimeMs    int64
	brokerURI    string
	pinotVersion string
	environment  string
}

func NewPinotClient(clientConfig *models.ClientConfig) (*PinotClient, error) {
	return &PinotClient{
		ClientConfig: clientConfig,
	}, nil
}

func (repository *PinotClient) ExecuteSQL(ctx context.Context, query string) (*models.ResultTable, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Pinot/ExecuteSQL", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	env, err := env.FromContext(ctx)
	if err != nil {
		return nil, err
	}

	isJumboEnabled := config.GetBool(ctx, "jumbo.enabled")
	start := time.Now()

	pinotConfig, err := env.PinotConfig(strings.ToLower(repository.ClientConfig.Tenant))
	if err != nil {
		return nil, err
	}
	brokerResponse, err := pinot.ExecuteSQL(ctx, repository.ClientConfig.TableName, query, pinotConfig)

	jumboConfig := pinotJumboConfig{
		runTimeMs:    time.Since(start).Milliseconds(),
		brokerURI:    pinotConfig.Broker,
		pinotVersion: pinotConfig.Version,
		environment:  config.GetString(ctx, "service.env"),
	}

	if err != nil {
		if isJumboEnabled {
			payload := repository.getJumboPayload(brokerResponse, false, query, jumboConfig)
			jumbo.SendData(ctx, repository.ClientConfig.Tenant, JumboTableName, payload)
		}
		return nil, err
	}

	totalRows := brokerResponse.ResultTable.GetRowCount()
	totalColumns := brokerResponse.ResultTable.GetColumnCount()
	var rows []*models.Row
	for r := 0; r < totalRows; r++ {
		var col []*models.AnyValue
		for c := 0; c < totalColumns; c++ {
			colValue, colType := util.GetColumnValue(ctx, brokerResponse.ResultTable.Get(r, c), brokerResponse.ResultTable.GetColumnDataType(c))
			col = append(col, &models.AnyValue{
				Type:  colType,
				Value: colValue,
			})
		}
		rows = append(rows, &models.Row{
			Columns: col,
		})
	}
	runTimeMs := time.Since(start).Milliseconds()
	if isJumboEnabled {
		jumboConfig.runTimeMs = runTimeMs
		payload := repository.getJumboPayload(brokerResponse, true, query, jumboConfig)
		jumbo.SendData(ctx, repository.ClientConfig.Tenant, JumboTableName, payload)
	}
	return &models.ResultTable{
		Rows:        rows,
		RowCount:    int32(totalRows),
		ColumnCount: int32(totalColumns),
		Schema: &models.MessageSchema{
			ColumnSchemas: brokerResponse.ResultTable.DataSchema.ColumnDataTypes,
			ColumnNames:   brokerResponse.ResultTable.DataSchema.ColumnNames,
		},
		RunTimeMS: runTimeMs,
	}, err
}

// Return type is kept as proto.Message since jumbo.NewEvent().WithPayload() now accepts proto
// & every generated proto struct implements this interface
func (repository *PinotClient) getJumboPayload(brokerResp *pinotClient.BrokerResponse, isSuccessfulQuery bool, query string, jumboConfig pinotJumboConfig) proto.Message {
	if brokerResp == nil {
		brokerResp = &pinotClient.BrokerResponse{}
	}

	var exceptions []*pinotquerylogs.Exception
	for _, exception := range brokerResp.Exceptions {
		exceptions = append(exceptions, &pinotquerylogs.Exception{
			ErrorCode: &wrapperspb.Int32Value{Value: int32(exception.ErrorCode)},
			Message:   &wrapperspb.StringValue{Value: exception.Message},
		})
	}
	var queryState pinotquerylogs.QueryState
	if isSuccessfulQuery {
		queryState = pinotquerylogs.QueryState_SUCCESS
	} else {
		queryState = pinotquerylogs.QueryState_FAILED
	}

	var serviceEnv pinotquerylogs.Environment
	switch jumboConfig.environment {
	case util.ProdEnv:
		serviceEnv = pinotquerylogs.Environment_PROD
	case util.PreProdEnv:
		serviceEnv = pinotquerylogs.Environment_PREPROD
	case util.DevEnv:
		serviceEnv = pinotquerylogs.Environment_DEV
	default:
		serviceEnv = pinotquerylogs.Environment_ENV_UNSPECIFIED
	}

	pinotQueryLogs := &pinotquerylogs.PinotQueryLogs{
		ClientId:                    &wrapperspb.StringValue{Value: repository.ClientConfig.ClientId},
		QueryId:                     &wrapperspb.StringValue{Value: repository.ClientConfig.ContractId},
		NumServersQueried:           &wrapperspb.Int64Value{Value: int64(brokerResp.NumServersQueried)},
		NumServersResponded:         &wrapperspb.Int64Value{Value: int64(brokerResp.NumServersResponded)},
		NumSegmentsQueried:          &wrapperspb.Int64Value{Value: int64(brokerResp.NumSegmentsQueried)},
		NumSegmentsProcessed:        &wrapperspb.Int64Value{Value: int64(brokerResp.NumSegmentsProcessed)},
		NumSegmentsMatched:          &wrapperspb.Int64Value{Value: int64(brokerResp.NumSegmentsMatched)},
		NumConsumingSegmentsQueried: &wrapperspb.Int64Value{Value: int64(brokerResp.NumConsumingSegmentsQueried)},
		NumDocsScanned:              &wrapperspb.Int64Value{Value: int64(brokerResp.NumDocsScanned)},
		NumEntriesScannedInFilter:   &wrapperspb.Int64Value{Value: int64(brokerResp.NumEntriesScannedInFilter)},
		NumEntriesScannedPostFilter: &wrapperspb.Int64Value{Value: int64(brokerResp.NumEntriesScannedPostFilter)},
		NumGroupsLimitReached:       &wrapperspb.BoolValue{Value: brokerResp.NumGroupsLimitReached},
		TotalDocs:                   &wrapperspb.Int64Value{Value: int64(brokerResp.TotalDocs)},
		BrokerTimeUsedMs:            &wrapperspb.Int64Value{Value: int64(brokerResp.TimeUsedMs)},
		TimeUsedMs:                  &wrapperspb.Int64Value{Value: jumboConfig.runTimeMs},
		MinConsumingFreshnessTimeMs: &wrapperspb.Int64Value{Value: int64(brokerResp.MinConsumingFreshnessTimeMs)},
		Exceptions:                  exceptions,
		QueryState:                  queryState,
		Query:                       &wrapperspb.StringValue{Value: query},
		Environment:                 serviceEnv,
		PinotVersion:                &wrapperspb.StringValue{Value: jumboConfig.pinotVersion},
		BrokerUrl:                   &wrapperspb.StringValue{Value: jumboConfig.brokerURI},
	}

	return pinotQueryLogs
}
