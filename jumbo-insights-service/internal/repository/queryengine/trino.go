package queryengine

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/env"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/repository/writer"
	"github.com/Zomato/jumbo-insights-service/internal/util"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
	"github.com/Zomato/jumbo-insights-service/pkg/trino"
)

const (
	ResponseChannelCapacity int = 10000
)

var (
	ErrReadEntryTimeout = errors.New("read entry timeout")
)

type TrinoClient struct {
	ClientConfig *models.ClientConfig
	writerClient writer.Client
	completed    chan struct{}
}

type processorConfig struct {
	startTime           time.Time
	processingStartTime time.Time
	noOfRows            int32
	noOfColumns         int32
}

func NewTrinoClient(clientConfig *models.ClientConfig, writerClient writer.Client) (*TrinoClient, error) {
	return &TrinoClient{
		ClientConfig: clientConfig,
		writerClient: writerClient,
		completed:    make(chan struct{}),
	}, nil
}

func (repository *TrinoClient) ExecuteSQL(ctx context.Context, query string) (*models.ResultTable, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Trino/ExecuteSQL", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	replyChan := make(chan *trino.Response, ResponseChannelCapacity)
	completed := make(chan struct{})
	errorChan := make(chan error)

	env, err := env.FromContext(ctx)
	if err != nil {
		return nil, err
	}
	var wg sync.WaitGroup

	trinoConfig, err := env.TrinoConfig(strings.ToLower(repository.ClientConfig.Tenant))
	if err != nil {
		return nil, err
	}
	request := &trino.Request{
		ClientConfig: repository.ClientConfig,
		Query:        query,
		ReplyChan:    replyChan,
		ShutdownChannel: &trino.ShutdownChannel{
			Completed: completed,
			ErrorChan: errorChan,
		},
		Wg:          &wg,
		TrinoConfig: trinoConfig,
	}
	processor := &processorConfig{
		startTime:   time.Now(),
		noOfRows:    0,
		noOfColumns: 0,
	}
	go trino.ExecuteSQL(ctx, request)                                         //nolint: ignore-recover
	go repository.processResponses(ctx, processor, replyChan, errorChan, &wg) //nolint: ignore-recover
	return repository.waitForCompletion(ctx, processor, errorChan, completed, &wg)
}

func (repository *TrinoClient) processResponses(ctx context.Context, processorConfig *processorConfig, replyChan chan *trino.Response, errorChan chan error, wg *sync.WaitGroup) {
	var schema *models.MessageSchema
	for {
		select {
		case response := <-replyChan:
			processorConfig.noOfRows++
			if schema == nil {
				schema = response.Schema
				processorConfig.noOfColumns = int32(len(schema.ColumnNames))
				err := repository.writerClient.SetSchema(ctx, schema)
				if err != nil {
					errorChan <- err
					return
				}
			}
			err := repository.writerClient.ProcessRow(ctx, response.Row)
			wg.Done()
			if err != nil {
				errorChan <- err
				return
			}
		case <-ctx.Done():
			return
		case <-repository.completed:
			log.FromContext(ctx).Debug("Completed processing")
			return
		}
	}
}

func (repository *TrinoClient) waitForCompletion(ctx context.Context, processorConfig *processorConfig, errorChan chan error, completed chan struct{}, wg *sync.WaitGroup) (*models.ResultTable, error) {
	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"event_name":   "execute_sql",
		"client_id":    repository.ClientConfig.ClientId,
		"contract_id":  repository.ClientConfig.ContractId,
		"execution_id": repository.ClientConfig.ExecutionID,
	})
	for {
		select {
		case err := <-errorChan:
			log.WithError(err).Error("Query execution failed")
			repository.completed <- struct{}{}
			return nil, err
		case <-completed:
			log.Info("received a value from completed channel")
			wg.Wait()
			log.Info("wait group completed")
			repository.completed <- struct{}{}
			return repository.finalizeResult(ctx, processorConfig)
		}
	}
}

func (repository *TrinoClient) finalizeResult(ctx context.Context, processorConfig *processorConfig) (*models.ResultTable, error) {
	err := repository.writerClient.Close(ctx)
	if err != nil {
		return nil, err
	}
	err = repository.writerClient.Compress(ctx)
	if err != nil {
		return nil, err
	}
	resultTable := repository.writerClient.GetResultTable()
	processingTimeMs := time.Since(processorConfig.processingStartTime).Milliseconds()
	runTimeMs := time.Since(processorConfig.startTime).Milliseconds()
	log.FromContext(ctx).WithFields(map[string]interface{}{
		"event_name":   "execute_sql",
		"client_id":    repository.ClientConfig.ClientId,
		"contract_id":  repository.ClientConfig.ContractId,
		"execution_id": repository.ClientConfig.ExecutionID,
	}).Info(fmt.Sprintf(`No of rows: %d`, processorConfig.noOfRows))
	resultTable.RowCount = processorConfig.noOfRows
	resultTable.RunTimeMS = runTimeMs
	resultTable.ColumnCount = processorConfig.noOfColumns
	resultTable.ProcessingTimeMS = processingTimeMs

	if resultTable.RowCount == 0 {
		err = util.ErrNoResult
	}
	return resultTable, err
}
