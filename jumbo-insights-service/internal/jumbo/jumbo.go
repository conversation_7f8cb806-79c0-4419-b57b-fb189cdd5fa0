package jumbo

import (
	"context"
	"strings"

	jumbo "github.com/Zomato/go/jumbo-v2"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	jumboEvent "github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/event"
	"github.com/Zomato/jumbo-insights-service/internal/env"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
	"google.golang.org/protobuf/proto"
)

const (
	ZOMATO    = "zomato"
	BLINKIT   = "blinkit"
	HYPERPURE = "hyperpure"
)

// SendToJumbo : Sends the given payload to jumbo table for analysis
// Use this for zomato, blinkit both
func SendData(ctx context.Context, tenant string, table string, payload proto.Message) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "jumbo/SendData", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	jumboClient := getClient(ctx, tenant)
	if jumboClient == nil {
		log.FromContext(ctx).WithFields(map[string]interface{}{
			"payload": payload,
		}).Warn("jumbo client in nil")
		return
	}
	// Decide the namespace(or tenant) on the basis of the tenant parameter received
	var eventNamespace jumboEvent.Namespace
	tenantLower := strings.ToLower(tenant)
	switch tenantLower {
	case ZOMATO:
		eventNamespace = jumboEvent.Namespace_ZOMATO
	case BLINKIT:
		eventNamespace = jumboEvent.Namespace_BLINKIT
	case HYPERPURE:
		eventNamespace = jumboEvent.Namespace_HYPERPURE
	}
	// Initialising Jumbo Event
	// NOTE: In V2 it is neccessary to specify the namespace for the event, since it's required
	// for construction of the routing topic
	event := jumbo.NewEvent().WithPayload(payload).WithNamespace(eventNamespace)
	// Publishing Event on Jumbo
	err := jumboClient.SendEvent(ctx, event)
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name": "jumbo_event_failure",
			"table":      table,
			"tenant":     eventNamespace.String(),
		}).Error("error in sending event to jumbo")
		tracer.NoticeError(ctx, err)
	}
}

func getClient(ctx context.Context, tenant string) *jumbo.JumboClient {
	env, err := env.FromContext(ctx)
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("[SendEventToJumbo] unable to get env from context")
		tracer.NoticeError(ctx, err)
		return nil
	}
	return env.JumboClient()
}
