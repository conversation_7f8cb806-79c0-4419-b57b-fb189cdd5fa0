package models

type ComparisonOperator int32

const (
	NotEquals   ComparisonOperator = 1
	GreaterThan ComparisonOperator = 2
	LessThan    ComparisonOperator = 3
	In          ComparisonOperator = 4
	NotIn       ComparisonOperator = 5
	Between     ComparisonOperator = 6
)

type BetweenValue struct {
	FromValue AnyValue
	ToValue   AnyValue
}

type AnyValue struct {
	Value interface{}
	Type  string
}

type Filter struct {
	Op    ComparisonOperator
	Name  string
	Value *AnyValue
}

type Interval struct {
	StartTime     int64
	EndTime       int64
	DatetimeField string
}

type TableName struct {
	Name  string
	Value string
}

type Querier struct {
	Filters    []*Filter
	Interval   *Interval
	TableNames []*TableName
}

type PaginationOptions struct {
	Limit     int32
	SortKey   string
	Order     string
	PageToken int32
}
