package models

type ResultType string
type QueryState string

const (
	ResultTypeCSV ResultType = "csv"
	ResultTypeZIP ResultType = "zip"
	ResultTypeRaw ResultType = "raw"

	QueryStateSubmitted QueryState = "SUBMITTED"
	QueryStateEnqueued  QueryState = "ENQUEUED"
	QueryStateRunning   QueryState = "RUNNING"
	QueryStateFinished  QueryState = "FINISHED"
	QueryStateFailed    QueryState = "FAILED"
)

type AsyncQueryContext struct {
	User       string
	FileName   string
	ResultType ResultType
}

type QueryExecutionInfo struct {
	ExecutionId     string
	QueryState      QueryState
	S3PresignedLink string
	SubmissionTime  int
	CompletionTime  int
	User            string
	Error           string
}
