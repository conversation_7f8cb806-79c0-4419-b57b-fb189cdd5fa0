package models

// It is used to store the select expressions
type Column struct {
	Name         string `json:"name" yaml:"name" dynamodbav:"name"`
	Func         string `json:"func" yaml:"func" dynamodbav:"func"`
	SourceColumn string `json:"source_column" yaml:"source_column" dynamodbav:"source_column"`
}

// Audit
type Audit struct {
	Description   string `json:"description" dynamodbav:"description" yaml:"description"`
	AuthorEmail   string `json:"author_email" dynamodbav:"author_email" yaml:"author_email"`
	TeamEmail     string `json:"team_email" dynamodbav:"team_email" yaml:"team_email"`
	Service       string `json:"service" dynamodbav:"service" yaml:"service"`
	PdServiceName string `json:"pd_service_name" dynamodbav:"pd_service_name" yaml:"pd_service_name"`
}

// It is used to store the query contract
type Query struct {
	QueryBackend     string       `json:"query_backend" yaml:"query_backend"`
	Tenant           string       `json:"tenant" yaml:"tenant"`
	Catalog          string       `json:"catalog" yaml:"catalog"`
	Schema           string       `json:"schema" yaml:"schema"`
	Table            string       `json:"table" yaml:"table"`
	Identifier       string       `json:"identifier" yaml:"identifier"`
	ContractVersion  string       `json:"contract_version" yaml:"contract_version"`
	Audit            Audit        `json:"audit" yaml:"audit"`
	CachingTTL       int64        `json:"caching_ttl" yaml:"caching_ttl"`
	RefreshInterval  int64        `json:"refresh_interval" yaml:"refresh_interval"`
	SLA              int64        `json:"sla" yaml:"sla"`
	RateLimit        int64        `json:"rate_limit" yaml:"rate_limit"`
	Columns          []Column     `json:"columns" yaml:"columns"`
	Aggregations     []string     `json:"aggregations" yaml:"aggregations"`
	Filters          []string     `json:"filters" yaml:"filters"`
	SQL              string       `json:"sql" yaml:"sql"`
	ContractType     string       `json:"type" yaml:"type"`
	Decryption       []Decryption `json:"decryption" yaml:"decryption"`
	PresignTimeHours int32        `json:"presign_time_hours" yaml:"presign_time_hours"`
	TableNames       []string     `json:"table_names" yaml:"table_names"`
}

// Decryption meta
type Decryption struct {
	SqlColumn    string `json:"sql_column" dynamodbav:"sql_column" yaml:"sql_column"`
	SourceColumn string `json:"source_column" dynamodbav:"source_column" yaml:"source_column"`
}

type TableConfig struct {
	Backend string `json:"backend" dynamodbav:"backend"`
	Catalog string `json:"catalog" dynamodbav:"catalog"`
	Schema  string `json:"schema" dynamodbav:"schema"`
	Table   string `json:"table" dynamodbav:"table"`
}

type QueryConfig struct {
	Type    string   `json:"type" dynamodbav:"type"`
	Sql     string   `json:"sql" dynamodbav:"sql"`
	Filters []string `json:"filters" dynamodbav:"filters"`
}

type PerformanceConfig struct {
	CachingTtl      int32 `json:"caching_ttl" dynamodbav:"caching_ttl"`
	RefreshInterval int32 `json:"refresh_interval" dynamodbav:"refresh_interval"`
	Sla             int32 `json:"sla" dynamodbav:"sla"`
	RateLimit       int32 `json:"rate_limit" dynamodbav:"rate_limit"`
}
