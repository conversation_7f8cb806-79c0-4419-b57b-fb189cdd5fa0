package models

type DynamoQueryState struct {
	ExecutionID      string `json:"_key" dynamodbav:"partition_key"`
	ClientID         string `json:"sort_key" dynamodbav:"sort_key"`
	User             string `json:"user" dynamodbav:"user"`
	FileName         string `json:"file_name" dynamodbav:"file_name"`
	ResultType       string `json:"result_type" dynamodbav:"result_type"`
	ContractId       string `json:"contract_id" dynamodbav:"contract_id"`
	ContractVersion  string `json:"contract_version" dynamodbav:"contract_version"`
	ItemType         string `json:"item_type" dynamodbav:"item_type"`
	Query            string `json:"query" dynamodbav:"query"`
	State            string `json:"state" dynamodbav:"state"`
	SubmissionTime   int    `json:"submission_time" dynamodbav:"submission_time"`
	CompletionTime   int    `json:"completion_time" dynamodbav:"completion_time"`
	TTL              int    `json:"ttl" dynamodbav:"ttl"`
	S3Key            string `json:"s3_key" dynamodbav:"s3_key"`
	ErrorCode        int    `json:"error_code" dynamodbav:"error_code"`
	PresignTimeHours int32  `json:"presign_time_hours"  dynamodbav:"presign_time_hours"`
	Error            string `json:"error" dynamodbav:"error"`
}

type DynamoTemplate struct {
	TemplateId      string            `json:"partition_key" dynamodbav:"partition_key"`
	TemplateVersion string            `json:"sort_key" dynamodbav:"sort_key"`
	TemplateStatus  string            `json:"template_status" dynamodbav:"template_status"`
	ItemType        string            `json:"item_type" dynamodbav:"item_type"`
	Tenant          string            `json:"tenant" dynamodbav:"tenant"`
	TableConfig     TableConfig       `json:"table_config" dynamodbav:"table_config"`
	Audit           Audit             `json:"audit" dynamodbav:"audit"`
	Performance     PerformanceConfig `json:"performance_config" dynamodbav:"performance_config"`
	Query           QueryConfig       `json:"query_config" dynamodbav:"query_config"`
	Columns         []Column          `json:"columns" dynamodbav:"columns"`
	Aggregations    []string          `json:"aggregations" dynamodbav:"aggregations"`
	Decryption      []Decryption      `json:"decryption" dynamodbav:"decryption"`
	AddedAt         string            `json:"added_at" dynamodbav:"added_at"`
}
type LatestDynamoTemplate struct {
	TemplateId      string            `json:"partition_key" dynamodbav:"partition_key"`
	LatestVersion   string            `json:"sort_key" dynamodbav:"sort_key"`
	TemplateVersion string            `json:"template_version" dynamodbav:"template_version"`
	TemplateStatus  string            `json:"template_status" dynamodbav:"template_status"`
	ItemType        string            `json:"item_type" dynamodbav:"item_type"`
	Tenant          string            `json:"tenant" dynamodbav:"tenant"`
	TableConfig     TableConfig       `json:"table_config" dynamodbav:"table_config"`
	Audit           Audit             `json:"audit" dynamodbav:"audit"`
	Performance     PerformanceConfig `json:"performance_config" dynamodbav:"performance_config"`
	Query           QueryConfig       `json:"query_config" dynamodbav:"query_config"`
	Columns         []Column          `json:"columns" dynamodbav:"columns"`
	Aggregations    []string          `json:"aggregations" dynamodbav:"aggregations"`
	Decryption      []Decryption      `json:"decryption" dynamodbav:"decryption"`
	AddedAt         string            `json:"added_at" dynamodbav:"added_at"`
}
type UpdateTemplateStatusInput struct {
	TemplateStatus string `json:"template_status,omitempty"`
	Added_At       string `json:"added_at,omitempty"`
}
