package serde

import (
	"context"

	"github.com/Zomato/go/config"
	"github.com/Zomato/jumbo-insights-service/pkg/crypto"
)

func InitDecryptionConfig(ctx context.Context) map[string]*crypto.Decryption {
	decryptionSlice := config.GetSlice(ctx, "crypto.decryption")
	decryptionConfig := make(map[string]*crypto.Decryption)
	for _, decryptionInterface := range decryptionSlice {
		decryptionMap := decryptionInterface.(map[string]interface{})
		sourceColumn := decryptionMap["source_column"].(string)
		if _, ok := decryptionConfig[sourceColumn]; !ok {
			decryptionConfig[sourceColumn] = &crypto.Decryption{
				SourceColumn: sourceColumn,
				AESKey:       decryptionMap["aes_key"].(string),
			}
		}
	}
	return decryptionConfig
}
