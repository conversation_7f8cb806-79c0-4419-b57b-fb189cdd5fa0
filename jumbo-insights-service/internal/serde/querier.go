package serde

import (
	"encoding/hex"
	"fmt"
	"strings"

	commonpb "github.com/Zomato/jumbo-insights-service-client-golang/common"
	querypb "github.com/Zomato/jumbo-insights-service-client-golang/querier/v1"

	"github.com/Zomato/jumbo-insights-service/internal/models"
)

func getAnyValue(anyValue *commonpb.AnyValue) *models.AnyValue {
	var value interface{}
	var dataType string

	if anyValue == nil {
		return nil
	}

	switch anyValue.Value.(type) {
	case *commonpb.AnyValue_StringValue:
		// Referred Pinot Java PreparedStatement parameter sanitization logic
		// https://github.com/apache/pinot/blob/master/pinot-clients/pinot-java-client/src/main/java/org/apache/pinot/client/PreparedStatement.java#L85
		value = fmt.Sprintf(`'%s'`, strings.ReplaceAll(anyValue.GetStringValue(), "'", "''"))
		dataType = "STRING"
	case *commonpb.AnyValue_IntValue:
		value = anyValue.GetIntValue()
		dataType = "INT"
	case *commonpb.AnyValue_DoubleValue:
		value = anyValue.GetDoubleValue()
		dataType = "DOUBLE"
	case *commonpb.AnyValue_BytesValue:
		// Referred Trino Java PreparedStatement parameter sanitization logic
		// https://github.com/trinodb/trino/blob/master/client/trino-jdbc/src/main/java/io/trino/jdbc/TrinoPreparedStatement.java#L1015
		value = fmt.Sprintf(`X'%s'`, hex.EncodeToString(anyValue.GetBytesValue()))
		dataType = "BYTES"
	case *commonpb.AnyValue_ArrayValue:
		// https://blog.FromContext(ctx).boot.dev/golang/empty-vs-nil-slices-golang/
		arrayValue := getArrayValue(anyValue.GetArrayValue())
		if arrayValue == nil {
			value = nil
		} else {
			value = arrayValue
		}
		dataType = "ARRAY_VALUE"
	case *commonpb.AnyValue_BetweenValue:
		value = getBetweenValue(anyValue.GetBetweenValue())
		dataType = "BETWEEN_VALUE"
	}

	switch value {
	case nil:
		return nil
	default:
		return &models.AnyValue{
			Value: value,
			Type:  dataType,
		}
	}
}

func getBetweenValue(betweenValuePB *commonpb.BetweenValue) interface{} {
	return models.BetweenValue{
		FromValue: *getAnyValue(betweenValuePB.GetFromValue()),
		ToValue:   *getAnyValue(betweenValuePB.GetToValue()),
	}
}

func getArrayValue(arrayValuePB *commonpb.ArrayValue) []interface{} {
	if len(arrayValuePB.Values) == 0 {
		return nil
	}
	var arrayValue []interface{}
	for _, value := range arrayValuePB.Values {
		anyValue := getAnyValue(value)
		if anyValue.Type == "STRING" {
			arrayValue = append(arrayValue, fmt.Sprintf("%s", anyValue.Value))
		} else {
			arrayValue = append(arrayValue, anyValue.Value)
		}
	}
	return arrayValue
}

func InitFilteDto(filters []*querypb.Filter) []*models.Filter {
	var filtersDto []*models.Filter

	for _, filter := range filters {
		filtersDto = append(filtersDto, &models.Filter{
			Op:    models.ComparisonOperator(filter.GetOp()),
			Name:  filter.GetField(),
			Value: getAnyValue(filter.Value),
		})
	}
	return filtersDto
}

func InitIntervalDto(interval *querypb.Interval) *models.Interval {
	if interval.GetDatetimeField() == "" || interval.GetEndTime() == 0 || interval.GetStartTime() == 0 {
		return nil
	}

	return &models.Interval{
		StartTime:     interval.GetStartTime(),
		EndTime:       interval.GetEndTime(),
		DatetimeField: interval.GetDatetimeField(),
	}
}
func InitTableNameDto(tables []*querypb.TableName) []*models.TableName {

	var tableNamesDto []*models.TableName
	for _, table := range tables {
		tableNamesDto = append(tableNamesDto, &models.TableName{
			Name:  table.GetName(),
			Value: table.GetValue(),
		})
	}
	return tableNamesDto
}

func InitQuerierDto(querier *querypb.Querier) *models.Querier {
	return &models.Querier{
		Filters:    InitFilteDto(querier.GetFilters()),
		Interval:   InitIntervalDto(querier.GetInterval()),
		TableNames: InitTableNameDto(querier.GetTableNames()),
	}
}

func InitPaginationOptionsDto(paginationOptions *querypb.PaginationOptions) *models.PaginationOptions {
	return &models.PaginationOptions{
		Limit:     paginationOptions.GetLimit(),
		SortKey:   paginationOptions.GetSortKey(),
		Order:     paginationOptions.GetSortOrder().String(),
		PageToken: paginationOptions.GetPageToken(),
	}
}
