package serde

import (
	asyncquerypb "github.com/Zomato/jumbo-insights-service-client-golang/asyncquerier/v1"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/Zomato/jumbo-insights-service/internal/models"
)

var (
	SecondToMillisMultiplier int64 = 1000
	SecondToNanoMultiplier   int64 = 1000000
)

func getResultType(resultType asyncquerypb.AsyncQueryContext_ResultType) models.ResultType {
	switch resultType {
	case asyncquerypb.AsyncQueryContext_CSV:
		return models.ResultTypeCSV
	case asyncquerypb.AsyncQueryContext_ZIP:
		return models.ResultTypeZIP
	case asyncquerypb.AsyncQueryContext_RESULT_TYPE_UNSPECIFIED:
		return ""
	default:
		return ""
	}
}

func getQueryStatePb(queryState models.QueryState) asyncquerypb.QueryState {
	switch queryState {
	case models.QueryStateSubmitted:
		return asyncquerypb.QueryState_SUBMITTED
	case models.QueryStateEnqueued:
		return asyncquerypb.QueryState_ENQUEUED
	case models.QueryStateRunning:
		return asyncquerypb.QueryState_RUNNING
	case models.QueryStateFinished:
		return asyncquerypb.QueryState_FINISHED
	case models.QueryStateFailed:
		return asyncquerypb.QueryState_FAILED
	default:
		return asyncquerypb.QueryState_QUERY_STATE_UNSPECIFIED
	}
}

func InitAsyncQueryContext(asyncQueryContextPB *asyncquerypb.AsyncQueryContext) *models.AsyncQueryContext {
	return &models.AsyncQueryContext{
		User:       asyncQueryContextPB.GetUser(),
		FileName:   asyncQueryContextPB.GetFileName(),
		ResultType: getResultType(asyncQueryContextPB.GetResultType()),
	}
}

func GetQueryExecutionInfoPB(queryExecutionInfo *models.QueryExecutionInfo) *asyncquerypb.QueryExecutionInfo {
	if queryExecutionInfo == nil {
		return nil
	}

	submissionTime := &timestamppb.Timestamp{Seconds: int64(queryExecutionInfo.SubmissionTime / int(SecondToMillisMultiplier)),
		Nanos: int32((queryExecutionInfo.SubmissionTime % int(SecondToMillisMultiplier)) * int(SecondToNanoMultiplier))}
	completionTime := &timestamppb.Timestamp{Seconds: int64(queryExecutionInfo.CompletionTime / int(SecondToMillisMultiplier)),
		Nanos: int32((queryExecutionInfo.CompletionTime % int(SecondToMillisMultiplier)) * int(SecondToNanoMultiplier))}

	return &asyncquerypb.QueryExecutionInfo{
		User:            queryExecutionInfo.User,
		ExecutionId:     queryExecutionInfo.ExecutionId,
		QueryState:      getQueryStatePb(queryExecutionInfo.QueryState),
		S3PresignedLink: queryExecutionInfo.S3PresignedLink,
		SubmissionTime:  submissionTime,
		CompletionTime:  completionTime,
		Error:           queryExecutionInfo.Error,
	}
}
