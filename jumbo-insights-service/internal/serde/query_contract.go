package serde

import (
	"context"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	templatepb "github.com/Zomato/jumbo-insights-service-client-golang/template/v1"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/hashicorp/go-multierror"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gopkg.in/yaml.v2"
)

const WhiteslitedQueryContractExt string = ".yaml"
const TemplateType = "Template"

func GetConfigFileInfo(basePath string) (map[string]fs.FileInfo, error) {
	configFilesMap := make(map[string]fs.FileInfo)
	fileSystem := os.DirFS(basePath)
	var errCombined error
	err := fs.WalkDir(fileSystem, ".", func(p string, d fs.DirEntry, err error) error {
		if err != nil {
			errCombined = multierror.Append(errCombined, err)
			return nil
		}
		if filepath.Ext(p) == WhiteslitedQueryContractExt {
			currentFileInfo, err := d.Info()
			if err != nil {
				errCombined = multierror.Append(errCombined, err)
				return nil
			}
			fullPath := filepath.Join(basePath, p)
			configFilesMap[fullPath] = currentFileInfo
		}
		return nil
	})
	if err != nil {
		errCombined = multierror.Append(errCombined, err)
	}
	return configFilesMap, errCombined
}

func InitQueryContractMap(ctx context.Context) map[string]*models.Query {
	envFlag := config.GetString(ctx, "contract.contract_type")
	if envFlag == "dynamo" {
		return map[string]*models.Query{}
	}
	queryContracts := make(map[string]*models.Query)
	contractPath := config.GetString(ctx, "contract.query_contract")
	filesMap, err := GetConfigFileInfo(contractPath)
	if err != nil {
		log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"event_name": "get_query_contracts",
		}).Error("Got error while reading the query contracts")
		return queryContracts
	}

	for filePath, file := range filesMap {
		var queryContract *models.Query
		data, err := os.ReadFile(filePath)
		if err != nil {
			log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
				"event_name":          "get_query_contract",
				"query_contract_file": file.Name(),
			}).Error("Got error while reading the query contract")
			continue
		}
		err = yaml.Unmarshal([]byte(data), &queryContract)
		if err != nil {
			log.FromContext(ctx).WithError(err).WithFields(map[string]interface{}{
				"event_name": "query_contract_yaml_unmarshal",
				"file_name":  file.Name(),
			}).Error("Got error while reading the query contract")
			continue
		}
		log.FromContext(ctx).Info("Read contract: ", queryContract.Identifier)
		queryContracts[queryContract.Identifier] = queryContract
	}
	return queryContracts
}
func ChangeDynamotemplatetoProto(templateData *models.DynamoTemplate) *templatepb.Template {
	templateDetails := &templatepb.Template{}
	templateDetails.Audit = &templatepb.AuditInfo{
		AuthorEmail:   templateData.Audit.AuthorEmail,
		TeamEmail:     templateData.Audit.TeamEmail,
		Description:   templateData.Audit.Description,
		PdServiceName: templateData.Audit.PdServiceName,
		Service:       templateData.Audit.Service,
	}
	descriptor := templatepb.TableBackend(0).Descriptor()
	templateDetails.TableConfig = &templatepb.TableConfig{
		Backend: templatepb.TableBackend(stringToEnum(descriptor, templateData.TableConfig.Backend)),
		Catalog: templateData.TableConfig.Catalog,
		Schema:  templateData.TableConfig.Schema,
		Table:   templateData.TableConfig.Table,
	}

	templateDetails.TemplateVersion = templateData.TemplateVersion
	descriptor = templatepb.Tenant(0).Descriptor()
	templateDetails.Tenant = templatepb.Tenant(stringToEnum(descriptor, templateData.Tenant))
	descriptor = templatepb.TemplateStatus(0).Descriptor()
	templateDetails.TemplateStatus = templatepb.TemplateStatus(stringToEnum(descriptor, templateData.TemplateStatus))
	templateDetails.Performance = &templatepb.PerformanceConfig{
		RateLimit:       templateData.Performance.RateLimit,
		CachingTtl:      templateData.Performance.CachingTtl,
		RefreshInterval: templateData.Performance.RefreshInterval,
		Sla:             templateData.Performance.Sla,
	}
	templateDetails.Query = &templatepb.QueryConfig{
		Filters: templateData.Query.Filters,
		Type:    templateData.Query.Type,
		Sql:     templateData.Query.Sql,
	}
	for _, column := range templateData.Decryption {
		col := &templatepb.DecryptionConfig{}
		col.SqlColumn = column.SqlColumn
		col.SourceColumn = column.SourceColumn
		templateDetails.Decryption = append(templateDetails.Decryption, col)
	}
	templateDetails.Aggregations = templateData.Aggregations
	for _, column := range templateData.Columns {
		col := &templatepb.Column{
			Name:         column.Name,
			Func:         column.Func,
			SourceColumn: column.SourceColumn,
		}
		templateDetails.Columns = append(templateDetails.Columns, col)
	}
	templateDetails.TemplateId = templateData.TemplateId
	t, err := time.Parse(time.RFC3339, templateData.AddedAt)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "ChangeDynamoProto",
		}).Error("Got error while converting time to protobuf time")
	}
	templateDetails.AddedAt = timestamppb.New(t)
	// templateDetails.LatestProdVersion = templateData.LatestProdVersion

	return templateDetails

}
func ChangePrototoDynamo(templateData *templatepb.Template) *models.DynamoTemplate {
	templateDetails := &models.DynamoTemplate{}
	templateDetails.Audit.AuthorEmail = templateData.Audit.AuthorEmail
	templateDetails.Audit.Description = templateData.Audit.Description
	templateDetails.Audit.PdServiceName = templateData.Audit.PdServiceName
	templateDetails.Audit.Service = templateData.Audit.Service
	templateDetails.Audit.TeamEmail = templateData.Audit.TeamEmail
	templateDetails.TableConfig.Backend = templateData.TableConfig.Backend.String()
	templateDetails.TemplateVersion = templateData.TemplateVersion
	templateDetails.TableConfig.Table = templateData.TableConfig.Table
	templateDetails.Tenant = templateData.Tenant.String()
	templateDetails.TemplateStatus = string(templateData.TemplateStatus.String())
	templateDetails.Performance.RateLimit = templateData.Performance.RateLimit
	templateDetails.TableConfig.Catalog = templateData.TableConfig.Catalog
	templateDetails.Query.Type = templateData.Query.Type
	templateDetails.Query.Sql = templateData.Query.Sql
	templateDetails.Performance.CachingTtl = templateData.Performance.CachingTtl
	templateDetails.Performance.RefreshInterval = templateData.Performance.RefreshInterval
	templateDetails.Performance.Sla = templateData.Performance.Sla
	templateDetails.Query.Filters = templateData.Query.Filters
	templateDetails.TableConfig.Schema = templateData.TableConfig.Schema
	templateDetails.Aggregations = templateData.Aggregations
	// templateDetails.LatestProdVersion = templateData.LatestProdVersion

	for _, column := range templateData.Decryption {
		col := models.Decryption{}
		col.SqlColumn = column.SqlColumn
		col.SourceColumn = column.SourceColumn
		templateDetails.Decryption = append(templateDetails.Decryption, col)
	}
	for _, columns := range templateData.Columns {
		col := models.Column{
			Name:         columns.Name,
			Func:         columns.Func,
			SourceColumn: columns.SourceColumn,
		}
		templateDetails.Columns = append(templateDetails.Columns, col)
	}
	templateDetails.TemplateId = templateData.TemplateId
	return templateDetails

}
func ChangeDynamoToLatestDynamo(templateData *models.DynamoTemplate) *models.LatestDynamoTemplate {
	latestVersion := fmt.Sprintf("%s_%s", strings.ToLower(templateData.TemplateStatus), "latest")
	templateData.ItemType = TemplateType
	return &models.LatestDynamoTemplate{
		TemplateId:      templateData.TemplateId,
		TemplateVersion: templateData.TemplateVersion,
		TemplateStatus:  templateData.TemplateStatus,
		Tenant:          templateData.Tenant,
		TableConfig:     templateData.TableConfig,
		Audit:           templateData.Audit,
		AddedAt:         templateData.AddedAt,
		Query:           templateData.Query,
		Performance:     templateData.Performance,
		Decryption:      templateData.Decryption,
		LatestVersion:   latestVersion,
		ItemType:        templateData.ItemType,
		Aggregations:    templateData.Aggregations,
		Columns:         templateData.Columns,
	}
}
func ChangeLatestDynamoToDynamo(templateData *models.LatestDynamoTemplate) *models.DynamoTemplate {

	return &models.DynamoTemplate{
		TemplateId:      templateData.TemplateId,
		TemplateVersion: templateData.TemplateVersion,
		TemplateStatus:  templateData.TemplateStatus,
		Tenant:          templateData.Tenant,
		TableConfig:     templateData.TableConfig,
		Audit:           templateData.Audit,
		AddedAt:         templateData.AddedAt,
		Query:           templateData.Query,
		Performance:     templateData.Performance,
		Decryption:      templateData.Decryption,
		ItemType:        templateData.ItemType,
		Aggregations:    templateData.Aggregations,
		Columns:         templateData.Columns,
	}
}

func stringToEnum(enumDescriptor protoreflect.EnumDescriptor, value string) protoreflect.EnumNumber {
	// Iterate over all enum values
	for i := 0; i < enumDescriptor.Values().Len(); i++ {
		enumValue := enumDescriptor.Values().Get(i)
		if string(enumValue.Name()) == value {
			return enumValue.Number()
		}
	}
	// Return the default value (0) if no match is found
	return enumDescriptor.Values().ByNumber(0).Number()
}
