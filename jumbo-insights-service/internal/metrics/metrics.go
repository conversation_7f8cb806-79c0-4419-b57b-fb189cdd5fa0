package metrics

import (
	"context"

	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/metrics"
)

const (
	// QueriesRequestsTotal gives total email requests to service
	QueriesRequestsTotal = "queries_requests_total"

	// QueriesFailedTotal : count of failed queries
	QueriesFailedTotal = "queries_failed_total"

	// QueriesConfigFailedTotal : count of query config failures
	QueriesConfigFailedTotal = "queries_config_failed_total"

	// QueriesConfigFailedTotal : count of query served by cache
	QueriesCacheHitTotal = "queries_cache_hit_total"

	// QueryRunTime : run time of each query
	queryRunTime = "query_run_time"

	// QueryProcessingTime : run time of each query
	queryProcessingTime = "query_processing_time"

	// QueryRowsTotal : total rows return by query
	queryRowsTotal = "query_total_rows"

	// SQSPickupDelay : total rows return by query
	sqsPickupDelay = "sqs_pickup_delay"

	// QueryID metric label name
	QueryIDDimension = "query_id"
	// TableName metric label name
	TableNameDimension = "table_name"
	// ClientID metric label name
	ClientIDDimension = "client_id"
	// Source name
	SourceDimension = "source"
)

var (
	int64Counters = make(map[string]metrics.Int64Counter)

	// QueryRunTime used to record query run time
	QueryRunTime metrics.Int64ValueRecorder

	// QueryProcessingTime used to record processing time taken by the service to process the rows
	QueryProcessingTime metrics.Int64ValueRecorder

	// QueryTotalRows used to record query total rows
	QueryTotalRows metrics.Int64ValueRecorder

	// SQSPickupDelay used to record async payload sqs enqueu time
	SQSPickupDelay metrics.Int64ValueRecorder

	// Counter metrics names map
	counterMetricNames = []string{
		QueriesRequestsTotal,
		QueriesFailedTotal,
		QueriesConfigFailedTotal,
		QueriesCacheHitTotal,
	}
)

// RegisterMetrics registers metrics and saves mapping by metric names
func RegisterMetrics() {
	for _, name := range counterMetricNames {
		counter, err := metrics.NewInt64Counter(name)
		if err != nil {
			log.WithError(err).WithFields(map[string]interface{}{
				"event_name":   "register_metric_counters",
				"counter_name": name,
			}).Error("unable to register new metric counter")
			continue
		}
		int64Counters[name] = *counter
	}

	// Register Int64ValueRecorder
	runTimerecorder, err := metrics.NewInt64ValueRecorder(queryRunTime)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name":    "register_metrics",
			"recorder_name": queryRunTime,
		}).Error("unable to register new value recorder")
	}
	QueryRunTime = *runTimerecorder

	// Register Int64ValueRecorder
	processingTimerecorder, err := metrics.NewInt64ValueRecorder(queryProcessingTime)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name":    "register_metrics",
			"recorder_name": queryProcessingTime,
		}).Error("unable to register new value recorder")
	}
	QueryProcessingTime = *processingTimerecorder

	// Register Int64ValueRecorder
	totalRowsrecorder, err := metrics.NewInt64ValueRecorder(queryRowsTotal)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name":    "register_metrics",
			"recorder_name": queryRowsTotal,
		}).Error("unable to register new value recorder")
	}
	QueryTotalRows = *totalRowsrecorder

	// Register Int64ValueRecorder
	sqsPickupDelayrecorder, err := metrics.NewInt64ValueRecorder(sqsPickupDelay)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name":    "register_metrics",
			"recorder_name": sqsPickupDelay,
		}).Error("unable to register new value recorder")
	}
	SQSPickupDelay = *sqsPickupDelayrecorder
}

// AddValueToInt64Counter adds given value to counter
func AddValueToInt64Counter(ctx context.Context, metric string, value int64, labels ...metrics.Label) {
	if _, ok := int64Counters[metric]; ok {
		c := int64Counters[metric]
		c.Add(ctx, value, labels...)
	}
}
