package asyncquerier

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/Zomato/jumbo-insights-service/internal/env"
	"github.com/Zomato/jumbo-insights-service/internal/models"
	"github.com/Zomato/jumbo-insights-service/internal/util"
	"github.com/Zomato/jumbo-insights-service/pkg/dynamodb"
	jislog "github.com/Zomato/jumbo-insights-service/pkg/log"
	"github.com/Zomato/jumbo-insights-service/pkg/sqs"
	ztracer "github.com/Zomato/jumbo-insights-service/pkg/tracer"
)

var (
	UserQueryGSI    string = "user_insights_index"
	ErrUnknownQuery error  = errors.New("unknown query, query is not present in dynamodb")
)

type UpdateQueryStateInput struct {
	State          string `json:"state,omitempty"`
	CompletionTime int    `json:"completion_time,omitempty"`
	S3Key          string `json:"s3_key,omitempty"`
	Error          string `json:"error,omitempty"`
}

func ListUserQueries(ctx context.Context, clientID, user string) ([]*models.QueryExecutionInfo, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "asyncQuerier/ListUserQueries", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"event_name": "list_user_queries",
		"user":       user,
		"client_id":  clientID,
	})

	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return nil, err
	}

	getRequest := dynamodb.QueryParams{
		PrimaryKeyName:  "user",
		PrimaryKeyValue: user,
		SortKeyName:     "sort_key",
		SortKeyValue:    clientID,
	}

	var queryStates []*models.DynamoQueryState

	_, err = env.DynamoDBClient().QueryItemsFromGSI(ctx, getRequest, UserQueryGSI, &queryStates)
	if err != nil {
		log.WithError(err).Error("Error in listing user queries")
		return nil, err
	}

	var queryExecutionInfos []*models.QueryExecutionInfo
	for _, queryState := range queryStates {
		var presignURL string

		if queryState.State == string(models.QueryStateFinished) {
			presignURL, err = GetPresignURL(ctx, clientID, queryState.ContractId, queryState.ContractVersion, queryState.ExecutionID, queryState.FileName, queryState.ResultType, queryState.PresignTimeHours)
			if err != nil {
				log.WithError(err).Error("Got error while generating presign url")
				return nil, err
			}
		}

		queryExecutionInfos = append(queryExecutionInfos, &models.QueryExecutionInfo{
			ExecutionId:     queryState.ExecutionID,
			QueryState:      models.QueryState(queryState.State),
			S3PresignedLink: presignURL,
			SubmissionTime:  queryState.SubmissionTime,
			CompletionTime:  queryState.CompletionTime,
			User:            queryState.User,
			Error:           queryState.Error,
		})
	}

	return queryExecutionInfos, nil
}

func SubmitQuery(ctx context.Context, clientID, contractID, contractVersion, query string, queryContext *models.AsyncQueryContext, queryConfig *models.Query) (*models.QueryExecutionInfo, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "asyncQuerier/SubmitQuery", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"event_name":  "get_query_state",
		"contract_id": contractID,
		"client_id":   clientID,
	})

	if queryConfig.QueryBackend != "trino" {
		err := fmt.Errorf("async query is only allowed for trino queries")
		log.WithError(err).Error("Got error while submitting query")
		return nil, err
	}

	executionID := util.GetQueryHash(contractID, contractVersion, clientID, query)

	queryState, err := GetQueryState(ctx, executionID, clientID)
	if err != nil {
		return nil, err
	}

	var presignURL string
	var msgId *string

	switch queryState.State {
	case string(models.QueryStateFailed), string(models.QueryStateRunning), string(models.QueryStateEnqueued), string(models.QueryStateFinished):
		presignURL, err = GetPresignURL(ctx, clientID, queryState.ContractId, queryState.ContractVersion, queryState.ExecutionID, queryState.FileName, queryState.ResultType, queryState.PresignTimeHours)
		if err != nil {
			log.WithError(err).Error("Got error while generating presign url")
			return nil, err
		}
		return &models.QueryExecutionInfo{
			ExecutionId:     executionID,
			QueryState:      models.QueryState(queryState.State),
			S3PresignedLink: presignURL,
			SubmissionTime:  queryState.SubmissionTime,
			CompletionTime:  queryState.CompletionTime,
			User:            queryState.User,
			Error:           queryState.Error,
		}, nil
	case string(models.QueryStateSubmitted):
		queueURL := config.GetString(ctx, "queues.trino.url")
		msgId, err = EnqueueJob(ctx, queueURL, executionID, contractID, contractVersion, clientID)
		if err != nil {
			log.WithError(err).Error("Got error while enqueuing the query state in sqs")
			return &models.QueryExecutionInfo{
				ExecutionId: executionID,
				QueryState:  models.QueryStateFailed,
			}, err
		}
		err = UpdateQueryState(ctx, executionID, clientID, &UpdateQueryStateInput{
			State: string(models.QueryStateEnqueued),
		})
		if err != nil {
			log.WithError(err).Error("Failed to update query state to enqueued")
			return &models.QueryExecutionInfo{
				ExecutionId: executionID,
				QueryState:  models.QueryStateFailed,
			}, err
		}
	default:
		currentTime := util.GetCurrentTime()
		ttlTime := currentTime.Add(time.Hour * config.GetDuration(ctx, "dynamodb.ttl_hours"))
		queryStateModel := &models.DynamoQueryState{
			ExecutionID:      executionID,
			ClientID:         clientID,
			ContractId:       contractID,
			ContractVersion:  contractVersion,
			Query:            query,
			State:            string(models.QueryStateSubmitted),
			SubmissionTime:   int(currentTime.UnixMilli()),
			TTL:              int(ttlTime.Unix()),
			User:             queryContext.User,
			FileName:         util.SanitizeName(queryContext.FileName),
			ResultType:       string(queryContext.ResultType),
			PresignTimeHours: queryConfig.PresignTimeHours,
			ItemType:         "Query",
		}

		err = PutQueryState(ctx, contractID, queryStateModel)
		if err != nil {
			log.WithError(err).Error("Got error while inserting the query state in dynamodb")
			return &models.QueryExecutionInfo{
				ExecutionId: executionID,
				QueryState:  models.QueryStateFailed,
			}, err
		}

		queueURL := config.GetString(ctx, "queues.trino.url")
		msgId, err = EnqueueJob(ctx, queueURL, executionID, contractID, contractVersion, clientID)
		if err != nil {
			log.WithError(err).Error("Got error while enqueuing the query state in sqs")
			return &models.QueryExecutionInfo{
				ExecutionId: executionID,
				QueryState:  models.QueryStateFailed,
			}, err
		}

		err = UpdateQueryState(ctx, executionID, clientID, &UpdateQueryStateInput{
			State: string(models.QueryStateEnqueued),
		})
		if err != nil {
			log.WithError(err).Error("Failed to update query state to enqueued")
			return &models.QueryExecutionInfo{
				ExecutionId: executionID,
				QueryState:  models.QueryStateFailed,
			}, err
		}
	}

	log.WithFields(map[string]interface{}{
		"event_name":       "query_enqueued",
		"client_id":        clientID,
		"query":            query,
		"contract_id":      contractID,
		"contract_version": contractVersion,
		"execution_id":     executionID,
		"sqs_message_id":   msgId,
	}).Info("Query Enqueued")

	return &models.QueryExecutionInfo{
		ExecutionId:    executionID,
		QueryState:     models.QueryStateEnqueued,
		SubmissionTime: queryState.SubmissionTime,
		CompletionTime: queryState.CompletionTime,
		User:           queryState.User,
	}, nil
}

func GetQueryInfo(ctx context.Context, clientID string, executionID string) (*models.QueryExecutionInfo, error) {
	/*
		- Fetch query state from dynamodb
		- If not present, return invalid query error
		- Else if query is not completed, return the query state
	*/
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "asyncQuerier/GetQueryInfo", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"event_name":   "get_query_info",
		"execution_id": executionID,
		"client_id":    clientID,
	})

	queryState, err := GetQueryState(ctx, executionID, clientID)

	switch {
	case err != nil:
		log.WithError(err).Error("Error in getting query state")
		return nil, err
	case queryState.State == "":
		return &models.QueryExecutionInfo{
			ExecutionId: executionID,
		}, ErrUnknownQuery
	}

	presignURL, err := GetPresignURL(ctx, clientID, queryState.ContractId, queryState.ContractVersion, queryState.ExecutionID, queryState.FileName, queryState.ResultType, queryState.PresignTimeHours)
	if err != nil {
		log.WithError(err).Error("Got error while generating presign url")
		return nil, err
	}

	return &models.QueryExecutionInfo{
		ExecutionId:     executionID,
		QueryState:      models.QueryState(queryState.State),
		S3PresignedLink: presignURL,
		SubmissionTime:  queryState.SubmissionTime,
		CompletionTime:  queryState.CompletionTime,
		User:            queryState.User,
		Error:           queryState.Error,
	}, nil
}

func GetQueryState(ctx context.Context, executionID, clientID string) (*models.DynamoQueryState, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "asyncQuerier/GetQueryState", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"event_name":   "get_query_state",
		"execution_id": executionID,
		"client_id":    clientID,
	})

	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return nil, err
	}

	keys := dynamodb.Keys{
		PartitionKey: executionID,
		SortKey:      clientID,
	}
	inputRequest := dynamodb.GetItemParams{KeyCondition: keys}

	queryState := models.DynamoQueryState{}
	_, err = env.DynamoDBClient().GetItem(ctx, inputRequest, &queryState)

	if err != nil {
		log.WithError(err).Error("error while getting query state")
		tracer.NoticeError(ctx, err)
		return nil, err
	}

	return &queryState, nil
}

func PutQueryState(ctx context.Context, contractID string, queryStateModel *models.DynamoQueryState) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "asyncQuerier/PutQueryState", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"event_name":       "put_query_state",
		"execution_id":     queryStateModel.ExecutionID,
		"contract_id":      contractID,
		"contract_version": queryStateModel.ContractVersion,
		"client_id":        queryStateModel.ClientID,
	})

	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return err
	}

	err = env.DynamoDBClient().PutItem(ctx, queryStateModel)
	if err != nil {
		log.WithError(err).Error("error while inserting query state")
		tracer.NoticeError(ctx, err)
		return err
	}

	return nil
}

func UpdateQueryState(ctx context.Context, executionID, clientID string, updateValues *UpdateQueryStateInput) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "asyncQuerier/UpdateQueryState", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"event_name":   "update_query_data",
		"execution_id": executionID,
		"client_id":    clientID,
	})

	var updateValuesMap map[string]interface{}
	data, err := json.Marshal(updateValues)
	if err != nil {
		log.WithError(err).Error("unable to marshal input struct")
		return err
	}
	err = json.Unmarshal(data, &updateValuesMap)
	if err != nil {
		log.WithError(err).Error("unable to unmarshal input struct")
		return err
	}

	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		return err
	}

	key := dynamodb.Keys{
		PartitionKey: executionID,
		SortKey:      clientID,
	}
	inputRequest := &dynamodb.UpdateItemParams{
		ValuesToBeUpdated: updateValuesMap,
		ValuesToBeRemoved: []string{},
		KeyCondition:      key,
	}
	return env.DynamoDBClient().UpdateItem(ctx, inputRequest, nil)
}

func EnqueueJob(ctx context.Context, queueURL, executionID, contractID, contractVersion, clientID string) (*string, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "asyncQuerier/EnqueueJob", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"event_name":       "sqs_enqueue_query",
		"execution_id":     executionID,
		"client_id":        clientID,
		"contract_id":      contractID,
		"contract_version": contractVersion,
	})

	var msgID *string

	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return msgID, err
	}

	sqsPayload := &models.SQSPayload{
		ExecutionID:     executionID,
		ContractId:      contractID,
		ContractVersion: contractVersion,
		ClientID:        clientID,
		TraceId:         ctx.Value(jislog.TraceIDKey).(string),
		EnqueueTime:     time.Now().UnixMilli(),
	}

	jsonPayload, err := json.Marshal(sqsPayload)
	if err != nil {
		log.WithError(err).Error("error in marshalling email data for enqueue")
		tracer.NoticeError(ctx, err)
		return msgID, err
	}

	msgID, err = env.SQSClient().SendMessage(ctx, &sqs.SendMessageInputParam{
		Message:  string(jsonPayload),
		QueueURL: queueURL,
	})
	if err != nil {
		log.WithError(err).Error("error in sending sqs message")
		tracer.NoticeError(ctx, err)
		return msgID, err
	}

	return msgID, nil
}

func GetPresignURL(ctx context.Context, clientID, contractID, contractVersion, queryExecutionID, fileName, resultType string, presignTimeHours int32) (string, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "asyncQuerier/GetPresignURL", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	log := log.FromContext(ctx).WithFields(map[string]interface{}{
		"event_name":  "get_presign_url",
		"file_name":   fileName,
		"client_id":   clientID,
		"contract_id": contractID,
	})

	env, err := env.FromContext(ctx)
	if err != nil {
		log.WithError(err).Error("unable to get env from context")
		tracer.NoticeError(ctx, err)
		return "", err
	}

	bucket := config.GetString(ctx, "s3.bucket")
	s3Key := util.GetS3FilePath(clientID, contractID, contractVersion, queryExecutionID, fileName, resultType)
	fileExists, err := env.S3().CheckIfFileExists(ctx, bucket, s3Key)
	if err != nil {
		log.WithError(err).Error("unable to check if file exists on s3 or not")
		tracer.NoticeError(ctx, err)
		return "", err
	}

	if !fileExists {
		return "", nil
	}

	if presignTimeHours < config.GetInt32(ctx, "s3.presign_time_hours") {
		presignTimeHours = config.GetInt32(ctx, "s3.presign_time_hours")
	}

	return env.S3().GetPresignURL(ctx, bucket, s3Key, presignTimeHours)
}
