# binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
.DS_Store

# test binary, build with `go test -c`
*.test

# output of the go coverage tool, specifically when used with LiteIDE
*.out

# intellij Idea workspace files
.idea/
*.iml

# vscode workspace files
.vscode
*.code-workspace
launch.json

# golang vendor directory
vendor

# data directory
data

# tmp directory
.tmp

# local test files
client/local

# JAVA build proto in client
client/java/src
client/java/.gradle

# temporary file generated during build
build

# .netrc file containing github access token
.netrc
main

# proto gen docker dir
protoc-gen

# not to be commited creds
.creds

# gosec bin and others
bin

# delve debugger temp file
__debug_bin

# generated protos clients
*.pb.go
*_pb.rb
*.php
*_pb.js
*_pb2.py
*_pb2_grpc.py
*init__.py
*.java

# secrets
.DS_Storesendgrid.env

insights-service-linux-amd64
scripts/benchmarking/k6/*.json
# ignore loki generated files
.loki
