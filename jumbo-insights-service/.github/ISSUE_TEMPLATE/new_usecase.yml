name: New Use Case
description: Create a new use case for the service - https://zomato.atlassian.net/l/cp/AkJr7ik0

body:
  - type: markdown
    attributes:
      value: |
        ## Use Case Overview

  - type: input
    id: description
    attributes:
      label: Description
      description: A brief description of the new use case.
      placeholder: Provide a detailed description here.
    validations:
      required: true

  - type: input
    id: figma_link
    attributes:
      label: Figma Link
      description: Please share the figma link
      placeholder: Please share the figma link
    validations:
      required: true

  - type: textarea
    id: business_impact
    attributes:
      label: Business Impact
      description: Explain the significance and the expected impact on business.
      placeholder: Describe the business impact here.
    validations:
      required: true


  - type: markdown
    attributes:
      value: |
        ## Data Requirements

  - type: input
    id: etl_type
    attributes:
      label: Real-time or Scheduled ETL
      description: Indicate whether the data needs to be ingested in real-time or via scheduled ETL.
      placeholder: Real-time or Scheduled ETL
    validations:
      required: true

  - type: input
    id: data_retention_period
    attributes:
      label: Data Retention Period
      description: Indicate the desired data retention period (default is 6 months).
      placeholder: Data retention period
    validations:
      required: true

  - type: markdown
    attributes:
      value: |
        ## ETL Development
        Go through this - https://zomato.atlassian.net/wiki/spaces/DE/pages/3584458954/Preparing+data