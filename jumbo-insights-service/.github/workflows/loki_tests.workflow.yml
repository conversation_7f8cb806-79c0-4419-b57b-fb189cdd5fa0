name: <PERSON>
run-name: <PERSON>
on: 
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - closed
    branches: 
      - dev #replace with your default branch

jobs:
  LokiPushMock:
    if: github.event.pull_request.merged == true
    uses: Zomato/github-actions/.github/workflows/loki_push_mock.yml@master
  LokiTest:
    if: github.event.pull_request.state == 'open'
    uses: Zomato/github-actions/.github/workflows/loki_test.yml@master
    with:
      enabled: false