name: deployment-dev
run-name: ${{ github.workflow }}

on:
  push:
    branches:
      - dev

permissions:
  id-token: write
  contents: read
  packages: read
  checks: write
  pull-requests: read

jobs:
  Build:
    runs-on:
      group: Dev
      labels: [self-hosted, od, 8v, acc-infradev]
    concurrency:
      group: dev-build
    steps:
      - name: Build
        uses: Zomato/deployment-controller/actions/build@release
        with:
          buildspec_path: deployment/dev/buildspec.yml

  Deploy:
    needs: Build
    runs-on:
      group: Dev
      labels: [self-hosted, od, 4v, acc-infradev]
    concurrency:
      group: dev-deploy
    steps:
      - name: Deploy
        uses: Zomato/deployment-controller/actions/deploy@release
