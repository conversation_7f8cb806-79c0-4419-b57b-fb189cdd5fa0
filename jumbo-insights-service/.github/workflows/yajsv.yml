name: Query contract config validation
on:
  pull_request:
    types: 
      - opened
      - reopened
      - edited
      - synchronize
    branches: 
      - dev

concurrency: 
  group: ${{ github.repository }}-${{ github.workflow }}-${{ github.actor }}-${{ github.head_ref }}
  # Concurrency Group: (Repo Name + Workflow Name + Github User + Source Branch of the PR) ... for eg: search-service-AM1729-dev
  # For the same person opening/ reopening the PR (i.e. Same PR source branch + Same User) older runs would get cancelled
  # It's the unique identifier for a person opening a PR and a particular workflow running
  cancel-in-progress: true
  
permissions:
  id-token: write
  pull-requests: write
  checks: write 
  contents: write


jobs:
  json-schema-validator:
    name: JSO<PERSON> Schema Linter
    runs-on: [self-hosted, spot, 1v,acc-infraprod,arch-x64]
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        
      - name: Install JSON Schema Validator
        shell: bash
        run: |
          curl --output "yajsv" -sfSL "https://github.com/neilpa/yajsv/releases/download/v1.4.1/yajsv.$(uname -s | tr '[:upper:]' '[:lower:]').amd64" && chmod +x "yajsv"

      - name: Run JSON Schema Validator
        shell: bash
        run: |
          ./yajsv -s query_contract_config_schema.json $(find "./configs/contracts/query" -type f -name "*.yaml")
