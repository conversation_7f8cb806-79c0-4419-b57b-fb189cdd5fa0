name: deployment-preprod
run-name: ${{ github.workflow }}

on:
  push: 
    branches:
     - master 

permissions:
  id-token: write
  contents: read
  packages: read
  checks: write
  pull-requests: read

jobs:
  Build:
    runs-on: [self-hosted, od, 4v, acc-infraprod]
    concurrency:
      group: preprod-build
    steps:
      - name: Build
        uses: Zomato/deployment-controller/actions/build@release
        with:
          buildspec_path: deployment/preprod/buildspec.yml

  Deploy:
    needs: Build
    runs-on: [self-hosted, od, 2v, acc-infraprod]
    concurrency:
      group: preprod-deploy
    steps:
      - name: Deploy
        uses: Zomato/deployment-controller/actions/deploy@release