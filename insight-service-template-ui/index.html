<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <!-- <link rel="icon" type="image/svg+xml" href="/vite.svg" /> -->
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Zomato | Admin Dashboard</title>
        <script>
            (function (h, o, u, n, d) {
                h = h[d] = h[d] || {
                    q: [],
                    onReady: function (c) {
                        h.q.push(c);
                    },
                };
                d = o.createElement(u);
                d.async = 1;
                d.src = n;
                n = o.getElementsByTagName(u)[0];
                n.parentNode.insertBefore(d, n);
            })(window, document, 'script', 'https://www.datadoghq-browser-agent.com/us1/v5/datadog-rum.js', 'DD_RUM');
            window.DD_RUM.onReady(function () {
                window.DD_RUM.init({
                    clientToken: '%REACT_APP_CLIENT_TOKEN%',
                    applicationId: '%REACT_APP_DD_APPLICATION_ID%',
                    site: 'datadoghq.com',
                    service: 'insight-service-ui',
                    env: '%REACT_APP_MODE%',
                    sessionSampleRate: 20,
                    sessionReplaySampleRate: 0,
                    trackUserInteractions: true,
                    trackResources: true,
                    trackLongTasks: true,
                    defaultPrivacyLevel: 'mask',
                });
            });
        </script>
    </head>
    <body>
        <div id="root"></div>
        <div id="portal-root"></div>
        <script type="module" src="/src/main.jsx"></script>
    </body>
</html>
