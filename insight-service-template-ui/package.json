{"name": "dashboard-builder", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "test": "echo \"Error: no test specified\" && exit 1", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "prepare": "husky", "format": "npx prettier --write .", "format-file": "npx prettier --write $file", "lint": "npx eslint \"src/**/*.{js,jsx}\"", "lint-file": "eslint $file --cache", "lint:fix": "npx eslint \"src/**/*.{js,jsx}\" --fix"}, "author": "", "license": "ISC", "dependencies": {"@monaco-editor/react": "^4.6.0", "@tanstack/match-sorter-utils": "^8.11.8", "@tanstack/react-table": "^8.11.8", "@uiw/react-md-editor": "^4.0.3", "@zomato/networking": "^2.0.2", "@zomato/zoauth": "^1.4.9", "clsx": "^2.1.0", "date-fns": "^3.6.0", "dompurify": "^3.2.3", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "moment": "^2.30.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.12", "react-hook-form": "^7.50.1", "react-hotkeys-hook": "^4.5.0", "react-router": "^6.27.0", "react-router-dom": "^6.22.0", "react-select": "^5.8.0", "react-toastify": "^10.0.4", "rehype-sanitize": "^6.0.0", "tailwind-merge": "^2.4.0", "vite-plugin-environment": "^1.1.3", "zustand": "^5.0.3"}, "devDependencies": {"@babel/eslint-parser": "^7.23.10", "@babel/preset-react": "^7.23.3", "@commitlint/cli": "^18.6.0", "@commitlint/config-conventional": "^18.6.0", "@modyfi/vite-plugin-yaml": "^1.1.0", "@storybook/addon-essentials": "^7.6.13", "@storybook/addon-interactions": "^7.6.13", "@storybook/addon-links": "^7.6.13", "@storybook/addon-onboarding": "^1.0.11", "@storybook/addon-postcss": "^2.0.0", "@storybook/blocks": "^7.6.13", "@storybook/builder-vite": "^7.6.14", "@storybook/react": "^7.6.13", "@storybook/react-vite": "^7.6.13", "@storybook/test": "^7.6.13", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@vitejs/plugin-basic-ssl": "^1.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-simple-import-sort": "^12.0.0", "eslint-plugin-storybook": "^0.6.15", "eslint-plugin-tailwindcss": "^3.14.2", "eslint-plugin-unused-imports": "^3.1.0", "husky": "^9.0.11", "msw": "^2.4.4", "msw-storybook-addon": "^2.0.3", "postcss": "^8.4.34", "prettier-plugin-tailwindcss": "^0.5.12", "prop-types": "^15.8.1", "storybook": "^7.6.13", "storybook-addon-react-router-v6": "^2.0.10", "tailwindcss": "^3.4.1", "vite": "^5.0.12", "vite-jsconfig-paths": "^2.0.1"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.9.2"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "msw": {"workerDirectory": ["public"]}}