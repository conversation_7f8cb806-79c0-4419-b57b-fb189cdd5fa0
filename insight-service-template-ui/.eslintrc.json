{"env": {"browser": true, "es6": true}, "extends": ["eslint:recommended", "plugin:tailwindcss/recommended", "plugin:prettier/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "rules": {"no-console": "warn", "no-unused-vars": "warn", "strict": "error", "yoda": "error", "react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off", "react/display-name": "off", "react/prop-types": 0}, "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}, "parser": "@babel/eslint-parser", "requireConfigFile": false, "babelOptions": {"presets": ["@babel/preset-react"]}}, "plugins": ["simple-import-sort"], "ignorePatterns": ["node_modules", "build", ".eslintrc.json", ".prettieri<PERSON>re", "package.json", "tsconfig.json", "package-lock.json", "jsconfig.json", ".giti<PERSON>re", ".github", "*.css", "readme.md"]}