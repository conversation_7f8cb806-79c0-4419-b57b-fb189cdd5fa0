import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import DashboardFunctions from '../functions';
import errorConfig from '../utils/error.yaml';
import { getValueUsingAccessorKey } from '../utils/network';
import useErrorContext from './useErrorContext';
import useRequest from './useRequest';

export const useTableData = ({
    networkCalls,
    accessorKey,
    getData,
    primaryKey = 'id',
    TableData,
    editAccessorKey,
    editFormControls,
    isTableLoading,
}) => {
    const { customRequest } = useRequest();
    const { pushError } = useErrorContext();
    const [refresh, setRefresh] = useState(0);
    const [data, setData] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [searchParams, setSearchParams] = useSearchParams();
    const [fetchingEditData, setFetchingEditData] = useState(true);
    const [isEditing, setIsEditing] = useState({
        status: false,
        id: null,
    });
    const [error, setError] = useState(null);

    useEffect(() => {
        if (searchParams.get('action') === 'edit' && !networkCalls?.onEditGet && !isLoading && searchParams.get('id'))
            handleEditGet();
    }, [data, searchParams.get('id')]);

    useEffect(() => {
        fetchData();
    }, [refresh]);

    const fetchData = async () => {
        if (TableData) {
            setData(TableData);
            setIsLoading(false);
            return;
        } else if (getData) {
            setIsLoading(true);
            if (DashboardFunctions[getData] === undefined) {
                pushError({
                    error: `Function ${getData} is not defined in the functions file`,
                    solution: `Please define the function ${getData} in the src/functions/index.js file.`,
                });
                return;
            } else {
                const res = await (typeof getData === 'function' ? getData() : DashboardFunctions[getData]());
                updateTableData(res);
            }
        } else if (networkCalls?.onGet === undefined) {
            pushError({
                error: `Please provide onGet inside networkCalls of the table or getData (defined in src/functions/index.js) `,
                solution: errorConfig.missingOnGetOrGetStaticDataInTable,
            });
            return;
        } else {
            try {
                setIsLoading(true);
                const res = await customRequest(networkCalls.onGet);
                if (!res?.customError) updateTableData(res?.data);
            } catch (error) {
                setError(error);
                setIsLoading(false);
                toast.error('Error while fetching the data');
            }
        }
    };

    const updateTableData = async tableData => {
        try {
            const newData = accessorKey ? getValueUsingAccessorKey(tableData, accessorKey) : tableData;
            if (newData === undefined || !Array.isArray(newData)) {
                pushError({
                    error: accessorKey
                        ? `Can't find data using the accessor key: ${accessorKey} provided in the table configuration. Please provide correct accesor key.`
                        : `Can't find data, please provide an accessor key in the table configuration`,
                    solution: errorConfig.missingAccessorKeyInTable,
                });
                setIsLoading(false);
                return;
            }
            setData(newData);
            setIsLoading(false);
        } catch (error) {
            setError(error);
            setIsLoading(false);
            toast.error('Error while fetching the data');
        }
    };

    const handleEditSubmit = async data => {
        if (networkCalls?.onEdit === undefined) {
            pushError({
                error: `Please provide onEdit inside networkCalls of the table`,
                solution: errorConfig.missingOnEditInTable,
            });
            return;
        }

        const payload = {
            ...data,
            [primaryKey]: data[primaryKey] ?? searchParams.get(primaryKey),
        };

        try {
            setIsEditing({
                status: true,
                id: searchParams.get(primaryKey),
            });
            const res = await customRequest(networkCalls?.onEdit, {
                body: payload,
                options: payload,
            });
            if (res.customError) return;
            searchParams.delete(primaryKey);
            searchParams.delete('action');
            setSearchParams(searchParams);
            fetchData();
            toast.success('Data updated successfully');
        } catch (error) {
            toast.error('Error while updating the data');
        } finally {
            setIsEditing({
                status: false,
                id: null,
            });
        }
    };

    const handleEditGet = async () => {
        setFetchingEditData(true);
        const currData = await data.find(item => item[primaryKey] == searchParams.get(primaryKey));

        if (networkCalls?.onEditGet) {
            const res = await customRequest(networkCalls?.onEditGet, {
                options: {
                    [primaryKey]: searchParams.get(primaryKey),
                    ...currData,
                },
            });
            const newData = accessorKey ? getValueUsingAccessorKey(res.data, editAccessorKey) : res.data;
            editFormControls?.reset(newData);
        } else editFormControls?.reset(currData);

        setFetchingEditData(false);
    };

    return {
        data,
        setData,
        fetchData,
        loadingStates: {
            isLoading: isTableLoading ?? isLoading,
        },
        error,
        setRefresh,
        setIsLoading,
        handleEditSubmit,
        handleEditGet,
        fetchingEditData,
        isEditing,
    };
};
