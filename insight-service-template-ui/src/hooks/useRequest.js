import { request } from '@zomato/networking';

import { isStorybook } from '@/utils';

import baseConfig from '../configs/config.yaml';
import errorConfig from '../utils/error.yaml';
import useErrorContext from './useErrorContext';

export const isInsideEndpointMapping = endpoint => {
    if (isStorybook()) return true;
    return baseConfig?.endpointsMapping && baseConfig?.endpointsMapping[endpoint];
};

const useRequest = () => {
    const { pushError } = useErrorContext();

    const customRequest = async (url, options) => {
        if (isStorybook()) {
            return request(url, {
                ...options,
            });
        } else if (baseConfig?.serviceConstants === undefined) {
            pushError({
                error: `ServiceConstants not found! add ServiceConstants inside in config.yaml file.`,
                solution: errorConfig.serviceConstantNotFoundInMapping,
            });
        } else if (baseConfig?.endpointsMapping && baseConfig?.endpointsMapping[url]) {
            const { method } = baseConfig.endpointsMapping[url];
            if (method === 'GET') {
                return request(url, {
                    query: options?.query || options?.body || {},
                    ...options,
                });
            } else {
                return request(url, {
                    ...options,
                });
            }
        } else {
            pushError({
                error: `Endpoint not found! Please add "${url}" inside endpointsMapping in config.yaml file.`,
                solution: errorConfig.endpointNotFoundInMapping,
            });
            return {
                customError: true,
            };
        }
    };

    return {
        customRequest,
    };
};

export default useRequest;
