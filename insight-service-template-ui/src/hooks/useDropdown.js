import { useEffect, useState } from 'react';

import errorConfig from '../utils/error.yaml';
import { parseDataForApiRequest } from '../utils/network';
import { getValueUsingAccessorKey } from '../utils/network';
import useErrorContext from './useErrorContext';
import useRequest from './useRequest';

export const useDropdown = ({
    dependencies,
    networkCall,
    dependentValues = [],
    options,
    formConfig = [],
    onlyValue,
    isOpen,
}) => {
    const { pushError } = useErrorContext();
    const { customRequest } = useRequest();
    const [canFetch, setCanFetch] = useState(false);
    const [parsedOptions, setParsedOptions] = useState([]);
    const [previousReqValue, setPreviousReqValues] = useState({
        dependentValues: undefined,
        parsedOptions: [],
    });
    const [dependentLabels, setDependentLabels] = useState([]);
    const dependentValuesArray = Object.values(dependentValues || {}) || [];

    const checkDependent = () => {
        if (dependencies.length === 0) return true;
        return dependentValuesArray?.every(dependent => {
            if (onlyValue) return dependent !== undefined && dependent !== null;
            else
                return (
                    dependent?.length > 0 ||
                    (dependent !== undefined &&
                        dependent.value !== null &&
                        dependent.value !== '' &&
                        dependent.value !== undefined)
                );
        });
    };

    useEffect(() => {
        if (options && checkDependent()) {
            setParsedOptions(options);
            setCanFetch(true);
        } else if (networkCall?.onGet && checkDependent()) {
            isOpen && getOptions();
            setCanFetch(true);
        } else if (networkCall?.onGet === undefined && isOpen) {
            pushError({
                error: `Please provide "onGet" inside networkCall of the dropdown or give "options"`,
                solution: errorConfig.missingOnGetInDropdown,
            });
        }
    }, [dependentValuesArray, isOpen]);

    const getOptionsFromData = data => {
        let correctLabelAccessorKey = false;
        let correctValueAccessorKey = false;
        const parsedOptions = data.map(option => {
            const value = networkCall.valueKey
                ? getValueUsingAccessorKey(option, networkCall.valueKey)
                : option.id || option.value;
            const label = networkCall.labelKey
                ? getValueUsingAccessorKey(option, networkCall.labelKey)
                : option.name || option.label;

            if (label !== undefined) correctLabelAccessorKey = true;
            if (value !== undefined) correctValueAccessorKey = true;
            return {
                value: value,
                label: label,
            };
        });
        if (!correctLabelAccessorKey) {
            pushError({
                error: `Please provide the right labelKey in networkCall of the dropdown or check the response of the API`,
                solution: errorConfig.invalidLabelKeyInDropdown,
            });
            return;
        } else if (!correctValueAccessorKey) {
            pushError({
                error: `Please provide the right valueKey in networkCall of the dropdown or check the response of the API`,
                solution: errorConfig.invalidValueKeyInDropdown,
            });
            return;
        }
        return parsedOptions;
    };

    const getOptions = async () => {
        const parsedDependentValues = parseDataForApiRequest(dependentValues);
        if (
            JSON.stringify(dependentValues) === JSON.stringify(previousReqValue.dependentValues) &&
            previousReqValue.parsedOptions.length > 0
        )
            return;
        try {
            const res = await customRequest(networkCall.onGet, {
                body: parsedDependentValues,
            });
            const data = networkCall.accessorKey
                ? getValueUsingAccessorKey(res.data, networkCall.accessorKey)
                : res.data;
            if (!Array.isArray(data) || data === undefined || data === null) {
                pushError({
                    error: `Please provide the right accessorKey in networkCall of the dropdown or check the response of the API`,
                    solution: errorConfig.invalidAccessorKeyInDropdown,
                });
                return;
            }
            const parsedOptions = getOptionsFromData(data);
            if (parsedOptions) {
                setParsedOptions(parsedOptions);
            }
        } finally {
            setPreviousReqValues({
                dependentValues: dependentValues,
                parsedOptions: parsedOptions,
            });
        }
    };

    useEffect(() => {
        if (formConfig.contents?.length > 0 && dependencies.length > 0) {
            const newLabels = [];

            const getDependentLabels = (formConfig, dependencies) => {
                formConfig.contents.forEach(content => {
                    if (content.type === 'Dropdown' && dependencies.includes(content.id)) {
                        newLabels.push(content.label);
                    }
                    if (content.contents) {
                        getDependentLabels(content, dependencies);
                    }
                });
            };

            getDependentLabels(formConfig, dependencies);
            setDependentLabels(newLabels);
        }
    }, [formConfig, dependencies]);

    return {
        parsedOptions,
        canFetch,
        dependentLabels,
    };
};
