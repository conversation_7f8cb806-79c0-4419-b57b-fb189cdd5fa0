import { useContext } from 'react';

import { ErrorContext } from '../context/ErrorContext';

const useErrorContext = () => {
    const context = useContext(ErrorContext);
    if (!context) {
        throw Error(
            `Context Not Found, ${ErrorContext.displayName}. You're likely to wrap the context provider, or using the context outside the provider`,
        );
    }
    return context;
};

export default useErrorContext;
