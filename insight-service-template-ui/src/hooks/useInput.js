import DOMPurify from 'dompurify';

import { toNum } from '@/utils';

const useInput = ({ allowNegativeValue, allowFloatValue, onKeyDown, type = 'text', onChange, sanitize }) => {
    const handleNumericKeyDown = event => {
        const key = event.key;

        // Allow: Delete, Backspace, Tab, Escape, Enter, home, end, left, right, down, up
        if (
            [46, 8, 9, 27, 13, 110].includes(event.keyCode) ||
            // Allow: Ctrl+A, Command+A, Ctrl+C, Command+C, Ctrl+V, Command+V
            ((event.keyCode === 65 || event.keyCode === 67 || event.keyCode === 86) &&
                (event.ctrlKey || event.metaKey)) ||
            // Allow: home, end, left, right, down, up
            (event.keyCode >= 35 && event.keyCode <= 40) || // Allow: -
            (event.keyCode === 189 && !event.shiftKey && allowNegativeValue) || // Allow: .
            (event.keyCode === 190 && allowFloatValue)
        ) {
            return;
        }

        if (isNaN(Number(key))) event.preventDefault();
    };

    const handleKeyDown = e => {
        if (onKeyDown) onKeyDown(e);
        if (type === 'number') handleNumericKeyDown(e);
    };

    const handleValueChange = (e, value) => {
        if (onChange) {
            const currVal = value ?? e.target.value;
            const newVal = type === 'number' ? (currVal === '0' ? 0 : toNum(currVal)) : currVal;

            const newEvent = {
                ...e,
                target: { ...e.target, value: newVal },
            };
            onChange(newEvent);
        }
    };

    const handleInputChange = e => {
        if (e.target.value.includes('e') && type === 'number') return;

        if (sanitize) {
            const sanitizedValue = DOMPurify.sanitize(e.target.value);
            handleValueChange(e, sanitizedValue);
        } else handleValueChange(e, e.target.value);
    };

    const handleFocus = e => {
        const pickers = ['date', 'datetime-local', 'time', 'week'];
        if (pickers.includes(type)) e.target.showPicker();
    };

    return {
        handleKeyDown,
        handleInputChange,
        handleFocus,
    };
};

export default useInput;
