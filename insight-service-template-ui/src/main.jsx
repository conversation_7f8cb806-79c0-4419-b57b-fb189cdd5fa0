import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Router } from 'react-router-dom';
import 'react-toastify/dist/ReactToastify.css';

import App from './App.jsx';
import CustomToastContainer from './components/molecules/CustomToastContainer/index.jsx';
import baseConfig from './configs/config.yaml';
import ErrorContextProvider from './context/ErrorContext.jsx';
import './index.css';

ReactDOM.createRoot(document.getElementById('root')).render(
    <ErrorContextProvider>
        <CustomToastContainer />
        <Router basename={(baseConfig !== null && baseConfig?.baseRoute) || '/'}>
            <App />
        </Router>
    </ErrorContextProvider>,
);
