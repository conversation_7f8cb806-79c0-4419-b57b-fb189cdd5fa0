# define your end points here
serviceConstantNotFoundInMapping:
    serviceConstants:
        defaultOrigin: API_BASE_PATH
        origins:
            zomatoMock: API_BASE_PATH_INTERNAL

        commonHeaders:
            Accept: application/json
            Authorization: Bearer token

endpointNotFoundInMapping:
    endpointsMapping:
        getCountries:
            origin: exampleOrigin # origin is defined in serviceConstants inside config.yaml
            endPoint: /countries
            method: GET

missingOnGetOrGetStaticDataInTable:
    SOLUTION_ONE:
        - type: Table
          networkCalls:
              onGet: getTable

    SOLUTION_TWO:
        - type: Table
          getData: getTableData

missingOnEditInTable:
    - type: Table
      networkCalls:
          onEdit: editData

missingOnDeleteInTable:
    - type: Table
      networkCalls:
          onDelete: deleteData

missingOnCreateInTable:
    - type: Table
      networkCalls:
          onCreate: createData

missingOnFilterInTable:
    - type: Table
      networkCalls:
          onFilter: filterData

missingOnGetInDropdown:
    SOLUTION_ONE:
        - type: Dropdown
          props:
              networkCall:
                  onGet: getDropdown

    SOLUTION_TWO:
        - type: Dropdown
          props:
              staticOptions:
                  - label: 'Option 1'
                    value: 'value 1'
                  - label: 'Option 2'
                    value: 'value 2'

invalidAccessorKeyInDropdown:
    - type: Dropdown
      props:
          networkCall:
              onGet: getDropdown
              accessorKey: dropdown.data

invalidLabelKeyInDropdown:
    - type: Dropdown
      props:
          networkCall:
              onGet: getDropdown
              labelKey: dropdown.label

invalidValueKeyInDropdown:
    - type: Dropdown
      props:
          networkCall:
              onGet: getDropdown
              valueKey: dropdown.value

wrongPrimaryKeyInTable:
    - type: Table
      primaryKey: primary_id

missingAccessorKeyInTable:
    - type: Table
      accessorKey: table.data
