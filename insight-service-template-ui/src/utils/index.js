import { rankItem } from '@tanstack/match-sorter-utils';
import clsx from 'clsx';
import { twMerge } from 'tailwind-merge';

import toaster from '@/components/molecules/Toaster';
import { COMMON_FORM_PADDING_STYLES, COMMON_FORM_STYLES } from '@/constants';

export const scrollToError = (formControls, errors) => {
    const elements = (errors ?? Object.keys(formControls.formState.errors))
        .map(name => document.getElementsByName(name)[0] || document.getElementById(name))
        .filter(el => !!el);
    elements.sort((a, b) => a.getBoundingClientRect().top - b.getBoundingClientRect().top);

    if (elements.length > 0) {
        let errorElement = elements[0];
        errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        errorElement.focus({ preventScroll: true });
    }
};

export function cn(...inputs) {
    return twMerge(clsx(inputs));
}

export function reportErrorToDataDog(error, props) {
    if (typeof window !== 'undefined' && window?.DD_RUM) {
        window?.DD_RUM?.addError(error, props);
    }
}

export const getUserRoles = roles => {
    const prefix = '';

    const allRoles = roles.map(role => {
        const roles = role.name.replace(prefix, '');
        return roles;
    });

    return allRoles.filter(role => role !== undefined);
};

export const fuzzyFilter = (row, columnId, value, addMeta) => {
    // Rank the item, convert the value to a string by using JSON.stringify and then remove the quotes
    const stringifiedValue = value ? JSON.stringify(value).replace(/['"]+/g, '') : '';
    const cellVal = row.getValue(columnId);
    const stringifiedRowValue = cellVal ? JSON.stringify(cellVal).replace(/['"]+/g, '') : '';

    const itemRank = rankItem(stringifiedRowValue, stringifiedValue);

    // Store the itemRank info
    addMeta({
        itemRank,
    });

    // Return if the item should be filtered in/out
    return itemRank.passed;
};

// url = 'https://example.com/:name.first/:name.last/:id/create'
// data = { name: { first: 'Noman', last : 'Khan' }, id: 123 }
// getURLFromData(url, data) => 'https://example.com/Noman/Khan/123/create'
export const getURLFromData = (url, data) => {
    const urlParts = url.split('/');
    const updatedUrlParts = urlParts.map(part => {
        const key = part.replace(':', '');
        const keyParts = key.split('.');
        let value = data;
        keyParts.forEach(part => {
            value = value[part];
        });
        return value;
    });
    return updatedUrlParts.join('/');
};

export const toNum = v => {
    if (typeof v === 'number') return v;
    if (typeof v === 'string') return +v ? +v : v;
    else return '';
};

export const isSuccess = status => {
    return status?.status === 'success';
};

export const flattenObject = (obj, parentKey = '') => {
    return Object.keys(obj).reduce((acc, key) => {
        const newKey = parentKey ? `${parentKey}.${key}` : key;
        if (typeof obj[key] === 'object' && obj[key] !== null) {
            return { ...acc, ...flattenObject(obj[key], newKey) };
        }
        return { ...acc, [newKey]: obj[key] };
    }, {});
};

export const normalizeConfig = (config, noPadding = false) => {
    if (Array.isArray(config)) {
        return {
            type: 'Box',
            className: COMMON_FORM_STYLES + (noPadding ? '' : COMMON_FORM_PADDING_STYLES),
            contents: config,
        };
    } else return config;
};

export const isStorybook = () => {
    return typeof window !== 'undefined' && window._isStorybook_ === true;
};

export const noop = () => {};

export const wait = ms => new Promise(resolve => setTimeout(resolve, ms));

export const isEmpty = value => {
    if (Array.isArray(value)) {
        return value.length === 0;
    }
    if (typeof value === 'object' && value !== null) {
        return Object.keys(value).length === 0;
    }
    return value === undefined || value === null || value === '';
};

export const uuid = () => {
    let url = URL.createObjectURL(new Blob());

    URL.revokeObjectURL(url);

    return url.substring(url.lastIndexOf('/') + 1);
};

const sanitizeDataRecursive = obj => {
    for (const key in obj) {
        if (key in obj) {
            const value = obj[key];
            if (value && typeof value === 'object' && 'label' in value && 'value' in value) {
                obj[key] = null;
            }

            if (value && typeof value === 'object' && value !== null) {
                sanitizeDataRecursive(value);
            }

            if (
                typeof value === 'string' ||
                typeof value === 'number' ||
                typeof value === 'boolean' ||
                value === null ||
                value === undefined ||
                Array.isArray(value)
            ) {
                obj[key] = null;
            }
        }
    }
    return obj;
};

export const resetFormState = (formControls, values, recursivelySetValues = false) => {
    if (!formControls) return;

    if (recursivelySetValues) {
        formControls.reset(
            prev => {
                return values ?? sanitizeDataRecursive(prev);
            },
            {
                keepDefaultValues: true,
            },
        );
    } else {
        formControls.reset(
            prev => {
                return (
                    values ??
                    Object.keys(prev || {}).reduce((acc, key) => {
                        acc[key] = null;
                        return acc;
                    }, {})
                );
            },
            {
                keepDefaultValues: true,
            },
        );
    }
    formControls.clearErrors();
};

export const capitalizeFirstLetter = str => {
    if (!str) return '';
    return str
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');
};

export const toastError = (error, msg) => {
    const message =
        error?.response?.data?.status?.message ||
        error?.data?.status?.message ||
        error?.status?.message ||
        msg ||
        'Something went wrong';
    if (error?.code === 'ERR_NETWORK') {
        return;
    }
    toaster.error(capitalizeFirstLetter(message));
};
