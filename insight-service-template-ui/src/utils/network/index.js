export const parseDataForApiRequest = formData => {
    const parsedData = {};

    const recursiveParse = data => {
        if (Array.isArray(data) && data.length > 0 && data[0]?.label !== undefined && data[0]?.value !== undefined) {
            return data.map(item => item.value);
        } else if (typeof data === 'object' && data !== null) {
            if ('label' in data && 'value' in data) {
                return data.value;
            } else {
                const result = {};
                Object.entries(data).forEach(([key, value]) => {
                    result[key] = recursiveParse(value);
                });
                return result;
            }
        } else if (typeof data === 'string') {
            return data.trim();
        }
        return data; // Return the original data for other types
    };

    Object.entries(formData || {}).forEach(([key, data]) => {
        parsedData[key] = recursiveParse(data);
    });

    return parsedData;
};

export const getValueUsingAccessorKey = (data, key) => {
    const keys = key?.split('.');
    let value = data;
    keys?.forEach(k => {
        if (value === undefined) return;
        value = value[k];
    });
    return value;
};
