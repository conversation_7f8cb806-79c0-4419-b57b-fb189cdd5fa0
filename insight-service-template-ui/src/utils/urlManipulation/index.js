import { QUERY_PARAM_IDENTIFIER } from '../../constants';

export const extractVariables = url => {
    const regex = /\/:(\w+)/g;
    const variables = [];
    let match;
    while ((match = regex.exec(url)) !== null) {
        variables.push(match[1]);
    }
    return variables;
};

export const updateQueryParam = ({ queryParam, value }) => {
    const url = new URL(window.location);
    url.searchParams.set(queryParam, value);
    window.history.replaceState({}, '', url);
};

export const deleteQueryParam = ({ queryParam }) => {
    const url = new URL(window.location);
    url.searchParams.delete(queryParam);
    window.history.replaceState({}, '', url);
};

export const updateMultipleQueryParams = ({ queryParam, prefix = '' }) => {
    const url = new URL(window.location);
    Object.entries(queryParam).forEach(([key, value]) => {
        if (value) {
            url.searchParams.set(`${prefix}${QUERY_PARAM_IDENTIFIER.SEPARATOR}${key}`, value);
        }
    });
    window.history.replaceState({}, '', url);
};

export const clearAllQueryParams = ({ prefix }) => {
    const url = new URL(window.location);
    const searchParams = new URLSearchParams(location.search);
    const identifier = `${prefix}${QUERY_PARAM_IDENTIFIER.SEPARATOR}`;
    const entries = searchParams.entries();
    for (let [key] of entries) {
        if (key.startsWith(identifier)) {
            searchParams.delete(key);
        }
    }
    url.search = searchParams.toString();
    window.history.replaceState({}, '', url);
};

export const readQueryParams = ({ prefix }) => {
    const searchParams = new URLSearchParams(location.search);
    const identifier = `${prefix}${QUERY_PARAM_IDENTIFIER.SEPARATOR}`;
    const identifierLength = identifier.length;
    const entries = searchParams.entries();
    const result = {};
    for (let [key, value] of entries) {
        if (key.startsWith(identifier)) {
            const field = key.slice(identifierLength);
            result[field] = value;
        }
    }
    return result;
};
