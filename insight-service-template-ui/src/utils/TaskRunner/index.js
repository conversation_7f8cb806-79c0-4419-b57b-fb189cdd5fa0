export class TaskRunner {
    constructor() {
        this.queue = [];
        this.running = false;
    }

    addTask(task) {
        this.queue.push(task);
        if (!this.running) {
            this.run();
        }
    }

    // Execute tasks in the queue
    async run() {
        this.running = true;
        let previousOutput = undefined;
        while (this.queue.length > 0) {
            const task = this.queue.shift();
            try {
                previousOutput = await task(previousOutput);
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error('Error executing task:', error);
            }
        }
        this.running = false;
    }
}
