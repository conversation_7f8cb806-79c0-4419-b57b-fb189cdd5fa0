import { setNetworkConfig } from '@zomato/networking';
import { useEffect, useLayoutEffect, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { useLocation } from 'react-router-dom';

import ErrorDialog from './components/molecules/Error';
import Navbar from './components/molecules/navbar';
import PageFallback from './components/molecules/PageFallback';
import Routes from './components/molecules/Routes';
import Sidebar from './components/molecules/sidebar';
import baseConfig from './configs/config.yaml';
import sidebarConfig from './configs/sidebar.yaml';
import { LOGIN_PATHS } from './constants';
import useErrorContext from './hooks/useErrorContext';
import useUser from './hooks/useUser';
import { cn } from './utils';

function App() {
    const [sidebarOpen, setSidebarOpen] = useState(sidebarConfig ? true : false);
    const { checkUserAuth } = useUser();
    const location = useLocation();
    const transition = 'transition-all duration-300 ease-in-out';

    const { errors } = useErrorContext();
    const isProdEnv = import.meta.env.PROD;

    const isLoginPage = LOGIN_PATHS.includes(location.pathname);
    const canShowSidebar = !isLoginPage;

    useLayoutEffect(() => {
        setNetworkConfig({
            serviceConstants: (baseConfig !== null && baseConfig?.serviceConstants) || '',
            endpointsMapping: (baseConfig !== null && baseConfig?.endpointsMapping) || '',
        });
    }, []);

    useEffect(() => {
        checkUserAuth();
    }, []);

    return (
        <>
            <div className="flex min-h-screen min-w-full flex-col">
                <header className="fixed z-10 flex h-16 min-w-full items-center justify-center border border-b-gray-200 bg-white">
                    <Navbar
                        title={baseConfig !== null && baseConfig?.dashboardTitle}
                        root={baseConfig !== null && baseConfig?.baseRoute}
                    />
                </header>

                <div className="mt-16 flex h-[calc(100%-64px)] overflow-y-auto">
                    {canShowSidebar && (
                        <div
                            className={cn(
                                'fixed left-0 top-16 z-10 min-w-60',
                                {
                                    '-left-full': !sidebarOpen,
                                },
                                transition,
                            )}
                        >
                            <Sidebar
                                config={sidebarConfig}
                                setSidebarOpen={setSidebarOpen}
                                sidebarOpen={sidebarOpen}
                                transition={transition}
                            />
                        </div>
                    )}
                    <div
                        className={cn('flex min-h-[calc(100vh-64px)] flex-col', {
                            ' w-full  lg:ml-[240px] lg:min-w-[calc(100vw-240px)] lg:max-w-[calc(100vw-240px)]':
                                canShowSidebar,
                            'w-full': !canShowSidebar,
                        })}
                    >
                        <ErrorBoundary fallbackRender={PageFallback}>
                            <Routes />
                        </ErrorBoundary>
                    </div>
                </div>
            </div>
            {errors?.length && !isProdEnv ? <ErrorDialog message={errors[errors.length - 1]} /> : null}
        </>
    );
}

export default App;
