import { createContext, useState } from 'react';

export const ErrorContext = createContext(null);

const ErrorContextProvider = ({ children }) => {
    const [errors, setErrors] = useState([]);

    const pushError = error => {
        // setErrors([error]);
    };

    return <ErrorContext.Provider value={{ errors, setErrors, pushError }}>{children}</ErrorContext.Provider>;
};

export default ErrorContextProvider;
