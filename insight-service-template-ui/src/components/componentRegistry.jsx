import Box from '@atoms/Box';
import Header from '@atoms/Header';
import ZIcon from '@atoms/ZIcon';
import Accordion from '@molecules/Accordion';
import CreateButton from '@molecules/CreateButton';
import Dropdown from '@molecules/Dropdown';
import FileUpload from '@molecules/FileUpload';
import FilterButton from '@molecules/FilterButton';
import Form from '@molecules/Form';
import GetStarted from '@molecules/GetStarted';
import InputBox from '@molecules/InputBox';
import Markdown from '@molecules/Markdown';
import MarkdownEditor from '@molecules/MarkdownEditor';
import MultiInput from '@molecules/MultiInput';
import SideDrawer from '@molecules/SideDrawer';
import Table from '@molecules/Table';
import CustomStatusCell from '@molecules/Table/Cells/CustomStatusCell';
import DateCell from '@molecules/Table/Cells/DateCell';
import EditModal from '@molecules/Table/Cells/EditModal';
import DisabledView from '@molecules/Table/Cells/EditModal/DisabledView';
import Tabs from '@molecules/Tabs';

import CreateTemplate from '@/pages/CreateTemplate';
import EditTemplate from '@/pages/EditTemplate';
import TemplateList from '@/pages/TemplateList';
import VersionsList from '@/pages/VersionList';

import Button from './atoms/Button';
import withFormController from './HOCs/withFormController';
import ArrayField from './molecules/ArrayField';
import Editor from './molecules/Editor';
import MultiFields from './molecules/MultiFields';

export const componentRegistry = {
    Box: {
        '1.0': Box,
    },
    ZIcon: {
        '1.0': ZIcon,
    },
    Input: {
        '1.0': props => withFormController({ ...props, Component: InputBox }),
        '2.0': InputBox,
    },
    MultiFields: {
        '1.0': props => withFormController({ ...props, Component: MultiFields }),
        '2.0': MultiFields,
    },
    MultiInput: {
        '1.0': props => withFormController({ ...props, Component: MultiInput }),
        '2.0': MultiInput,
    },
    Dropdown: {
        '1.0': props => withFormController({ ...props, Component: Dropdown }),
        '2.0': Dropdown,
    },
    ArrayField: {
        '1.0': props => withFormController({ ...props, Component: ArrayField }),
        '2.0': ArrayField,
    },
    FileUpload: {
        '1.0': props => withFormController({ ...props, Component: FileUpload }),
        '2.0': FileUpload,
    },
    Editor: {
        '1.0': props => withFormController({ ...props, Component: Editor }),
    },
    Table: {
        '1.0': props => <Table {...props} />,
    },
    Header: {
        '1.0': Header,
    },
    Button: {
        '1.0': Button,
    },
    FilterButton: {
        '1.0': FilterButton,
    },
    CreateButton: {
        '1.0': CreateButton,
    },
    EditModal: {
        '1.0': EditModal,
    },
    Form: {
        '1.0': props => <Form {...props} />,
    },
    GetStarted: {
        '1.0': GetStarted,
    },
    Accordion: {
        '1.0': Accordion,
    },
    MarkdownEditor: {
        '1.0': MarkdownEditor,
    },
    Markdown: {
        '1.0': Markdown,
    },
    Tabs: {
        '1.0': Tabs,
    },
    SideDrawer: {
        '1.0': SideDrawer,
    },
    Date: {
        '1.0': DateCell,
    },
    StatusCell: {
        '1.0': CustomStatusCell,
    },
    DisabledView: {
        '1.0': props => withFormController({ ...props, Component: DisabledView }),
        '2.0': DisabledView,
    },
    TemplateList: {
        '1.0': TemplateList,
    },
    VersionsList: {
        '1.0': VersionsList,
    },
    CreateTemplate: {
        '1.0': CreateTemplate,
    },
    EditTemplate: {
        '1.0': EditTemplate,
    },
};
