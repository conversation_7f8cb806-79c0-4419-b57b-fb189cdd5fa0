.checkbox-wrapper {
    --check-color: #34b93d;
    --box-border-color: #000;
    --box-border-color-checked: #34b93d;
    --background-color: #fff;
    --checkbox-height: 1.3rem;
}

@keyframes bottomcheck {
    0% {
        height: 0;
    }
    100% {
        height: calc(var(--checkbox-height) / 2);
    }
}

@keyframes topcheck {
    0% {
        height: 0;
    }
    50% {
        height: 0;
    }
    100% {
        height: calc(var(--checkbox-height));
    }
}

.checkbox-wrapper input[type='checkbox'] {
    display: none;
}

.checkbox-wrapper .check-box {
    height: var(--checkbox-height);
    width: var(--checkbox-height);
    background-color: transparent;
    border: calc(var(--checkbox-height) * 0.1) solid var(--box-border-color);
    border-radius: 5px;
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    transition: border-color ease 0.2s;
    cursor: pointer;
}
.checkbox-wrapper .check-box::before,
.checkbox-wrapper .check-box::after {
    box-sizing: border-box;
    position: absolute;
    height: 0;
    width: calc(var(--checkbox-height) * 0.15);
    background-color: var(--check-color);
    display: inline-block;
    transform-origin: left top;
    border-radius: 5px;
    content: ' ';
    transition: opacity ease 0.2;
}
.checkbox-wrapper .check-box::before {
    top: calc(var(--checkbox-height) * 0.72);
    left: calc(var(--checkbox-height) * 0.41);
    box-shadow: 0 0 0 calc(var(--checkbox-height) * 0.05) var(--background-color);
    transform: rotate(-135deg);
}
.checkbox-wrapper .check-box::after {
    top: calc(var(--checkbox-height) * 0.37);
    left: calc(var(--checkbox-height) * 0.05);
    transform: rotate(-45deg);
}

.checkbox-wrapper input[type='checkbox']:checked + .check-box,
.checkbox-wrapper .check-box.checked {
    border-color: var(--box-border-color-checked);
}
.checkbox-wrapper input[type='checkbox']:checked + .check-box::after,
.checkbox-wrapper .check-box.checked::after {
    height: calc(var(--checkbox-height) / 2);
    animation: bottomcheck 0.1s ease 0s forwards;
}
.checkbox-wrapper input[type='checkbox']:checked + .check-box::before,
.checkbox-wrapper .check-box.checked::before {
    height: calc(var(--checkbox-height));
    animation: topcheck 0.2s ease 0s forwards;
}
