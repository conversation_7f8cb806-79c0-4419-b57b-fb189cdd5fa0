import './styles.css';

const Checkbox = ({ label = '', checked, onChange }) => {
    const handleOnChange = e => {
        onChange(e.target.checked);
    };

    return (
        <div className="checkbox-wrapper ">
            <div className="checkbox-wrapper flex items-center gap-[0.6em] text-sm font-normal">
                <input type="checkbox" id={`checkbox-${label}`} checked={checked} onChange={handleOnChange} />
                <label className="check-box" htmlFor={`checkbox-${label}`}></label>
                <label htmlFor={`checkbox-${label}`}>{label}</label>
            </div>
        </div>
    );
};

export default Checkbox;
