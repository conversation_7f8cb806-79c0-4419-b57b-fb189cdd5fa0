import { useState } from 'react';

import Markdown from '../../molecules/Markdown';

const TextLimiter = ({ text = '', limit, className, hideToggleBtn }) => {
    const [showMore, setShowMore] = useState(false);
    const parsedText = text.toString();
    const textStyles = 'text-left break-words w-max max-w-[300px]';

    return (
        <div className={className}>
            {parsedText.length > limit ? (
                <p className={textStyles}>
                    <Markdown>{showMore ? text : `${parsedText?.substring(0, limit)}...`}</Markdown>
                    {!hideToggleBtn && (
                        <span onClick={() => setShowMore(!showMore)} className="cursor-pointer text-xs text-blue-500">
                            {showMore ? ' Show less' : ' Show more'}
                        </span>
                    )}
                </p>
            ) : (
                <p className={textStyles}>
                    <Markdown>{text}</Markdown>
                </p>
            )}
        </div>
    );
};

export default TextLimiter;
