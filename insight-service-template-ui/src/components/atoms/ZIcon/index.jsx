import { cn } from '@/utils';

import './index.css';

const ZIcon = ({ unicode, className, style, ...props }) => {
    return (
        <i
            className={cn('z-icon select-none', className)}
            {...props}
            style={{
                fontFamily: 'Wasabi',
                '--unicode': `"\\${unicode.toUpperCase()}"`,
                ...style,
            }}
        />
    );
};

export default ZIcon;
