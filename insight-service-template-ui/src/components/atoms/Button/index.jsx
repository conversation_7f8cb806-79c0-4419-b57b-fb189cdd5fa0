/* eslint-disable tailwindcss/no-custom-classname */
import { PropTypes } from 'prop-types';

import { cn } from '@/utils';

import ZIcon from '../ZIcon';

/* 
    text-white bg-blue-500 border border-blue-500
    text-white !bg-gray-400 border !border-gray-400 hover:!bg-gray-400 cursor-not-allowed
    text-white bg-blue-400 border border-blue-400 hover:bg-blue-400
    border text-blue-500 border-blue-500
    text-gray-400 !bg-gray-100 border !border-gray-400 hover:!bg-gray-100
    text-blue-500 bg-blue-100 border border-blue-400 hover:bg-blue-100
    text-blue-500 bg-transparent
    text-gray-400
    text-white bg-blue-400 border border-blue-400
    */

const Button = ({
    variant = 'contained',
    size = 'md',
    disabled = false,
    text = 'Button',
    iconUnicode,
    iconPos = 'left',
    color = 'blue',
    block = false,
    isLoading = false,
    textLoading,
    onClick,
    className,
    contentAlign = 'center',
    children,
    ...restProps
}) => {
    const sizes = {
        sm: 'px-3 py-1 text-[10px]',
        md: 'px-3.5 py-2 text-xs',
        lg: 'px-4 py-2 text-sm',
    };

    const baseStyles = cn({
        'flex h-max items-center gap-2 rounded-md font-normal': true,
        'justify-center': contentAlign === 'center',
        'justify-start': contentAlign === 'left',
        'justify-end': contentAlign === 'right',
    });

    let btnClassName = null,
        btnOutlineClassName = null;

    switch (color) {
        case 'red':
            btnClassName = 'hover:bg-red-600 border-red-500';
            break;
        case 'blue':
            btnClassName = 'hover:bg-blue-600 border-blue-500';
            break;
        case 'sky':
            btnClassName = 'hover:bg-sky-600 border-sky-500 bg-sky-500';
            break;
        case 'orange':
            btnClassName = 'hover:bg-[#FB5607] border-[#FB5607] bg-[#FB5607]';
            break;
        case 'green':
            btnClassName = 'hover:bg-[#57cc99] border-[#57cc99] bg-[#57cc99]';
            break;
    }

    switch (color) {
        case 'red':
            btnOutlineClassName = 'hover:bg-red-50 border-red-500';
            break;
        case 'blue':
            btnOutlineClassName = 'hover:bg-blue-50 border-blue-500';
            break;
        case 'sky':
            btnOutlineClassName = 'hover:bg-sky-50 border-sky-500';
            break;
        case 'orange':
            btnOutlineClassName = 'hover:bg-[#FB5607] border-[#FB5607]';
            break;
        case 'green':
            btnOutlineClassName = 'hover:bg-[#57cc99] border-[#57cc99]';
            break;
    }

    const variants = {
        contained: {
            mainStyles: `text-white bg-${color}-500 border border-${color}-500 ${btnClassName}`,
            disabledStyles: `text-white !bg-gray-400 border !border-gray-400 hover:!bg-gray-400 cursor-not-allowed	`,
            loadingStyles: `text-white bg-${color}-400 border border-${color}-400 hover:bg-${color}-400`,
        },
        outlined: {
            mainStyles: `border text-${color}-500 border-${color}-500 ${btnOutlineClassName}`,
            disabledStyles: `text-gray-400 !bg-gray-100 border !border-gray-400 hover:!bg-gray-100`,
            loadingStyles: `text-${color}-500 bg-${color}-100 border border-${color}-400 hover:bg-${color}-100`,
        },
        text: {
            mainStyles: `text-${color}-500 bg-transparent`,
            disabledStyles: `text-gray-400`,
            loadingStyles: `text-white bg-${color}-400 border border-${color}-400`,
        },
    };

    const dynamicClass = cn(
        baseStyles,
        sizes[size],
        {
            '!text-red-500': variant === 'outlined' && color === 'red',
            'bg-red-500': variant === 'contained' && color === 'red',
            'w-full': block,
            'w-max': !block,
        },
        `${isLoading ? variants[variant].disabledStyles : ''} ${
            disabled ? variants[variant].disabledStyles : variants[variant].mainStyles
        }`,
        className,
    );

    const handleButtonClick = e => {
        if (!disabled && !isLoading && onClick) {
            onClick(e);
        }
    };

    return (
        <button className={dynamicClass} onClick={handleButtonClick} {...restProps}>
            {iconUnicode && iconPos === 'left' && <ZIcon unicode={iconUnicode} />}
            {isLoading ? textLoading || (text ?? children) : (text ?? children)}
            {iconUnicode && iconPos === 'right' && <ZIcon unicode={iconUnicode} />}
        </button>
    );
};

export default Button;

Button.propTypes = {
    variant: PropTypes.oneOf(['contained', 'text', 'outlined']),
    size: PropTypes.oneOf(['sm', 'md', 'lg']),
    disabled: PropTypes.bool,
    iconPos: PropTypes.oneOf(['left', 'right']),
    block: PropTypes.bool,
    isLoading: PropTypes.bool,
};
