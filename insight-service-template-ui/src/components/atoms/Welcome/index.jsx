const Welcome = () => {
    return (
        <div className="flex h-[calc(100vh-64px)] w-full flex-1 items-center justify-center">
            <div className="text-center">
                <svg className="w-auto text-black sm:h-64" viewBox="0 0 233 156">
                    <g clipPath="url(#clip0_35_3310)">
                        <path
                            d="M213.688 16.41C219.093 15.647 223.691 10.946 224.333 5.52502C223.875 10.193 226.768 14.994 231.109 16.77C228.886 16.896 226.919 18.428 225.779 20.34C224.64 22.253 224.215 24.505 224.006 26.721C223.887 21.227 218.979 16.273 213.688 16.41Z"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M7.77301 85.6941C13.178 84.9311 17.776 80.2301 18.418 74.8091C17.96 79.4771 20.853 84.2781 25.194 86.0541C22.971 86.1801 21.004 87.7121 19.864 89.6241C18.725 91.5371 18.3 93.7891 18.091 96.0051C17.972 90.5111 13.064 85.5571 7.77301 85.6941Z"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M71.1941 100.591L46.7531 152.729L57.6901 152.709L81.2481 106.386L71.1941 100.591Z"
                            fill="#010101"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M123.027 132.037L119.432 155.24L129.606 155.162L132.976 131.845L123.027 132.037Z"
                            fill="#010101"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M68.22 152.478C62.886 143.228 63.33 142.163 57.996 132.914C54.262 126.439 50.121 119.581 42.719 115.87C38.726 113.868 33.255 113.09 29.8 115.53C21.739 121.222 28.725 136.274 36.848 152.459C49.191 152.852 53.595 152.478 68.22 152.478Z"
                            fill="#55D087"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M35.345 118.631C39.87 132.702 43.551 138.893 50.003 152.478"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M39.879 152.478C30.735 138.61 24.951 129.669 12.488 125.791C10.045 125.031 7.23603 124.684 4.96103 125.644C2.38703 126.729 0.398025 129.211 0.803025 131.634C2.35803 140.927 7.88503 146.053 11.332 152.761C22.453 152.819 31.468 152.435 39.879 152.478Z"
                            fill="#55D087"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M7.97601 127.177C14.321 138.201 15.839 141.774 23.954 151.994"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M145.573 119.137C156.555 109.948 167.536 100.76 179.108 92.563C187.461 95.815 195.47 99.839 203.414 104.957C210.331 93.861 218.175 83.271 226.853 73.316"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M211.951 75.208C216.95 74.203 221.949 73.198 226.949 72.193C227.808 76.293 228.424 80.438 228.792 84.602"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M150.672 126.52C150.438 134.838 150.501 143.162 150.86 151.476C154.002 151.678 157.144 151.88 160.286 152.082C160.728 143.481 160.652 134.857 160.057 126.265C157.097 126.717 154.046 126.64 150.672 126.52Z"
                            fill="#7B5FF1"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M172.512 111.637C173.122 125.092 173.732 138.547 174.342 152.002C178.303 151.844 182.264 151.685 186.225 151.527C185.163 138.224 184.101 124.921 183.04 111.618C179.314 111.664 175.589 111.71 172.512 111.637Z"
                            fill="#7B5FF1"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M198.55 125.607C197.927 134.392 198.065 143.224 198.964 151.988C201.877 151.813 204.79 151.638 207.704 151.463C207.9 151.451 208.114 151.433 208.256 151.306C208.425 151.156 208.432 150.91 208.428 150.692C208.27 142.039 208.112 133.385 207.954 124.732C204.889 124.955 201.824 125.178 198.55 125.607Z"
                            fill="#7B5FF1"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M220.736 151.118C220.222 132.094 220.091 113.061 221.427 94.753C224.658 94.536 227.89 94.319 231.121 94.101C232.419 113.254 230.963 132.089 231.744 151.266C228.297 151.031 227.323 151.424 220.736 151.118Z"
                            fill="#7B5FF1"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M51.0741 42.401C42.2481 67.834 45.0041 97.272 63.7581 118.266C82.5121 139.26 118.435 148.956 145.674 136.828C173.376 124.494 185.022 94.14 181.456 67.306C178.408 44.376 165.429 21.852 143.505 8.77504C121.581 -4.30196 68.0561 -6.53296 51.0741 42.401Z"
                            fill="#FF8B37"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M68.6791 50.503C62.3161 68.839 64.3031 90.062 77.8231 105.198C91.3431 120.334 117.242 127.324 136.879 118.58C156.85 109.688 165.247 87.805 162.675 68.458C160.478 51.927 151.121 35.688 135.315 26.26C119.509 16.832 80.9211 15.224 68.6781 50.502L68.6791 50.503Z"
                            fill="white"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M85.6781 58.331C81.6931 69.814 82.9371 83.105 91.4051 92.584C99.8721 102.063 116.092 106.441 128.39 100.965C140.897 95.396 146.156 81.692 144.545 69.576C143.169 59.223 137.309 49.053 127.41 43.149C117.511 37.245 93.3451 36.238 85.6781 58.331Z"
                            fill="#FF8B37"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M102.833 66.227C101.248 70.795 101.743 76.081 105.111 79.852C108.479 83.622 114.931 85.364 119.822 83.186C124.797 80.971 126.889 75.52 126.248 70.7C125.701 66.582 123.37 62.537 119.432 60.188C115.495 57.839 105.883 57.439 102.833 66.227Z"
                            fill="white"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M169.826 35.176C170.375 32.575 171.342 28.512 171.891 25.912C171.98 25.489 172.076 25.052 172.328 24.701C172.575 24.359 172.947 24.133 173.309 23.916C177.647 21.319 181.985 18.721 186.322 16.124C185.71 19.072 185.204 22.042 184.807 25.027C187.443 25.461 190.078 25.895 192.714 26.33C188.72 30.155 179.505 36.308 179.505 36.308C179.505 36.308 172.594 35.911 169.826 35.176Z"
                            fill="#7B5FF1"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M117.476 72.1931C137.741 57.7461 158.006 43.2991 178.271 28.8521"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                        <path
                            d="M117.122 70.1011C116.912 71.1591 116.716 72.3201 117.232 73.2671C117.675 74.0811 118.564 74.5581 119.456 74.8091"
                            stroke="#010101"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        ></path>
                    </g>
                    <defs>
                        <clipPath id="clip0_35_3310">
                            <rect width="232.494" height="155.99" fill="white"></rect>
                        </clipPath>
                    </defs>
                </svg>
                <h1 className="mt-6 text-2xl font-bold tracking-tight text-gray-900 sm:text-4xl">hello!</h1>

                <p className="mt-4 text-gray-500">Select an option from the side bar to proceed.</p>
            </div>
        </div>
    );
};

export default Welcome;
