import ZIcon from '@atoms/ZIcon';

import { cn } from '@/utils';

const SideDrawer = ({ children, open, closeDrawer, headerText, hideHeader }) => {
    return (
        <>
            {open && (
                <div
                    className="fixed left-0 top-16 z-10 h-[calc(100vh-64px)] w-full bg-black/20"
                    onClick={closeDrawer}
                ></div>
            )}
            {
                <div
                    className={cn(
                        'fixed top-16 z-20 h-[calc(100vh-64px)] w-full border-l bg-white transition-all md:w-[600px]',
                        {
                            'right-0': open,
                            '-right-full': !open,
                        },
                    )}
                >
                    {open && (
                        <div className="flex h-[calc(100vh-64px)] flex-col">
                            {!hideHeader && (
                                <div className="flex items-center justify-between border-b px-4 py-2">
                                    <h2 className="text-lg font-medium">{headerText || 'Create form'}</h2>
                                    <div
                                        className="ml-auto flex size-8 cursor-pointer items-center justify-center rounded-md hover:bg-gray-100"
                                        onClick={closeDrawer}
                                    >
                                        <ZIcon unicode={'E921'} className="text-2xl" />
                                    </div>
                                </div>
                            )}
                            <div className="flex max-h-full flex-1 flex-col overflow-y-auto">{children}</div>
                        </div>
                    )}
                </div>
            }
        </>
    );
};

export default SideDrawer;
