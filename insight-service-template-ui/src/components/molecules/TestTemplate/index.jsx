import { useEffect } from 'react';

import useCustomFormControls from '@/hooks/useCustomFormControls';
import { scrollToError } from '@/utils';

import FormSideDrawer from '../FormSideDrawer';
import Modal from '../Modal';
import Table from '../Table';
import { getTestTemplateConfig } from './config';
import { useTestTemplate } from './hook/useTestTemplate';

const TestTemplate = ({ filters, templateFormControls, setTestedTemplate }) => {
    const formControls = useCustomFormControls();
    const handleFormValidationError = scrollToError(formControls);

    const { testTemplate, testingTemplate, showOutputModal, setShowOutputModal } = useTestTemplate({
        formControls,
        templateFormControls,
        setTestedTemplate,
    });

    useEffect(() => {
        const currFilter = formControls.watch('filters') || [];

        let resultArr = [];

        const notPresentInCurrFilter = filters.filter(filter => !currFilter.some(item => item.filter === filter.value));

        resultArr = [
            currFilter?.filter(filter => {
                return filters.some(item => item.value === filter.field);
            }),
            ...notPresentInCurrFilter.map(filter => ({
                field: filter?.value,
            })),
        ];

        formControls.setValue('filters', resultArr);
    }, [filters]);

    return (
        <>
            <FormSideDrawer
                header="Test template"
                config={getTestTemplateConfig({
                    filters,
                    interval: formControls.watch('interval'),
                    queryType: templateFormControls.watch('query.type'),
                })}
                action="test"
                isSubmitting={testingTemplate}
                formControls={formControls}
                onSubmit={formControls.handleSubmit(testTemplate, handleFormValidationError)}
            />
            <Modal
                title="Output"
                open={showOutputModal.isOpen}
                setOpen={() =>
                    setShowOutputModal({
                        isOpen: false,
                        data: null,
                    })
                }
            >
                <div className="min-w-0 p-4 sm:min-w-[700px]">
                    <Table
                        columns={showOutputModal.data?.columns}
                        data={showOutputModal.data?.rows || []}
                        noDataText="No data found"
                        actions={<></>}
                    />
                </div>
            </Modal>
        </>
    );
};

export default TestTemplate;
