import moment from 'moment';

import {
    COMMON_FORM_PADDING_STYLES,
    COMMON_FORM_STYLES,
    FILTERS_TYPE,
    OPERATIONS_OPTIONS,
    QUERY_FILTER_TYPES,
    QUERY_TYPES,
} from '@/constants';
import { isEmpty } from '@/utils';

export const getTestTemplateConfig = ({ filters, interval, queryType }) => {
    const start_time = interval?.start_time;
    const end_time = interval?.end_time;

    const intervalConfig =
        queryType === QUERY_TYPES.DSL
            ? [
                  {
                      type: 'Input',
                      id: 'interval.start_time',
                      label: 'Start time',
                      inputType: 'datetime-local',
                      max: end_time ? moment(end_time).format('YYYY-MM-DDTHH:mm') : end_time,
                      transform: {
                          output: e => {
                              if (isEmpty(e.target.value)) return e;
                              return moment(e.target.value).valueOf();
                          },
                          input: e => {
                              if (isEmpty(e)) return e;
                              return moment(new Date(e)).format('YYYY-MM-DDTHH:mm');
                          },
                      },
                      rules: {
                          required: 'Start time is required',
                      },
                  },
                  {
                      type: 'Input',
                      id: 'interval.end_time',
                      label: 'End time',
                      inputType: 'datetime-local',
                      min: start_time ? moment(start_time).format('YYYY-MM-DDTHH:mm') : start_time,
                      transform: {
                          output: e => {
                              if (isEmpty(e.target.value)) return e;
                              return moment(e.target.value).valueOf();
                          },
                          input: e => {
                              if (isEmpty(e)) return e;
                              return moment(new Date(e)).format('YYYY-MM-DDTHH:mm');
                          },
                      },
                      rules: {
                          required: 'End time is required',
                      },
                  },
                  {
                      type: 'Input',
                      label: 'Datetime field',
                      id: 'interval.datetime_field',
                      rules: {
                          required: 'Datetime field is required',
                      },
                  },
              ]
            : [];

    return {
        type: 'Box',
        className: COMMON_FORM_STYLES + COMMON_FORM_PADDING_STYLES,
        contents: [
            {
                type: 'Box',
                className: 'flex flex-col gap-4',
                contents: intervalConfig,
            },
            {
                type: 'MultiFields',
                id: 'filters',
                label: 'Value type',
                addBtnText: 'Add more',
                getConfig: ({ prefixId, formControls }) => {
                    let filterConfig = {};
                    const filterType = formControls.watch(`${prefixId}.value_type`);
                    switch (filterType) {
                        case QUERY_FILTER_TYPES.STRING_VALUE:
                            filterConfig = {
                                type: 'Input',
                                id: `value`,
                                label: 'Value',
                                placeholder: 'Enter value',
                            };
                            break;
                        case QUERY_FILTER_TYPES.INT_VALUE:
                            filterConfig = {
                                type: 'Input',
                                id: `value`,
                                label: 'Value',
                                allowFloatValue: false,
                                placeholder: 'Enter value',
                                inputType: 'number',
                            };
                            break;
                        case QUERY_FILTER_TYPES.BOOL_VALUE:
                            filterConfig = {
                                type: 'Dropdown',
                                id: `value`,
                                label: 'Value',
                                placeholder: 'Select value',
                                options: [
                                    { label: 'True', value: true },
                                    { label: 'False', value: false },
                                ],
                            };
                            break;
                        case QUERY_FILTER_TYPES.DOUBLE_VALUE:
                            filterConfig = {
                                type: 'Input',
                                id: `value`,
                                label: 'Value',
                                allowFloatValue: true,
                                placeholder: 'Enter value',
                                inputType: 'number',
                            };
                            break;
                        case QUERY_FILTER_TYPES.ARRAY_VALUE:
                            filterConfig = {
                                type: 'ArrayField',
                                id: `value`,
                                label: 'Array values',
                            };
                            break;
                        default:
                            break;
                    }

                    return [
                        {
                            type: 'Dropdown',
                            id: `op`,
                            label: 'Operation',
                            options: OPERATIONS_OPTIONS,
                        },
                        {
                            type: 'Dropdown',
                            options: filters,
                            id: `field`,
                            label: 'Filter',
                        },
                        {
                            type: 'Dropdown',
                            options: FILTERS_TYPE,
                            id: `value_type`,
                            label: 'Value',
                            afterChange: () => formControls.resetField(`${prefixId}.value`),
                        },
                        filterConfig,
                    ];
                },
                transform: {
                    input: data => {
                        return data?.filter(item => !isEmpty(item?.sql_column) && !isEmpty(item?.source_column));
                    },
                    output: data => {
                        return data?.filter(item => !isEmpty(item?.sql_column) && !isEmpty(item?.source_column));
                    },
                },
            },
        ],
    };
};
