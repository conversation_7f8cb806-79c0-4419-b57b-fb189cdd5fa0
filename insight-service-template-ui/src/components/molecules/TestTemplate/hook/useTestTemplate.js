import { request } from '@zomato/networking';
import { useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { QUERY_FILTER_TYPES } from '@/constants';
import { isSuccess, resetFormState, toastError, uuid } from '@/utils';

export const useTestTemplate = ({ formControls, templateFormControls, setTestedTemplate }) => {
    const [testingTemplate, setTestingTemplate] = useState(false);
    const [searchParams, setSearchParams] = useSearchParams();
    const [showOutputModal, setShowOutputModal] = useState({
        isOpen: false,
        data: null,
    });

    const testTemplate = async data => {
        data = {
            ...data,
            filters: data?.filters?.map(filter => {
                const { value_type, ...otherFilters } = filter;
                if (value_type === QUERY_FILTER_TYPES.ARRAY_VALUE) {
                    return {
                        ...otherFilters,
                        value: {
                            [value_type]: {
                                values: filter?.value?.value?.map(value => ({
                                    [filter?.value?.type]: value,
                                })),
                            },
                        },
                    };
                }
                return {
                    ...otherFilters,
                    value: {
                        [value_type]: filter.value,
                    },
                };
            }),
        };
        try {
            setTestingTemplate(true);
            const payload = {
                template: templateFormControls?.getValues(),
                querier: data,
            };

            const res = await request('testTemplate', {
                body: payload,
            });

            const status = res.data?.status;
            setTestedTemplate && setTestedTemplate(true);
            if (isSuccess(status)) {
                resetFormState(formControls);
                setShowOutputModal(true);
                updateOutputModal(res.data);
                setTestedTemplate && setTestedTemplate(true);
            } else {
                toastError(res);
            }
        } catch (error) {
            toastError(error);
        } finally {
            setTestingTemplate(false);
        }
    };

    const updateOutputModal = data => {
        const columnNames = data?.result?.schema?.column_name;
        const rows = data?.result?.rows?.map(row => {
            const obj = {};
            columnNames?.forEach((name, index) => {
                obj[name] = row?.columns?.map(column => Object.values(column)[0])[index];
            });
            return {
                id: uuid(),
                ...obj,
            };
        });
        const columns = columnNames?.map(name => ({
            header: name,
            accessorKey: name,
        }));
        searchParams.delete('action');
        setSearchParams(searchParams);
        setShowOutputModal({
            isOpen: true,
            data: {
                columns,
                rows,
            },
        });
    };

    return {
        testTemplate,
        testingTemplate,
        showOutputModal,
        setShowOutputModal,
    };
};
