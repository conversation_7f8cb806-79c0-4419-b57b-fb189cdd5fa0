import { useSearchParams } from 'react-router-dom';

const ToggleFilterBtn = ({ filterKey, activeText, inactiveText, onToggle }) => {
    const [searchParams, setSearchParams] = useSearchParams();

    const isFilterActive = searchParams.get(filterKey) === 'true';

    const toggleFilter = e => {
        e.stopPropagation();
        const isActive = searchParams.get(filterKey) === 'true';

        if (isActive) {
            searchParams.delete(filterKey);
        } else {
            searchParams.set(filterKey, 'true');
        }
        onToggle &&
            onToggle({
                [filterKey]: !isActive,
            });
        setSearchParams(searchParams);
    };

    return (
        <div className="flex items-center gap-2 px-3 py-1">
            <div>
                <label className="inline-flex cursor-pointer items-center">
                    <span className="mr-3 text-sm font-medium text-gray-900 ">
                        {isFilterActive ? activeText : inactiveText}
                    </span>
                    <input
                        type="checkbox"
                        value=""
                        className="peer sr-only"
                        onChange={toggleFilter}
                        checked={isFilterActive}
                    />

                    <div className="peer relative h-6 w-11 rounded-full bg-gray-200 after:absolute after:start-[2px] after:top-[2px] after:size-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-blue-600 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rtl:peer-checked:after:-translate-x-full "></div>
                </label>
            </div>
        </div>
    );
};

export default ToggleFilterBtn;
