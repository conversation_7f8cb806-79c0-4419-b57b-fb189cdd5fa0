import { useEffect, useState } from 'react';
import { useFieldArray } from 'react-hook-form';

import ZIcon from '@/components/atoms/ZIcon';
import { isEmpty } from '@/utils';

import Label from '../Label';
import RenderUIFrom from '../RenderUIFrom';

const MultiFields = ({
    getConfig,
    label,
    id,
    labelClassName,
    tooltipText,
    required,
    addBtnText = 'Add more',
    formControls,
}) => {
    const { fields, append, remove } = useFieldArray({
        control: formControls.control,
        name: id,
    });
    const [fieldIds, setFieldIds] = useState([]);

    useEffect(() => {
        if (isEmpty(fields)) handleAdd();
    }, []);

    const handleAdd = () => {
        append(fieldIds.reduce((acc, id) => ({ ...acc, [id]: null }), {}));
    };
    const handleRemove = index => {
        remove(index);
    };

    const parseConfig = index => {
        const parse = config => {
            return config.map(field => {
                if (!isEmpty(field?.id) && !fieldIds.includes(field.id)) setFieldIds(prev => [...prev, field.id]);

                if (field.children) {
                    return {
                        ...field,
                        children: parse(field.children),
                    };
                }

                return {
                    ...field,
                    id: `${id}.${index}.${field.id}`,
                };
            });
        };

        return parse(
            getConfig({
                index,
                id,
                prefixId: `${id}.${index}`,
                formControls,
                fields,
            }),
        );
    };

    return (
        <div>
            <Label className={labelClassName} label={label} required={required} tooltipText={tooltipText} />

            <div className="flex flex-col gap-3">
                {fields.map((field, index) => {
                    return (
                        <div
                            key={field.id}
                            className="relative flex flex-col gap-4 rounded-md border border-dashed p-2 py-3"
                        >
                            <RenderUIFrom
                                componentConfig={{
                                    type: 'Box',
                                    className: 'flex flex-col gap-3',
                                    contents: parseConfig(index),
                                }}
                                formControls={formControls}
                            />
                            {fields.length > 1 && (
                                <div
                                    className="absolute right-0 top-0 flex size-5 -translate-y-1/2 translate-x-1/2 cursor-pointer items-center justify-center  rounded-full border border-gray-400 bg-white text-xs text-gray-400"
                                    onClick={() => handleRemove(index)}
                                >
                                    <ZIcon className="flex flex-col gap-4 text-gray-600" unicode="E921" />
                                </div>
                            )}
                        </div>
                    );
                })}
            </div>

            <div
                onClick={handleAdd}
                className="my-3 flex w-max cursor-pointer gap-1 border-b border-blue-500 text-xs text-blue-500"
            >
                <ZIcon unicode="E988" />
                {addBtnText}
            </div>
        </div>
    );
};

export default MultiFields;
