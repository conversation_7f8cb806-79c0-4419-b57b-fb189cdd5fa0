import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import ZIcon from '@/components/atoms/ZIcon';
import { COMMON_FORM_STYLES } from '@/constants';
import useCustomFormControls from '@/hooks/useCustomFormControls';
import { cn } from '@/utils';
import { getValueUsingAccessorKey } from '@/utils/network';

import useErrorContext from '../../../hooks/useErrorContext';
import useRequest from '../../../hooks/useRequest';
import errorConfig from '../../../utils/error.yaml';
import Form from '../Form';
import Modal from '../Modal';

const FilterButton = ({
    config,
    onSubmit,
    accessorKey,
    isSubmitting: isSubmittingProp,
    formControls,
    setTableData,
    setTableLoading,
    loadingBtnText,
    actions,
    hideCTAs,
}) => {
    const customFormControls = useCustomFormControls(formControls);
    const { customRequest } = useRequest();
    const { pushError } = useErrorContext();
    const [parsedConfig, setParsedConfig] = useState(null);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [showModal, setShowModal] = React.useState(false);

    useEffect(() => {
        if (Array.isArray(config)) {
            const filterFormConfig = {
                type: 'Box',
                className: COMMON_FORM_STYLES + ' px-3 py-4',
                contents: config,
            };
            setParsedConfig(filterFormConfig);
        } else setParsedConfig(config);
    }, [config]);

    const handleFilter = async data => {
        if (onSubmit === undefined) {
            pushError({
                error: `Please provide onFilter inside networkCalls of the table`,
                solution: errorConfig.missingOnFilterInTable,
            });
            return;
        }

        try {
            setShowModal(false);
            setIsSubmitting(true);
            setTableLoading(true);
            const res = await customRequest(onSubmit, {
                query: data,
            });
            const newData = accessorKey ? getValueUsingAccessorKey(res.data, accessorKey) : res.data;
            setTableData(newData);
            setShowModal(false);
        } catch (error) {
            toast.error('Error while filtering the data');
        } finally {
            setTableLoading(false);
            setShowModal(false);
            setIsSubmitting(false);
        }
    };

    return (
        <>
            <Modal open={showModal} setOpen={setShowModal} title="Filter">
                <Form
                    config={parsedConfig}
                    isSubmitting={isSubmittingProp ?? isSubmitting}
                    loadingBtnText={loadingBtnText}
                    btnSize="lg"
                    style={{ edit: true }}
                    formControls={customFormControls}
                    actions={actions}
                    hideCTAs={hideCTAs}
                    onSubmit={typeof onSubmit === 'function' ? onSubmit : customFormControls.handleSubmit(handleFilter)}
                />
            </Modal>
            <div
                className={cn(
                    'relative flex size-8 w-max cursor-pointer items-center justify-center gap-2 rounded-md border border-blue-500 px-3 text-blue-500 hover:bg-blue-50 ',
                    {
                        'hover:bg-blue-100 ': !showModal,
                    },
                )}
                onClick={() => setShowModal(true)}
            >
                <ZIcon unicode="e877" className="text-base" />
                <span className="text-sm">Filter</span>
            </div>
        </>
    );
};

export default React.memo(FilterButton);
