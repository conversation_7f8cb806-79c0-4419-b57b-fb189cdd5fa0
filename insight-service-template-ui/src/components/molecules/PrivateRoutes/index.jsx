import { Navigate, Outlet, useLocation } from 'react-router-dom';

import useUser from '@/hooks/useUser';

import StatusBanner from '../StatusBanner';

const PrivateRoutes = () => {
    const { isLoggedIn, isAuthorized, checkingAuth } = useUser();
    const location = useLocation();

    if (checkingAuth) return null;

    const getLoginRedirection = () => {
        return `/login?redirect=${encodeURIComponent(`${location.pathname}${location.search}`)}`;
    };

    return isLoggedIn ? (
        !isAuthorized ? (
            <StatusBanner
                className="h-[calc(100vh-220px)]"
                title="Not Authorized"
                message="You are not authorized to view this page. Please contact your administrator for access."
            />
        ) : (
            <Outlet />
        )
    ) : (
        <Navigate to={getLoginRedirection()} state={{ from: location }} replace />
    );
};

export default PrivateRoutes;
