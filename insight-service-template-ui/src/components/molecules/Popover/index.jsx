import Button from '@atoms/Button/Button';
import React from 'react';

import useClickedOutside from '../../../hooks/useClickedOutside';

const Popover = ({ children, actions, text, size, variant, className, dropdownPosition = 'middle' }) => {
    const [show, setShow] = React.useState(false);
    const dropdownRef = React.useRef(null);
    const dropdownOptionsRef = React.useRef(null);
    useClickedOutside(dropdownRef, () => setShow(false));

    const handleDropdownPosition = () => {
        switch (dropdownPosition) {
            case 'left':
                return 'top-full left-0';
            case 'right':
                return 'top-full right-0';
            default:
                return 'top-full right-1/2 translate-x-1/2';
        }
    };

    const styles = {
        Input_dropdown: 'relative w-max',
        options: `mt-1 absolute ${handleDropdownPosition()} w-80 max-h-96 overflow-auto bg-white border border-gray-300 rounded-md shadow-lg z-10`,
        active: 'bg-gray-100',
        show: 'flex flex-col',
    };

    return (
        <div className={styles.Input_dropdown + ` ${className ? className : ''}`} ref={dropdownRef}>
            <div onClick={() => setShow(state => !state)}>
                <Button text={text} variant={variant} size={size} />
            </div>
            <div
                className={styles.options + ` ${show ? 'flex flex-col' : 'hidden'}` + ' hide_scrollbar'}
                ref={dropdownOptionsRef}
            >
                <div className="max-h-full overflow-auto p-4">{children}</div>
                {actions}
            </div>
        </div>
    );
};

export default Popover;
