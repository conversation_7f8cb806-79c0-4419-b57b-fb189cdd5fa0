import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

const CustomToastContainer = () => {
    return (
        <div>
            <ToastContainer
                toastStyle={{
                    padding: '0',
                }}
                bodyStyle={{
                    padding: '0',
                }}
                icon={false}
                closeButton={false}
                className="text-green-100"
                position="top-right"
                autoClose={2500}
                pauseOnHover
                hideProgressBar
            />
        </div>
    );
};

export default CustomToastContainer;
