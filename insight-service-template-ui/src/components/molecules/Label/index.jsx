import { cn, isEmpty } from '@/utils';

import InfoTooltip from '../InputBox/InfoTooltip';

const Label = ({ label, required = false, tooltipText, className }) => {
    if (isEmpty(label)) return null;

    return (
        <div className={cn('label flex items-center ', className)}>
            <label>
                {label}
                <span>{required ? '*' : ''}</span>
            </label>
            <div className="ml-2">{tooltipText && <InfoTooltip tooltipText={tooltipText} />}</div>
        </div>
    );
};

export default Label;
