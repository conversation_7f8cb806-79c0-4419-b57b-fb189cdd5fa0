import Error from '@atoms/Error';
import Spinner from '@atoms/Spinner';
import { memo, Suspense } from 'react';
import { Routes as LibRoutes, Route } from 'react-router-dom';

import LoginPage from '@/pages/Login';

import GetStarted from '../GetStarted';
import LoadTrigger from '../LoadTrigger';
import PrivateRoutes from '../PrivateRoutes';
import { RenderUIFrom } from '../RenderUIFrom';
import baseConfig from './../../../configs/config.yaml';

const _RenderRoute = ({ pageKey, onLoadConfig, viewConfig }) => {
    return (
        <LoadTrigger onLoadConfig={onLoadConfig}>
            <Suspense
                fallback={
                    <div className="flex  h-screen w-full items-center justify-center">
                        <Spinner size="xl" />
                    </div>
                }
            >
                {pageKey && viewConfig ? <RenderUIFrom componentConfig={viewConfig} index={pageKey} /> : null}
            </Suspense>
        </LoadTrigger>
    );
};

const _Routes = () => {
    return (
        <LibRoutes>
            {baseConfig &&
                baseConfig !== null &&
                Object.entries(baseConfig.pageMapping || {}).map(([pageKey, pageEntry]) => {
                    const isPrivate = pageEntry.isPrivate;
                    const viewConfig = baseConfig.pages[pageKey]?.view;
                    const onLoadConfig = baseConfig.pages[pageKey]?.onLoad;

                    return isPrivate ? (
                        <Route element={<PrivateRoutes />}>
                            <Route
                                key={pageKey}
                                path={pageEntry.path}
                                element={
                                    <_RenderRoute
                                        pageKey={pageKey}
                                        onLoadConfig={onLoadConfig}
                                        viewConfig={viewConfig}
                                    />
                                }
                            />
                        </Route>
                    ) : (
                        <Route
                            key={pageKey}
                            path={pageEntry.path}
                            element={
                                <_RenderRoute pageKey={pageKey} onLoadConfig={onLoadConfig} viewConfig={viewConfig} />
                            }
                        />
                    );
                })}
            {/* modify beyond this line only if you understand the flow completely */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/" Component={GetStarted} />
            <Route path="*" Component={Error} />;
        </LibRoutes>
    );
};

const Routes = memo(_Routes);

export default Routes;
