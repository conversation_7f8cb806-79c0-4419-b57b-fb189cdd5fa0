import Button from '@atoms/Button';
import FormSideDrawer from '@molecules/FormSideDrawer';
import { useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import errorConfig from '@/utils/error.yaml';

import useErrorContext from '../../../hooks/useErrorContext';
import useRequest, { isInsideEndpointMapping } from '../../../hooks/useRequest';

const CreateButton = ({ config, onSubmit, buttonText, headerText, getTableData, formControls, isSubmitting }) => {
    const { customRequest } = useRequest();
    const { pushError } = useErrorContext();
    const [searchParams, setSearchParams] = useSearchParams();
    const [isCreating, setIsCreating] = useState(false);
    const [, setError] = useState(null);

    const openDrawer = () => {
        setSearchParams({ action: 'create', ...searchParams });
    };

    const closeDrawer = () => {
        if (searchParams.has('action')) {
            setSearchParams(prev => {
                const newSearchParams = new URLSearchParams(prev.toString());
                newSearchParams.delete('action');
                return newSearchParams;
            });
        }
    };

    const handleCreate = async data => {
        if (onSubmit === undefined) {
            pushError({
                error: `Please provide onCreate inside networkCalls of the table`,
                solution: errorConfig.missingOnCreateInTable,
            });
            return;
        } else if (!isInsideEndpointMapping(onSubmit)) {
            pushError({
                error: `Please provide ${onSubmit} inside networkCalls of the table`,
                solution: errorConfig.missingOnCreateInTable,
            });
            return;
        }
        try {
            setIsCreating(true);
            const res =
                typeof onSubmit === 'function'
                    ? await onSubmit(data)
                    : await customRequest(onSubmit, {
                          body: data,
                      });
            if (res.customError) return;
            setIsCreating(false);
            closeDrawer();
            getTableData();
            toast.success('Data created successfully');
        } catch (error) {
            setError(error);
            setIsCreating(false);
            toast.error('Error while creating the data');
        }
    };

    return (
        <>
            <Button
                iconPos="left"
                onClick={openDrawer}
                text={buttonText || 'Create'}
                isLoading={isSubmitting ?? isCreating}
                textLoading="Creating..."
                iconUnicode="E922"
            />
            <FormSideDrawer
                formControls={formControls}
                header={headerText ?? buttonText ?? 'Create'}
                onSubmit={typeof onSubmit === 'function' ? onSubmit : handleCreate}
                isSubmitting={isSubmitting ?? isCreating}
                config={config ?? {}}
                removePadding={true}
                action="create"
            />
        </>
    );
};

export default CreateButton;
