.accordion_container .accordion_icon {
    --icon-size: 15px;

    position: relative;
    width: var(--icon-size);
    height: var(--icon-size);
    cursor: pointer;
}

.accordion_container .accordion_icon.active:before {
    transform: translateY(-50%) rotate(-90deg);
    opacity: 0;
}

.accordion_container .accordion_icon.active:after {
    transform: translateY(-50%) rotate(0);
}

.accordion_container .accordion_icon:before,
.accordion_container .accordion_icon:after {
    content: '';
    display: block;
    background-color: #000;
    position: absolute;
    top: 50%;
    left: 0;
    transition: 0.35s;
    width: 100%;
    height: 2px;
}

.accordion_container .accordion_icon:before {
    transform: translateY(-50%);
    opacity: 1;
}

.accordion_container .accordion_icon:after {
    transform: translateY(-50%) rotate(90deg);
}

.accordion_container .content_wrapper {
    display: grid;
    grid-template-rows: 0fr;
    overflow: hidden;
    transition: grid-template-rows 200ms;
}

.accordion_container .content_wrapper.active {
    grid-template-rows: 1fr;
}

.accordion_container .content {
    min-height: 0;
    visibility: hidden;
    transition: visibility 200ms;
}

.accordion_container .content.active {
    visibility: visible;
}
