import { useState } from 'react';

import ZIcon from '../../atoms/ZIcon';
import Markdown from '../Markdown';
import { RenderUIFrom } from '../RenderUIFrom';
import './accordion.css';

const Accordion = ({ plusIcon, multiple, openAllOnLoad, sections: data = [] }) => {
    const [sections, setSections] = useState(
        openAllOnLoad && multiple
            ? data.map(item => {
                  return {
                      ...item,
                      open: true,
                  };
              })
            : data,
    );

    const toggleSection = index => {
        const newSections = sections.map((section, i) => {
            if (i === index) {
                return {
                    ...section,
                    open: !section.open,
                };
            } else if (!multiple) {
                return {
                    ...section,
                    open: false,
                };
            }
            return section;
        });
        setSections(newSections);
    };

    return (
        <div>
            {sections.map((section, index) => {
                return (
                    <div className="accordion_container border-b border-gray-200" key={index}>
                        <div
                            className="flex w-full cursor-pointer items-center p-2 py-3 hover:bg-slate-50"
                            onClick={() => toggleSection(index)}
                        >
                            <h3 className="text-base">
                                <Markdown>{section.title}</Markdown>
                            </h3>
                            {plusIcon ? (
                                <div className={`accordion_icon ml-auto ${section.open ? 'active' : ''}`} />
                            ) : (
                                <div
                                    className={`ml-auto transition-transform  ${section.open ? 'rotate-0' : 'rotate-180'} `}
                                >
                                    <ZIcon unicode="E824" className="text-sm" />
                                </div>
                            )}
                        </div>
                        <div className={`content_wrapper max-w-full overflow-x-auto ${section.open ? 'active' : ''}`}>
                            <div className={`content w-full overflow-x-auto ${section.open ? 'active' : ''}`}>
                                {
                                    <div className="p-2 pb-3 text-sm font-light text-gray-700">
                                        {RenderUIFrom(section.view, `accordion_${index}`)}
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                );
            })}
        </div>
    );
};

export default Accordion;
