import { useEffect, useRef } from 'react';

import DashboardFunctions from '@/functions';
import { TaskRunner } from '@/utils/TaskRunner';

/**
 * not the best method to do this, but until we figure out
 * the alternatives, creating this. Abstracting it out to
 * reduce the surface area when we swap in a better solution.
 */
const LoadTrigger = ({ children, onLoadConfig }) => {
    const taskRunner = useRef(new TaskRunner()).current;

    useEffect(() => {
        if (!onLoadConfig) return;
        onLoadConfig.forEach(({ func, params }) => {
            const targetFn = DashboardFunctions[func];
            if (typeof targetFn === 'function') {
                taskRunner.addTask(prevOutput => targetFn.call(null, params, prevOutput));
            }
        });
    }, [taskRunner, onLoadConfig]);

    return children;
};

export default LoadTrigger;
