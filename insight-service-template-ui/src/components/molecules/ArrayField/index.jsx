import { useRef, useState } from 'react';

import ZIcon from '@/components/atoms/ZIcon';
import { ARRAY_VALUE_TYPES, ArrayValueTypes } from '@/constants';
import useInput from '@/hooks/useInput';
import { cn, isEmpty, toNum } from '@/utils';

import Dropdown from '../Dropdown';

const ArrayField = ({ value = [], onChange, label, required, limit, error, labelClassName }) => {
    const [inputType, setInputType] = useState();
    const [inputValue, setInputValue] = useState();
    const [focused, setFocused] = useState(false);
    const inputRef = useRef();

    const handleInputKeyDown = e => {
        const prevVal = value?.value;
        if (e.key === 'Enter') {
            const val =
                inputType === ArrayValueTypes.FLOAT || inputType === ArrayValueTypes.INTEGER
                    ? toNum(e.target.value)
                    : e.target.value;
            if (val && ((limit && prevVal?.length < limit) || isEmpty(limit))) {
                onChange({
                    type: inputType,
                    value: [...(prevVal || []), val],
                });
                e.target.value = '';
                setInputValue('');
            }
        }
        if (e.key === 'Backspace' && !e.target.value) {
            onChange({
                type: inputType,
                value: prevVal?.slice(0, prevVal?.length - 1),
            });
        }
    };

    const currInputType =
        inputType === ArrayValueTypes.FLOAT || inputType === ArrayValueTypes.INTEGER ? 'number' : 'text';

    const { handleKeyDown, handleInputChange } = useInput({
        onKeyDown: handleInputKeyDown,
        allowFloatValue: inputType === ArrayValueTypes.FLOAT,
        type: currInputType,
        onChange: e => setInputValue(e.target.value),
    });

    const removeItem = idx => {
        const newValue = value.filter((_, i) => i !== idx);
        onChange({
            type: inputType,
            value: newValue,
        });
    };

    const handleArrayTypeChange = val => {
        setInputType(val);
        setInputValue('');
        onChange({
            type: val,
            value: [],
        });
    };

    return (
        <div className="w-full">
            <Dropdown
                options={ARRAY_VALUE_TYPES}
                label="Array of"
                className="mb-3"
                onChange={handleArrayTypeChange}
                value={inputType}
            />
            {label && (
                <label className="label flex">
                    <div className={cn('flex', labelClassName)}>{label}</div>
                    <span>{required ? '*' : ''}</span>
                </label>
            )}
            <div
                className={cn('input flex flex-wrap gap-2', {
                    'border border-red-500': error,
                })}
                onClick={() => inputRef.current.focus()}
            >
                {value?.value?.map((val, idx) => {
                    return (
                        <div className="flex gap-1 rounded-sm bg-gray-200 p-0.5 px-1" key={val + idx}>
                            <div>{val}</div>
                            <div
                                className="group flex cursor-pointer items-center justify-center"
                                onClick={() => removeItem(idx)}
                            >
                                <ZIcon unicode="E921" className="group-hover:text-red-500" />
                            </div>
                        </div>
                    );
                })}
                {(value?.value?.length < limit || isEmpty(limit)) && (
                    <input
                        type={currInputType}
                        placeholder="Enter tag"
                        ref={inputRef}
                        className="border-none outline-none"
                        value={inputValue}
                        onKeyDown={handleKeyDown}
                        onChange={handleInputChange}
                        onFocus={() => setFocused(true)}
                        onBlur={() => setFocused(false)}
                    />
                )}
            </div>
            {limit && value?.value?.length >= limit && (
                <div className="mt-1 text-xs text-gray-500">Maximum {limit} tags allowed</div>
            )}
            {isEmpty(error) && (
                <div
                    className={cn('mt-1 text-[11px] text-gray-400', {
                        'text-gray-500': focused,
                    })}
                >
                    Press Enter to add, Backspace to remove
                </div>
            )}
        </div>
    );
};

export default ArrayField;
