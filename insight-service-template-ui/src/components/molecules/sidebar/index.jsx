import ZIcon from '@atoms/ZIcon';
import { useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { cn } from '@/utils';

import baseConfig from '../../../configs/config.yaml';

const pageMapping = baseConfig !== null && baseConfig?.pageMapping;

const DoubleChevron = ({ direction, isBold }) => {
    const unicode = direction === 'left' ? 'E92E' : 'E92F';
    const Icon = (
        <div className="flex">
            <ZIcon unicode={unicode} className="relative left-1" />
            <ZIcon unicode={unicode} className="relative right-1" />
        </div>
    );
    if (isBold) {
        return <strong>{Icon}</strong>;
    }
    return Icon;
};

export const Sidebar = ({ config = [], sidebarOpen, setSidebarOpen, transition }) => {
    const location = useLocation();
    const navigate = useNavigate();

    const toggleSidebar = useCallback(() => {
        setSidebarOpen(s => !s);
    }, [setSidebarOpen]);

    return (
        <div
            className={`fixed left-0 z-30 flex h-[calc(100%-60px)] flex-col border-e bg-white ${transition} w-60 min-w-60 ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}
        >
            <div
                className="shadow-sidebar-toggle absolute bottom-1/2 right-0 flex h-8 w-5 translate-x-full translate-y-1/2 cursor-pointer items-center justify-center  rounded-r-xl bg-white"
                onClick={toggleSidebar}
            >
                <DoubleChevron direction={sidebarOpen ? 'left' : 'right'} isBold={true} />
            </div>
            <div className="overflow-x-hidden overflow-y-scroll px-2 pb-6">
                <ul className="mt-6 space-y-1">
                    {config?.map((sideBarEntry, sidebarIdx) => {
                        const entryPageKey = sideBarEntry['page-key'];
                        const pagePath = entryPageKey && pageMapping ? pageMapping[entryPageKey]?.path : null;
                        const isSelected = location.pathname === pagePath;
                        const containsNestedOptions = sideBarEntry.options?.length >= 1;

                        return (
                            <li key={sideBarEntry.id || sideBarEntry['page-key'] || sidebarIdx}>
                                <details className="group [&_summary::-webkit-details-marker]:hidden">
                                    <summary
                                        className={cn(
                                            'flex cursor-pointer select-none items-center justify-between rounded-lg px-4 py-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700 ',
                                            {
                                                'bg-gray-200': !containsNestedOptions && isSelected,
                                            },
                                        )}
                                        onClick={() => navigate(pagePath)}
                                    >
                                        <span className="text-sm font-medium">{sideBarEntry.label}</span>
                                        {/* nested options begin here */}
                                        {containsNestedOptions ? (
                                            <span className="shrink-0 transition duration-300 group-open:-rotate-180">
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    className="size-5"
                                                    viewBox="0 0 20 20"
                                                    fill="currentColor"
                                                >
                                                    <path
                                                        fillRule="evenodd"
                                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                        clipRule="evenodd"
                                                    />
                                                </svg>
                                            </span>
                                        ) : null}
                                    </summary>

                                    <ul className="mt-2 space-y-1 px-4">
                                        {sideBarEntry.options?.map((option, optionIdx) => {
                                            const optionPath = pageMapping[option['page-key']]?.path;
                                            const isSelected = location.pathname === optionPath;
                                            return (
                                                <li key={option.id || option['page-key'] || optionPath || optionIdx}>
                                                    <span
                                                        className={cn(
                                                            'flex cursor-pointer select-none items-center justify-between rounded-lg px-4 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-700',
                                                            {
                                                                'bg-gray-200': isSelected,
                                                            },
                                                        )}
                                                        onClick={() => navigate(optionPath)}
                                                    >
                                                        {option.label}
                                                    </span>
                                                </li>
                                            );
                                        })}
                                    </ul>
                                </details>
                            </li>
                        );
                    })}
                </ul>
            </div>
        </div>
    );
};
export default Sidebar;
