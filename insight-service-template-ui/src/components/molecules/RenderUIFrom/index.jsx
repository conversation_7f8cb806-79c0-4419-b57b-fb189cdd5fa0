import { componentRegistry } from '../../componentRegistry';

export const RenderUIFrom = ({ componentConfig, index = 0, formControls, extraProps }) => {
    if (!componentConfig) return null;

    const { type = 'Box', version = '1.0', mods = {}, contents, ...otherProps } = componentConfig;

    // get target component from registry
    const Component = componentRegistry[type]?.[version];

    // throw directly if its not a supported component type or version.
    // this is important to flag the issue during dev, do not suppress
    // in the non production env.

    if (!Component && !Array.isArray(componentConfig)) {
        // eslint-disable-next-line no-console
        console.error(`Component ${type} with version ${version} not found.`);
        return null;
    }

    let renderedChildren = null;

    // only allow iterations to build a tree if it is a valid Array
    // else just render it as a primary type.
    // sending objects here will break the render tree
    if (
        (Array.isArray(componentConfig) && componentConfig.length > 0) ||
        (Array.isArray(contents) && contents.length > 0)
    ) {
        const newContents = componentConfig.length > 0 ? componentConfig : contents || [];
        renderedChildren = newContents.map((child, indx) =>
            RenderUIFrom({
                componentConfig: child,
                index: indx,
                formControls: { ...formControls },
                extraProps,
            }),
        );
    } else renderedChildren = contents;

    return (
        <Component
            key={index}
            {...otherProps}
            {...mods}
            {...extraProps}
            formControls={formControls}
            hookFormRegistration={formControls?.hookFormRegistration}
        >
            {renderedChildren}
        </Component>
    );
};

export default RenderUIFrom;
