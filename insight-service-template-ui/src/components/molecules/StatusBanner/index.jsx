import Button from '@/components/atoms/Button';
import { DEFAULT_PAGE_ERROR_MESSAGE, DEFAULT_PAGE_ERROR_TITLE, ERROR_IMG } from '@/constants';
import { cn } from '@/utils';

const StatusBanner = ({
    image = ERROR_IMG,
    message = DEFAULT_PAGE_ERROR_MESSAGE,
    title = DEFAULT_PAGE_ERROR_TITLE,
    className,
}) => {
    return (
        <div className={cn('flex h-[calc(100vh-120px)] w-full flex-col items-center justify-center', className)}>
            <img className="mx-auto max-w-full" width={400} src={image} alt="image" />
            <h1 className="mb-6 text-center text-2xl">{title}</h1>
            <p className="mb-6 max-w-[600px] text-center text-gray-600">{message}</p>
            <div className="flex justify-center">
                <Button variant="contained" text="Reload Page" onClick={() => window.location.reload()} />
            </div>
        </div>
    );
};

export default StatusBanner;
