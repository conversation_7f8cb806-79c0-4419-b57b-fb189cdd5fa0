import { useRef, useState } from 'react';

import ZIcon from '@/components/atoms/ZIcon';
import { cn, isEmpty } from '@/utils';

import Label from '../Label';

const MultiInput = ({ value = [], onChange, label, required, limit, error, labelClassName, tooltipText }) => {
    const [focused, setFocused] = useState(false);
    const inputRef = useRef(null);

    const handleInputKeyDown = e => {
        const currentValue = Array.isArray(value) ? value : [];
        if (e.key === 'Enter') {
            const val = e.target.value;
            if (val && ((limit && currentValue.length < limit) || isEmpty(limit))) {
                onChange([...currentValue, val]);
                e.target.value = '';
            }
        }
        if (e.key === 'Backspace' && !e.target.value) {
            const newValue = [...value];
            newValue.pop();
            onChange(newValue);
        }
    };

    const removeItem = (e, idx) => {
        e.stopPropagation();
        const newValue = [...value];
        newValue.splice(idx, 1);
        onChange(newValue);
    };

    return (
        <div className="w-full">
            <Label className={labelClassName} label={label} required={required} tooltipText={tooltipText} />
            <div
                className={cn('input flex flex-wrap gap-2', {
                    'border border-red-500': error,
                })}
                onClick={() => inputRef.current.focus()}
            >
                {value?.map((val, idx) => {
                    return (
                        <div className="flex gap-1 rounded-sm bg-gray-200 p-0.5 px-1" key={val + idx}>
                            <div>{val}</div>
                            <div
                                className="group flex cursor-pointer items-center justify-center"
                                onClick={e => removeItem(e, idx)}
                            >
                                <ZIcon unicode="E921" className="group-hover:text-red-500" />
                            </div>
                        </div>
                    );
                })}
                {(value?.length < limit || isEmpty(limit)) && (
                    <input
                        type="text"
                        placeholder="Enter tag"
                        className="border-none outline-none"
                        onKeyDown={handleInputKeyDown}
                        onFocus={() => setFocused(true)}
                        onBlur={() => setFocused(false)}
                        ref={inputRef}
                    />
                )}
            </div>
            {limit && value?.length >= limit && (
                <div className="mt-1 text-xxs text-gray-400">Maximum {limit} tags allowed</div>
            )}
            {isEmpty(error) && (
                <div
                    className={cn('mt-1 text-[11px] text-gray-400', {
                        'text-gray-500': focused,
                    })}
                >
                    Press Enter to add, Backspace to remove
                </div>
            )}
        </div>
    );
};

export default MultiInput;
