import ZIcon from '@atoms/ZIcon';
import yaml from 'js-yaml';
import React from 'react';

const ErrorDialog = ({ message }) => {
    const [isCollapsed, setIsCollapsed] = React.useState(false);

    return (
        <>
            {isCollapsed && (
                <div
                    className="fixed bottom-4 left-4 z-50 cursor-pointer rounded-md bg-red-500 p-4 py-2 text-xl text-white"
                    onClick={() => setIsCollapsed(false)}
                >
                    Error
                </div>
            )}
            {!isCollapsed && (
                <div className="fixed left-0 top-0 z-50 h-screen w-screen">
                    <div className="absolute inset-0 bg-black opacity-50 transition-opacity" />
                    <div className="fixed left-1/2 top-1/2 max-h-[calc(100%-100px)] w-[min(calc(100%-20px),700px)] -translate-x-1/2 -translate-y-1/2 overflow-auto rounded-md bg-gray-800 p-4 text-white">
                        <div className="flex">
                            <div className="text-xl font-medium">Dashboard builder logs</div>
                            <div
                                onClick={() => setIsCollapsed(true)}
                                className="mb-2 ml-auto flex size-8 cursor-pointer items-center justify-center rounded-md hover:bg-gray-700"
                            >
                                <ZIcon unicode="E8A4" className="m-3 text-sm " />
                            </div>
                        </div>
                        <div className="rounded-md bg-black p-3 text-red-500">Error: {message?.error}</div>

                        <div className="mt-4 max-w-full overflow-x-auto rounded-md bg-black p-3 ">
                            <div className="mb-2 text-green-500">Suggested example solutions:</div>
                            <pre className="text-yellow-500">{yaml.dump(message?.solution || '')}</pre>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default ErrorDialog;
