import { DOTS } from '../../../constants';
import ZIcon from '../../atoms/ZIcon';
import { getPaginationRange } from './helper';

const Pagination = ({ onPageChange, totalCount, siblingCount = 1, currentPage, pageSize = 1, className }) => {
    const styles = {
        pagination_container: 'flex justify-center items-center gap-2',
        pagination_item:
            'flex justify-center items-center w-5 h-5 rounded-full cursor-pointer text-xs hover:bg-gray-200',
        selected: 'bg-blue-500 text-white hover:bg-blue-500',
        disabled: 'opacity-50 cursor-not-allowed',
        dots: 'flex justify-center items-center w-2 h-2 rounded-full',
    };

    const paginationRange = getPaginationRange(currentPage, totalCount, siblingCount, pageSize);

    if (currentPage === 0 || paginationRange?.length < 2) {
        return null;
    }

    let lastPage = paginationRange && paginationRange[paginationRange?.length - 1];

    const onNext = () => {
        if (currentPage === lastPage) return;
        onPageChange(currentPage + 1);
    };

    const onPrevious = () => {
        if (currentPage === 1) return;
        onPageChange(currentPage - 1);
    };

    return (
        <div className={styles.pagination_container + ` ${className ? className : ''}`}>
            <ZIcon
                unicode="e92e"
                className={`mr-1 text-lg ${currentPage === 1 ? styles.disabled : ''} `}
                onClick={onPrevious}
            />
            {paginationRange?.map((pageNumber, index) => {
                if (pageNumber === DOTS) {
                    return (
                        <div key={index} className={styles.dots}>
                            ...
                        </div>
                    );
                }

                return (
                    <div
                        className={styles.pagination_item + ` ${pageNumber == currentPage ? styles.selected : ''}`}
                        onClick={() => onPageChange(pageNumber)}
                        key={index}
                    >
                        {pageNumber}
                    </div>
                );
            })}
            <ZIcon
                unicode="e92f"
                className={`ml-1 cursor-pointer text-lg ${currentPage === lastPage ? styles.disabled : ''}`}
                onClick={onNext}
            />
        </div>
    );
};

export default Pagination;
