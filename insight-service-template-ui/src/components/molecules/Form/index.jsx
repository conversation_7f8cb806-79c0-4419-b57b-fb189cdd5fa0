import Button from '@atoms/Button';
import useCustomFormControls from '@hooks/useCustomFormControls';
import RenderUIFrom from '@molecules/RenderUIFrom';
import { useEffect, useMemo, useState } from 'react';
import { useWatch } from 'react-hook-form';

import { cn } from '@/utils';
import { parseDataForApiRequest } from '@/utils/network';

const getWatchKeys = config => {
    const watchKeys = [];

    const extractDependencies = config => {
        if (config?.dependencies) {
            watchKeys.push(...(config?.dependencies || {}));
        }
        if (config?.contents) {
            config?.contents?.forEach(extractDependencies);
        }
    };

    extractDependencies(config);
    return watchKeys;
};

const Form = ({
    formControls,
    config,
    btnText,
    loadingBtnText,
    btnSize,
    isSubmitting = false,
    style = {
        edit: false,
    },
    removeDefaultStyles = true,
    note,
    actions,
    hideCTAs = false,
    onClear,
    checkDirty = true,
    removePadding = true,
    onSubmit,
    getDirty = false,
    className,
}) => {
    const customFormControls = useCustomFormControls(formControls);

    const watchKeys = useMemo(() => getWatchKeys(config) || [], [config]);
    const [mappedWatchValues, setMappedWatchValues] = useState(
        watchKeys.reduce((acc, key) => ({ ...acc, [key]: undefined }), {}),
    );

    const canSubmit = !isSubmitting && (customFormControls?.formState?.isDirty || !checkDirty);

    const getDirtyValues = () => {
        const dirtyValues = {};
        Object.keys(customFormControls?.dirtyFields).forEach(key => {
            dirtyValues[key] = customFormControls?.getValues(key);
        });
        return dirtyValues;
    };

    const formSubmit = e => {
        e.preventDefault();
        if (canSubmit)
            customFormControls?.handleSubmit(data => {
                onSubmit(parseDataForApiRequest(getDirty ? getDirtyValues() : data));
            })();
    };

    const watchValues = useWatch({
        control: customFormControls?.control,
        name: watchKeys,
    });

    useEffect(() => {
        if (watchValues) {
            const newWatchValues = {};
            watchKeys.forEach((key, index) => {
                const keyParts = key.split('.');

                keyParts.reduce((acc, part, i) => {
                    if (i === keyParts.length - 1) {
                        acc[part] = watchValues[index];
                    } else {
                        acc[part] = acc[part] || {};
                    }
                    return acc[part];
                }, newWatchValues);
            });
            setMappedWatchValues(newWatchValues);
        }
    }, [watchValues, watchKeys]);

    return (
        <div
            className={cn('relative flex max-w-full flex-col overflow-x-auto', {
                'max-h-full flex-1': style.edit,
            })}
        >
            {note && (
                <p className="w-full rounded-sm  bg-yellow-100 p-1 px-2 text-center text-sm text-yellow-600">{note}</p>
            )}
            <div
                className={cn(
                    {
                        ' px-3': !removeDefaultStyles && !removePadding,
                        'flex flex-1 flex-col overflow-y-auto pb-4': style.edit,
                        '!pb-0': removePadding,
                    },
                    className,
                )}
            >
                <RenderUIFrom
                    componentConfig={config}
                    formControls={{
                        ...customFormControls,
                        formConfig: config,
                    }}
                    extraProps={{
                        mappedWatchValues,
                    }}
                />
            </div>

            {!hideCTAs && (
                <div
                    className={cn({
                        'mt-auto flex h-max flex-col items-center justify-center gap-2 border-t p-3': true,
                    })}
                    data-testid="__filter_footer__"
                >
                    {actions ? (
                        actions
                    ) : (
                        <div className="flex items-center gap-2">
                            {onClear && (
                                <Button
                                    className="min-w-32"
                                    text="Clear"
                                    variant="outlined"
                                    disabled={isSubmitting}
                                    size={btnSize || 'lg'}
                                    onClick={() => onClear()}
                                />
                            )}
                            <Button
                                text={isSubmitting ? loadingBtnText || btnText || 'Submit' : btnText || 'Submit'}
                                className="min-w-32"
                                disabled={!canSubmit}
                                size={btnSize || 'lg'}
                                onClick={formSubmit}
                            />
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default Form;
