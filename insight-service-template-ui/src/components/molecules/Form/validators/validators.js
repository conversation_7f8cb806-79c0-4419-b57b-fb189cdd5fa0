/* eslint-disable no-useless-escape */
// restriction of 64 character in username and other valid email checks
const zomatoEmailRegex = /^[a-zA-Z0-9-_\.]{1,64}@zomato\.com/;
const blinkitEmailRegex = /^[a-zA-Z0-9-_\.]{1,64}@blinkit\.com/;
const extZomatoEmailRegex = /^[a-zA-Z0-9-_\.]{1,64}@ext\.zomato\.com/;

// generic email regex
const emailRegex = /^[a-zA-Z0-9-_\.]+@[a-zA-Z0-9-_\.]+\.[a-zA-Z]{2,5}$/;

// return true if value passed the given regex check. Email regex can be any of the above as per requirement
export const zomatoEmailRegexMatch = value => {
    if (!zomatoEmailRegex.test(value)) return 'Invalid Email';
};

export const blinkitEmailRegexMatch = value => {
    if (!blinkitEmailRegex.test(value)) return 'Invalid Email';
};

export const extZomatoEmailRegexMatch = value => {
    if (!extZomatoEmailRegex.test(value)) return 'Invalid Email';
};

export const emailRegexMatch = value => {
    if (!emailRegex.test(value)) return 'Invalid Email';
};

export const regexValidatorHandler = validators => value => {
    for (let i = 0; i < validators.length; i++) {
        const { regex, message } = validators[i];
        const parsedRegex = new RegExp(regex);
        if (!parsedRegex.test(value)) return message || 'Invalid input';
    }
};
