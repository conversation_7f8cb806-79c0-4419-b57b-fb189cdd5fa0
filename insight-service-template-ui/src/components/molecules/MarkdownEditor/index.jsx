import Markdown from '@molecules/Markdown';
import MDEditor, { commands } from '@uiw/react-md-editor';
import rehypeSanitize from 'rehype-sanitize';

import { cn } from '@/utils';

import './index.css';

const MarkdownEditor = ({
    value,
    onChange,
    placeholder = 'Type here...',
    disabled,
    required,
    className,
    label,
    error,
}) => {
    return (
        <div className={cn('relative flex w-full max-w-full flex-col text-sm ', className)}>
            <label className={'label'}>
                {label}
                <span>{required ? '*' : ''}</span>
            </label>
            {disabled && <Markdown text={value} className="rounded-md border !bg-gray-200 p-2 opacity-60" />}
            <div
                className={cn({
                    'cursor-not-allowed bg-gray-200': disabled,
                    'border-red-500': error,
                })}
            >
                <div className="w-full" data-color-mode="light">
                    {!disabled && (
                        <MDEditor
                            value={value}
                            onChange={onChange}
                            previewOptions={{
                                rehypePlugins: [[rehypeSanitize]],
                            }}
                            height={500}
                            commands={[...commands.getCommands(), commands.fullscreen]}
                            defaultValue={placeholder}
                            extraCommands={[
                                commands.group([], {
                                    name: 'Header',
                                    groupName: 'Header',
                                    icon: (
                                        <div className="h-full w-1/2 border-l border-gray-200 p-2 px-3 text-base font-medium">
                                            Preview
                                        </div>
                                    ),
                                }),
                            ]}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default MarkdownEditor;
