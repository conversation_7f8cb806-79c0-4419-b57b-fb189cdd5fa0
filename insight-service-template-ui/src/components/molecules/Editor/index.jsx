import MonacoEditor from '@monaco-editor/react';
import { useRef } from 'react';

import Label from '../Label';
import draculaTheme from './draculaTheme';

const Editor = ({ value, onChange, label, labelClassName, required, tooltipText }) => {
    const editorRef = useRef(null);

    const handleEditorDidMount = (editor, monaco) => {
        editorRef.current = editor;
        monaco.editor.defineTheme('dracula-theme', draculaTheme);
        monaco.editor.setTheme('dracula-theme');
    };

    return (
        <div>
            <Label className={labelClassName} label={label} required={required} tooltipText={tooltipText} />

            <MonacoEditor
                height="200px"
                defaultLanguage="sql"
                className="overflow-hidden rounded-md border"
                onMount={handleEditorDidMount}
                theme="dracula-theme"
                options={{
                    minimap: { enabled: false },
                }}
                onChange={onChange}
                value={value}
            />
        </div>
    );
};

export default Editor;
