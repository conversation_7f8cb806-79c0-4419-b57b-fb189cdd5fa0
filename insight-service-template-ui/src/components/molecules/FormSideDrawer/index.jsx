import Spinner from '@atoms/Spinner';
import SideDrawer from '@molecules/SideDrawer';
import { useEffect } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';
import { useSearchParams } from 'react-router-dom';

import { EDIT } from '@/constants';
import { noop } from '@/utils';

import Form from '../Form';

const FormSideDrawer = ({
    header,
    onGet,
    formControls,
    isSubmitting,
    loadingBtnText,
    loadHeader,
    btnText,
    action,
    CTAs,
    headerCTA,
    hideCTAs = false,
    nextStep,
    isNextStep = false,
    isLoading = false,
    checkDirty = true,
    removeDefaultStyles = false,
    removePadding = false,
    shouldFetchOnOpen = true,
    onSubmit = noop,
    config,
}) => {
    const [searchParams, setSearchParams] = useSearchParams();
    const id = searchParams.get('id');
    const open =
        (action ? searchParams.get('action') === action : searchParams.get('action') === EDIT) &&
        (id !== null || searchParams.get('action') === action);

    const setOpen = isOpen => {
        if (!isOpen) {
            searchParams.delete('action');
            searchParams.delete('id');
            searchParams.delete('type');
            setSearchParams(searchParams);
        }
    };

    useHotkeys('esc', () => setOpen(false));

    useEffect(() => {
        if (open && shouldFetchOnOpen) {
            document.body.style.overflow = 'hidden';
            onGet && onGet(id);
        } else {
            document.body.style.overflow = 'auto';
            formControls?.reset && formControls.reset();
        }

        return () => {
            document.body.style.overflow = 'auto';
            formControls?.reset && formControls.reset();
        };
    }, [open]);

    return (
        <SideDrawer
            open={open}
            setOpen={setOpen}
            closeDrawer={() => setOpen(false)}
            header={header}
            headerCTA={headerCTA}
            renderComponent={isNextStep && nextStep}
            key={id}
            headerText={header}
            hideHeader={loadHeader && isLoading}
        >
            {isLoading ? (
                <div className="flex h-[calc(100dvh-120px)] items-center justify-center gap-4">
                    <Spinner size="xl" />
                    <label className="text-lg">Fetching details</label>
                </div>
            ) : (
                <Form
                    config={config}
                    isSubmitting={isSubmitting}
                    loadingBtnText={loadingBtnText}
                    btnText={btnText}
                    btnSize="lg"
                    style={{ edit: true }}
                    removeDefaultStyles={removeDefaultStyles}
                    formControls={formControls}
                    actions={CTAs}
                    hideCTAs={hideCTAs}
                    checkDirty={checkDirty}
                    removePadding={removePadding}
                    onSubmit={onSubmit}
                />
            )}
        </SideDrawer>
    );
};

export default FormSideDrawer;
