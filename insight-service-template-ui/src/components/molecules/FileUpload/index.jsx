import clsx from 'clsx';
import { createRef, useCallback } from 'react';

const FileUpload = ({ inputRef, value, onChange, label, error, multiple = false, required, className, accept }) => {
    const ref = inputRef ?? createRef();
    const handleFileSelect = e => {
        const files = e.target.files;
        onChange && onChange(files);
    };

    const handleSyntheticClick = () => {
        ref.current.click();
        ref.current.value = '';
    };

    const getValueToDisplay = useCallback(() => {
        if (multiple) return `${value?.length ?? 0} files selected`;
        else if (value && value.length > 0) return value[0].name;
        else return 'No file chosen';
    }, [value, multiple]);

    return (
        <div className={clsx('relative flex w-full max-w-full flex-col text-sm ' + className)}>
            <div className="label">
                {label}
                <span>{required ? '*' : ''}</span>
            </div>
            <input type="file" hidden multiple={multiple} onChange={handleFileSelect} accept={accept} ref={ref} />
            <div
                className={clsx(
                    'flex w-full cursor-pointer items-center justify-between rounded-md border p-2',
                    error ? 'border-red-500' : 'border-gray-300',
                )}
                onClick={handleSyntheticClick}
            >
                <div className="flex items-center gap-2">
                    <span className=" text-gray-400 ">{getValueToDisplay()}</span>
                </div>
                <div className="font-semibold text-blue-500 underline">
                    <span>Choose File</span>
                </div>
            </div>
        </div>
    );
};

export default FileUpload;
