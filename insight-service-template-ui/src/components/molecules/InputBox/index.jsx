import { forwardRef } from 'react';

import useInput from '@/hooks/useInput';
import { cn } from '@/utils';

import Label from '../Label';

const InputBox = forwardRef(
    (
        {
            name,
            label,
            inputType = 'text',
            error,
            placeholder,
            required,
            disabled = false,
            className,
            labelClassName,
            inputClassName,
            allowFloatValue = true,
            value = '',
            onChange,
            helperView,
            leftAccessory,
            onKeyDown,
            allowNegativeValue = true,
            autoFocus = false,
            sanitize = true,
            tooltipText,
            textbox,
            ...props
        },
        ref,
    ) => {
        const { handleKeyDown, handleInputChange, handleFocus } = useInput({
            allowNegativeValue,
            allowFloatValue,
            onKeyDown,
            type: inputType,
            onChange,
            sanitize,
        });

        return (
            <div className={cn('relative flex w-full max-w-full flex-col text-sm', className)} id={name}>
                <Label className={labelClassName} label={label} required={required} tooltipText={tooltipText} />
                {textbox ? (
                    <>
                        <div
                            className={cn('input focus-within:border-blue-500', {
                                'cursor-not-allowed bg-gray-200': disabled,
                                'border-red-500 hover:border-red-500': error,
                            })}
                        >
                            <textarea
                                className={cn({
                                    'w-full resize-y bg-transparent focus:border-none focus:outline-none': true,
                                    'cursor-not-allowed': disabled,
                                })}
                                placeholder={placeholder ?? label}
                                disabled={disabled}
                                onChange={e => onChange(e.target.value)}
                                value={value ?? ''}
                                rows={3}
                                ref={ref}
                                {...props}
                            />
                        </div>
                        {helperView && !error && (
                            <div className="mt-0.5 flex justify-between text-xs text-gray-500">{helperView()}</div>
                        )}
                    </>
                ) : (
                    <div
                        className={cn(
                            'flex w-full rounded-md border border-gray-300 bg-white px-2 py-2 text-xs text-gray-900 focus-within:border-blue-500',
                            {
                                'cursor-not-allowed bg-gray-200': disabled,
                                'border-red-500 hover:border-red-500 focus:border-red-500': error,
                            },
                            inputClassName,
                        )}
                    >
                        {leftAccessory && <div className="pr-1 font-semibold text-gray-600">{leftAccessory}</div>}
                        <input
                            className="w-full bg-transparent outline-none focus:border-none focus:outline-none"
                            type={inputType || 'text'}
                            placeholder={placeholder || (inputType === 'number' ? 'Enter number' : 'Enter')}
                            disabled={disabled}
                            onChange={handleInputChange}
                            onKeyDown={handleKeyDown}
                            value={value ?? ''}
                            ref={ref}
                            onFocus={handleFocus}
                            autoFocus={autoFocus}
                            {...props}
                        />
                        {helperView && <div className="flex justify-between">{helperView()}</div>}
                    </div>
                )}
            </div>
        );
    },
);

export default InputBox;
