import Button from '@atoms/Button';

const PageFallback = () => {
    return (
        <div className="flex h-[calc(100vh-120px)] w-full flex-col justify-center align-middle">
            <img
                className="mx-auto max-w-full"
                width={400}
                src="https://b.zmtcdn.com/data/o2_assets/832b35389ac6853fad9cd98de54f95311706878060.jpeg"
                alt=""
            />
            <h1 className="mb-6 text-center text-2xl">Uh oh!</h1>
            <p className="text-center text-gray-600">Something went wrong! try reloading the page</p>
            <p className="mb-6 text-center text-gray-600">Open console for more details</p>
            <div className="flex justify-center">
                <Button variant="contained" text="Reload Page" onClick={() => window.location.reload()} />
            </div>
        </div>
    );
};

export default PageFallback;
