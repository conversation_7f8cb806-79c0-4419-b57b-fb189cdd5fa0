import Button from '@/components/atoms/Button';

import Modal from '../Modal';

const ConfirmationModal = ({
    isOpen,
    setIsOpen,
    secondaryBtnText,
    primaryBtnText,
    onSecondaryBtnClick,
    onPrimaryBtnClick,
    message,
    title,
}) => {
    const stopPropagation = e => {
        e.stopPropagation();
    };

    return (
        <Modal open={isOpen} setOpen={setIsOpen} title={title ?? 'Confirm'}>
            <div className="flex flex-col justify-center p-4" onClick={stopPropagation}>
                <div className="w-[96%] max-w-full text-base text-gray-600">{message}</div>
                <div className="mt-8 flex gap-4">
                    <Button block text={secondaryBtnText} variant="outlined" size="md" onClick={onSecondaryBtnClick} />
                    <Button block text={primaryBtnText} size="md" onClick={onPrimaryBtnClick} />
                </div>
            </div>
        </Modal>
    );
};

export default ConfirmationModal;
