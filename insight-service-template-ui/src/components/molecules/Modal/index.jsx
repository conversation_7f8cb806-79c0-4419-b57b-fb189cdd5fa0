import { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

import { cn } from '@/utils';

import ZIcon from '../../atoms/ZIcon';

const Modal = ({ open, setOpen, children, title, className }) => {
    const modalRef = useRef(null);

    const closeModal = () => {
        setOpen(false);
    };

    useEffect(() => {
        if (open) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'auto';
        }

        return () => {
            document.body.style.overflow = 'auto';
        };
    }, [open]);

    return (
        open &&
        createPortal(
            <div
                ref={modalRef}
                aria-hidden="true"
                className={cn(
                    'fixed inset-x-0 top-0 z-20 size-full max-h-full items-end justify-center overflow-hidden sm:items-center md:inset-0 md:items-center',
                    {
                        hidden: !open,
                        flex: open,
                    },
                )}
            >
                <div
                    className={cn(
                        'z-50 h-max min-w-full overflow-y-auto rounded-t-lg bg-white sm:bottom-1/2 sm:right-1/2 sm:min-w-[400px] sm:rounded-lg ',
                        className,
                    )}
                >
                    <div className="relative">
                        <div className={!title && 'pt-10'}>
                            {title ? (
                                <div className="flex items-center justify-between gap-10 border-b border-gray-200 p-3">
                                    <h3 className="text-lg font-normal">{title}</h3>
                                    <div
                                        onClick={closeModal}
                                        className="flex size-8  cursor-pointer items-center justify-center rounded-md hover:bg-gray-100"
                                    >
                                        <ZIcon unicode="E8A4" className="text-sm text-gray-800" />
                                    </div>
                                </div>
                            ) : (
                                <div
                                    onClick={closeModal}
                                    className="absolute right-2 top-2 flex size-8 cursor-pointer items-center justify-center rounded-md hover:bg-gray-100"
                                >
                                    <ZIcon unicode="E8A4" className="m-3 text-sm text-gray-800" />
                                </div>
                            )}
                        </div>
                        <div
                            className={`max-h-[calc(100vh-250px)] w-full overflow-y-auto overflow-x-hidden sm:max-w-[calc(100vw-120px)]`}
                        >
                            {children}
                        </div>
                    </div>
                </div>
                <div
                    className="absolute inset-0 z-40 bg-black opacity-50 transition-opacity"
                    onClick={closeModal}
                ></div>
            </div>,
            document.body,
        )
    );
};

export default Modal;
