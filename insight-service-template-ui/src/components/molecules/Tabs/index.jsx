import { useEffect, useState } from 'react';

import Markdown from '../Markdown';
import { RenderUIFrom } from '../RenderUIFrom';

const paramName = 'tab';

const Tabs = ({ tabs = [] }) => {
    const [currentActiveTab, setCurrentActiveTab] = useState();

    const changeTab = tab => {
        var searchParams = new URLSearchParams(window.location.search);

        if (searchParams.has(paramName)) {
            searchParams.set(paramName, tab?.path);
        } else {
            searchParams.append(paramName, tab?.path);
        }

        var newUrl = window.location.pathname + '?' + searchParams.toString();

        history.pushState({}, '', newUrl);
        setCurrentActiveTab(tab);
    };

    useEffect(() => {
        if (tabs.length > 0) {
            const searchParams = new URLSearchParams(window.location.search);
            if (searchParams.has(paramName)) {
                const tab = tabs.find(
                    tab => tab?.path?.toLocaleLowerCase() === searchParams.get(paramName)?.toLocaleLowerCase(),
                );
                if (tab) changeTab(tab);
                else changeTab(tabs[0]);
            } else changeTab(tabs[0]);
        }
    }, [tabs]);

    return (
        <div>
            <div className="border-b border-gray-200">
                <nav className="flex gap-6" aria-label="Tabs">
                    {tabs.map(tab => {
                        const isActiveTab =
                            tab?.path?.toLocaleLowerCase() === currentActiveTab?.path?.toLocaleLowerCase();

                        return (
                            <div
                                onClick={() => changeTab(tab)}
                                className={`shrink-0 cursor-pointer border-b-2 px-1 pb-3 text-sm font-medium text-gray-500  hover:text-gray-700 ${isActiveTab ? 'border-sky-500' : 'border-transparent hover:border-gray-300'}`}
                                key={tab?.path}
                            >
                                <Markdown>{tab.title}</Markdown>
                            </div>
                        );
                    })}
                </nav>
            </div>
            <div className="mt-4">{RenderUIFrom(currentActiveTab?.view, currentActiveTab?.path)}</div>
        </div>
    );
};

export default Tabs;
