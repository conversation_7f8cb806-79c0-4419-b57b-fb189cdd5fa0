import useDebounce from '@hooks/useDebounce';
import { useDropdown } from '@hooks/useDropdown';
import { useCallback, useState } from 'react';
import Select from 'react-select';
import AsyncSelect from 'react-select/async';

import { cn } from '@/utils';

import Label from '../Label';

const Dropdown = ({
    networkCall,
    options,
    dependencies = [],
    mappedWatchValues,
    dependencyKeyMap,
    placeholder,
    onChange,
    label,
    required,
    value,
    disabled,
    className,
    error,
    tooltipText,
    isAsync,
    loadOptions,
    debounce,
    labelClassName = '',
    formControls,
    onlyValue = true,
    multiple = false,
    afterChange,
    children,
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchVal, setSearchVal] = useState('');
    const [showDependencyTooltip, setShowDependencyTooltip] = useState(false);

    const getDependentValues = () => {
        const dependentValues = {};
        dependencies.map(dependency => {
            dependentValues[dependency] = mappedWatchValues[dependency];
        });
        return dependentValues;
    };

    const dependentValues = getDependentValues();

    const { parsedOptions, dependentLabels, canFetch } = useDropdown({
        dependencies,
        dependentValues,
        dependencyKeyMap,
        options,
        networkCall,
        formConfig: formControls?.formConfig,
        onlyValue,
        isOpen,
    });

    const dropdownStyles = {
        menuPortal: base => ({ ...base, zIndex: 130 }),
        menuList: base => ({ ...base, fontSize: '0.75rem', borderRadius: '6px' }),
        control: base => ({
            ...base,
            borderRadius: '6px',
            cursor: disabled || !canFetch ? 'not-allowed' : 'pointer',
            backgroundColor: disabled || !canFetch ? '#e5e7eb' : 'white',
            border: disabled || !canFetch ? '1px solid #d1d5db' : error ? '1px solid #FF0000' : '1px solid #E5E7EB',
        }),
        option: (base, { isFocused }) => {
            return {
                ...base,
                backgroundColor: isFocused ? '#E5E7EB' : 'white',
                color: 'black',
                cursor: 'pointer',
            };
        },
    };

    const handleSelect = item => {
        let newVal = item;
        if (onlyValue) {
            multiple ? (newVal = item?.map(option => option.value)) : (newVal = item?.value);
        }

        onChange(newVal);
        afterChange && afterChange(newVal);
    };

    const handleDropdownMouseEnter = () => {
        !canFetch && setShowDependencyTooltip(true);
    };

    const handleDropdownMouseLeave = () => {
        setShowDependencyTooltip(false);
    };

    const loadOptionsDebounced = useDebounce(loadOptions, debounce);

    const handleSearchChange = (searchVal, callback) => {
        setSearchVal(searchVal);
        loadOptionsDebounced(searchVal, callback);
    };

    const getNoMessage = () => {
        if (searchVal?.trim() === '') return 'Type to search';
        return 'No options found';
    };

    const getCurrVal = useCallback(() => {
        if (onlyValue) {
            if (multiple) {
                return parsedOptions.filter(option => value?.includes(option.value));
            } else {
                return parsedOptions.find(option => option.value === value);
            }
        } else return value;
    }, [value, parsedOptions, onlyValue, multiple]);

    return (
        <>
            <div className={cn('z-10 flex w-full flex-col ', className)}>
                <Label label={label} required={required} tooltipText={tooltipText} className={labelClassName} />
                <div className="relative">
                    {showDependencyTooltip && dependentLabels.length > 0 && !canFetch && (
                        <div className="absolute -top-2 right-0 max-w-full -translate-y-full ">
                            <div className="relative rounded-md bg-blue-100 p-1 px-2 text-xs text-blue-800">
                                <div>
                                    Please select
                                    {dependentLabels.map((label, index) => {
                                        return (
                                            <span key={label || index}>{` "${label}"${
                                                index === dependentLabels.length - 1 ? '' : ','
                                            }`}</span>
                                        );
                                    })}
                                </div>
                                <div className="absolute -bottom-1 right-3 -z-10 size-2 rotate-45 bg-blue-100" />
                            </div>
                        </div>
                    )}
                </div>
                <div
                    onMouseEnter={handleDropdownMouseEnter}
                    onMouseLeave={handleDropdownMouseLeave}
                    className={cn({
                        'cursor-not-allowed': disabled,
                    })}
                >
                    {isAsync ? (
                        <AsyncSelect
                            cacheOptions
                            defaultOptions
                            placeholder={placeholder ?? 'Select'}
                            value={getCurrVal() || ''}
                            noOptionsMessage={getNoMessage}
                            onChange={handleSelect}
                            isDisabled={disabled}
                            menuPortalTarget={document.body}
                            styles={dropdownStyles}
                            className="text-xs"
                            menuPlacement="auto"
                            loadOptions={handleSearchChange}
                            onMenuOpen={() => setIsOpen(true)}
                            onMenuClose={() => setIsOpen(false)}
                            isMulti={multiple}
                        />
                    ) : (
                        <Select
                            options={parsedOptions}
                            placeholder={placeholder ?? 'Select'}
                            value={getCurrVal() || ''}
                            onChange={handleSelect}
                            isDisabled={disabled || !canFetch}
                            menuPortalTarget={document.body}
                            styles={dropdownStyles}
                            className="text-xs"
                            menuPlacement="auto"
                            onMenuOpen={() => setIsOpen(true)}
                            onMenuClose={() => setIsOpen(false)}
                            isMulti={multiple}
                        />
                    )}
                </div>
            </div>
            {children}
        </>
    );
};

export default Dropdown;
