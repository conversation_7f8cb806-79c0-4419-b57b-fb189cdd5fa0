export const getDropdownStyles = error => {
    return {
        menuPortal: base => ({ ...base, zIndex: 50 }),
        menuList: base => ({
            ...base,
            fontSize: '0.75rem',
            borderRadius: '6px',
        }),
        control: base => ({
            ...base,
            borderRadius: '6px',
            border: error ? '1px solid #FF0000' : '1px solid #E5E7EB',
        }),
        option: (base, { isFocused }) => {
            return {
                ...base,
                backgroundColor: isFocused ? '#E5E7EB' : 'white',
                color: 'black',
                cursor: 'pointer',
            };
        },
    };
};
