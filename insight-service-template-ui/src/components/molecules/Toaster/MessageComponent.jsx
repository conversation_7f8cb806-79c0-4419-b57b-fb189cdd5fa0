import ZIcon from '@/components/atoms/ZIcon';
import { cn } from '@/utils';

const MessageComponent = ({ type, title, message, closeToast, iconUnicode }) => {
    return (
        <div
            className={cn({
                'flex min-h-16 flex-col items-center justify-center': true,
                'bg-green-100': type === 'success',
                'bg-red-100': type === 'error',
                'bg-yellow-100': type === 'warn',
                'bg-blue-100': type === 'info',
            })}
        >
            <div className="flex  w-full items-start gap-2 rounded-md p-3">
                <ZIcon
                    unicode={iconUnicode}
                    className={cn({
                        'text-green-600': type === 'success',
                        'text-red-600': type === 'error',
                        'text-yellow-600': type === 'warn',
                        'text-blue-600': type === 'info',
                    })}
                />
                <div className="flex flex-col gap-1">
                    <div className="text-sm font-semibold leading-6 text-gray-800">{title}</div>
                    <div className="text-sm leading-4 text-gray-600">{message}</div>
                </div>
                <div className="ml-auto cursor-pointer" onClick={closeToast}>
                    <ZIcon unicode="E986" className="ml-auto mt-0.5 text-gray-500" />
                </div>
            </div>
        </div>
    );
};

export default MessageComponent;
