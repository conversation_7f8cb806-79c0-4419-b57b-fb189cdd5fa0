import { toast } from 'react-toastify';

import MessageComponent from './MessageComponent';

const getProps = myProps => {
    if (typeof myProps === 'string') {
        return { message: myProps };
    }
    return myProps;
};

const toaster = {
    success: (myProps, toastProps) => {
        return toast(<MessageComponent {...getProps(myProps)} iconUnicode="E93C" type="success" />, toastProps);
    },

    error: (myProps, toastProps) => {
        return toast(<MessageComponent {...getProps(myProps)} iconUnicode="E87F" type="error" />, toastProps);
    },

    warn: (myProps, toastProps) => {
        return toast(<MessageComponent {...getProps(myProps)} iconUnicode="E881" type="warn" />, toastProps);
    },

    info: (myProps, toastProps) => {
        return toast(<MessageComponent {...getProps(myProps)} iconUnicode="E805" type="info" />, toastProps);
    },
};

export default toaster;
