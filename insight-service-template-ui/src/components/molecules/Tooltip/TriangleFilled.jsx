import { forwardRef } from 'react';

const TriangleFilled = forwardRef((props, ref) => {
    return (
        <svg ref={ref} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" {...props}>
            <g fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2">
                <path d="M0 0h24v24H0z"></path>
                <path
                    fill="currentColor"
                    d="M12 1.67a2.914 2.914 0 0 0-2.492 1.403L1.398 16.61a2.914 2.914 0 0 0 2.484 4.385h16.225a2.914 2.914 0 0 0 2.503-4.371L14.494 3.078A2.917 2.917 0 0 0 12 1.67"
                ></path>
            </g>
        </svg>
    );
});

export default TriangleFilled;

TriangleFilled.displayName = 'TriangleFilled';
