import { useEffect, useRef, useState } from 'react';

import { cn, isEmpty } from '@/utils';

import TriangleFilled from './TriangleFilled';
import TriangleInvertedFilled from './TriangleInvertedFilled';

const Tooltip = ({ content, children, className }) => {
    const [hover, setHover] = useState(false);
    const hoverTimeout = useRef(null);
    const tooltipContentRef = useRef(null);
    const triangleRef = useRef(null);
    const triangleInvertedRef = useRef(null);
    const tooltipRef = useRef(null);

    const delay = 300;

    const handleMouseEnter = () => {
        hoverTimeout.current = setTimeout(() => {
            setHover(true);
        }, delay);
    };

    const handleMouseLeave = () => {
        if (hoverTimeout.current) {
            clearTimeout(hoverTimeout.current);
            hoverTimeout.current = null;
        }
        setHover(false);
    };

    const updateTooltipPosition = () => {
        if (tooltipContentRef.current && tooltipRef.current && triangleRef.current && triangleInvertedRef.current) {
            const rect = tooltipContentRef.current.getBoundingClientRect();

            let { top, left, right } = rect;
            const padding = 40;

            // overflowing from left side
            if (left < 0 + padding) {
                const newLeft = Math.abs(left) + padding;
                tooltipContentRef.current.style.left = `${newLeft}px`;
            }
            // overflowing from right side
            else if (right + padding > window.innerWidth) {
                const newRight = right + padding - window.innerWidth;
                tooltipContentRef.current.style.right = `${newRight}px`;
            }

            // overflowing from top side
            if (top < 0) {
                // unset top and set bottom
                tooltipRef.current.style.top = 'unset';
                tooltipRef.current.style.bottom = '0';
                tooltipRef.current.style.transform = 'translateY(calc(100% + 10px))';
                triangleInvertedRef.current.style.display = 'none';
            }
        }
    };

    // Update position on window resize
    useEffect(() => {
        const handleResize = () => {
            if (hover) {
                updateTooltipPosition();
            }
        };

        handleResize();
        window.addEventListener('resize', handleResize);

        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, [hover]);

    return (
        <div
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            className={cn('relative inline-flex flex-col items-center ', className)}
        >
            {hover && !isEmpty(content) && content && (
                <div
                    ref={tooltipRef}
                    className="absolute left-0 top-0 mx-auto flex w-full items-center justify-center gap-0  [transform:translateY(calc(-100%-10px))] [z-index:999999]"
                >
                    <div className="mx-auto flex w-0 flex-col items-center justify-center text-yellow-300">
                        <TriangleFilled ref={triangleRef} style={{ marginBottom: '-7px', display: 'none' }} />

                        <div
                            ref={tooltipContentRef}
                            className="relative whitespace-nowrap rounded-md border border-yellow-300 bg-yellow-100 p-2.5 text-xs  leading-relaxed tracking-wide text-yellow-900 shadow-sm [font-weight:400]"
                        >
                            <div className="w-max max-w-screen-xs text-wrap">{content}</div>
                        </div>

                        <TriangleInvertedFilled
                            ref={triangleInvertedRef}
                            style={{ marginTop: '-7px', display: 'none' }}
                        />
                    </div>
                </div>
            )}
            {children}
        </div>
    );
};

export default Tooltip;
