import ZIcon from '@atoms/ZIcon';
import { useEffect, useState } from 'react';

function DebouncedInput({ value: initialValue, onChange, debounce = 200, unicode, ...props }) {
    const [value, setValue] = useState(initialValue);

    useEffect(() => {
        setValue(initialValue);
    }, [initialValue]);

    useEffect(() => {
        const timeout = setTimeout(() => {
            onChange(value);
        }, debounce);

        return () => clearTimeout(timeout);
    }, [value]);

    return (
        <div className="input flex items-center gap-1">
            <input
                className="w-full focus:border-none focus:outline-none"
                {...props}
                value={value}
                onChange={e => setValue(e.target.value)}
            />
            {unicode && <ZIcon unicode={unicode} className="text-xs text-gray-400" />}
        </div>
    );
}

export default DebouncedInput;
