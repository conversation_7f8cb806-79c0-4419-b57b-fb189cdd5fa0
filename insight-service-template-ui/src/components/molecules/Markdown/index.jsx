import MDEditor from '@uiw/react-md-editor';

const Markdown = ({ text, children }) => {
    const stringText = text?.toString() || children?.toString() || '';

    return (
        <div className="w-full bg-transparent" data-color-mode="light">
            <MDEditor.Markdown
                source={stringText}
                rehypeRewrite={(node, index, parent) => {
                    if (node.tagName === 'a' && parent && /^h(1|2|3|4|5|6)/.test(parent.tagName)) {
                        parent.children = parent.children.slice(1);
                    }
                    if (node.tagName === 'h1' || node.tagName === 'h2') {
                        node.properties = {
                            ...node.properties,
                            style: 'border-bottom: none',
                        };
                    }
                }}
                style={{
                    fontSize: 'inherit',
                    fontFamily: 'inherit',
                    fontWeight: 'inherit',
                    whiteSpace: 'pre-wrap',
                    overflowWrap: 'break-word',
                    background: 'transparent',
                }}
            />
        </div>
    );
};

export default Markdown;
