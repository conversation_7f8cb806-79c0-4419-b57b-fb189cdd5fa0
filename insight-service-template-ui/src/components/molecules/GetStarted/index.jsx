import Hi from '@atoms/Highlighter';

import Launch from '../../../assets/Launch.svg';

function GetStarted() {
    return (
        <div className="flex w-full flex-col justify-center p-6 align-middle">
            <h1 className="mb-8 text-center text-3xl">You&apos;re all set to begin 🎯</h1>
            <div className="mt-6 flex justify-center">
                <img width={200} src={Launch} alt="Get started image" />
            </div>
            <div className="mt-16 flex w-2/3 flex-col self-center leading-8">
                <h1 className="mb-6 text-2xl text-gray-700">Let&apos;s get you started</h1>
                <h1 className="mb-4 text-xl text-gray-500 underline">Steps</h1>
                <div className="mb-4">
                    <h2 className="mt-1 inline text-gray-700">1.</h2>
                    <p className=" ml-4 mt-2 inline text-gray-600">
                        Open <Hi>src/configs/config.yaml</Hi>. Update <Hi>serviceConstants</Hi> and{' '}
                        <Hi>endpointsMapping</Hi> for network calls. Define pages under <Hi>pages</Hi> key in yaml .
                        Explore pre-build configs in <Hi>docs/exampleConfigs</Hi> folder and interact with components in
                        the{' '}
                        <a
                            href="http://localhost:6006/?path=/docs/configure-your-project--docs"
                            target="_blank"
                            rel="noreferrer"
                            className="text-blue-600 underline"
                        >
                            Storybook
                        </a>
                    </p>
                </div>
                <div>
                    <h2 className="mt-4 inline text-gray-700">2.</h2>
                    <p className=" ml-4 mt-2 inline text-gray-600">
                        Open <Hi>src/configs/sidebar.yaml</Hi>. Update or add more sidebar items as needed. Remember to
                        provide the correct <Hi>page-key</Hi> value, that match with any of the page keys defined in{' '}
                        <Hi>src/configs/config.yaml</Hi>. This sidebar menu will render the corresponding page on the
                        specified path.
                    </p>
                </div>
            </div>
        </div>
    );
}

export default GetStarted;
