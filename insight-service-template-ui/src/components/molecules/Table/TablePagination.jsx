import ZIcon from '@atoms/ZIcon';
import Pagination from '@molecules/Pagination';

const BTag = ({ children }) => {
    return <span className="text-gray-600">{children}</span>;
};

const DEFAULT_PAGE_SIZE = 10;

const TablePagination = ({
    totalCount,
    currentPageRowCount,
    siblingCount,
    currentPage,
    onPageChange,
    pageSize = DEFAULT_PAGE_SIZE,
    onPageSizeChange,
    message = null,
}) => {
    const handlePageSizeChange = e => {
        if (e.key === 'Enter') {
            const { value } = e.currentTarget;
            if (value === '') return;
            const parsedValue = parseInt(value);
            onPageSizeChange(parsedValue);
            if (currentPage > Math.ceil(totalCount / parsedValue)) {
                onPageChange(Math.ceil(totalCount / parsedValue));
            }
        }
    };

    const handleOnInputNumber = e => {
        const { value } = e.currentTarget;
        if (value === '') return;
        e.currentTarget.value = Math.max(1, parseInt(value)).toString().slice(0, 2);
    };

    return (
        <div className="flex min-w-full flex-wrap-reverse items-center justify-center gap-4 border-t border-gray-200 p-3 md:flex-nowrap md:justify-between">
            <div className="w-max text-[13px] font-normal text-gray-400 md:w-fit">
                Showing <BTag>{(currentPage - 1) * pageSize + 1}</BTag> to{' '}
                <BTag>{(currentPage - 1) * pageSize + currentPageRowCount}</BTag> of <BTag>{totalCount}</BTag> entries
            </div>
            {message ? (
                <div className="gap-1 text-[13px] font-normal text-gray-400 md:w-fit">
                    {message}
                    <ZIcon unicode="E806" className="ml-2" />
                </div>
            ) : null}
            <div className="flex flex-wrap items-center justify-center gap-x-8 gap-y-4 md:flex-nowrap">
                <div className="flex items-center">
                    <div className="mr-4 text-xs font-normal text-gray-400">Rows per page</div>
                    <input
                        type="number"
                        defaultValue={pageSize}
                        min={1}
                        step={1}
                        max={pageSize}
                        onKeyDown={handlePageSizeChange}
                        onInput={handleOnInputNumber}
                        className="block w-16 rounded-lg border border-gray-300 bg-gray-50 p-2 py-1 text-xs font-medium text-gray-900 focus:border-blue-400 focus:ring-blue-500"
                    />
                </div>
                <div className="font-normal text-gray-400">
                    <Pagination
                        totalCount={totalCount}
                        siblingCount={siblingCount}
                        currentPage={currentPage}
                        pageSize={pageSize || 1}
                        onPageChange={onPageChange}
                    />
                </div>
            </div>
        </div>
    );
};
export default TablePagination;
