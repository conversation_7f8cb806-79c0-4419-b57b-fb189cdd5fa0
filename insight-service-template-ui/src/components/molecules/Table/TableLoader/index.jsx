import Spinner from '@atoms/Spinner';

const TableLoader = ({ isLoading }) => {
    if (!isLoading) return null;

    const dynamicClasses = 'flex gap-3 border-b h-[calc(100vh-350px)] min-h-64 items-center justify-center rounded-md';

    return (
        <div className={dynamicClasses}>
            <Spinner size="xl" color="blue-500" />
            <span className="text-xl">Fetching Data</span>
        </div>
    );
};

export default TableLoader;
