import React from 'react';

function TableInputView({ row, cell, setTableData, onEdit }) {
    const [isEditing, setIsEditing] = React.useState(false);
    const [value, setValue] = React.useState(row.getValue(cell.column.id));

    const onSubmit = updatedValue => {
        const newData = setTableData(currentTableData =>
            currentTableData.map(data => {
                if (data.id === row.original.id) {
                    return {
                        ...data,
                        [cell.column.id]: updatedValue,
                    };
                }
                return data;
            }),
        );
        onEdit && onEdit(newData);
    };

    const handlekeyDown = e => {
        if (e.key === 'Enter') {
            onSubmit(value);
            setIsEditing(false);
        }
    };

    const handleInputChange = e => {
        setValue(e.target.value);
    };

    return (
        <div>
            {isEditing ? (
                <input
                    value={value}
                    type="text"
                    className="w-24 rounded-md border-2 border-gray-300 px-2 py-1"
                    onChange={handleInputChange}
                    onKeyDown={handlekeyDown}
                    onBlur={() => setIsEditing(false)}
                />
            ) : (
                <div className="text-gray-800" onDoubleClick={() => setIsEditing(true)}>
                    {value}
                </div>
            )}
        </div>
    );
}

export default TableInputView;
