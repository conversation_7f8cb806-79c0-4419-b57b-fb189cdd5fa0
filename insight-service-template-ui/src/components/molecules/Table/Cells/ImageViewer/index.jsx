import React, { useState } from 'react';

import { cn } from '@/utils';

import Modal from '../../../Modal';

function ImageViewer({ images = [] }) {
    const [open, setOpen] = React.useState(false);
    const [slideIdx, setSlideIdx] = useState(0);

    const nextSlide = () => {
        const nextIdx = slideIdx < images?.length - 1 ? slideIdx + 1 : 0;
        setSlideIdx(nextIdx);
    };

    const prevSlide = () => {
        const prevIdx = slideIdx > 0 ? slideIdx - 1 : images?.length - 1;
        setSlideIdx(prevIdx);
    };

    return (
        <>
            <Modal title={'Images'} open={open} setOpen={setOpen}>
                <div className="relative max-w-[400px] overflow-x-hidden">
                    <div
                        className={cn('absolute left-2 top-1/2 z-10 translate-y-1/2 rounded-full shadow-md', {
                            hidden: images?.length === 1,
                        })}
                        onClick={prevSlide}
                    >
                        <button className="size-6 rounded-full bg-white">{'<'}</button>
                    </div>
                    <div
                        className="flex translate-x-6"
                        style={{
                            transform: `translateX(-${slideIdx * 100}%)`,
                            transition: 'all 0.5s ease-in-out',
                        }}
                    >
                        {images?.map((image, index) => {
                            return (
                                <div key={index + image} className="h-80 min-w-full">
                                    <img className="size-full object-cover" src={image} alt="" />
                                </div>
                            );
                        })}
                    </div>
                    <div
                        className={cn('absolute bottom-1/2 right-2 z-10 translate-y-1/2 rounded-full shadow-md', {
                            hidden: images?.length === 1,
                        })}
                        onClick={nextSlide}
                    >
                        <button className="size-6 rounded-full bg-white">{'>'}</button>
                    </div>
                </div>
            </Modal>
            <div
                className="relative"
                onClick={() => {
                    images.length > 0 && setOpen(true);
                }}
            >
                <div className="flex h-12 min-w-12 max-w-12 flex-wrap justify-center overflow-hidden rounded-sm shadow-sm">
                    {images.length > 0 ? (
                        images?.map((image, index) => {
                            return (
                                <img className={`w-1/2 flex-1 object-cover`} key={index + image} src={image} alt="" />
                            );
                        })
                    ) : (
                        <div className="flex items-center bg-gray-200 text-center text-xs">No Image</div>
                    )}
                </div>
                {images.length > 0 && (
                    <div
                        className="absolute left-0 top-0 flex size-full max-w-12 cursor-pointer items-center 
              justify-center bg-black/50 text-gray-50 opacity-0 transition-all duration-200 hover:opacity-100
              "
                    >
                        View
                    </div>
                )}
            </div>
        </>
    );
}

export default ImageViewer;
