'use client';

import Button from '@atoms/Button';
import TextLimiter from '@atoms/TextLimiter';
import { useState } from 'react';

export const DEFAULT_VISIBLE_CHIPS = 2;
function MultiChips({ chips = [] }) {
    const [expanded, setExpanded] = useState(false);
    return (
        <div className="flex w-max max-w-[250px] flex-wrap gap-2">
            {chips.slice(0, expanded ? undefined : 2).map((chip, index) => {
                return (
                    <div
                        key={index}
                        className="max-w-full text-ellipsis break-words rounded-md border bg-gray-50 p-1 px-2"
                    >
                        <TextLimiter limit={100} text={chip} />
                    </div>
                );
            })}
            {chips.length > DEFAULT_VISIBLE_CHIPS && (
                <Button
                    variant="text"
                    text={expanded ? 'Show less' : 'Show all'}
                    onClick={() => setExpanded(prev => !prev)}
                />
            )}
        </div>
    );
}

export default MultiChips;
