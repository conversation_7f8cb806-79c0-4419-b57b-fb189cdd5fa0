import { cn } from '@/utils';

const CustomStatusCell = ({ row, cell, greenStatus = [], blueStatus = [], redStatus = [], yellowStatus = [] }) => {
    const text = row.original[cell.column.id];
    const lowerText = text?.toLowerCase();

    return (
        <div>
            <span
                className={cn({
                    'rounded-md px-2 py-1 text-sm': true,
                    'bg-green-100 text-green-800': greenStatus.includes(lowerText),
                    'bg-blue-100 text-blue-800': blueStatus.includes(lowerText),
                    'bg-red-100 text-red-800': redStatus.includes(lowerText),
                    'bg-yellow-100 text-yellow-800': yellowStatus.includes(lowerText),
                    'bg-gray-100 text-gray-800':
                        !greenStatus.includes(lowerText) &&
                        !blueStatus.includes(lowerText) &&
                        !redStatus.includes(lowerText) &&
                        !yellowStatus.includes(lowerText),
                })}
            >
                {text}
            </span>
        </div>
    );
};

export default CustomStatusCell;
