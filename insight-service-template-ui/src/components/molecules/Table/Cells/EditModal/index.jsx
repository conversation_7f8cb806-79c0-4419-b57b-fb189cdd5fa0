import Button from '@atoms/Button';
import useErrorContext from '@hooks/useErrorContext';
import useRequest from '@hooks/useRequest';
import Modal from '@molecules/Modal';
import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import errorConfig from '@/utils/error.yaml';

function EditModal({ row, onDelete, getTableData, primaryKey = 'id', isEditing }) {
    const { pushError } = useErrorContext();
    const [searchParams, setSearchParams] = useSearchParams();
    const [deleteModalOpen, setDeleteModalOpen] = React.useState(false);
    const [isDeleting, setIsDeleting] = React.useState(false);
    const { customRequest } = useRequest();

    const handleDelete = async () => {
        if (onDelete === undefined) {
            pushError({
                error: `Please provide onDelete inside networkCalls of the table`,
                solution: errorConfig.missingOnDeleteInTable,
            });
            return;
        }
        try {
            setIsDeleting(true);
            const res = await customRequest(onDelete, {
                body: {
                    ...row.original,
                },
                options: {
                    [primaryKey]: encodeURIComponent(row.original[primaryKey]),
                },
            });
            if (res.customError) return;
            getTableData();
            toast.success('Data deleted successfully');
        } catch (error) {
            toast.error('Error while deleting the data');
        } finally {
            setIsDeleting(false);
            setDeleteModalOpen(false);
        }
    };

    const handleEditOpen = () => {
        searchParams.set('action', 'edit');
        searchParams.set('id', row.original[primaryKey]);
        setSearchParams(searchParams);
    };

    return (
        <div onClick={e => e.stopPropagation()}>
            <Modal open={deleteModalOpen} setOpen={setDeleteModalOpen} className="!min-w-fit">
                <div className="flex flex-col items-center justify-center pb-6 pl-6 pr-8">
                    <h1 className="mb-4 text-center text-xl ">Are you sure?</h1>
                    <p className="mb-8 w-80 text-center text-base text-gray-500">
                        Do you really want to delete this record? This process cannot be undone.
                    </p>
                    <div className="flex items-center justify-center gap-2">
                        <Button
                            onClick={() => setDeleteModalOpen(false)}
                            variant="outlined"
                            block={true}
                            text="No"
                            color="red"
                            size="md"
                            className={'min-w-20'}
                        />

                        <Button
                            onClick={handleDelete}
                            block={true}
                            variant="contained"
                            text="Yes"
                            color="red"
                            textLoading="Deleting..."
                            size="md"
                            className={'min-w-20'}
                            isLoading={isDeleting}
                        />
                    </div>
                </div>
            </Modal>
            <div className="flex gap-2">
                <Button
                    onClick={handleEditOpen}
                    variant="outlined"
                    color="blue"
                    text="Edit"
                    textLoading="Editing..."
                    size="md"
                    className={'min-w-20'}
                    isLoading={isEditing?.status && isEditing?.id === row.original[primaryKey]}
                />
                {onDelete && (
                    <Button
                        onClick={() => setDeleteModalOpen(true)}
                        variant="outlined"
                        text="Delete"
                        textLoading="Deleting..."
                        color="red"
                        size="md"
                        className={'min-w-20'}
                        isLoading={isDeleting}
                    />
                )}
            </div>
        </div>
    );
}

export default EditModal;
