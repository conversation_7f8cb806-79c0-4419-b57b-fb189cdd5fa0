export const checkIfImage = value => {
    const stringVal = value.toString();
    return stringVal?.endsWith('.png') ||
        stringVal?.endsWith('.jpg') ||
        stringVal?.endsWith('.jpeg') ||
        stringVal?.endsWith('.gif')
        ? true
        : false;
};

export const checkIfImageArray = values => {
    for (let i = 0; i < values.length; i++) {
        if (checkIfImage(values[i])) return true;
    }
    return false;
};

export const getValueType = value => {
    if (typeof value === 'string' || typeof value === 'number') return 'string';
    else if (Array.isArray(value)) {
        if (checkIfImageArray(value)) return 'imageArray';
        else return 'multiselectArray';
    }
};
