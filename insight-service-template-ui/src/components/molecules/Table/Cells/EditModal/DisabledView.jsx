import { format as formatDate } from 'date-fns';

export const checkIfImage = value => {
    const stringVal = value.toString();
    return stringVal?.endsWith('.png') ||
        stringVal?.endsWith('.jpg') ||
        stringVal?.endsWith('.jpeg') ||
        stringVal?.endsWith('.gif')
        ? true
        : false;
};

export const checkIfImageArray = values => {
    for (let i = 0; i < values.length; i++) {
        if (checkIfImage(values[i])) return true;
    }
    return false;
};

const getValueType = value => {
    if (typeof value === 'string' || typeof value === 'number') return 'string';
    else if (typeof value === 'boolean') return 'boolean';
    else if (Array.isArray(value)) {
        if (checkIfImageArray(value)) return 'imageArray';
        else return 'multiselectArray';
    } else if (typeof value === 'object') return 'object';
};

const RenderValue = ({ value, valueType, type, format }) => {
    if (type === 'date') {
        const date = new Date(value);
        return <div>{formatDate(date, format)}</div>;
    }
    switch (valueType) {
        case 'string':
            return <div>{value}</div>;
        case 'boolean':
            return <div>{value.toString()}</div>;
        case 'object':
            return <div>{JSON.stringify(value)}</div>;
        case 'imageArray':
            return (
                <div className="flex flex-wrap gap-2">
                    {value.map((val, index) => {
                        return <img key={index} src={val} alt="img" className="size-20 rounded-md" />;
                    })}
                </div>
            );
        case 'multiselectArray':
            return (
                <div>
                    {value.map((val, index) => {
                        return <div key={index}>{JSON.stringify(val)}</div>;
                    })}
                </div>
            );
        default:
            return null;
    }
};

const DisabledView = ({ label, value, type, format }) => {
    const valueType = getValueType(value);

    return (
        <div>
            <div className="label">{label}</div>
            <div className="input cursor-not-allowed bg-gray-200 opacity-75">
                <RenderValue value={value} type={type} format={format} valueType={valueType} />
            </div>
        </div>
    );
};

export default DisabledView;
