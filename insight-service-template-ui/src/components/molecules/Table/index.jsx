import Button from '@atoms/Button';
import ZIcon from '@atoms/ZIcon';
import useCustomFormControls from '@hooks/useCustomFormControls';
import { useTableData } from '@hooks/useTableData';
import FormSideDrawer from '@molecules/FormSideDrawer';
import { rankItem } from '@tanstack/match-sorter-utils';
import {
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from '@tanstack/react-table';
import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { QUERY_PARAM_IDENTIFIER } from '@/constants';
import { cn, normalizeConfig } from '@/utils';
import { deleteQueryParam, extractVariables, updateQueryParam } from '@/utils/urlManipulation';

import CreateButton from '../CreateButton';
import DebouncedInput from '../DebouncedInput';
import FilterButton from '../FilterButton';
import NoResults from './NoResults';
import { parseTableColumnsForCell } from './parsers/cellParser';
import { parseTableColumnsForEditForm } from './parsers/columnFormParser';
import TableLoader from './TableLoader';
import TablePagination from './TablePagination';

const fuzzyFilter = (row, columnId, value, addMeta) => {
    // Rank the item
    // convert the value to a string by using JSON.stringify and then remove the quotes
    const stringifiedValue = value ? JSON.stringify(value).replace(/['"]+/g, '') : '';
    const cellVal = row.getValue(columnId);
    const stringifiedRowValue = cellVal ? JSON.stringify(cellVal).replace(/['"]+/g, '') : '';

    const itemRank = rankItem(stringifiedRowValue, stringifiedValue);

    // Store the itemRank info
    addMeta({
        itemRank,
    });

    // Return if the item should be filtered in/out
    return itemRank.passed;
};

function Table({
    columns = [],
    data: TableData,
    isLoading: isTableLoading,
    networkCalls,
    createButtonText,
    createButtonHeaderText,
    createConfig,
    filterConfig,
    accessorKey,
    editAccessorKey,
    pagination: controlledPagination,
    hiddenColumns = [],
    enableSearch = true,
    allFieldsVisibleInEdit,
    editHeader,
    primaryKey,
    searchPlaceholder,
    getData,
    onRowClick,
    editFormControls,
    createFormControls,
    editKeyMap,
    onEdit,
    editConfig,
    actions,
    header,
    noPagination = false,
    onPageSizeChange,
    onPageChange,
    onSearchChange,
    handleFilterSubmit,
}) {
    const customEditFormControls = useCustomFormControls(editFormControls);
    const customCreateFormControls = useCustomFormControls(createFormControls);
    const navigate = useNavigate();
    const {
        data,
        setData,
        loadingStates: { isLoading },
        setRefresh,
        setIsLoading,
        fetchData,
        handleEditSubmit,
        handleEditGet,
        fetchingEditData,
        isEditing,
    } = useTableData({
        networkCalls,
        accessorKey,
        getData,
        primaryKey,
        onEdit,
        TableData,
        editAccessorKey,
        editFormControls: customEditFormControls,
        isTableLoading,
    });

    const [globalFilter, setGlobalFilter] = React.useState('');
    const location = useLocation();
    const searchQueryParamKey = QUERY_PARAM_IDENTIFIER.TABLE_SEARCH_KEY;

    const parsedColumnConfig = parseTableColumnsForCell({
        columns,
        data,
        hiddenColumns,
        networkCalls,
    });

    const table = useReactTable({
        columns: parsedColumnConfig,
        data,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onGlobalFilterChange: setGlobalFilter,
        globalFilterFn: fuzzyFilter,
        state: {
            globalFilter,
        },
        defaultColumn: {
            enableSorting: false,
        },
    });

    const editFormConfig = parseTableColumnsForEditForm(columns, data, hiddenColumns, allFieldsVisibleInEdit) || [];

    const handlePageSizeChange = size => {
        const sizeNumber = parseInt(size.toString());
        table.setPageSize(sizeNumber);
        onPageSizeChange && onPageSizeChange(sizeNumber);
    };

    const handlePageChange = page => {
        table.setPageIndex(Number(page) - 1);
        onPageChange && onPageChange(page);
    };

    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const query = searchParams.get(searchQueryParamKey);
        if (query) {
            setGlobalFilter(query);
        }
    }, [location.search, searchQueryParamKey]);

    const handleRowClick = row => {
        if (!onRowClick) return;
        let urlStruct = onRowClick;
        const variableParams = extractVariables(onRowClick);
        for (const param of variableParams) {
            urlStruct = urlStruct.replace(`:${param}`, encodeURIComponent(row.original[param]));
        }
        navigate(urlStruct);
        return;
    };

    const handleSearch = value => {
        if (value) {
            updateQueryParam({ queryParam: searchQueryParamKey, value });
        } else {
            deleteQueryParam({ queryParam: searchQueryParamKey });
        }
        setGlobalFilter(value);
    };

    const handleResetSearch = () => {
        deleteQueryParam({ queryParam: searchQueryParamKey });
        setGlobalFilter('');
    };

    const displayLoader = (data.length == 0 || parsedColumnConfig.length === 0) && isLoading;
    const parsedEditConfig = normalizeConfig(editConfig);
    const parsedCreateConfig = normalizeConfig(createConfig);

    return (
        <div
            className={cn('relative flex w-full max-w-full flex-col', {
                'pointer-events-none cursor-none': displayLoader,
            })}
        >
            {header ?? (
                <div className="mb-4 flex flex-wrap items-center justify-between gap-y-3">
                    <div className="flex w-full max-w-full flex-row items-center sm:w-64">
                        {enableSearch && (
                            <DebouncedInput
                                type="text"
                                name="search"
                                id="search"
                                placeholder={searchPlaceholder ?? 'Search here...'}
                                onChange={onSearchChange ?? handleSearch}
                                unicode="E806"
                                debounce={0}
                                value={globalFilter}
                            />
                        )}
                    </div>
                    <div className="mr-auto">
                        {enableSearch && globalFilter && (
                            <Button variant="outlined" className="ml-4" text="Clear" onClick={handleResetSearch} />
                        )}
                    </div>
                    {actions ?? (
                        <div className="flex gap-2">
                            {createConfig && (
                                <CreateButton
                                    config={parsedCreateConfig}
                                    onSubmit={networkCalls?.onCreate}
                                    buttonText={createButtonText}
                                    headerText={createButtonHeaderText}
                                    setTableData={setData}
                                    getTableData={fetchData}
                                    formControls={customCreateFormControls}
                                />
                            )}
                            {filterConfig && (
                                <FilterButton
                                    config={filterConfig}
                                    onSubmit={handleFilterSubmit ?? networkCalls?.onFilter}
                                    setTableData={setData}
                                    accessorKey={accessorKey}
                                    setTableLoading={setIsLoading}
                                />
                            )}
                            <FormSideDrawer
                                formControls={customEditFormControls}
                                header={editHeader ?? 'Edit'}
                                onSubmit={handleEditSubmit}
                                isLoading={fetchingEditData}
                                isSubmitting={isEditing.status}
                                onGet={handleEditGet}
                                shouldFetchOnOpen={!!networkCalls?.onEditGet}
                                config={parsedEditConfig ?? editFormConfig}
                                removePadding={true}
                            />

                            <div
                                onClick={() => setRefresh(count => count + 1)}
                                className="flex cursor-pointer items-center justify-center gap-2 rounded-md border border-blue-500 px-3 py-1 text-blue-500 hover:bg-blue-50"
                            >
                                <ZIcon unicode="EC81" />
                                <span className="text-sm">Refresh</span>
                            </div>
                        </div>
                    )}
                </div>
            )}
            <div className="table_wrapper">
                <div className="max-h-[calc(100vh-260px)] w-full overflow-x-auto">
                    <table className="h-full overflow-auto">
                        <thead className="sticky top-0 z-[1] bg-slate-100">
                            <tr>
                                {table?.getHeaderGroups()?.map(headerGroup => {
                                    return headerGroup.headers.map((header, headerIdx) => {
                                        const column = header.column;
                                        const canSort = column.getCanSort();

                                        return (
                                            <th
                                                className={cn(canSort && 'cursor-pointer')}
                                                onClick={() => {
                                                    if (canSort) column.toggleSorting();
                                                }}
                                                key={headerIdx + column.columnDef.accessorKey}
                                            >
                                                <div className="flex w-max max-w-screen-xs items-center gap-2">
                                                    <div className="first-letter:uppercase">
                                                        {flexRender(column.columnDef.header, header.getContext())}
                                                    </div>
                                                    <div className="text-gray-400">
                                                        {column.getIsSorted() ? (
                                                            column.getIsSorted() === 'desc' ? (
                                                                <ZIcon unicode="E85A" className="text-xs" />
                                                            ) : (
                                                                <ZIcon unicode="E85B" className="text-xs" />
                                                            )
                                                        ) : canSort ? (
                                                            <ZIcon unicode="EC6D" className="text-lg" />
                                                        ) : null}
                                                    </div>
                                                </div>
                                            </th>
                                        );
                                    });
                                })}
                            </tr>
                        </thead>
                        <tbody>
                            {table?.getRowModel().rows?.length > 0 &&
                                !isLoading &&
                                table?.getRowModel().rows.map((row, rowIdx) => {
                                    return (
                                        <tr
                                            className={cn({ 'hover:bg-gray-50': true, 'cursor-pointer': onRowClick })}
                                            key={rowIdx + row.id}
                                            onClick={onRowClick ? () => handleRowClick(row, row.id) : null}
                                        >
                                            {row?.getVisibleCells().map((cell, cellIdx) => (
                                                <td key={cell.id + cellIdx}>
                                                    {flexRender(cell.column.columnDef.cell, {
                                                        ...cell.getContext(),
                                                        setTableData: setData,
                                                        editFormConfig: editFormConfig,
                                                        onEdit: networkCalls?.onEdit,
                                                        onDelete: networkCalls?.onDelete,
                                                        getTableData: fetchData,
                                                        primaryKey,
                                                        editFormControls: customEditFormControls,
                                                        editKeyMap,
                                                        isEditing,
                                                        editConfig,
                                                        createConfig,
                                                    })}
                                                </td>
                                            ))}
                                        </tr>
                                    );
                                })}
                        </tbody>
                    </table>
                    {parsedColumnConfig.length > 0 && isLoading && !displayLoader && (
                        <TableLoader isLoading={isLoading} forRowsLoading={true} />
                    )}
                    {table?.getRowModel().rows?.length === 0 && !isLoading && <NoResults />}
                </div>
                {table?.getRowModel().rows?.length && !noPagination ? (
                    <TablePagination
                        currentPageRowCount={table?.getRowModel().rows?.length || 0}
                        totalCount={
                            controlledPagination?.totalCount || Object.entries(table.getRowModel().rowsById).length
                        }
                        currentPage={controlledPagination?.page ?? table.getState().pagination.pageIndex + 1}
                        onPageChange={handlePageChange}
                        pageSize={controlledPagination?.pageSize ?? table.getState().pagination.pageSize}
                        onPageSizeChange={handlePageSizeChange}
                        siblingCount={controlledPagination?.siblingCount}
                        message={globalFilter ? 'Showing filtered results' : null}
                    />
                ) : null}
                <TableLoader isLoading={displayLoader} />
            </div>
        </div>
    );
}

export default Table;
