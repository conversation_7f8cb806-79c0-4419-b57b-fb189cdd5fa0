import { COMMON_FORM_STYLES, FORM_COMPONENTS } from '../../../../constants';
import { getDataType } from './cellParser';

export const parseTableColumnsForEditForm = (columns, data, hiddenColumns, allFieldsVisibleInEdit = true) => {
    const editFormConfig = {
        type: 'Box',
        className: COMMON_FORM_STYLES + ' px-3 py-4',
        contents: [],
    };
    const exploredColumns = [];

    columns.forEach(column => {
        if (column.editable) {
            if (column.editOverrides) {
                exploredColumns.push(column.accessorKey);
                editFormConfig.contents.push({
                    id: column.editOverrides?.key ?? column.accessorKey,
                    type: column.editOverrides?.type ?? FORM_COMPONENTS.INPUT,
                    label: column.editOverrides.props?.label ?? column.header ?? column.accessorKey.replace(/_/g, ' '),
                    placeholder:
                        column.editOverrides.props?.placeholder ??
                        column.header ??
                        column.accessorKey.replace(/_/g, ' '),
                    stringify: column.editOverrides?.type === undefined ? true : column.editOverrides?.props?.stringify,
                    ...column.editOverrides,
                });
            } else {
                exploredColumns.push(column.accessorKey);
                const inferredFormConfigFromDataType = getInferredFormConfigFromDataType(column, data);
                editFormConfig.contents.push(inferredFormConfigFromDataType);
            }
        } else if (column.accessorKey !== 'action' && column.accessorKey !== 'actions' && allFieldsVisibleInEdit) {
            exploredColumns.push(column.accessorKey);
            editFormConfig.contents.push({
                id: column.accessorKey,
                type: FORM_COMPONENTS.DISABLED_VIEW,
                label: column.header ?? column.accessorKey.replace(/_/g, ' '),
                disabled: true,
            });
        }
    });

    data.length > 0 &&
        Object.entries(data[0]).map(([key]) => {
            if (!hiddenColumns.includes(key) && !exploredColumns.includes(key) && allFieldsVisibleInEdit) {
                editFormConfig.contents.push({
                    id: key,
                    type: FORM_COMPONENTS.DISABLED_VIEW,
                    label: key,
                    disabled: true,
                });
            }
        });

    return editFormConfig;
};

const getInferredFormConfigFromDataType = (column, data) => {
    const valueType = getDataType(data, column.accessorKey);
    const label = column.header ?? column.accessorKey.replace(/_/g, ' ');

    const props = {
        id: column.accessorKey,
        label: label,
        placeholder: label,
    };

    switch (valueType) {
        case 'string':
            return {
                ...props,
                type: FORM_COMPONENTS.INPUT,
                rules: {
                    required: label + ' is required',
                },
            };
        case 'boolean':
            return {
                ...props,
                type: FORM_COMPONENTS.CHECKBOX,
                rules: {
                    required: label + ' is required',
                },
            };
        case 'object':
            return {
                ...props,
                type: FORM_COMPONENTS.TEXTAREA,
                rules: {
                    required: label + ' is required',
                },
                stringify: true,
            };
        case 'number':
            return {
                ...props,
                type: FORM_COMPONENTS.INPUT,
                rules: {
                    required: label + ' is required',
                },
                inputType: 'number',
            };
        case 'imageArray':
            return {
                ...props,
                type: FORM_COMPONENTS.IMAGE_INPUT,
                rules: {
                    required: label + ' is required',
                },
            };
        default:
            return {
                ...props,
                type: FORM_COMPONENTS.INPUT,
                stringify: true,
                rules: {
                    required: label + ' is required',
                },
            };
    }
};
