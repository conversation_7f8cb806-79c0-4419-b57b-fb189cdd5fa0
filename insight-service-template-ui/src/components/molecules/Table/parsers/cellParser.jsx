import TextLimiter from '@atoms/TextLimiter';

import { TABLE_CELL_COMPONENTS } from '@/constants';

import { componentRegistry } from '../../../componentRegistry';
import EditModal from '../Cells/EditModal';
import ImageViewer from '../Cells/ImageViewer';
import MultiChips from '../Cells/MultiChips';

export const getCellValue = (row, cell) => {
    return cell.getValue(cell.column.id);
};

export const checkIfImage = value => {
    const stringVal = value.toString();
    return stringVal?.endsWith('.png') ||
        stringVal?.endsWith('.jpg') ||
        stringVal?.endsWith('.jpeg') ||
        stringVal?.endsWith('.gif')
        ? true
        : false;
};

export const checkIfImageArray = values => {
    for (let i = 0; i < values.length; i++) {
        if (checkIfImage(values[i])) return true;
    }
    return false;
};

export const getDataType = (data, accessorKey) => {
    for (let i = 0; i < data.length; i++) {
        if (data[i][accessorKey] === null) continue;

        if (Array.isArray(data[i][accessorKey])) {
            if (data[i][accessorKey].length === 0) continue;
            else if (checkIfImageArray(data[i][accessorKey])) return 'imageArray';
            else return 'multiChip';
        } else if (typeof data[i][accessorKey] === 'object') {
            return 'object';
        } else {
            if (data[i][accessorKey] === null || data[i][accessorKey] === undefined) continue;
            else if (checkIfImage(data[i][accessorKey])) return 'image';
            else if (typeof data[i][accessorKey] === 'boolean') return 'boolean';
            else if (!isNaN(data[i][accessorKey])) return 'number';
            else return 'string';
        }
    }
    return 'string';
};

const parseColumnConfig = (column, data, key) => {
    const newColumn = {
        header: column?.header ?? column?.accessorKey?.replace(/_/g, ' '),
        ...column,
    };

    if (componentRegistry[column.cell] !== undefined) {
        const Component = componentRegistry[column.cell][column.version ?? '1.0'];
        let restProps = {};
        Object.keys(newColumn)
            .filter(key => !['header', 'accessorKey', 'cell', 'version'].includes(key))
            .map(key => (restProps[key] = newColumn[key]));

        return {
            ...newColumn,
            cell: props => <Component {...props} {...restProps} />,
        };
    }
    switch (column.cell) {
        case TABLE_CELL_COMPONENTS.IMAGE_VIEWER:
            return {
                ...newColumn,
                cell: ({ row, cell }) => {
                    const cellValue = getCellValue(row, cell);
                    return <ImageViewer images={cellValue} />;
                },
            };
        case TABLE_CELL_COMPONENTS.MULTI_CHIP:
            return {
                ...newColumn,
                cell: ({ row, cell }) => {
                    const cellValue = getCellValue(row, cell);
                    return <MultiChips chips={cellValue} />;
                },
            };
        default:
            return column.cell === undefined
                ? {
                      ...newColumn,
                      cell: ({ row, cell }) => {
                          return getCellComponentSmartlyIfNotSpecified({
                              row,
                              cell,
                              data,
                              key,
                          });
                      },
                  }
                : newColumn;
    }
};

export const parseTableColumnsForCell = ({ columns, data, hiddenColumns, networkCalls }) => {
    const newColumns = [];
    let anyColumnEditable = false;

    if (columns.length > 0) {
        columns.forEach(column => {
            if (hiddenColumns.includes(column.accessorKey) || column.isHidden) return;
            if (column.editable) anyColumnEditable = true;
            newColumns.push(parseColumnConfig(column, data, column.accessorKey));
        });
    }

    if (data.length > 0) {
        Object.entries(data[0]).map(([key, val]) => {
            if (hiddenColumns.includes(key)) return;
            if (!newColumns.find(column => column.accessorKey === key) && val !== null && val !== undefined) {
                newColumns.push({
                    header: key.replace(/_/g, ' '),
                    accessorKey: key,
                    cell: ({ row, cell }) => {
                        return getCellComponentSmartlyIfNotSpecified({
                            row,
                            cell,
                            data,
                            key,
                        });
                    },
                });
            }
        });
    }

    if (networkCalls?.onEdit || networkCalls?.onDelete || anyColumnEditable) {
        newColumns.push({
            header: networkCalls?.onEdit && networkCalls?.onDelete ? 'Actions' : 'Action',
            accessorKey: 'actions',
            cell: EditModal,
        });
    }
    return newColumns;
};

const getCellComponentSmartlyIfNotSpecified = ({ cell, data, key }) => {
    const valueType = getDataType(data, key);
    const value = cell.getValue(cell.column.id);

    if (valueType === 'multiChip') {
        return <MultiChips chips={value} />;
    } else if (valueType === 'imageArray') {
        return <ImageViewer images={value} />;
    } else if (valueType === 'image') {
        return <ImageViewer images={[value]} />;
    } else if (valueType === 'number' || valueType === 'string') {
        return <TextLimiter text={value} limit={100} />;
    } else if (valueType === 'boolean') {
        return <TextLimiter text={value.toString()} limit={100} />;
    } else if (valueType === 'object') {
        return <TextLimiter text={JSON.stringify(value)} limit={100} />;
    }
};
