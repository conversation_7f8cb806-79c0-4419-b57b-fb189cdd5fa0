import { regexValidatorHandler } from '@molecules/Form/validators/validators';
import { Controller } from 'react-hook-form';

import { getValueUsingAccessorKey } from '@/utils/network';

import ErrorWrapper from './ErrorWrapper';

const withFormController = ({ Component, formControls, ...props }) => {
    if (!Component) return null;
    if (!props?.name && !props?.id && !props?.key) {
        throw new Error('Name or id is required for form component');
    }
    const newFormComponentProps = {};

    if (props.rules?.required) newFormComponentProps['required'] = props.rules?.required;
    if (formControls?.formState?.errors !== undefined) {
        newFormComponentProps['error'] = getValueUsingAccessorKey(
            formControls?.formState?.errors,
            props.name ?? props.id,
        );
    }

    if (formControls?.onReset !== undefined) newFormComponentProps['onReset'] = formControls?.onReset;

    const validate = props.rules?.validate;
    const rules = props?.rules
        ? {
              ...props.rules,
              validate: validate
                  ? Array.isArray(validate)
                      ? value => regexValidatorHandler(validate)(value, formControls)
                      : value => validate(value, formControls)
                  : undefined,
          }
        : {};

    if (!formControls?.control) return null;

    return (
        <ErrorWrapper
            error={newFormComponentProps.error}
            errorPosition={props?.errorPosition}
            name={props?.name ?? props?.id ?? props?.key}
            key={props?.name ?? props?.id ?? props?.key}
        >
            <Controller
                name={props?.name ?? props?.id ?? props?.key}
                key={props?.name ?? props?.id ?? props?.key}
                control={formControls?.control}
                rules={rules}
                defaultValue={props?.defaultValue}
                render={({ field }) => {
                    if (props?.hidden) return null;
                    delete props?.hookFormRegistration;

                    const onChange = e => {
                        if (props?.transform?.output) field.onChange(props.transform.output(e));
                        else field.onChange(e);
                    };

                    const getValue = () => {
                        if (props?.transform?.input) return props.transform.input(field.value);
                        return field.value;
                    };

                    return (
                        <Component
                            {...props}
                            {...field}
                            error={newFormComponentProps.error}
                            onChange={onChange}
                            value={getValue()}
                            {...newFormComponentProps}
                            formControls={formControls}
                        />
                    );
                }}
            />
        </ErrorWrapper>
    );
};

export default withFormController;
