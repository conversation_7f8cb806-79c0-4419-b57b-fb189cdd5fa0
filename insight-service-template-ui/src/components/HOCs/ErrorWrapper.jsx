import { cn } from '@/utils';

const ErrorWrapper = ({ children, error, errorPosition = 'relative', className, name }) => {
    return (
        <div
            className={cn({ 'relative flex w-full flex-col': true, 'mb-1': errorPosition === 'absolute' }, className)}
            name={name}
        >
            <div className={cn({ 'w-full': true, 'mb-1': errorPosition === 'absolute' })}>{children}</div>
            {error && (
                <span
                    className={cn({
                        error: true,
                        'absolute bottom-0 left-0 translate-y-full': errorPosition === 'absolute',
                    })}
                >
                    {error.message}
                </span>
            )}
        </div>
    );
};

export default ErrorWrapper;
