# define your api base path and origins here
serviceConstants:
    defaultOrigin: REACT_APP_API_BASE_URL
    origins:
        zomato: REACT_APP_API_BASE_URL

    commonHeaders:
        Accept: application/json
        Authorization: Bearer token

# define your end points here
endpointsMapping:
    getTemplates:
        origin: zomato
        endPoint: /templates
        method: GET
        pauseOtherRequests: true
    getVersion:
        origin: zomato
        endPoint: /templates/:templateId/template-versions/:versionId
        method: GET
        pauseOtherRequests: true
    getVersions:
        origin: zomato
        endPoint: /templates/:templateId/template-versions
        method: GET
        pauseOtherRequests: true
    syncTemplates:
        origin: zomato
        endPoint: /templates/sync
        method: GET
        pauseOtherRequests: true
    editTemplate:
        origin: zomato
        endPoint: /templates/:templateId/template-versions/:versionId
        method: POST
        pauseOtherRequests: true
    createTemplate:
        origin: zomato
        endPoint: /templates/register
        method: POST
        pauseOtherRequests: true
    moveVersionToProd:
        origin: zomato
        endPoint: /templates/:templateId/template-versions/:versionId/move-to-prod
        method: POST
        pauseOtherRequests: true
    testTemplate:
        origin: zomato
        endPoint: /templates/preview
        method: POST
        pauseOtherRequests: true

# define your page mapping here
pageMapping:
    templates:
        isPrivate: true
        path: /
    versionsList:
        isPrivate: true
        path: /templates/:templateId
    createTemplate:
        isPrivate: true
        path: /create-template
    editTemplate:
        isPrivate: true
        path: /templates/:templateId/edit-version/:versionId

# project level config
dashboardTitle: 'Data Platform Templates'
baseRoute: /jumbo/insights-service/dashboard
# root: Welcome // not allowing root component edits for now

# define your pages here
pages:
    templates:
        view:
            type: TemplateList
    versionsList:
        view:
            type: VersionsList
    createTemplate:
        view:
            type: CreateTemplate
    editTemplate:
        view:
            type: EditTemplate