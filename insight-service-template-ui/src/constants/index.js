export const ERROR_IMG = 'https://b.zmtcdn.com/data/o2_assets/832b35389ac6853fad9cd98de54f95311706878060.jpeg';
export const DEFAULT_PAGE_ERROR_MESSAGE =
    'We track these errors automatically, but if the problem persists feel free to contact us. In the meantime, try refreshing.';
export const DEFAULT_PAGE_ERROR_TITLE = 'Uh oh!';

export const USER_ROLES = {
    JUMBO_ADMIN: 'zomato.dataplatform.jumboinsightsservice_admin',
    JUMBO_USER: 'zomato.dataplatform.jumboinsightsservice_user',
    DP_ADMIN: 'zomato.dataplatform.admin',
};

export const COMMON_FORM_STYLES = ' flex flex-col gap-4 ';
export const COMMON_FORM_PADDING_STYLES = ' py-5 px-1 ';

export const DEFAULT_TEMPLATE_STATUS = 'TEMPLATE_STATUS_UNSPECIFIED';

export const EDIT = 'edit';
export const CREATE = 'create';
export const VIEW = 'view';

export const DOTS = '...';

export const FORM_COMPONENTS = {
    INPUT: 'Input',
    DROPDOWN: 'Dropdown',
    CHECKBOX: 'Checkbox',
    DISABLED_VIEW: 'DisabledView',
    MARKDOWN_EDITOR: 'MarkdownEditor',
};

export const TABLE_CELL_COMPONENTS = {
    TABLE_INPUT_VIEW: 'TableInputView',
    IMAGE_VIEWER: 'ImageViewer',
    EDIT_MODAL: 'EditModal',
    MULTI_CHIP: 'MultiChip',
    CUSTOM_STATUS_CELL: 'CustomStatusCell',
};

export const QUERY_PARAM_IDENTIFIER = {
    FILTER: 'filter',
    TABLE_SEARCH_KEY: 'tableSearchKey',
    SEPARATOR: '*',
};

export const TENANT_OPTIONS = [
    {
        label: 'Zomato',
        value: 'ZOMATO',
    },
    {
        label: 'Blinkit',
        value: 'BLINKIT',
    },
    {
        label: 'Hyperpure',
        value: 'HYPERPURE',
    },
];

export const LOGIN_PATHS = ['/login', '/login/'];

export const TENANT_OPTIONS_MAP = {
    PINOT: 'PINOT',
    TRINO: 'TRINO',
};

export const BACKEND_OPTIONS = [
    {
        label: 'Pinot',
        value: TENANT_OPTIONS_MAP.PINOT,
    },
    {
        label: 'Trino',
        value: TENANT_OPTIONS_MAP.TRINO,
    },
];

export const OPERATIONS = {
    EQUALS: 'EQUALS',
    NOT_EQUALS: 'NOT_EQUALS',
    GREATER_THAN: 'GREATER_THAN',
    LESS_THAN: 'LESS_THAN',
    IN: 'IN',
    NOT_IN: 'NOT_IN',
    BETWEEN: 'BETWEEN',
};

export const QUERY_FILTER_TYPES = {
    ARRAY_VALUE: 'array_value',
    BOOL_VALUE: 'bool_value',
    DOUBLE_VALUE: 'double_value',
    INT_VALUE: 'int_value',
    STRING_VALUE: 'string_value',
};

export const ArrayValueTypes = {
    STRING: QUERY_FILTER_TYPES.STRING_VALUE,
    INTEGER: QUERY_FILTER_TYPES.INT_VALUE,
    FLOAT: QUERY_FILTER_TYPES.DOUBLE_VALUE,
};

export const ARRAY_VALUE_TYPES = [
    {
        label: 'String',
        value: ArrayValueTypes.STRING,
    },
    {
        label: 'Integer',
        value: ArrayValueTypes.INTEGER,
    },
    {
        label: 'Float',
        value: ArrayValueTypes.FLOAT,
    },
];

export const QUERY_TYPES = {
    SQL: 'sql',
    OPEN_SQL: 'open_sql',
    DSL: 'dsl',
};

export const QUERY_TYPES_OPTIONS = [
    {
        label: 'SQL',
        value: QUERY_TYPES.SQL,
    },
    {
        label: 'Open SQL',
        value: QUERY_TYPES.OPEN_SQL,
    },
    {
        label: 'DSL',
        value: QUERY_TYPES.DSL,
    },
];

export const OPERATIONS_OPTIONS = [
    {
        label: 'Equals',
        value: OPERATIONS.EQUALS,
    },
    {
        label: 'Not equals',
        value: OPERATIONS.NOT_EQUALS,
    },
    {
        label: 'Greater than',
        value: OPERATIONS.GREATER_THAN,
    },
    {
        label: 'Less than',
        value: OPERATIONS.LESS_THAN,
    },
    {
        label: 'In',
        value: OPERATIONS.IN,
    },
    {
        label: 'Not in',
        value: OPERATIONS.NOT_IN,
    },
    {
        label: 'Between',
        value: OPERATIONS.BETWEEN,
    },
];

export const FILTERS_TYPE = [
    {
        label: 'String',
        value: QUERY_FILTER_TYPES.STRING_VALUE,
    },
    {
        label: 'Integer',
        value: QUERY_FILTER_TYPES.INT_VALUE,
    },
    {
        label: 'Float',
        value: QUERY_FILTER_TYPES.DOUBLE_VALUE,
    },
    {
        label: 'Boolean',
        value: QUERY_FILTER_TYPES.BOOL_VALUE,
    },
    {
        label: 'Array',
        value: QUERY_FILTER_TYPES.ARRAY_VALUE,
    },
];

export const VERSION_STATUS_DEV = 'DEV';
export const VERSION_STATUS_PROD = 'PROD';

export const FILTER_TRIGGER = 'FILTER_TRIGGER';
export const FILTER_ADD_BTN = 'FILTER_ADD_BTN';
