import { request } from '@zomato/networking';
import { useState } from 'react';
import { useNavigate } from 'react-router';

import toaster from '@/components/molecules/Toaster';
import { isSuccess, resetFormState, toastError } from '@/utils';

export const useCreateTemplate = ({ formControls }) => {
    const [creatingTemplate, setCreatingTemplate] = useState(false);
    const [testedTemplate, setTestedTemplate] = useState(false);
    const navigate = useNavigate();

    const createTemplate = async data => {
        setCreatingTemplate(true);
        try {
            const res = await request('createTemplate', {
                body: {
                    template: data,
                },
            });
            const status = res.data?.status;
            const tempName = res.data?.template_id ?? 'Unknown';
            const tempVersion = res.data?.template_version ?? 'Unknown';
            if (isSuccess(status)) {
                navigate('/');
                toaster.success(`Template ${tempName} with version ${tempVersion} created successfully`);
                resetFormState(formControls);
            } else toastError(res.data);
        } catch (error) {
            toastError(error);
        } finally {
            setCreatingTemplate(false);
        }
    };

    return {
        createTemplate,
        creatingTemplate,
        setTestedTemplate,
        testedTemplate,
    };
};
