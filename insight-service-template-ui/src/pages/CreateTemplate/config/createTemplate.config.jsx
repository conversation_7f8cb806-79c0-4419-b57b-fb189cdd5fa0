import {
    BACKEND_OPTIONS,
    COMMON_FORM_PADDING_STYLES,
    COMMON_FORM_STYLES,
    DEFAULT_TEMPLATE_STATUS,
    QUERY_TYPES,
    QUERY_TYPES_OPTIONS,
    TENANT_OPTIONS,
    TENANT_OPTIONS_MAP,
} from '@/constants';
import { isEmpty } from '@/utils';

export const getSeparatorConfig = ({ text }) => {
    return {
        type: 'Box',
        className: 'flex gap-3 py-3 text-base text-gray-800 border-t mt-4',
        text: text,
    };
};

export const getTemplateConfig = ({ edit = false, queryType = QUERY_TYPES.DSL, formControls } = {}) => {
    let queryConfig = [];
    const backend = formControls?.watch('table_config.backend');

    if (queryType === QUERY_TYPES.SQL || queryType === QUERY_TYPES.OPEN_SQL) {
        queryConfig = [
            {
                type: 'Editor',
                label: 'SQL Query',
                id: 'query.sql',
                rules: {
                    required: 'SQL Query is required',
                },
            },
            {
                type: 'MultiInput',
                id: 'query.filters',
                label: 'Filters',
                placeholder: 'Enter filters',
                rules: {
                    required: 'Filters are required',
                },
            },
            {
                type: 'MultiFields',
                id: 'decryption',
                label: 'Decryption',
                addBtnText: 'Add more',
                getConfig: () => [
                    {
                        type: 'Input',
                        id: 'sql_column',
                        label: 'Sql Column',
                        placeholder: 'Enter sql column',
                    },
                    {
                        type: 'Input',
                        id: 'source_column',
                        label: 'Source Column',
                        placeholder: 'Enter source column',
                    },
                ],
                transform: {
                    input: data => {
                        return data?.filter(item => !isEmpty(item?.sql_column) && !isEmpty(item?.source_column));
                    },
                    output: data => {
                        return data?.filter(item => !isEmpty(item?.sql_column) && !isEmpty(item?.source_column));
                    },
                },
            },
        ];
    } else if (queryType === QUERY_TYPES.DSL) {
        queryConfig = [
            {
                type: 'MultiInput',
                id: 'query.filters',
                label: 'Filters',
                placeholder: 'Enter filters',
                rules: {
                    required: 'Filters are required',
                },
            },
            {
                type: 'MultiInput',
                id: 'aggregations',
                label: 'Aggregations (Group by columns)',
                placeholder: 'Enter filters',
                rules: {
                    required: 'Aggregations are required',
                },
            },
            {
                type: 'MultiFields',
                id: 'columns',
                label: 'Columns (Dimensions)',
                addBtnText: 'Add more',
                getConfig: () => [
                    {
                        type: 'Input',
                        id: 'name',
                        label: 'Name',
                        placeholder: 'Enter name',
                    },
                    {
                        type: 'Input',
                        id: 'func',
                        label: 'Function',
                        placeholder: 'Enter function',
                    },
                    {
                        type: 'Input',
                        id: 'source_column',
                        label: 'Source Column',
                        placeholder: 'Enter source column',
                    },
                ],
            },
        ];
    }

    return {
        type: 'Box',
        className: COMMON_FORM_STYLES + COMMON_FORM_PADDING_STYLES,
        contents: [
            {
                type: 'Input',
                id: 'template_id',
                label: 'Template Id',
                placeholder: 'Enter template id',
                disabled: edit,
                rules: { required: 'Template id is required' },
            },
            {
                type: 'Input',
                id: 'template_status',
                label: 'Template Status',
                hidden: true,
                defaultValue: DEFAULT_TEMPLATE_STATUS,
            },
            {
                type: 'Dropdown',
                id: 'tenant',
                label: 'Tenant',
                placeholder: 'Select tenant',
                rules: { required: 'Tenant is required' },
                options: TENANT_OPTIONS,
            },

            getSeparatorConfig({ text: 'Table Configuration' }),
            {
                type: 'Dropdown',
                id: 'table_config.backend',
                label: 'Backend (Server)',
                placeholder: 'Select a server',
                tooltipText: 'can be pinot/trino',
                rules: { required: 'Server is required' },
                options: BACKEND_OPTIONS,
            },
            {
                type: 'Input',
                id: 'table_config.catalog',
                label: 'Catalog',
                placeholder: 'Enter catalog',
                rules: backend === TENANT_OPTIONS_MAP.TRINO ? { required: 'Catalog is required' } : {},
            },
            {
                type: 'Input',
                id: 'table_config.schema',
                label: 'Schema',
                placeholder: 'Enter your schema',
                rules: backend === TENANT_OPTIONS_MAP.TRINO ? { required: 'Schema is required' } : {},
            },
            {
                type: 'Input',
                id: 'table_config.table',
                label: 'Table',
                placeholder: 'Enter table',
                rules: { required: 'Table is required' },
            },
            getSeparatorConfig({ text: 'Audit' }),
            {
                type: 'Input',
                id: 'audit.author_email',
                label: 'Author Email',
                placeholder: 'Enter author email',
            },
            {
                type: 'Input',
                id: 'audit.team_email',
                label: 'Team Email',
                placeholder: 'Enter team email',
            },
            {
                type: 'Input',
                id: 'audit.service',
                label: 'Service',
                placeholder: 'Enter service',
            },
            {
                type: 'Input',
                id: 'audit.pd_service_name',
                label: 'PD Service Name',
                placeholder: 'Enter PD service name',
            },
            {
                type: 'Input',
                id: 'audit.description',
                label: 'Description',
                textbox: true,
                placeholder: 'Enter description',
            },
            getSeparatorConfig({ text: 'Performance' }),
            {
                type: 'Input',
                inputType: 'number',
                allowFloat: false,
                allowNegative: false,
                id: 'performance.caching_ttl',
                label: 'Caching TTL (Time in second)',
                placeholder: 'Enter caching ttl',
            },
            {
                type: 'Input',
                inputType: 'number',
                allowFloat: false,
                allowNegative: false,
                id: 'performance.refresh_interval',
                label: 'Refresh Interval (Time in seconds, 0 to disable fetch from cache)',
                placeholder: 'Enter refresh interval',
                tooltipText: 'Irrespective of cache value, fresh result will be fetched after the refresh interval',
            },
            {
                type: 'Input',
                inputType: 'number',
                allowFloat: false,
                allowNegative: false,
                id: 'performance.sla',
                label: 'SLA (Time in in ms)',
                placeholder: 'Enter SLA',
            },
            {
                type: 'Input',
                inputType: 'number',
                allowFloat: false,
                allowNegative: false,
                id: 'performance.rate_limit',
                label: 'Rate Limit (Request per second)',
                placeholder: 'Enter rate limit',
            },
            getSeparatorConfig({ text: 'Query' }),
            {
                type: 'Dropdown',
                id: 'query.type',
                label: 'Query Type (Contract type)',
                options: QUERY_TYPES_OPTIONS,
                placeholder: 'Enter query type',
                defaultValue: QUERY_TYPES.DSL,
                afterChange: () => {
                    formControls.setValue('query.filters', null);
                    formControls.setValue('query.sql', null);
                    formControls.setValue('aggregations', null);
                    formControls.setValue('columns', [
                        {
                            name: '',
                            func: '',
                            source_column: '',
                        },
                    ]);
                    formControls.setValue('decryption', [
                        {
                            sql_column: '',
                            source_column: '',
                        },
                    ]);
                },
            },
            ...queryConfig,
        ],
    };
};
