import { Link, useSearchParams } from 'react-router-dom';

import Button from '@/components/atoms/Button';
import ZIcon from '@/components/atoms/ZIcon';
import RenderUIFrom from '@/components/molecules/RenderUIFrom';
import TestTemplate from '@/components/molecules/TestTemplate';
import Tooltip from '@/components/molecules/Tooltip';
import useCustomFormControls from '@/hooks/useCustomFormControls';
import { scrollToError } from '@/utils';

import { getTemplateConfig } from './config/createTemplate.config';
import { useCreateTemplate } from './hook/useCreateTemplate';

const CreateTemplate = () => {
    const [searchParams, setSearchParams] = useSearchParams();
    const formControls = useCustomFormControls();
    const { createTemplate, setTestedTemplate, testedTemplate } = useCreateTemplate({
        formControls,
    });
    const handleFormValidationError = () => scrollToError(formControls);

    const openTestDrawer = () => {
        searchParams.set('action', 'test');
        setSearchParams(searchParams);
    };

    const filters =
        formControls.watch('query.filters')?.map(filter => ({
            label: filter,
            value: filter,
        })) ?? [];

    return (
        <div className="bg-gray-100 px-6 py-4 pb-20">
            <TestTemplate filters={filters} setTestedTemplate={setTestedTemplate} templateFormControls={formControls} />
            <div className="mx-auto max-w-[1000px]">
                <Link to="/" className="mb-6 flex cursor-pointer gap-2 text-blue-500">
                    <ZIcon unicode="E923" />
                    Go Back
                </Link>

                <div className="mx-auto mt-4 rounded-md bg-white p-4 shadow-md">
                    <div className="mb-4 pb-3 text-lg font-medium">Create template</div>
                    <RenderUIFrom
                        componentConfig={getTemplateConfig({
                            queryType: formControls.watch('query.type'),
                            formControls,
                        })}
                        formControls={formControls}
                    />
                    <div className=" mt-6 flex gap-2">
                        <Button text="Test" block variant="outlined" onClick={openTestDrawer} />
                        <Tooltip
                            content={!testedTemplate && 'Please test your template before submitting'}
                            className="w-full"
                        >
                            <Button
                                text="Submit"
                                block
                                disabled={!testedTemplate}
                                onClick={formControls.handleSubmit(createTemplate, handleFormValidationError)}
                            />
                        </Tooltip>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CreateTemplate;
