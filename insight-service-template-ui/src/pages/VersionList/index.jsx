import { useEffect, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';

import Button from '@/components/atoms/Button';
import Header from '@/components/atoms/Header';
import ConfirmationModal from '@/components/molecules/ConfirmationModal';
import FullScreenLoader from '@/components/molecules/FullScreenLoader';
import NoResults from '@/components/molecules/Table/NoResults';
import ToggleFilterBtn from '@/components/molecules/ToggleFilterBtn';
import { USER_ROLES, VERSION_STATUS_PROD } from '@/constants';
import useCustomFormControls from '@/hooks/useCustomFormControls';
import useUser from '@/hooks/useUser';

import { useVersion } from './hooks/useVersionList';

const VersionsList = () => {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const { isAuthorizedToAccess } = useUser();
    const [confirmModalState, setConfirmModalState] = useState({ isOpen: false, data: {} });
    const { templateId } = useParams();
    const formControls = useCustomFormControls();
    const testFormControls = useCustomFormControls();
    const { getVersionList, versionList, fetchingVersionList, moveToProd, movingToProd } = useVersion({
        formControls,
        testFormControls,
        templateId,
    });

    useEffect(() => {
        getVersionList({
            latest: searchParams.get('latest') === 'true',
        });
    }, []);

    const handleMoveToProd = (e, id) => {
        e.stopPropagation();
        setConfirmModalState({ isOpen: true, data: { id } });
    };

    const handlePrimaryBtnClick = () => {
        moveToProd(confirmModalState.data.id);
        setConfirmModalState({ isOpen: false, data: {} });
    };

    const goToEditPage = version => {
        navigate(`/templates/${templateId}/edit-version/${version}`);
    };

    return (
        <div className="px-6 py-4">
            <Header
                title={`Versions for template ${templateId}`}
                actions={
                    <ToggleFilterBtn
                        activeText="Latest"
                        onToggle={getVersionList}
                        inactiveText="All"
                        filterKey="latest"
                    />
                }
            />
            <ConfirmationModal
                isOpen={confirmModalState.isOpen}
                setIsOpen={setConfirmModalState}
                message="Are you sure you want to move this version to prod?"
                onSecondaryBtnClick={() => setConfirmModalState({ isOpen: false, data: {} })}
                onPrimaryBtnClick={handlePrimaryBtnClick}
                primaryBtnText="Yes"
                secondaryBtnText="No"
            />
            {fetchingVersionList ? (
                <FullScreenLoader />
            ) : versionList?.length === 0 ? (
                <NoResults imgSize={350} />
            ) : (
                <div className="card_container">
                    {versionList.map((version, index) => (
                        <div
                            onClick={() => goToEditPage(version.template_version)}
                            key={version.template_id + index + version.template_version}
                            className="flex size-full cursor-pointer flex-col rounded-md border p-3"
                        >
                            <div className="text-ellipsis text-lg font-normal">{version.template_version}</div>

                            <div className="mt-2 flex gap-2">
                                <div className="rounded-md border border-blue-500 bg-blue-50 px-3 py-1 text-xs lowercase text-blue-800 first-letter:uppercase">
                                    {version.template_status}
                                </div>
                                <div className="rounded-md border border-yellow-800 bg-yellow-50 px-3 py-1 text-xs lowercase text-yellow-800 first-letter:uppercase">
                                    {version.tenant}
                                </div>
                            </div>
                            {VERSION_STATUS_PROD === version.template_status ? (
                                <div className="mt-4 cursor-auto rounded-md border border-green-500 bg-green-50 py-2 text-center text-xs text-green-800">
                                    This version is on prod
                                </div>
                            ) : (
                                isAuthorizedToAccess([USER_ROLES.JUMBO_ADMIN, USER_ROLES.DP_ADMIN]) && (
                                    <Button
                                        text="Move to prod"
                                        variant="contained"
                                        className="mt-4"
                                        block
                                        onClick={e => handleMoveToProd(e, version.template_version)}
                                        isLoading={version.template_version === movingToProd.id && movingToProd.loading}
                                        textLoading="Moving to prod..."
                                    />
                                )
                            )}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default VersionsList;
