import { request } from '@zomato/networking';
import { useState } from 'react';

import toaster from '@/components/molecules/Toaster';
import { isSuccess, toastError } from '@/utils';

export const useVersion = ({ templateId }) => {
    const [versionList, setVersionList] = useState([]);
    const [fetchingVersionList, setFetchingVersionList] = useState(false);
    const [movingToProd, setMovingToProd] = useState({
        id: null,
        loading: false,
    });

    const getVersionList = async ({ latest }) => {
        setFetchingVersionList(true);
        const isLatest = latest;
        try {
            const res = await request('getVersions', {
                options: {
                    templateId,
                },
                query: isLatest ? { latest: true } : {},
            });
            const status = res.data?.status;
            if (isSuccess(status)) {
                setVersionList(res.data?.templates ?? []);
            } else toastError(res.data);
        } catch (err) {
            toastError(err);
        } finally {
            setFetchingVersionList(false);
        }
    };

    const moveToProd = async id => {
        setMovingToProd({ id, loading: true });
        try {
            const res = await request('moveVersionToProd', {
                options: {
                    templateId,
                    versionId: id,
                },
            });
            const status = res.data?.status;
            if (isSuccess(status)) {
                toaster.success({
                    title: 'Success',
                    message: 'Version moved to production',
                });
                getVersionList();
            } else {
                toaster.error({
                    title: 'Error',
                    message: 'Failed to move version to production',
                });
            }
        } catch (err) {
            toastError(err);
        } finally {
            setMovingToProd({ id: null, loading: false });
        }
    };

    const testTemplate = async data => {};

    const createTemplate = async data => {};

    return {
        versionList,
        fetchingVersionList,
        getVersionList,
        moveToProd,
        movingToProd,
        testTemplate,
        createTemplate,
    };
};
