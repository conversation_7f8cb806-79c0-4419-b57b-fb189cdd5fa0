import { useEffect } from 'react';
import { Link, useParams, useSearchParams } from 'react-router-dom';

import Button from '@/components/atoms/Button';
import Spinner from '@/components/atoms/Spinner';
import ZIcon from '@/components/atoms/ZIcon';
import RenderUIFrom from '@/components/molecules/RenderUIFrom';
import TestTemplate from '@/components/molecules/TestTemplate';
import Tooltip from '@/components/molecules/Tooltip';
import useCustomFormControls from '@/hooks/useCustomFormControls';
import { scrollToError } from '@/utils';

import { getTemplateConfig } from '../CreateTemplate/config/createTemplate.config';
import { useEditTemplate } from './hook/useEditTemplate';

const EditTemplate = () => {
    const { versionId, templateId } = useParams();
    const [searchParams, setSearchParams] = useSearchParams();
    const formControls = useCustomFormControls();
    const { updateVersion, updatingVersion, fetchingVersion, getVersion, testedTemplate, setTestedTemplate } =
        useEditTemplate({
            formControls,
        });
    const handleFormValidationError = () => scrollToError(formControls);

    useEffect(() => {
        getVersion();
    }, []);

    const openTestDrawer = () => {
        searchParams.set('action', 'test');
        setSearchParams(searchParams);
    };

    const filters =
        formControls.watch('query.filters')?.map(filter => ({
            label: filter,
            value: filter,
        })) ?? [];

    return (
        <div className="flex-1 bg-gray-100 px-6 py-4 pb-20">
            <TestTemplate filters={filters} templateFormControls={formControls} setTestedTemplate={setTestedTemplate} />
            <div className="mx-auto max-w-[1000px]">
                <Link to={`/templates/${templateId}`} className="mb-6 flex cursor-pointer gap-2 text-blue-500">
                    <ZIcon unicode="E923" />
                    Go Back
                </Link>

                <div className="mx-auto mt-4 rounded-md bg-white p-4 shadow-md">
                    <div className="mb-2 pb-3 text-lg font-medium">Edit template: {versionId}</div>
                    {fetchingVersion ? (
                        <>
                            <div className="flex h-[500px] w-full items-center justify-center gap-4">
                                <Spinner size="xl" />
                                <div className="text-lg  text-gray-500">Fetching {versionId}...</div>
                            </div>
                        </>
                    ) : (
                        <>
                            <RenderUIFrom
                                componentConfig={getTemplateConfig({
                                    queryType: formControls.watch('query.type'),
                                    edit: true,
                                    formControls,
                                })}
                                formControls={formControls}
                            />
                            <div className=" mt-6 flex gap-2">
                                <Button text="Test" block variant="outlined" onClick={openTestDrawer} />

                                <Tooltip
                                    content={!testedTemplate && 'Please test your template before submitting'}
                                    className="w-full"
                                >
                                    <Button
                                        text="Submit"
                                        block
                                        isLoading={updatingVersion}
                                        disabled={!testedTemplate}
                                        textLoading="Updating"
                                        onClick={formControls.handleSubmit(updateVersion, handleFormValidationError)}
                                    />
                                </Tooltip>
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default EditTemplate;
