import { request } from '@zomato/networking';
import { useState } from 'react';
import { useNavigate, useParams } from 'react-router';

import toaster from '@/components/molecules/Toaster';
import { isSuccess, resetFormState, toastError } from '@/utils';

export const useEditTemplate = ({ formControls }) => {
    const { templateId, versionId } = useParams();
    const [updatingVersion, setUpdatingVersion] = useState(false);
    const [fetchingVersion, setFetchingVersion] = useState(false);
    const [testedTemplate, setTestedTemplate] = useState(false);
    const navigate = useNavigate();

    const updateVersion = async data => {
        setUpdatingVersion(true);
        try {
            const res = await request('editTemplate', {
                options: {
                    templateId,
                    versionId,
                },
                body: {
                    template: data,
                },
            });

            const status = res.data?.status;
            const tempName = res.data?.template_id ?? 'Unknown';
            const tempVersion = res.data?.template_version ?? 'Unknown';
            if (isSuccess(status)) {
                navigate(`/templates/${templateId}`);
                toaster.success(`Template ${tempName} with version ${tempVersion} updated successfully`);
            } else toastError(res.data);
        } catch (err) {
            toastError(err);
        } finally {
            setUpdatingVersion(false);
        }
    };

    const getVersion = async () => {
        resetFormState(formControls);
        setFetchingVersion(true);
        try {
            const res = await request('getVersion', {
                options: {
                    templateId,
                    versionId,
                },
            });
            const status = res.data?.status;
            if (isSuccess(status)) {
                resetFormState(formControls, res.data?.template);
            } else toastError(res.data);
        } catch (err) {
            toastError(err);
        } finally {
            setFetchingVersion(false);
        }
    };

    return {
        updateVersion,
        updatingVersion,
        fetchingVersion,
        getVersion,
        setTestedTemplate,
        testedTemplate,
    };
};
