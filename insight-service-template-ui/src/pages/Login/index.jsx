import { useCallback, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import toaster from '@/components/molecules/Toaster';
import { auth } from '@/components/molecules/Zauth';
import useUser from '@/hooks/useUser';

export default function LoginPage() {
    const { isLoggedIn, loggingOut, checkingAuth, checkUserAuth } = useUser();
    const location = useLocation();
    const navigate = useNavigate();

    const searchParams = new URLSearchParams(location.search);
    const from = location?.state?.from?.pathname + location?.state?.from?.search || searchParams.get('redirect') || '/';

    const handleLogin = useCallback(async () => {
        try {
            auth.login('IFRAME', 'login_ui').then(async () => {
                checkUserAuth();
            });
        } catch (e) {
            toaster.error('Some error occurred while logging in');
        }
    }, []);

    useEffect(() => {
        if (isLoggedIn && !loggingOut) navigate(from);
    }, [isLoggedIn, loggingOut]);

    if (checkingAuth) return <></>;

    return (
        <div className="mx-4 my-auto ml-16 flex flex-col items-center justify-center gap-8">
            <div className="flex flex-col gap-4 md:gap-4">
                <h1 className="text-center text-3xl font-semibold md:text-4xl md:leading-tight">Please Login</h1>
            </div>
            <button
                data-testid="login-button"
                onClick={() => handleLogin()}
                className="w-48 rounded-md border bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
            >
                Login
            </button>
        </div>
    );
}
