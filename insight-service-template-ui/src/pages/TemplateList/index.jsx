import { isEmpty } from 'lodash';
import { useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';

import Button from '@/components/atoms/Button';
import Header from '@/components/atoms/Header';
import FullScreenLoader from '@/components/molecules/FullScreenLoader';
import Modal from '@/components/molecules/Modal';
import NoResults from '@/components/molecules/Table/NoResults';
import { USER_ROLES } from '@/constants';
import useCustomFormControls from '@/hooks/useCustomFormControls';
import useUser from '@/hooks/useUser';

import { useTemplate } from './hook/useTemplateList';

const TemplateList = () => {
    const navigate = useNavigate();
    const formControls = useCustomFormControls();
    const { isAuthorizedToAccess } = useUser();
    const { getTemplates, templateList, fetchingTemplates, syncTemplates, isSyncing } = useTemplate({
        formControls,
    });

    useEffect(() => {
        getTemplates();
    }, []);

    const handleSearchKeyDown = e => {
        if (e.key === 'Enter') {
            const templateId = e.target.value;
            if (!isEmpty(templateId)) {
                navigate(`/templates/${templateId}`);
            }
        }
    };

    return (
        <div className="px-6 py-4">
            <Header title="Templates" />
            <div className="mb-6 flex items-center gap-4">
                <div className="flex items-center gap-2">
                    <div>Go to </div>
                    <input className="input w-64" placeholder="Enter template id" onKeyDown={handleSearchKeyDown} />
                </div>
                <div className="ml-auto flex gap-2">
                    {isAuthorizedToAccess([USER_ROLES.JUMBO_ADMIN, USER_ROLES.DP_ADMIN]) && (
                        <Button
                            text="Sync"
                            onClick={syncTemplates}
                            isLoading={isSyncing}
                            textLoading="Syncing..."
                            className="w-28"
                            variant="outlined"
                            iconUnicode="E841"
                        />
                    )}

                    <Link to="/create-template">
                        <Button text="Create Template" />
                    </Link>
                </div>
            </div>
            <Modal title="Test Template">
                <div>Test Template</div>
            </Modal>
            {fetchingTemplates ? (
                <FullScreenLoader />
            ) : templateList?.length > 0 ? (
                <div className="card_container">
                    {templateList.map(template => (
                        <Link
                            to={`/templates/${template}`}
                            key={template}
                            className="flex h-[94px] w-full cursor-pointer rounded-md border bg-blue-50  p-2 shadow-sm"
                        >
                            <div className="line-clamp-3 max-w-full break-words">{template}</div>
                        </Link>
                    ))}
                </div>
            ) : (
                <NoResults imgSize={350} />
            )}
        </div>
    );
};

export default TemplateList;
