import { request } from '@zomato/networking';
import { useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import toaster from '@/components/molecules/Toaster';
import { isSuccess, resetFormState, toastError } from '@/utils';

export const useTemplate = ({ formControls }) => {
    const [searchParams, setSearchParams] = useSearchParams();
    const [templateList, setTemplateList] = useState([]);
    const [fetchingTemplates, setFetchingTemplates] = useState(false);
    const [creatingTemplate, setCreatingTemplate] = useState(false);
    const [isSyncing, setIsSyncing] = useState(false);

    const getTemplates = async () => {
        try {
            setFetchingTemplates(true);
            const res = await request('getTemplates');
            const status = res.data?.status;
            if (isSuccess(status)) {
                setTemplateList(res.data?.template_id ?? []);
            } else toastError(res.data);
        } catch (error) {
            toastError(error);
        } finally {
            setFetchingTemplates(false);
        }
    };

    const createTemplate = async data => {
        setCreatingTemplate(true);
        try {
            const res = await request('createTemplate', {
                body: {
                    template: data,
                },
            });
            const tempName = res.data?.template_id ?? 'Unknown';
            const tempVersion = res.data?.template_version ?? 'Unknown';
            const status = res.data?.status;
            if (isSuccess(status)) {
                toaster.success(`Template ${tempName} with version ${tempVersion} created successfully`);
                searchParams.delete('action');
                setSearchParams(searchParams);
                getTemplates();
                resetFormState(formControls);
            } else toastError(res.data);
        } catch (error) {
            toastError(error);
        } finally {
            setCreatingTemplate(false);
        }
    };

    const syncTemplates = async () => {
        setIsSyncing(true);
        try {
            const res = await request('syncTemplates');
            const status = res.data?.status;
            if (isSuccess(status)) getTemplates();
            else toastError(res.data);
        } catch (error) {
            toastError(error);
        } finally {
            setIsSyncing(false);
        }
    };

    return {
        templateList,
        fetchingTemplates,
        getTemplates,
        createTemplate,
        creatingTemplate,
        isSyncing,
        syncTemplates,
    };
};
