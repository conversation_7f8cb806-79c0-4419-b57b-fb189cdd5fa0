@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
    font-family: 'Wasabi';
    font-display: swap;
    src:
        url('https://b.zmtcdn.com/data/o2_assets/5d16cfe0c2551fed364ba6897010f7eb1731662077.ttf') format('truetype'),
        url('https://b.zmtcdn.com/data/o2_assets/b1212e65021a96be0c4289d3a9645d261731662105.woff2') format('woff2'),
        url('https://b.zmtcdn.com/data/o2_assets/c75ab7f1b4f33bf8a4a86414c37a6c9b1731662090.woff') format('woff');
    font-style: normal;
    unicode-range: U+0-10FFFF;
}

@layer base {
    .table_wrapper {
        @apply rounded-md border;
    }

    .table_wrapper table {
        @apply w-full overflow-x-auto rounded-md text-left text-gray-900;
    }

    .table_wrapper table thead {
        @apply bg-[#f1f7f9] text-xs uppercase;
    }

    .table_wrapper table thead tr th {
        @apply px-3 py-3 font-medium;
    }

    .table_wrapper table tbody tr {
        @apply border-b border-[#e3ebed] bg-white text-sm;
    }

    .table_wrapper table tbody tr:last-child {
        @apply border-b-0;
    }

    .table_wrapper table tbody tr td {
        @apply px-3 py-3;
    }

    .input {
        @apply block w-full rounded-md border border-gray-300 bg-white px-2 py-2 text-xs text-gray-900;
    }

    .input:focus {
        @apply border-blue-500;
    }

    .error {
        @apply mt-1 text-xs text-red-500;
    }

    .label {
        @apply mb-2.5 block text-xs font-medium text-gray-600;
    }

    .label span {
        @apply ml-1 text-red-400;
    }

    .label::first-letter {
        text-transform: uppercase;
    }
}

@font-face {
    font-family: Okra;
    src: url(https://b.zmtcdn.com/data/file_assets/a5144e9ee41f343224a9efc3efcbf1bc1698397784.ttf) format('truetype');
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family:
        'Okra',
        'Wasabi',
        sans-serif,
        system-ui,
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        Oxygen,
        Ubuntu,
        Cantarell,
        'Open Sans',
        'Helvetica Neue',
        sans-serif;
}

.disable-scroll {
    overflow: hidden;
}

.shadow-sidebar-toggle {
    box-shadow: 1px 1px 2px 0 rgba(0, 0, 0, 0.2);
}

.card_container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
    place-items: center;
    overflow-x: hidden;
}
