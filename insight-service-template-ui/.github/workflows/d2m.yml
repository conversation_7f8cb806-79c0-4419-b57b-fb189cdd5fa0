name: Raise PR to Master

on:
    push:
        branches:
            - dev

jobs:
    raise-pr:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout code
              uses: actions/checkout@v2

            - name: Check if PR exists
              id: check-pr
              env:
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
              run: |
                  prs=$(gh pr list \
                  --repo "$GITHUB_REPOSITORY" \
                  --json baseRefName,headRefName \
                  --jq '
                      map(select(.baseRefName == "master" and .headRefName == "dev"))
                      | length
                  ')
                  if [[ $prs -gt 0 ]]; then
                     echo "skip=true" >> "$GITHUB_OUTPUT"
                  fi

            - name: Create PR to Master
              if: steps.check-pr.outputs.skip != 'true'
              id: create-pr
              env:
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
              run: |
                  gh pr create --base master --head dev --title d2m --body d2m;
