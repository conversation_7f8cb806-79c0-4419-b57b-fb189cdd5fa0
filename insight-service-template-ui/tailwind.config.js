/* eslint-env node */

/** @type {import('tailwindcss').Config} */

module.exports = {
    content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
    theme: {
        extend: {
            screens: {
                xxs: '320px',
                xs: '440px',
                sm: '640px',
                md: '768px',
                lg: '1024px',
                xl: '1280px',
            },
            boxShadow: {
                sm: '0px 2px 8px 0px rgba(28, 28, 28, 0.08)',
            },
            fontSize: {
                xxs: '0.6875rem',
            },
        },
    },
    plugins: [],
};
