import viteYaml from '@modyfi/vite-plugin-yaml';
import basicSsl from '@vitejs/plugin-basic-ssl';
import react from '@vitejs/plugin-react';
import { defineConfig, loadEnv } from 'vite';
import jsconfigPaths from 'vite-jsconfig-paths';
import EnvironmentPlugin from 'vite-plugin-environment';

export default defineConfig(({ mode }) => {
    const envPrefix = ['VITE_APP_', 'REACT_APP_'];
    // eslint-disable-next-line no-undef
    const env = loadEnv(mode, process.cwd(), envPrefix);
    function replaceEnv(env, jsString) {
        return Object.entries(env).reduce((updateStr, [key, value]) => {
            return updateStr.replaceAll(key, value);
        }, jsString);
    }
    return {
        plugins: [
            react(),
            EnvironmentPlugin({
                REACT_APP_COOKIE_DOMAIN: env.REACT_APP_COOKIE_DOMAIN,
                REACT_APP_ACCOUNTS_DOMAIN: env.REACT_APP_ACCOUNTS_DOMAIN,
                REACT_APP_GOOGLE_CID: env.REACT_APP_GOOGLE_CID,
                REACT_APP_GW_DOMAIN: env.REACT_APP_GW_DOMAIN,
                REACT_APP_API_BASE_URL: env.REACT_APP_API_BASE_URL,
            }),
            (() => {
                const yamlFiles = viteYaml();
                const { transform: yamlHandler } = yamlFiles;
                async function transform(code, id) {
                    const modifiedJsStringData = await yamlHandler(code, id);
                    const jsString = modifiedJsStringData?.code;
                    if (!jsString) {
                        return null;
                    }
                    const stringInjectedWithEnvs = replaceEnv(env, jsString);
                    return { ...modifiedJsStringData, code: stringInjectedWithEnvs };
                }
                return { ...yamlFiles, transform };
            })(),
            jsconfigPaths(),
            basicSsl(),
        ],
        envPrefix,
        server: {
            host: 'test.eks.zdev.net',
            https: true,
        },
        build: {
            outDir: 'build',
        },
        base: '/jumbo/insights-service/dashboard',
    };
});
